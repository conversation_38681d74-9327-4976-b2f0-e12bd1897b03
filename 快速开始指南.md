# 船舶综合管理系统 - 快速开始指南

## 环境要求

### 基础环境
- **JDK**: 17 或更高版本
- **Maven**: 3.6.0 或更高版本
- **Node.js**: 16.0 或更高版本 (前端开发需要)
- **Git**: 版本控制工具

### 中间件环境
- **MySQL**: 8.0.33 或更高版本
- **Redis**: 6.2.12 或更高版本
- **Nacos**: 2.2.2 版本

## 快速部署

### 1. 环境准备

#### 安装MySQL
```bash
# Docker方式安装MySQL
docker run -d --name mysql \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=Lm19941117@ \
  -e MYSQL_DATABASE=yumeng-cloud \
  mysql:8.0.33
```

#### 安装Redis
```bash
# Docker方式安装Redis
docker run -d --name redis \
  -p 6379:6379 \
  redis:6.2.12
```

#### 安装Nacos
```bash
# Docker方式安装Nacos
docker run -d --name nacos \
  -p 8848:8848 \
  -p 9848:9848 \
  -p 9849:9849 \
  -e MODE=standalone \
  nacos/nacos-server:v2.2.2
```

### 2. 数据库初始化

#### 导入SQL脚本
```bash
# 进入项目sql目录
cd sql/

# 导入核心数据库
mysql -h localhost -u root -p yumeng-cloud < yumeng-cloud.sql

# 导入其他数据库
mysql -h localhost -u root -p yumeng-job < yumeng-job.sql
mysql -h localhost -u root -p yumeng-workflow < yumeng-workflow.sql
mysql -h localhost -u root -p yumeng-salary < yumeng-salary.sql
mysql -h localhost -u root -p yumeng-warehouse < yumeng-warehouse.sql
mysql -h localhost -u root -p yumeng-seata < yumeng-seata.sql
```

### 3. 配置Nacos

#### 访问Nacos控制台
- 地址: http://localhost:8848/nacos
- 用户名: nacos
- 密码: nacos

#### 导入配置文件
将 `config/nacos/` 目录下的配置文件导入到Nacos配置中心：
- `datasource.yml` - 数据源配置
- `redis.yml` - Redis配置
- `yumeng-gateway.yml` - 网关配置
- `yumeng-ai.yml` - AI模块配置

### 4. 启动服务

#### 启动顺序
1. **Nacos服务** (已启动)
2. **认证中心**
3. **网关服务**
4. **业务服务**

#### 启动认证中心
```bash
cd yumeng-auth
mvn spring-boot:run
```

#### 启动网关服务
```bash
cd yumeng-gateway
mvn spring-boot:run
```

#### 启动系统管理服务
```bash
cd yumeng-modules/yumeng-system
mvn spring-boot:run
```

#### 启动其他业务服务
```bash
# 工作流服务
cd yumeng-modules/yumeng-workflow
mvn spring-boot:run

# 资源管理服务
cd yumeng-modules/yumeng-resource
mvn spring-boot:run

# AI聊天服务
cd yumeng-ai/yumeng-chat
mvn spring-boot:run
```

### 5. 验证部署

#### 检查服务注册
访问Nacos控制台，确认所有服务都已注册成功：
- yumeng-auth
- yumeng-gateway
- yumeng-system
- yumeng-workflow
- yumeng-resource
- yumeng-chat

#### 测试接口
```bash
# 测试网关健康检查
curl http://localhost:8080/actuator/health

# 测试登录接口
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## 开发环境配置

### IDE配置

#### IDEA配置
1. 导入项目到IntelliJ IDEA
2. 配置JDK版本为17
3. 配置Maven设置
4. 安装Lombok插件
5. 配置代码格式化规则

#### VS Code配置
1. 安装Java扩展包
2. 安装Spring Boot扩展
3. 配置Maven路径
4. 安装代码格式化插件

### 本地开发配置

#### 修改hosts文件
```bash
# 添加以下内容到hosts文件
127.0.0.1 nacos-server
127.0.0.1 mysql-server
127.0.0.1 redis-server
```

#### 配置环境变量
```bash
export JAVA_HOME=/path/to/jdk17
export MAVEN_HOME=/path/to/maven
export PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$PATH
```

## Docker部署

### 使用Docker Compose

#### 创建docker-compose.yml
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0.33
    container_name: yumeng-mysql
    environment:
      MYSQL_ROOT_PASSWORD: Lm19941117@
      MYSQL_DATABASE: yumeng-cloud
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d

  redis:
    image: redis:6.2.12
    container_name: yumeng-redis
    ports:
      - "6379:6379"

  nacos:
    image: nacos/nacos-server:v2.2.2
    container_name: yumeng-nacos
    environment:
      MODE: standalone
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"

volumes:
  mysql_data:
```

#### 启动所有服务
```bash
docker-compose up -d
```

### 构建应用镜像

#### 构建网关服务镜像
```bash
cd yumeng-gateway
docker build -t yumeng/yumeng-gateway:latest .
```

#### 构建其他服务镜像
```bash
# 认证中心
cd yumeng-auth
docker build -t yumeng/yumeng-auth:latest .

# 系统管理
cd yumeng-modules/yumeng-system
docker build -t yumeng/yumeng-system:latest .
```

## 常见问题

### 启动问题

#### 服务无法注册到Nacos
**问题**: 服务启动后无法在Nacos控制台看到
**解决方案**:
1. 检查Nacos服务是否正常运行
2. 检查网络连接是否正常
3. 检查配置文件中的Nacos地址是否正确
4. 检查防火墙设置

#### 数据库连接失败
**问题**: 应用启动时数据库连接异常
**解决方案**:
1. 检查MySQL服务是否启动
2. 检查数据库连接配置是否正确
3. 检查数据库用户权限
4. 检查网络连接

#### Redis连接失败
**问题**: 缓存功能异常，Redis连接失败
**解决方案**:
1. 检查Redis服务是否启动
2. 检查Redis连接配置
3. 检查Redis密码设置
4. 检查网络连接

### 配置问题

#### 端口冲突
**问题**: 服务启动时提示端口被占用
**解决方案**:
1. 使用 `netstat -an | grep 端口号` 查看端口占用
2. 修改application.yml中的端口配置
3. 停止占用端口的进程

#### 配置文件加载失败
**问题**: Nacos配置中心的配置无法加载
**解决方案**:
1. 检查Nacos配置是否正确导入
2. 检查配置文件格式是否正确
3. 检查配置分组和命名空间
4. 重启服务重新加载配置

## 性能调优

### JVM参数优化
```bash
# 生产环境JVM参数建议
-Xms2g -Xmx2g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/logs/heapdump.hprof
```

### 数据库连接池优化
```yaml
spring:
  datasource:
    dynamic:
      hikari:
        maximum-pool-size: 20
        minimum-idle: 10
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
```

### Redis连接优化
```yaml
spring:
  redis:
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
```

## 监控配置

### 启用Actuator监控
```yaml
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
```

### 配置日志级别
```yaml
logging:
  level:
    com.ym: debug
    org.springframework.cloud: info
    com.alibaba.nacos: info
```

## 下一步

1. **前端集成**: 配置前端项目并连接API
2. **业务开发**: 根据需求开发具体业务功能
3. **测试部署**: 配置测试环境进行集成测试
4. **生产部署**: 配置生产环境并上线

## 技术支持

- **文档地址**: [项目详细文档.md](./项目详细文档.md)
- **问题反馈**: 通过GitLab Issues提交问题
- **技术交流**: 联系开发团队获取支持

---

*快速开始指南最后更新时间: 2025-08-01*
