package com.ym.service;

import com.ym.domain.bo.QueryVectorBo;
import com.ym.domain.bo.StoreEmbeddingBo;

import java.util.List;

/**
 * 向量库管理
 * <AUTHOR>
 */
public interface VectorStoreService {

    void storeEmbeddings(StoreEmbeddingBo storeEmbeddingBo);

    List<String> getQueryVector(QueryVectorBo queryVectorBo);

    void createSchema(String kid,String modelName);

    void removeById(String id,String modelName);


}
