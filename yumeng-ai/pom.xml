<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ym</groupId>
        <artifactId>ship-Integrated-management-api</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>yumeng-ai</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>yumeng-chat-api</module>
        <module>yumeng-knowledge-api</module>
        <module>yumeng-chat</module>
    </modules>
    <description>
        yuemng-ai ai模块
    </description>
    <dependencies>
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-elasticsearch</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-sensitive</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-seata</artifactId>
        </dependency>
    </dependencies>
</project>
