import request from "@/utils/request";
import { AxiosPromise } from "axios";
import {
  ChannelVO,
  ChannelForm,
  ChannelQuery,
} from "@/api/salary/channel/types";

/**
 * 查询代发渠道 列表
 * @param query
 * @returns {*}
 */

export const listChannel = (
  query?: ChannelQuery,
): AxiosPromise<ChannelVO[]> => {
  return request({
    url: "/salary/channel/list",
    method: "get",
    params: query,
  });
};

/**
 * 查询代发渠道 详细
 * @param id
 */
export const getChannel = (id: string | number): AxiosPromise<ChannelVO> => {
  return request({
    url: "/salary/channel/" + id,
    method: "get",
  });
};

/**
 * 新增代发渠道
 * @param data
 */
export const addChannel = (data: ChannelForm) => {
  return request({
    url: "/salary/channel",
    method: "post",
    data: data,
  });
};

/**
 * 获取有效的代发渠道列表
 * @returns {*}
 */
export const getRmbChannelList = (): AxiosPromise<ChannelVO[]> => {
  return request({
    url: "/salary/channel/RmbList",
    method: "get",
  });
};

/**
 * 修改代发渠道
 * @param data
 */
export const updateChannel = (data: ChannelForm) => {
  return request({
    url: "/salary/channel",
    method: "put",
    data: data,
  });
};

/**
 * 删除代发渠道
 * @param id
 */
export const delChannel = (id: string | number | Array<string | number>) => {
  return request({
    url: "/salary/channel/" + id,
    method: "delete",
  });
};

/**
 * 更新渠道状态
 * @param id 渠道ID
 * @param enable 状态(1-有效，0-无效)
 */
export const updateChannelStatus = (id: string | number, enable: string) => {
  return request({
    url: "/salary/channel/status",
    method: "put",
    data: { id, enable },
  });
};
