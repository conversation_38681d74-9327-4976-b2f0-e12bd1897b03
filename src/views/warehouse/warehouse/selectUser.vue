<template>
  <el-row>
    <el-dialog
      v-model="visible"
      title="选择库管员"
      width="800px"
      top="5vh"
      append-to-body
    >
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="用户名称" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号码" prop="phonenumber">
          <el-input
            v-model="queryParams.phonenumber"
            placeholder="请输入手机号码"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row>
        <el-table
          ref="tableRef"
          :data="userList"
          height="260px"
          @row-click="clickRow"
          highlight-current-row
        >
          <el-table-column align="center" width="55">
            <template #default="scope">
              <el-radio
                v-model="selectedUserId"
                :label="scope.row.userId"
                @change="handleRadioChange(scope.row)"
              >&nbsp;</el-radio>
            </template>
          </el-table-column>
          <el-table-column
            label="用户名称"
            prop="userName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="用户昵称"
            prop="nickName"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="邮箱"
            prop="email"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="手机"
            prop="phonenumber"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <dict-tag
                :options="sys_normal_disable"
                :value="scope.row.status"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSelectUser">确 定</el-button>
          <el-button @click="visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-row>
</template>

<script setup name="WarehouseSelectUser" lang="ts">
import { defineExpose, defineEmits } from "vue";
import { unallocatedUserList } from "@/api/system/role";
import { UserVO } from "@/api/system/user/types";
import { UserQuery } from "@/api/system/user/types";

const props = defineProps({
  roleId: {
    type: [Number, String],
    default: 0
  },
  // 已选择的用户ID，用于默认选中
  selectedUser: {
    type: Object,
    default: null
  }
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable } = toRefs<any>(
  proxy?.useDict("sys_normal_disable")
);

const userList = ref<UserVO[]>([]);
const visible = ref(false);
const total = ref(0);
const userIds = ref<Array<string | number>>([]);
const selectedUserId = ref<string | number>('');

const queryParams = reactive<UserQuery>({
  pageNum: 1,
  pageSize: 10,
  roleId: undefined,
  userName: undefined,
  phonenumber: undefined,
});

const tableRef = ref<ElTableInstance>();
const queryFormRef = ref<ElFormInstance>();

const show = () => {
  queryParams.roleId = props.roleId;
  getList();
  visible.value = true;
  
  // 如果传入了已选择的用户，默认选中
  if (props.selectedUser && props.selectedUser.userId) {
    selectedUserId.value = props.selectedUser.userId;
    userIds.value = [props.selectedUser.userId];
  } else {
    selectedUserId.value = '';
    userIds.value = [];
  }
};

/**
 * 处理单选框变化
 */
const handleRadioChange = (row: any) => {
  userIds.value = [row.userId];
  tableRef.value?.setCurrentRow(row);
};

/**
 * 选择行
 */
const clickRow = (row: any) => {
  selectedUserId.value = row.userId;
  userIds.value = [row.userId];
  tableRef.value?.setCurrentRow(row);
};

/** 查询数据 */
const getList = async () => {
  const res = await unallocatedUserList(queryParams);
  userList.value = res.rows;
  total.value = res.total;
  
  // 加载数据后，如果有已选用户，尝试选中
  if (selectedUserId.value && userList.value.length > 0) {
    const selectedUser = userList.value.find((user) => user.userId === selectedUserId.value);
    if (selectedUser) {
      tableRef.value?.setCurrentRow(selectedUser);
    }
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  getList();
};

const emit = defineEmits(["ok"]);
/**选择用户操作 */
const handleSelectUser = () => {
  if (userIds.value.length === 0) {
    proxy?.$modal.msgError("请选择用户");
    return;
  }

  const selectedUser = userList.value.find((user) =>
    userIds.value.includes(user.userId)
  );

  if (!selectedUser) {
    proxy?.$modal.msgError("未找到选中的用户");
    return;
  }

  emit("ok", [selectedUser]);
  visible.value = false;
};

// 暴露
defineExpose({
  show,
  selectedUserId,
  handleRadioChange,
  clickRow,
  userList
});
</script>

<style scoped></style> 