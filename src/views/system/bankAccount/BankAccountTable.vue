<template>
  <div class="dynamic-table-container">
    <el-button class="config-button" @click="handleConfigClick">
      <el-icon><Setting /></el-icon> 配置表格
    </el-button>
    <el-table
      ref="tableRef"
      :data="tableData"
      border
      style="width: 100%"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      @header-dragend="handleHeaderDragEnd"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        v-for="column in orderedVisibleColumns"
        :key="column.columnName"
        :prop="column.columnName"
        :label="column.columnLabel || column.columnName"
        :align="column.align || 'center'"
        :width="column.width"
        :sortable="column.sortable !== false"
      >
        <template #default="scope">
          <!-- 币种类型列的特殊处理 -->
          <template v-if="column.columnName === 'currencyType'">
            <el-tag v-if="scope.row.currencyType === '1'">人民币</el-tag>
            <el-tag type="warning" v-else-if="scope.row.currencyType === '2'">外币</el-tag>
            <el-tag type="info" v-else>外币/人民币</el-tag>
          </template>

          <!-- Swift Code的特殊处理 -->
          <template v-else-if="column.columnName === 'swiftCode'">
            <div v-if="scope.row.swiftCode" class="swift-code-container">
              <el-tag type="info" size="small" class="swift-code-tag">
                <el-icon><CreditCard /></el-icon>
                {{ scope.row.swiftCode }}
              </el-tag>
              <el-tooltip content="点击复制Swift Code" placement="top">
                <el-button
                  link
                  type="primary"
                  size="small"
                  @click="copySwiftCode(scope.row.swiftCode)"
                  class="copy-btn"
                >
                  <el-icon><DocumentCopy /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
            <span v-else class="text-muted">-</span>
          </template>
          <!-- 银行卡号的特殊处理（脱敏显示） -->
          <template v-else-if="column.columnName === 'cardNumber'">
            <div class="card-number-container">
              <span class="card-number">
                {{ showFullCardNumbers.has(scope.row.id) ? scope.row.cardNumber : formatCardNumber(scope.row.cardNumber) }}
              </span>
              <el-tooltip :content="showFullCardNumbers.has(scope.row.id) ? '点击隐藏卡号' : '点击查看完整卡号'" placement="top">
                <el-button
                  link
                  type="primary"
                  size="small"
                  @click="toggleCardNumber(scope.row)"
                  class="toggle-btn"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
          <!-- 其他列使用默认渲染 -->
          <template v-else>
            {{ scope.row[column.columnName] || '-' }}
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="180"
        class-name="small-padding fixed-width"
        align="center"
      >
        <template #default="scope">
          <el-tooltip content="编辑" placement="top">
            <el-button link type="primary" @click="handleEdit(scope.row)" v-hasPermi="['system:bankAccount:edit']">编辑</el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="danger" @click="handleDelete(scope.row)" v-hasPermi="['system:bankAccount:remove']">删除</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 配置面板 -->
    <el-drawer v-model="showConfigPanel" title="表格配置" size="900px">
      <el-form :model="tableConfig">
        <el-divider>列配置</el-divider>
        <div
          class="column-config-panel"
          style="
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 32px;
          "
        >
          <!-- 可用列 -->
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: flex-start;
            "
          >
            <div>可用列 ({{ availableColumns.length }})</div>
            <select
              multiple
              size="40"
              v-model="selectedAvailable"
              style="min-width: 280px; max-width: 320px"
            >
              <option
                v-for="col in availableColumns"
                :key="col.columnName"
                :value="col.columnName"
                :style="' font-size: 16px;'"
              >
                {{ col.columnLabel }}
              </option>
            </select>
          </div>
          <!-- 操作按钮 -->
          <div
            style="
              display: flex;
              flex-direction: column;
              gap: 12px;
              align-items: center;
              justify-content: center;
            "
          >
            <el-button
              @click="moveToAvailable"
              type="primary"
              :icon="ArrowLeftBold"
              :disabled="!selectedVisible.length || hasFixedSelected"
              style="margin: 0"
            ></el-button>
            <el-button
              @click="moveToVisible"
              type="primary"
              :disabled="!selectedAvailable.length"
              :icon="ArrowRightBold"
              style="margin: 0"
            ></el-button>
          </div>
          <!-- 显示列 -->
          <div style="display: flex; flex-direction: row; align-items: center">
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: flex-start;
              "
            >
              <div>显示列 ({{ visibleColumnsList.length }})</div>
              <select
                multiple
                size="40"
                v-model="selectedVisible"
                style="min-width: 280px; max-width: 320px"
              >
                <option
                  v-for="col in visibleColumnsList"
                  :key="col.columnName"
                  :value="col.columnName"
                  :style="
                    (col.fixed ? 'color: red; font-weight: bold;' : '') +
                    ' font-size: 16px;'
                  "
                >
                  {{ col.columnLabel }}<span v-if="col.fixed">*</span>
                </option>
              </select>
            </div>
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-left: 12px;
                gap: 12px;
              "
            >
              <el-button
                @click="moveUp"
                :disabled="!canMoveUp"
                type="primary"
                :icon="ArrowUp"
                style="margin: 0"
              ></el-button>
              <el-button
                @click="moveDown"
                :disabled="!canMoveDown"
                type="primary"
                :icon="ArrowDown"
                style="margin: 0"
              ></el-button>
            </div>
          </div>
        </div>
        <p style="font-size: 14px; margin-left: 75%">* = 一直展示</p>
      </el-form>
      <template #footer>
        <el-button @click="showConfigPanel = false">取消</el-button>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, getCurrentInstance } from "vue";
import {
  ArrowRightBold,
  ArrowLeftBold,
  ArrowUp,
  ArrowDown,
  Setting,
  CreditCard,
  DocumentCopy,
  View,
} from "@element-plus/icons-vue";
import { useTableConfigStore } from "@/store/tableConfig";

// 定义组件的输入属性（Props）
const props = defineProps({
  tableData: {
    type: Array,
    required: true,
    // 表格数据数组，包含要显示的行数据
  },
  defaultColumns: {
    type: Array,
    required: true,
    // 默认显示的列名数组
  },
  configName: {
    type: String,
    required: true,
    // 表格配置的唯一标识名，用于保存和加载配置
  },
  loading: {
    type: Boolean,
    default: false,
    // 表格加载状态，控制loading动画显示
  },
});

// 定义组件的事件输出（Emits）
const emit = defineEmits([
  'selection-change',  // 选择变化事件
  'edit',             // 编辑事件
  'delete'            // 删除事件
]);

// 初始化表格配置状态管理
const tableConfigStore = useTableConfigStore();
// 控制配置面板的显示/隐藏状态
const showConfigPanel = ref(false);
// 表格配置数据
const tableConfig = ref({
  columns: [],
  visibleColumnKeys: [],
  configName: props.configName,
});

const tableRef = ref();
const { proxy } = getCurrentInstance();

// 固定列定义
const defaultFixedColumns = ["belongName", "cardNumber", "depositBank"];

// 默认列配置
const getDefaultColumns = () => {
  const columnMap = {
    belongName: { columnName: "belongName", columnLabel: "账户名称", fixed: true, align: "center", width: 150 },
    cardNumber: { columnName: "cardNumber", columnLabel: "银行卡号", fixed: true, align: "center", width: 180 },
    cardHolder: { columnName: "cardHolder", columnLabel: "持卡人", align: "center", width: 120 },
    alias: { columnName: "alias", columnLabel: "账户别名", align: "center", width: 100 },
    depositBank: { columnName: "depositBank", columnLabel: "开户银行", fixed: true, align: "center", width: 150 },
    depositBranch: { columnName: "depositBranch", columnLabel: "开户支行", align: "center", width: 200 },
    currencyType: { columnName: "currencyType", columnLabel: "币种类型", align: "center", width: 100 },
    swiftCode: { columnName: "swiftCode", columnLabel: "Swift Code", align: "center", width: 120 },
    reservedPhone: { columnName: "reservedPhone", columnLabel: "预留手机号", align: "center", width: 120 },
    belongShip: { columnName: "belongShip", columnLabel: "所属船舶", align: "center", width: 120 },
    notes: { columnName: "notes", columnLabel: "备注", align: "center", width: 200 },
  };

  return props.defaultColumns.map(col => columnMap[col] || { columnName: col, columnLabel: col, align: "center" });
};

// 计算有序可见列
const orderedVisibleColumns = computed(() => {
  return tableConfig.value.visibleColumnKeys
    .map((key) =>
      tableConfig.value.columns.find((col) => col.columnName === key),
    )
    .filter(Boolean);
});

// 用于存储显示完整卡号的行ID
const showFullCardNumbers = ref(new Set());

// 格式化银行卡号（脱敏显示）
const formatCardNumber = (cardNumber) => {
  if (!cardNumber) return '-';
  if (cardNumber.length <= 8) return cardNumber;
  return cardNumber.substring(0, 4) + '****' + cardNumber.substring(cardNumber.length - 4);
};

// 切换银行卡号显示状态
const toggleCardNumber = (row) => {
  if (showFullCardNumbers.value.has(row.id)) {
    showFullCardNumbers.value.delete(row.id);
  } else {
    showFullCardNumbers.value.add(row.id);
  }
};

// 复制Swift Code到剪贴板
const copySwiftCode = async (swiftCode) => {
  try {
    await navigator.clipboard.writeText(swiftCode);
    proxy?.$modal.msgSuccess('Swift Code已复制到剪贴板');
  } catch (err) {
    // 降级方案：使用传统方法复制
    const textArea = document.createElement('textarea');
    textArea.value = swiftCode;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    proxy?.$modal.msgSuccess('Swift Code已复制到剪贴板');
  }
};

// 加载配置
const loadConfig = async () => {
  const config = await tableConfigStore.loadConfig(props.configName);
  if (config && config.columns) {
    // 只提取需要的字段
    const columns = config.columns.map((col) => ({
      columnName: col.columnName,
      columnLabel: col.columnLabel,
      fixed: defaultFixedColumns.includes(col.columnName),
      visible: col.visible,
      sortOrder: col.sortOrder,
      width: col.width,
      align: col.align || "center",
      sortable: col.sortable !== false,
      customRender: col.customRender,
    }));

    // 计算 visibleColumnKeys，按 sortOrder 升序
    const visibleColumnKeys = columns
      .filter((col) => col.visible)
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
      .map((col) => col.columnName);

    // 顺序：先展示列，再剩余列
    const visibleSet = new Set(visibleColumnKeys);
    const visibleCols = visibleColumnKeys
      .map((key) => columns.find((col) => col.columnName === key))
      .filter(Boolean);
    const restCols = columns.filter((col) => !visibleSet.has(col.columnName));

    tableConfig.value.columns = [...visibleCols, ...restCols];
    tableConfig.value.visibleColumnKeys = visibleColumnKeys;
    tableConfig.value.configName = props.configName;
    if (config.id) tableConfig.value.id = config.id;
  } else {
    // 使用默认配置
    const defaultCols = getDefaultColumns();
    tableConfig.value.columns = defaultCols.map((col) => ({
      ...col,
      fixed: defaultFixedColumns.includes(col.columnName),
    }));
    tableConfig.value.visibleColumnKeys = defaultCols.map(col => col.columnName);
    tableConfig.value.configName = props.configName;
  }
};

// 处理配置按钮点击
const handleConfigClick = async () => {
  await loadConfig();
  showConfigPanel.value = true;
};

// 配置面板相关的响应式变量
const selectedAvailable = ref([]);
const selectedVisible = ref([]);

const availableColumns = computed(() =>
  tableConfig.value.columns.filter(
    (col) => !tableConfig.value.visibleColumnKeys.includes(col.columnName),
  ),
);

const visibleColumnsList = computed(() =>
  tableConfig.value.visibleColumnKeys
    .map((key) =>
      tableConfig.value.columns.find((col) => col.columnName === key),
    )
    .filter(Boolean),
);

const hasFixedSelected = computed(() => {
  return selectedVisible.value.some((key) => {
    const col = tableConfig.value.columns.find((c) => c.columnName === key);
    return col && col.fixed;
  });
});

const moveToVisible = () => {
  tableConfig.value.visibleColumnKeys.push(...selectedAvailable.value);
  selectedAvailable.value = [];
};

const moveToAvailable = () => {
  const toRemove = selectedVisible.value.filter((key) => {
    const col = tableConfig.value.columns.find((c) => c.columnName === key);
    return !col.fixed;
  });
  tableConfig.value.visibleColumnKeys =
    tableConfig.value.visibleColumnKeys.filter(
      (key) => !toRemove.includes(key),
    );
  selectedVisible.value = [];
};

const canMoveUp = computed(() => {
  if (selectedVisible.value.length !== 1) return false;
  const idx = tableConfig.value.visibleColumnKeys.indexOf(
    selectedVisible.value[0],
  );
  return idx > 0;
});

const canMoveDown = computed(() => {
  if (selectedVisible.value.length !== 1) return false;
  const idx = tableConfig.value.visibleColumnKeys.indexOf(
    selectedVisible.value[0],
  );
  return idx !== -1 && idx < tableConfig.value.visibleColumnKeys.length - 1;
});

const moveUp = () => {
  if (selectedVisible.value.length === 1) {
    const idx = tableConfig.value.visibleColumnKeys.indexOf(
      selectedVisible.value[0],
    );
    if (idx > 0) {
      const arr = tableConfig.value.visibleColumnKeys;
      [arr[idx - 1], arr[idx]] = [arr[idx], arr[idx - 1]];
    }
  }
};

const moveDown = () => {
  if (selectedVisible.value.length === 1) {
    const idx = tableConfig.value.visibleColumnKeys.indexOf(
      selectedVisible.value[0],
    );
    if (idx < tableConfig.value.visibleColumnKeys.length - 1) {
      const arr = tableConfig.value.visibleColumnKeys;
      [arr[idx], arr[idx + 1]] = [arr[idx + 1], arr[idx]];
    }
  }
};

// 保存配置
const saveConfig = async () => {
  proxy?.$modal.loading("正在保存配置，请稍候...");
  // 获取真实宽度
  await nextTick(); // 确保表格已渲染
  const colEls =
    tableRef.value?.$el.querySelectorAll(
      ".el-table__header-wrapper colgroup col",
    ) || [];
  const colWidthMap = {};
  colEls.forEach((colEl, idx) => {
    const width = colEl.getAttribute("width");
    // 由于有选择列，需要调整索引
    // colEls[0] 是选择列，colEls[1] 开始才是数据列
    const col = orderedVisibleColumns.value[idx - 1]; // 减1是因为选择列占用了索引0
    if (col && width) {
      colWidthMap[col.columnName] = Number(width);
    }
  });

  const visibleKeys = tableConfig.value.visibleColumnKeys;
  const columns = tableConfig.value.columns.map((col) => ({
    columnName: col.columnName,
    columnLabel: col.columnLabel,
    fixed: !!col.fixed,
    visible: visibleKeys.includes(col.columnName),
    sortOrder: visibleKeys.includes(col.columnName)
      ? visibleKeys.indexOf(col.columnName) + 1
      : 0,
    width: colWidthMap[col.columnName] || undefined,
    align: col.align,
    sortable: col.sortable,
    customRender: col.customRender,
  }));

  const data = {
    id: tableConfig.value.id || undefined,
    configName: tableConfig.value.configName,
    columns,
  };

  console.log('发送到后端的数据:', JSON.stringify(data));
  await tableConfigStore.saveConfig(props.configName, data);
  showConfigPanel.value = false;
  await loadConfig();
  proxy?.$modal.closeLoading();
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection);
};

// 处理表头拖拽结束
const handleHeaderDragEnd = async (newWidth, column, event) => {
  // 由于Element Plus的header-dragend事件参数可能不包含列信息
  // 我们使用DOM方法来获取所有列的当前宽度并更新配置
  await nextTick();

  const colEls = tableRef.value?.$el.querySelectorAll(
    ".el-table__header-wrapper colgroup col",
  ) || [];

  // 更新所有列的宽度
  colEls.forEach((colEl, idx) => {
    const width = colEl.getAttribute("width");
    // 跳过选择列（索引0）
    const col = orderedVisibleColumns.value[idx - 1];
    if (col && width) {
      const numWidth = Number(width);
      col.width = numWidth;
    }
  });

  // 保存配置
  saveConfig();
};

// 处理编辑
const handleEdit = (row) => {
  emit('edit', row);
};

// 处理删除
const handleDelete = (row) => {
  emit('delete', row);
};

// 组件挂载时加载配置
onMounted(() => {
  loadConfig();
});
</script>

<style scoped>
.column-config-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.drag-handle {
  cursor: move;
}

.config-button {
  margin-bottom: 10px;
  float: right;
}

.dynamic-table-container {
  clear: both;
}

.transfer-footer {
  margin-left: 15px;
  padding: 6px 5px;
}

.move-btn-group {
  display: flex;
  justify-content: flex-end;
  margin-right: 8px;
}

.move-btn-group-side {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 16px;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.move-btn-group-side .el-button {
  margin-left: 0 !important;
  margin-right: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Swift Code 相关样式 */
.swift-code-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.swift-code-tag {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  letter-spacing: 1px;
}

.copy-btn {
  padding: 2px 4px;
  min-height: auto;
}

/* 银行卡号相关样式 */
.card-number-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-number {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  letter-spacing: 1px;
}

.toggle-btn {
  padding: 2px 4px;
  min-height: auto;
}

.text-muted {
  color: #909399;
}

.transfer-wrapper {
  display: flex;
  align-items: flex-start;
  position: relative;
}

.center-row {
  display: flex;
  justify-content: center;
  width: 100%;
}

.swift-code {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #409EFF;
}

.card-number {
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

.text-muted {
  color: #999;
}
</style>
