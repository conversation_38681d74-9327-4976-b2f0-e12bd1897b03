<template>
  <div class="p-2">
    <transition
      :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave"
    >
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form
            ref="queryFormRef"
            :model="queryParams"
            :inline="true"
            label-width="100px"
          >
            <el-form-item label="审核业务类型" prop="businessType">
              <el-select
                v-model="queryParams.businessType"
                placeholder="请选择审核业务类型"
                clearable
              >
                <el-option
                  v-for="dict in business_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['salary:workorderConfig:add']"
              >新增流程</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="workorderConfigList">
        <el-table-column label="业务类型" align="center" prop="businessType">
          <template #default="scope">
            <dict-tag
              :options="business_type"
              :value="scope.row.businessType"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['salary:workorderConfig:edit']"
              >编辑</el-button
            >
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['salary:workorderConfig:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 添加/编辑审批流程对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="800px"
      append-to-body
    >
      <el-form
        ref="workorderConfigFormRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="form.businessType" placeholder="请选择业务类型">
            <el-option
              v-for="dict in business_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>

        <!-- 审批节点配置 -->
        <el-form-item label="审批节点">
          <div class="flex flex-wrap gap-4">
            <!-- 已添加的审批人 -->
            <template v-for="(node, index) in form.approvalNodes" :key="index">
              <div class="flex items-center">
                <!-- 审批人卡片 -->
                <div
                  class="relative w-[120px] h-[120px] border rounded-lg flex items-center justify-center"
                >
                  <template v-if="node.approver">
                    <div class="text-center">
                      <el-avatar :size="50" class="mb-2">{{
                        node.approver.auditingUserName.charAt(0)
                      }}</el-avatar>
                      <div class="text-sm">
                        {{ node.approver.auditingUserName }}
                      </div>
                    </div>
                    <el-button
                      class="absolute top-1 right-1"
                      type="danger"
                      link
                      @click="removeNode(index)"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </template>
                  <template v-else>
                    <el-icon class="text-2xl text-gray-400"><Plus /></el-icon>
                  </template>
                </div>
                <div
                  v-if="index < form.approvalNodes.length - 1"
                  class="flex items-center"
                >
                  <div class="w-[30px] h-[2px] bg-gray-300"></div>
                  <el-icon class="text-gray-300"><ArrowRight /></el-icon>
                </div>
              </div>
            </template>

            <!-- 添加新节点按钮 -->
            <div
              class="w-[120px] h-[120px] border border-dashed rounded-lg flex items-center justify-center cursor-pointer hover:border-primary"
              @click="handleAddApprover"
            >
              <el-icon class="text-2xl text-gray-400"><Plus /></el-icon>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改选择审批人的对话框 -->
    <select-user
      ref="selectUserRef"
      :roleId="0"
      :existingApprovers="form.approvalNodes.map((node) => node.approver)"
      @ok="handleSelectUserOk"
    />
  </div>
</template>

<script setup name="WorkorderConfig" lang="ts">
import {
  listWorkorderConfig,
  getWorkorderConfig,
  delWorkorderConfig,
  addWorkorderConfig,
  updateWorkorderConfig,
} from "@/api/salary/workorderConfig";
import {
  WorkorderConfigVO,
  WorkorderConfigQuery,
  WorkorderConfigForm,
} from "@/api/salary/workorderConfig/types";
import { listUser } from "@/api/system/user";
import SelectUser from "@/views/salary/workorderConfig/selectUser.vue";

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { business_type } = toRefs<any>(proxy?.useDict("business_type"));

const workorderConfigList = ref<WorkorderConfigVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const workorderConfigFormRef = ref<ElFormInstance>();
const selectUserRef = ref();

const dialog = reactive<DialogOption>({
  visible: false,
  title: "",
});

const approverDialog = reactive({
  visible: false,
  selectedApprover: null,
  currentIndex: -1,
});

const initFormData = {
  id: undefined,
  businessType: undefined,
  approvalNodes: [],
};
const data = reactive<PageData<WorkorderConfigForm, WorkorderConfigQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    businessType: undefined,
    params: {},
  },
  rules: {
    businessType: [
      { required: true, message: "业务类型不能为空", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询审批配置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listWorkorderConfig(queryParams.value);
  workorderConfigList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  workorderConfigFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WorkorderConfigVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  form.value.approvalNodes = []; // 确保清空审批节点
  dialog.visible = true;
  dialog.title = "添加审批配置";
};

/** 修改按钮操作 */
const handleUpdate = async (row?: WorkorderConfigVO) => {
  reset();
  const businessType = row?.businessType;
  const res = await getWorkorderConfig(businessType);

  // 将返回的数据映射到表单结构
  form.value = {
    id: res.data.id,
    businessType: res.data.businessType,
    approvalNodes: res.data.auditingUserConfigBoList.map((item) => ({
      approver: {
        auditingUserId: item.auditingUserId,
        auditingUserName: item.auditingUserName,
        sort: item.sort,
      },
    })),
  };

  dialog.visible = true;
  dialog.title = "修改审批配置";
};

/** 提交按钮 */
const submitForm = () => {
  workorderConfigFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      // 转换数据格式
      const submitData = {
        ...form.value,
        auditingUserConfigBoList: form.value.approvalNodes.map(
          (node, index) => ({
            auditingUserId: node.approver.auditingUserId,
            auditingUserName: node.approver.auditingUserName,
            sort: index + 1,
          }),
        ),
      };

      if (form.value.id) {
        await updateWorkorderConfig(submitData).finally(
          () => (buttonLoading.value = false),
        );
      } else {
        await addWorkorderConfig(submitData).finally(
          () => (buttonLoading.value = false),
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WorkorderConfigVO) => {
  const businessType = row?.businessType;
  const businessTypeLabel = business_type.value.find(
    (item: any) => String(item.value) === String(businessType),
  )?.label;

  await proxy?.$modal
    .confirm('是否确认删除业务类型为"' + businessTypeLabel + '"的审批配置？')
    .finally(() => (loading.value = false));
  await delWorkorderConfig(businessType);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "salary/workorderConfig/export",
    {
      ...queryParams.value,
    },
    `workorderConfig_${new Date().getTime()}.xlsx`,
  );
};

// 修改添加审批人的方法
const handleAddApprover = () => {
  approverDialog.currentIndex = -1;
  selectUserRef.value.show();
};

// 修改选择用户回调方法
const handleSelectUserOk = (selectedUsers: any[]) => {
  if (selectedUsers.length === 0) {
    proxy?.$modal.msgError("请选择审批人");
    return;
  }

  const selectedUser = selectedUsers[0]; // 只取第一个选中的用户
  if (approverDialog.currentIndex === -1) {
    // 新增节点
    form.value.approvalNodes.push({
      approver: {
        auditingUserId: selectedUser.userId,
        auditingUserName: selectedUser.nickName,
        sort: form.value.approvalNodes.length + 1, // 添加排序字段，从1开始递增
      },
    });
  } else {
    // 修改已有节点
    form.value.approvalNodes[approverDialog.currentIndex].approver = {
      auditingUserId: selectedUser.userId,
      auditingUserName: selectedUser.nickName,
      sort: approverDialog.currentIndex + 1, // 保持原有排序
    };
  }
};

// 删除节点方法
const removeNode = (index: number) => {
  form.value.approvalNodes.splice(index, 1);
};

// 获取节点标签类型
const getNodeType = (index: number) => {
  const types = ["", "success", "warning", "danger", "info"];
  return types[index % types.length];
};

// 修改用户列表数据获取方法
const userList = ref([]);
const getUserList = async () => {
  const res = await listUser();
  userList.value = res.rows;
};

// 添加时间格式化方法
const parseTime = (time: string) => {
  if (!time) return "";
  return proxy?.parseTime(time);
};

onMounted(() => {
  getList();
  getUserList();
});
</script>

<style scoped>
.mx-1 {
  margin: 0 4px;
}

.gap-4 {
  gap: 1rem;
}
</style>
