<template>
  <div class="usd-statement-page">
    <el-card shadow="hover" class="mb-[10px]">
      <el-form ref="queryFormRef" :model="filters" :inline="true">
        <el-form-item label="服务公司" prop="companyId" class="elFormItem">
          <el-select v-model="filters.companyId" placeholder="全部" clearable>
            <el-option label="全部" value="" />
            <el-option
              v-for="item in companyList"
              :key="item.id"
              :label="item.displayName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="交易类型" prop="transactionType">
          <el-select
            v-model="filters.transactionType"
            placeholder="全部"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="出账" value="1" />
            <el-option label="入账" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易时间" prop="dateRange" style="width: 512px">
          <el-date-picker
            v-model="dateRangeValue"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :editable="false"
          />
        </el-form-item>
        <el-form-item class="button-group">
          <el-button
            type="primary"
            style="margin-right: 1rem; width: 80px"
            @click="onSearch"
            >查询</el-button
          >
          <el-button style="width: 80px" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 查询结果统计 -->
    <div class="result-summary-wrapper">
      <div class="result-summary">以下为查询结果（共计：{{ total }}条）</div>
      <div class="buttons-group">
        <el-button
          type="primary"
          style="width: 80px; margin-right: 12px;"
          :icon="Refresh"
          @click="handleManualRefresh"
        >刷新</el-button>

        <!-- 多个交易流水按钮 -->
        <template v-if="userStore.companyType === '2'">
          <el-button
            v-hasPermi="['salary:usdFlow:add']"
            type="primary"
            style="width: 140px; margin-right: 8px;"
            @click="openShipFlowDialog"
          >航运给船舶记录</el-button>
          <el-button
            v-hasPermi="['salary:usdFlow:add']"
            type="success"
            style="width: 140px; margin-right: 8px;"
            @click="openSelfFlowDialog"
          >航运给自身记录</el-button>
          <el-button
            v-hasPermi="['salary:usdFlow:add']"
            type="warning"
            style="width: 140px;"
            @click="openDistributorFlowDialog"
          >航运给分销商记录</el-button>
        </template>

        <!-- 分销商场景（假设分销商类型为4，您可以根据实际情况调整） -->
        <template v-else-if="userStore.companyType === '4'">
          <el-button
            v-hasPermi="['salary:usdFlow:add']"
            type="primary"
            style="width: 140px; margin-right: 8px;"
            @click="openShipFlowDialog"
          >分销商给船舶记录</el-button>
          <el-button
            v-hasPermi="['salary:usdFlow:add']"
            type="success"
            style="width: 140px;"
            @click="openSelfFlowDialog"
          >分销商给自身记录</el-button>
        </template>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="pagedData"
      border
      stripe
      class="usd-table"
    >
      <el-table-column prop="id" label="财务流水号" min-width="160" />
      <el-table-column label="服务公司" min-width="140">
        <template #default="scope">
          <span>{{ getCompanyName(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="交易类型" min-width="80">
        <template #default="scope">
          <span>{{
            scope.row.transactionType === "1"
              ? "出账"
              : scope.row.transactionType === "2"
                ? "入账"
                : ""
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="transactionAmount"
        label="交易金额"
        min-width="100"
      />
      <el-table-column label="交易时间" min-width="160">
        <template #default="scope">
          <span>{{ scope.row.transactionTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="purpose" label="交易用途" min-width="120" />
      <el-table-column prop="remark" label="备注" min-width="120" />
      <el-table-column prop="accountBalance" label="余额" min-width="80">
        <template #default="scope">
          <span :style="{ color: '#ff6600', fontWeight: 'bold' }">{{
            scope.row.accountBalance
          }}</span>
        </template>
      </el-table-column>
    </el-table>
        <!-- 分页 -->
        <div class="pagination-wrapper">
      <el-pagination
        background
        layout="prev, pager, next, jumper, sizes, total"
        :total="total"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :current-page="currentPage"
        @current-change="onPageChange"
        @size-change="onSizeChange"
      />
    </div>

    <!-- 分销商特定表格 -->
    <div v-if="userStore.companyType === '4'" class="distributor-table-container">
      <div class="distributor-table-title">分销商交易明细</div>
      <el-table
        v-loading="isLoadingDistributorData"
        :data="distributorPagedData"
        border
        stripe
        class="usd-table distributor-table"
      >
        <el-table-column prop="id" label="财务流水号" min-width="160" />
        <el-table-column label="服务公司" min-width="140">
          <template #default="scope">
            <span>{{ getCompanyName(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="交易类型" min-width="80">
          <template #default="scope">
            <span>{{
              scope.row.transactionType === "1"
                ? "出账"
                : scope.row.transactionType === "2"
                  ? "入账"
                  : ""
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="transactionAmount"
          label="交易金额"
          min-width="100"
        />
        <el-table-column label="交易时间" min-width="160">
          <template #default="scope">
            <span>{{ scope.row.transactionTime }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="purpose" label="交易用途" min-width="120" />
        <el-table-column prop="remark" label="备注" min-width="120" />
        <el-table-column prop="accountBalance" label="余额" min-width="80">
          <template #default="scope">
            <span :style="{ color: '#ff6600', fontWeight: 'bold' }">{{
              scope.row.accountBalance
            }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分销商表格分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          background
          layout="prev, pager, next, jumper, sizes, total"
          :total="distributorTotal"
          :page-size="distributorPageSize"
          :page-sizes="[10, 20, 50, 100]"
          :current-page="distributorCurrentPage"
          @current-change="onDistributorPageChange"
          @size-change="onDistributorSizeChange"
        />
      </div>
    </div>

    <!-- 航运给船舶记录交易流水弹窗 -->
    <el-drawer
      v-model="shipFlowDialogVisible"
      title="航运给船舶记录交易流水"
      direction="rtl"
      :size="drawerWidth"
      destroy-on-close
      :close-on-click-modal="false"
      class="custom-drawer"
    >
      <el-form
        ref="shipFlowFormRef"
        :model="shipFlowForm"
        :rules="shipFlowFormRules"
        label-width="120px"
        class="drawer-form"
      >
        <el-form-item label="船舶公司" prop="shippingId">
          <el-select
            v-model="shipFlowForm.shippingId"
            placeholder="请选择船舶公司"
            style="width: 100%"
          >
            <el-option
              v-for="item in companyList.filter(
                (c) => c.companyType === '1' || !c.companyType,
              )"
              :key="item.id"
              :label="item.displayName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="交易类型" prop="transactionType">
          <el-select
            v-model="shipFlowForm.transactionType"
            placeholder="请选择交易类型"
            style="width: 100%"
          >
            <el-option label="出账" value="1" />
            <el-option label="入账" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易金额" prop="transactionAmount">
          <el-input-number
            v-model="shipFlowForm.transactionAmount"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="交易时间" prop="transactionTime">
          <el-date-picker
            v-model="shipFlowForm.transactionTime"
            type="datetime"
            placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            :editable="false"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="美金工单ID" prop="usdworkoderId">
          <el-input
            v-model="shipFlowForm.usdworkoderId"
            placeholder="请输入美金工单ID（可选）"
          />
        </el-form-item>
        <el-form-item label="交易用途" prop="purpose">
          <el-input v-model="shipFlowForm.purpose" placeholder="请输入交易用途" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="shipFlowForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
        <el-form-item class="drawer-footer">
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="submitShipFlowForm"
            >确 定</el-button
          >
          <el-button @click="shipFlowDialogVisible = false">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 航运给自身记录交易流水弹窗 -->
    <el-drawer
      v-model="selfFlowDialogVisible"
      title="航运给自身记录交易流水"
      direction="rtl"
      :size="drawerWidth"
      destroy-on-close
      :close-on-click-modal="false"
      class="custom-drawer"
    >
      <el-form
        ref="selfFlowFormRef"
        :model="selfFlowForm"
        :rules="selfFlowFormRules"
        label-width="120px"
        class="drawer-form"
      >
        <el-form-item label="交易类型" prop="transactionType">
          <el-select
            v-model="selfFlowForm.transactionType"
            placeholder="请选择交易类型"
            style="width: 100%"
          >
            <el-option label="出账" value="1" />
            <el-option label="入账" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易金额" prop="transactionAmount">
          <el-input-number
            v-model="selfFlowForm.transactionAmount"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="交易时间" prop="transactionTime">
          <el-date-picker
            v-model="selfFlowForm.transactionTime"
            type="datetime"
            placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            :editable="false"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="美金工单ID" prop="usdworkoderId">
          <el-input
            v-model="selfFlowForm.usdworkoderId"
            placeholder="请输入美金工单ID（可选）"
          />
        </el-form-item>
        <el-form-item label="交易用途" prop="purpose">
          <el-input v-model="selfFlowForm.purpose" placeholder="请输入交易用途" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="selfFlowForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
        <el-form-item class="drawer-footer">
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="submitSelfFlowForm"
            >确 定</el-button
          >
          <el-button @click="selfFlowDialogVisible = false">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>

    <!-- 航运给分销商记录交易流水弹窗 -->
    <el-drawer
      v-model="distributorFlowDialogVisible"
      title="航运给分销商记录交易流水"
      direction="rtl"
      :size="drawerWidth"
      destroy-on-close
      :close-on-click-modal="false"
      class="custom-drawer"
    >
      <el-form
        ref="distributorFlowFormRef"
        :model="distributorFlowForm"
        :rules="distributorFlowFormRules"
        label-width="120px"
        class="drawer-form"
      >
        <el-form-item label="分销商" prop="distributorId">
          <el-select
            v-model="distributorFlowForm.distributorId"
            placeholder="请选择分销商"
            style="width: 100%"
          >
            <el-option
              v-for="item in distributorList"
              :key="item.id"
              :label="item.displayName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="交易类型" prop="transactionType">
          <el-select
            v-model="distributorFlowForm.transactionType"
            placeholder="请选择交易类型"
            style="width: 100%"
          >
            <el-option label="出账" value="1" />
            <el-option label="入账" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易金额" prop="transactionAmount">
          <el-input-number
            v-model="distributorFlowForm.transactionAmount"
            :precision="2"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="交易时间" prop="transactionTime">
          <el-date-picker
            v-model="distributorFlowForm.transactionTime"
            type="datetime"
            placeholder="选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            :editable="false"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="美金工单ID" prop="usdworkoderId">
          <el-input
            v-model="distributorFlowForm.usdworkoderId"
            placeholder="请输入美金工单ID（可选）"
          />
        </el-form-item>
        <el-form-item label="交易用途" prop="purpose">
          <el-input v-model="distributorFlowForm.purpose" placeholder="请输入交易用途" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="distributorFlowForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
        <el-form-item class="drawer-footer">
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="submitDistributorFlowForm"
            >确 定</el-button
          >
          <el-button @click="distributorFlowDialogVisible = false">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from "vue";
import { listUsdFlow, addUsdFlow, addDistributorUsdFlow } from "@/api/salary/usdFlow";
import { getPropertyList } from "@/api/salary/usdConfig";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/store/modules/user";
import { Refresh } from "@element-plus/icons-vue";

const userStore = useUserStore();
const loading = ref(false);
const submitLoading = ref(false);

// 弹窗显示状态
const shipFlowDialogVisible = ref(false);
const selfFlowDialogVisible = ref(false);
const distributorFlowDialogVisible = ref(false);

// 表单引用
const shipFlowFormRef = ref();
const selfFlowFormRef = ref();
const distributorFlowFormRef = ref();

const showSearch = ref(true);

// 日期范围值
const dateRangeValue = ref([]);

// 过滤条件
const filters = reactive({
  companyId: "",
  transactionType: "",
  dateRange: [],
  params: {},
});

// 监控日期选择器的值变化
watch(
  dateRangeValue,
  (newVal) => {
    if (newVal) {
      const formattedDates = newVal.map((date: Date) => {
        if (date) {
          // 格式化日期为 yyyy-MM-dd
          return new Date(date).toISOString().split("T")[0];
        }
        return "";
      });
      filters.dateRange = formattedDates;
    } else {
      filters.dateRange = [];
    }
  },
  { immediate: true },
);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const flowList = ref<any[]>([]);
const companyList = ref<any[]>([]);
const distributorList = ref<any[]>([]); // 分销商列表

// 分销商特定数据
const distributorCurrentPage = ref(1);
const distributorPageSize = ref(10);
const distributorTotal = ref(0);
const distributorFlowList = ref<any[]>([]);
const isLoadingDistributorData = ref(false);

// 航运给船舶表单数据
const shipFlowForm = reactive({
  transportId: "",
  shippingId: "",
  transactionType: "",
  transactionAmount: 0,
  transactionTime: new Date().toISOString().slice(0, 19).replace("T", " "),
  usdworkoderId: "",
  purpose: "",
  remark: "",
});

// 航运给自身表单数据
const selfFlowForm = reactive({
  transportId: "",
  shippingId: "", // 自身记录时，shippingId = transportId
  transactionType: "",
  transactionAmount: 0,
  transactionTime: new Date().toISOString().slice(0, 19).replace("T", " "),
  usdworkoderId: "",
  purpose: "",
  remark: "",
});

// 航运给分销商表单数据
const distributorFlowForm = reactive({
  transportId: "",
  distributorId: "",
  transactionType: "",
  transactionAmount: 0,
  transactionTime: new Date().toISOString().slice(0, 19).replace("T", " "),
  usdworkoderId: "",
  purpose: "",
  remark: "",
});

// 航运给船舶表单验证规则
const shipFlowFormRules = {
  shippingId: [
    { required: true, message: "请选择船舶公司", trigger: "change" },
  ],
  transactionType: [
    { required: true, message: "请选择交易类型", trigger: "change" },
  ],
  transactionAmount: [
    { required: true, message: "请输入交易金额", trigger: "blur" },
  ],
  transactionTime: [
    { required: true, message: "请选择交易时间", trigger: "change" },
  ],
  purpose: [{ required: true, message: "请输入交易用途", trigger: "blur" }],
};

// 航运给自身表单验证规则
const selfFlowFormRules = {
  transactionType: [
    { required: true, message: "请选择交易类型", trigger: "change" },
  ],
  transactionAmount: [
    { required: true, message: "请输入交易金额", trigger: "blur" },
  ],
  transactionTime: [
    { required: true, message: "请选择交易时间", trigger: "change" },
  ],
  purpose: [{ required: true, message: "请输入交易用途", trigger: "blur" }],
};

// 航运给分销商表单验证规则
const distributorFlowFormRules = {
  distributorId: [
    { required: true, message: "请选择分销商", trigger: "change" },
  ],
  transactionType: [
    { required: true, message: "请选择交易类型", trigger: "change" },
  ],
  transactionAmount: [
    { required: true, message: "请输入交易金额", trigger: "blur" },
  ],
  transactionTime: [
    { required: true, message: "请选择交易时间", trigger: "change" },
  ],
  purpose: [{ required: true, message: "请输入交易用途", trigger: "blur" }],
};

// 打开航运给船舶流水弹窗
const openShipFlowDialog = () => {
  // 只有航运公司和分销商能添加流水
  if (userStore.companyType !== "2" && userStore.companyType !== "4") {
    ElMessage.warning("只有航运公司或分销商可以添加交易流水");
    return;
  }
  resetShipFlowForm();
  shipFlowForm.transportId = String(userStore.companyId);
  shipFlowDialogVisible.value = true;
};

// 打开航运给自身流水弹窗
const openSelfFlowDialog = () => {
  if (userStore.companyType !== "2" && userStore.companyType !== "4") {
    ElMessage.warning("只有航运公司或分销商可以添加交易流水");
    return;
  }
  resetSelfFlowForm();
  selfFlowForm.transportId = String(userStore.companyId);
  selfFlowForm.shippingId = String(userStore.companyId); // 自身记录
  selfFlowDialogVisible.value = true;
};

// 打开航运给分销商流水弹窗
const openDistributorFlowDialog = () => {
  if (userStore.companyType !== "2") {
    ElMessage.warning("只有航运公司可以给分销商添加交易流水");
    return;
  }
  resetDistributorFlowForm();
  distributorFlowForm.transportId = String(userStore.companyId);
  distributorFlowDialogVisible.value = true;
};

// 重置航运给船舶表单
const resetShipFlowForm = () => {
  shipFlowForm.transportId = "";
  shipFlowForm.shippingId = "";
  shipFlowForm.transactionType = "";
  shipFlowForm.transactionAmount = 0;
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");
  shipFlowForm.transactionTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  shipFlowForm.usdworkoderId = "";
  shipFlowForm.purpose = "";
  shipFlowForm.remark = "";
};

// 重置航运给自身表单
const resetSelfFlowForm = () => {
  selfFlowForm.transportId = "";
  selfFlowForm.shippingId = "";
  selfFlowForm.transactionType = "";
  selfFlowForm.transactionAmount = 0;
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");
  selfFlowForm.transactionTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  selfFlowForm.usdworkoderId = "";
  selfFlowForm.purpose = "";
  selfFlowForm.remark = "";
};

// 重置航运给分销商表单
const resetDistributorFlowForm = () => {
  distributorFlowForm.transportId = "";
  distributorFlowForm.distributorId = "";
  distributorFlowForm.transactionType = "";
  distributorFlowForm.transactionAmount = 0;
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");
  distributorFlowForm.transactionTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  distributorFlowForm.usdworkoderId = "";
  distributorFlowForm.purpose = "";
  distributorFlowForm.remark = "";
};

// 提交航运给船舶表单
const submitShipFlowForm = () => {
  shipFlowFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      submitLoading.value = true;
      try {
        const submitData = {
          transportId: String(shipFlowForm.transportId),
          shippingId: String(shipFlowForm.shippingId),
          transactionType: shipFlowForm.transactionType,
          transactionAmount: shipFlowForm.transactionAmount,
          transactionTime: shipFlowForm.transactionTime,
          usdworkoderId: shipFlowForm.usdworkoderId || "0",
          purpose: shipFlowForm.purpose,
          remark: shipFlowForm.remark,
        };

        const res = await addUsdFlow(submitData);
        if (res.code === 200) {
          ElMessage.success("航运给船舶交易流水添加成功");
          shipFlowDialogVisible.value = false;
          fetchFlowList();
        } else {
          ElMessage.error(res.msg || "添加失败");
        }
      } catch (error) {
        console.error("添加流水失败:", error);
        ElMessage.error("添加流水失败");
      } finally {
        submitLoading.value = false;
      }
    }
  });
};

// 提交航运给自身表单
const submitSelfFlowForm = () => {
  selfFlowFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      submitLoading.value = true;
      try {
        const submitData = {
          transportId: String(selfFlowForm.transportId),
          shippingId: String(selfFlowForm.shippingId), // 自身记录，两个ID相同
          transactionType: selfFlowForm.transactionType,
          transactionAmount: selfFlowForm.transactionAmount,
          transactionTime: selfFlowForm.transactionTime,
          usdworkoderId: selfFlowForm.usdworkoderId || "0",
          purpose: selfFlowForm.purpose,
          remark: selfFlowForm.remark,
        };

        const res = await addUsdFlow(submitData);
        if (res.code === 200) {
          ElMessage.success("航运给自身交易流水添加成功");
          selfFlowDialogVisible.value = false;
          fetchFlowList();
        } else {
          ElMessage.error(res.msg || "添加失败");
        }
      } catch (error) {
        console.error("添加流水失败:", error);
        ElMessage.error("添加流水失败");
      } finally {
        submitLoading.value = false;
      }
    }
  });
};

// 提交航运给分销商表单
const submitDistributorFlowForm = () => {
  distributorFlowFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      submitLoading.value = true;
      try {
        const submitData = {
          transportId: String(distributorFlowForm.transportId),
          distributorsId: String(distributorFlowForm.distributorId), // 使用distributorsId字段
          transactionType: distributorFlowForm.transactionType,
          transactionAmount: distributorFlowForm.transactionAmount,
          transactionTime: distributorFlowForm.transactionTime,
          usdworkoderId: distributorFlowForm.usdworkoderId || "0",
          purpose: distributorFlowForm.purpose,
          remark: distributorFlowForm.remark,
        };

        const distributorRes = await addDistributorUsdFlow(submitData);
        if (distributorRes.code === 200) {
          ElMessage.success("航运给分销商交易流水添加成功");
          distributorFlowDialogVisible.value = false;
          fetchFlowList();
        } else {
          ElMessage.error(distributorRes.msg || "添加失败");
        }
      } catch (error) {
        console.error("添加流水失败:", error);
        ElMessage.error("添加流水失败");
      } finally {
        submitLoading.value = false;
      }
    }
  });
};

// 获取公司名称
const getCompanyName = (row: any) => {
  // 根据当前登录用户类型确定显示哪个公司名称
  if (userStore.companyType === "1" ||userStore.companyType === "4") {
    // 船舶公司登录，显示航运公司名称
    const company = companyList.value.find(
      (item) => item.id === row.transportId,
    );
    return row.transportName;
    //return company ? company.displayName : row.transportId;
  } else if (userStore.companyType === "2") {
    // 航运公司登录，显示船舶公司名称
    const company = companyList.value.find(
      (item) => item.id === row.shippingId,
    );
    return row.shippingName;
    //return company ? company.displayName : row.shippingId;
  }
  return "未知企业";
};

// 分页数据
const pagedData = computed(() => {
  return flowList.value;
});

// 分销商分页数据
const distributorPagedData = computed(() => {
  return distributorFlowList.value;
});

// 获取有服务关系的企业列表
const fetchCompanyList = async () => {
  // 如果公司类型为空或没有公司ID，不发送请求
  if (!userStore.companyType || !userStore.companyId) {
    companyList.value = [];
    return;
  }

  try {
    const params: any = {};
    if (userStore.companyType === "1") {
      params.shippingId = userStore.companyId;
    } else if (userStore.companyType === "2") {
      params.transportId = userStore.companyId;
    }

    const res = await getPropertyList(params);
    if (res.code === 200 && res.data) {
      companyList.value = res.data.map((item: any) => {
        // 根据公司类型设置显示的公司名称
        let displayName = "";
        // 如果是船舶公司登录,显示航运公司名称
        if (userStore.companyType === "1" && item.transportName) {
          displayName = item.transportName;
        }
        // 如果是航运公司登录,显示船舶公司名称
        else if (userStore.companyType === "2" && item.shippingName) {
          displayName = item.shippingName;
        }
        // 默认显示任一不为空的名称
        else {
          displayName = item.transportName || item.shippingName || "未知企业";
        }
        return {
          ...item,
          id:
            userStore.companyType === "1" ? item.transportId : item.shippingId, // 根据登录用户类型选择ID
          displayName,
          companyType:
            userStore.companyType === "1"
              ? "2"
              : userStore.companyType === "2"
                ? "1"
                : null, // 设置公司类型
        };
      });
    }
  } catch (error) {
    console.error("获取企业列表失败:", error);
    ElMessage.error("获取企业列表失败");
  }
};

// 获取分销商列表
const fetchDistributorList = async () => {
  // 如果公司类型为空或没有公司ID，不发送请求
  if (!userStore.companyType || !userStore.companyId) {
    distributorList.value = [];
    return;
  }

  try {
    const params: any = {};
    if (userStore.companyType === "2") {
      // 航运公司登录，获取其管理的分销商
      params.transportId = userStore.companyId;
    }

    const response = await getDistributorList(params);
    if (response.code === 200 && response.data) {
      distributorList.value = response.data.map((item: any) => ({
        ...item,
        id: item.distributorsId,
        displayName: item.distributorsName || item.displayName || "未知分销商",
      }));
    }
  } catch (error) {
    console.error("获取分销商列表失败:", error);
    ElMessage.error("获取分销商列表失败");
  }
};

// 获取流水列表
const fetchFlowList = async () => {
  // 如果公司类型为空或没有公司ID，不发送请求
  if (!userStore.companyType || !userStore.companyId) {
    flowList.value = [];
    total.value = 0;
    loading.value = false;
    return;
  }

  loading.value = true;
  try {
    const queryParams: any = {};
    // 设置公司ID
    if (filters.companyId) {
      if (userStore.companyType === "1") {
        // 船舶公司登录，查询与特定航运公司的交易
        queryParams.transportId = filters.companyId;
        queryParams.shippingId = userStore.companyId;
      } else if (userStore.companyType === "2") {
        // 航运公司登录，查询与特定船舶公司的交易
        queryParams.shippingId = filters.companyId;
        queryParams.transportId = userStore.companyId;
      } else if (userStore.companyType === "4") {
        queryParams.shippingId = userStore.companyId;
      }
    } else {
      // 没有选择特定公司，查询所有服务关系的交易
      if (userStore.companyType === "1") {
        queryParams.shippingId = userStore.companyId;
      } else if (userStore.companyType === "2") {
        queryParams.transportId = userStore.companyId;
      } else if (userStore.companyType === "4") {
        queryParams.shippingId = userStore.companyId;
      } else {
        ElMessage.error("获取交易流水失败");
        return;
      }
    }

    // 设置交易类型
    if (filters.transactionType) {
      queryParams.transactionType = filters.transactionType;
    }

    // 设置日期范围
    if (filters.dateRange && filters.dateRange.length === 2) {
      queryParams.params = {
        beginTransactionTime: filters.dateRange[0] + " 00:00:00",
        endTransactionTime: filters.dateRange[1] + " 23:59:59",
      };
    }

    console.log(queryParams);
    // 设置分页参数
    queryParams.pageNum = currentPage.value;
    queryParams.pageSize = pageSize.value;

    const res = await listUsdFlow(queryParams);
    if (res.code === 200) {
      flowList.value = res.rows || [];
      total.value = res.total || 0;
    } else {
      ElMessage.error(res.msg || "获取交易流水失败");
    }
  } catch (error) {
    console.error("获取交易流水失败:", error);
    ElMessage.error("获取交易流水失败");
  } finally {
    loading.value = false;
  }
};

// 获取分销商特定流水列表
const fetchDistributorFlowData = async () => {
  // 如果不是分销商类型，不执行查询
  if (userStore.companyType !== "4") {
    distributorFlowList.value = [];
    distributorTotal.value = 0;
    return;
  }

  isLoadingDistributorData.value = true;
  try {
    const queryParams: any = {};

    // 分销商的特定查询条件
    queryParams.transportId = userStore.companyId;

    // 设置交易类型
    if (filters.transactionType) {
      queryParams.transactionType = filters.transactionType;
    }

    // 设置日期范围
    if (filters.dateRange && filters.dateRange.length === 2) {
      queryParams.params = {
        beginTransactionTime: filters.dateRange[0] + " 00:00:00",
        endTransactionTime: filters.dateRange[1] + " 23:59:59",
      };
    }

    // 设置分页参数
    queryParams.pageNum = distributorCurrentPage.value;
    queryParams.pageSize = distributorPageSize.value;

    console.log("分销商查询参数:", queryParams);

    const res = await listUsdFlow(queryParams);
    if (res.code === 200) {
      distributorFlowList.value = res.rows || [];
      distributorTotal.value = res.total || 0;
    } else {
      ElMessage.error(res.msg || "获取分销商交易流水失败");
    }
  } catch (error) {
    console.error("获取分销商交易流水失败:", error);
    ElMessage.error("获取分销商交易流水失败");
  } finally {
    isLoadingDistributorData.value = false;
  }
};

// 搜索
const onSearch = () => {
  currentPage.value = 1;
  distributorCurrentPage.value = 1; // 重置分销商页码
  fetchFlowList();
  if (userStore.companyType === "4") {
    fetchDistributorFlowData(); // 同时查询分销商数据
  }
};

// 重置
const onReset = () => {
  filters.companyId = "";
  filters.transactionType = "";
  dateRangeValue.value = []; // 重置日期选择器
  filters.dateRange = [];
  filters.params = {};
  currentPage.value = 1;
  distributorCurrentPage.value = 1; // 重置分销商页码
  fetchFlowList();
  if (userStore.companyType === "4") {
    fetchDistributorFlowData(); // 同时查询分销商数据
  }
};

// 页码变化
const onPageChange = (page: number) => {
  currentPage.value = page;
  fetchFlowList();
};

// 页大小变化
const onSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchFlowList();
};

// 分销商页码变化
const onDistributorPageChange = (page: number) => {
  distributorCurrentPage.value = page;
  fetchDistributorFlowData();
};

// 分销商页大小变化
const onDistributorSizeChange = (size: number) => {
  distributorPageSize.value = size;
  distributorCurrentPage.value = 1;
  fetchDistributorFlowData();
};

// 手动刷新服务公司列表和流水列表
const handleManualRefresh = async () => {
  try {
    loading.value = true;
    await fetchCompanyList();
    await fetchDistributorList();
    await fetchFlowList();

    // 如果是分销商，同时刷新分销商数据
    if (userStore.companyType === "4") {
      await fetchDistributorFlowData();
    }

    ElMessage.success("数据已刷新");
  } catch (error) {
    console.error("刷新数据失败:", error);
    ElMessage.error("刷新数据失败");
  } finally {
    loading.value = false;
  }
};

// 添加响应式侧边栏宽度
const drawerWidth = computed(() => {
  const width = window.innerWidth;
  if (width < 768) {
    return "100%";
  } else if (width < 1024) {
    return "60%";
  } else if (width < 1440) {
    return "40%";
  } else {
    return "30%";
  }
});

onMounted(() => {
  fetchCompanyList().then(async () => {
    await fetchDistributorList();
    await fetchFlowList();

    // 如果是分销商，同时查询分销商数据
    if (userStore.companyType === "4") {
      await fetchDistributorFlowData();
    }
  });
});
</script>

<style scoped>
.usd-statement-page {
  padding: 24px;
  background: #fff;
}
.breadcrumb {
  font-size: 14px;
  color: #409eff;
  margin-bottom: 16px;
}
.breadcrumb .arrow {
  color: #999;
  margin: 0 8px;
}
.result-summary-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  background-color: #f5f8ff;
}
.result-summary {
  background: #f5f8ff;
  color: #000;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 16px;
  flex-grow: 1;
}
.buttons-group {
  display: flex;
  align-items: center;
}
.refresh-btn {
  color: #fff;
  cursor: pointer;
  padding: 8px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  font-size: 18px;
  transition: all 0.3s;
}
.refresh-btn:hover {
  transform: rotate(180deg);
}
.add-btn {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  color: #606266;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 4px;
  margin-left: 12px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}
.add-btn:hover {
  background-color: #ecf5ff;
  color: #409eff;
  border-color: #c6e2ff;
}
.add-icon {
  font-weight: bold;
  margin-right: 4px;
  font-size: 14px;
}
.usd-table {
  margin-bottom: 16px;
  width: 100%;
}
.pagination-wrapper {
  text-align: right;
  margin-top: 16px;
  padding: 0 0 16px 0;
}
.w-full {
  width: 100%;
}

/* 分页组件自定义样式 */
:deep(.el-pagination) {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-pagination .el-pagination__total) {
  order: 3;
  margin-left: 16px;
}

:deep(.el-pagination .el-pagination__sizes) {
  order: 2;
  margin-left: 16px;
}

:deep(.el-pagination .el-pagination__jump) {
  order: 4;
  margin-left: 16px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next),
:deep(.el-pagination .el-pager) {
  order: 1;
}
@media screen and (min-width: 0px) and (max-width: 800px) {
  .button-group {
    margin-left: 15rem;
  }
}
@media screen and (min-width: 800px) {
  .button-group {
    margin-left: 29rem;
  }
}
@media screen and (min-width: 800px) and (max-width: 1372px) {
  .button-group {
    margin-left: 29rem;
  }
}
@media screen and (min-width: 1372px) and (max-width: 1600px) {
  .button-group {
    margin-left: 62.5rem;
  }
}
@media screen and (min-width: 1600px) {
  .button-group {
    margin-left: 0rem;
  }
}
.drawer-form {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.drawer-footer {
  position: sticky;
  bottom: 0;
  background-color: #fff;
  padding: 20px 0;
  margin-bottom: 0;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

/* 弹框样式 */
:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}
:deep(.el-drawer__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}
:deep(.el-drawer__body) {
  padding: 0;
  height: calc(100% - 55px);
  overflow: hidden;
}

/* 分销商表格样式 */
.distributor-table-container {
  margin-top: 30px;
  margin-bottom: 20px;
}

.distributor-table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding: 8px 16px;
  background-color: #f5f8ff;
  border-radius: 4px;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .drawer-form {
    padding: 16px;
  }
  :deep(.el-form-item__label) {
    width: 100px !important;
  }
  :deep(.el-drawer__header) {
    padding: 12px 16px;
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .drawer-form {
    padding: 18px;
  }
}

@media screen and (min-width: 1025px) {
  .drawer-form {
    padding: 20px;
  }
}
</style>
