<template>
  <div class="p-2">
    <transition
      :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave"
    >
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form
            ref="queryFormRef"
            :model="queryParams"
            :inline="true"
            class="search-form"
          >
            <el-form-item label="公司名称" prop="companyName">
              <el-input
                v-model="queryParams.companyName"
                placeholder="请输入公司名称"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="公司证件类型" prop="companyCertType">
              <el-select
                v-model="queryParams.companyCertType"
                placeholder="请选择公司证件类型"
                clearable
              >
                <el-option
                  v-for="dict in company_cert_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="公司证件号码" prop="companyCertNo">
              <el-input
                v-model="queryParams.companyCertNo"
                placeholder="请输入公司证件号码"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="成立时间" prop="establishTime">
              <el-date-picker
                v-model="queryParams.establishTime"
                clearable
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择成立时间"
              />
            </el-form-item>
            <el-form-item label="公司地址" prop="companyAddress">
              <el-input
                v-model="queryParams.companyAddress"
                placeholder="请输入公司地址"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="登记机关" prop="registrationAuthority">
              <el-input
                v-model="queryParams.registrationAuthority"
                placeholder="请输入登记机关"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="邮箱地址" prop="email">
              <el-input
                v-model="queryParams.email"
                placeholder="请输入邮箱地址"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="法人姓名" prop="legalPerson">
              <el-input
                v-model="queryParams.legalPerson"
                placeholder="请输入法人姓名"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="法人证件类型" prop="legalCertType">
              <el-select
                v-model="queryParams.legalCertType"
                placeholder="请选择法人证件类型"
                clearable
              >
                <el-option
                  v-for="dict in legal_cert_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="法人证件号码" prop="legalCertNo">
              <el-input
                v-model="queryParams.legalCertNo"
                placeholder="请输入法人证件号码"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="企业类型" prop="companyType">
              <el-select
                v-model="queryParams.companyType"
                placeholder="请选择企业类型"
                clearable
              >
                <el-option
                  v-for="dict in company_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="地址一致性" prop="addressConsistent">
              <el-select
                v-model="queryParams.addressConsistent"
                placeholder="请选择地址一致性"
                clearable
              >
                <el-option
                  v-for="dict in address_consistent"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="实际经营地址" prop="actualAddress">
              <el-input
                v-model="queryParams.actualAddress"
                placeholder="请输入实际经营地址"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="关联用户" prop="userId">
              <el-input
                v-model="queryParams.userId"
                placeholder="请输入关联用户ID"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="申请状态" prop="applicationStatus">
              <el-select
                v-model="queryParams.applicationStatus"
                placeholder="请选择申请状态"
                clearable
              >
                <el-option
                  v-for="dict in company_application_satus"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item class="search-buttons">
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['salary:company_info:add']"
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['salary:company_info:edit']"
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['salary:company_info:remove']"
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['salary:company_info:export']"
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @query-table="getList"
          ></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="company_infoList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="主键ID" align="center" prop="id" v-if="true" /> -->
        <el-table-column label="公司名称" align="center" prop="companyName" />
        <el-table-column
          label="公司证件类型"
          align="center"
          prop="companyCertType"
        >
          <template #default="scope">
            <dict-tag
              :options="company_cert_type"
              :value="scope.row.companyCertType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="公司证件号码"
          align="center"
          prop="companyCertNo"
        />
        <el-table-column
          label="成立时间"
          align="center"
          prop="establishTime"
          width="180"
        >
          <template #default="scope">
            <span>{{
              parseTime(scope.row.establishTime, "{y}-{m}-{d} {h}:{i}:{s}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="公司地址"
          align="center"
          prop="companyAddress"
        />
        <el-table-column
          label="登记机关"
          align="center"
          prop="registrationAuthority"
        />
        <el-table-column label="邮箱地址" align="center" prop="email" />
        <el-table-column label="法人姓名" align="center" prop="legalPerson" />
        <el-table-column
          label="法人证件类型"
          align="center"
          prop="legalCertType"
        >
          <template #default="scope">
            <dict-tag
              :options="legal_cert_type"
              :value="scope.row.legalCertType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="法人证件号码"
          align="center"
          prop="legalCertNo"
        />
        <el-table-column label="企业类型" align="center" prop="companyType">
          <template #default="scope">
            <dict-tag :options="company_type" :value="scope.row.companyType" />
          </template>
        </el-table-column>
        <el-table-column
          label="实际地址是否与注册地一致"
          align="center"
          prop="addressConsistent"
        >
          <template #default="scope">
            <dict-tag
              :options="address_consistent"
              :value="scope.row.addressConsistent"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="实际经营地址"
          align="center"
          prop="actualAddress"
        />
        <el-table-column label="公司关联用户" align="center" prop="userId" />
        <el-table-column label="阿里云" align="center" prop="fileId">
          <template #default="scope">
            <div v-if="scope.row.fileId">
              <el-button
                type="primary"
                link
                @click="previewPhoto(scope.row.fileId)"
              >
                查看照片资料
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="申请状态"
          align="center"
          prop="applicationStatus"
        >
          <template #default="scope">
            <dict-tag
              :options="company_application_satus"
              :value="scope.row.applicationStatus"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button
                v-hasPermi="['salary:company_info:edit']"
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                v-hasPermi="['salary:company_info:remove']"
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </el-card>
    <!-- 添加或修改公司信息对话框 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="700px"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-form
        ref="company_infoFormRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <h3 class="form-section-title">基本信息</h3>
        <el-divider />
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公司名称" prop="companyName">
              <el-input
                v-model="form.companyName"
                placeholder="请输入公司名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司证件类型" prop="companyCertType">
              <el-select
                v-model="form.companyCertType"
                placeholder="请选择公司证件类型"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in company_cert_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公司证件号码" prop="companyCertNo">
              <el-input
                v-model="form.companyCertNo"
                placeholder="请输入公司证件号码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成立时间" prop="establishTime">
              <el-date-picker
                v-model="form.establishTime"
                clearable
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择成立时间"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="公司地址" prop="companyAddress">
          <el-input
            v-model="form.companyAddress"
            placeholder="请输入公司地址"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="登记机关" prop="registrationAuthority">
              <el-input
                v-model="form.registrationAuthority"
                placeholder="请输入登记机关"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱地址" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-col>
        </el-row>

        <h3 class="form-section-title">企业信息</h3>
        <el-divider />
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="法人姓名" prop="legalPerson">
              <el-input
                v-model="form.legalPerson"
                placeholder="请输入法人姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人证件类型" prop="legalCertType">
              <el-select
                v-model="form.legalCertType"
                placeholder="请选择法人证件类型"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in legal_cert_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="法人证件号码" prop="legalCertNo">
              <el-input
                v-model="form.legalCertNo"
                placeholder="请输入法人证件号码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="企业类型" prop="companyType">
              <el-select
                v-model="form.companyType"
                placeholder="请选择企业类型"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in company_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="地址一致性" prop="addressConsistent">
          <el-select
            v-model="form.addressConsistent"
            placeholder="请选择实际地址是否与注册地一致"
            style="width: 100%"
          >
            <el-option
              v-for="dict in address_consistent"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="form.addressConsistent === '2'"
          label="实际经营地址"
          prop="actualAddress"
        >
          <el-input
            v-model="form.actualAddress"
            placeholder="请输入实际经营地址"
          />
          <div v-if="form.addressConsistent === '2'" class="el-form-item-tips">
            <el-alert
              title="注意：当选择地址不一致时，必须填写实际经营地址"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>
        </el-form-item>

        <el-form-item label="公司关联用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入公司关联用户id" />
        </el-form-item>

        <div v-if="dialog.title === '添加公司信息'">
          <h3 class="form-section-title">资料上传</h3>
          <el-divider />
          <el-form-item label="照片资料" prop="fileId">
            <el-upload
              ref="uploadRef"
              action="#"
              :limit="3"
              list-type="picture-card"
              :http-request="handleFileUpload"
              :file-list="fileList"
              :on-exceed="handleExceed"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
            <div class="upload-tip">
              <el-alert
                title="请上传公司营业执照、法人身份证正反面等照片资料"
                type="info"
                :closable="false"
                description="支持JPG/PNG/JPEG格式，每张照片大小不超过5MB"
              />
            </div>
            <el-form-item
              v-if="fileList.length > 0"
              label="照片类型"
              class="file-type-select"
            >
              <el-select
                v-model="currentFileType"
                placeholder="请选择照片类型"
                style="width: 100%"
              >
                <el-option label="营业执照" value="1" />
                <el-option label="法人身份证正面" value="2" />
                <el-option label="法人身份证反面" value="3" />
              </el-select>
            </el-form-item>
          </el-form-item>
        </div>

        <h3 class="form-section-title">申请状态</h3>
        <el-divider />
        <el-form-item label="申请状态" prop="applicationStatus">
          <el-select
            v-model="form.applicationStatus"
            placeholder="请选择申请状态"
            style="width: 100%"
          >
            <el-option
              v-for="dict in company_application_satus"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 照片预览对话框 -->
    <el-dialog v-model="photoDialogVisible" title="照片预览" width="60%">
      <div class="photo-gallery">
        <div
          v-for="(photo, index) in photoList"
          :key="index"
          class="photo-item"
        >
          <el-image
            :src="photo.url"
            fit="contain"
            :preview-src-list="photoUrlList"
            :initial-index="index"
            style="width: 100%; height: 300px"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>加载失败</span>
              </div>
            </template>
          </el-image>
          <div class="photo-caption">
            {{ photo.title }}
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Company_info" lang="ts">
import {
  listCompany_info,
  getCompany_info,
  delCompany_info,
  addCompany_info,
  updateCompany_info,
} from "@/api/salary/company_info";
import {
  Company_infoVO,
  Company_infoQuery,
  Company_infoForm,
} from "@/api/salary/company_info/types";
import { uploadCompanyFile } from "@/api/salary/company_info/apply";
import type { UploadProps, UploadUserFile, UploadInstance } from "element-plus";
import { watch } from "vue";

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  address_consistent,
  company_type,
  company_cert_type,
  legal_cert_type,
  company_application_satus,
} = toRefs<any>(
  proxy?.useDict(
    "address_consistent",
    "company_type",
    "company_cert_type",
    "legal_cert_type",
    "company_application_satus",
  ),
);

const company_infoList = ref<Company_infoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const company_infoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: "",
});

const photoDialogVisible = ref(false);
const photoList = ref<{ url: string; title: string }[]>([]);
const photoUrlList = computed(() => photoList.value.map((item) => item.url));

// 文件上传引用
const uploadRef = ref<UploadInstance>();
// 文件列表
const fileList = ref<UploadUserFile[]>([]);
// 当前选择的文件类型
const currentFileType = ref("1");

const initFormData: Company_infoForm = {
  id: undefined,
  companyName: undefined,
  companyCertType: undefined,
  companyCertNo: undefined,
  establishTime: undefined,
  companyAddress: undefined,
  registrationAuthority: undefined,
  email: undefined,
  legalPerson: undefined,
  legalCertType: undefined,
  legalCertNo: undefined,
  companyType: undefined,
  addressConsistent: undefined,
  actualAddress: undefined,
  userId: undefined,
  fileId: undefined,
  applicationStatus: undefined,
};
const data = reactive<PageData<Company_infoForm, Company_infoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    companyName: undefined,
    companyCertType: undefined,
    companyCertNo: undefined,
    establishTime: undefined,
    companyAddress: undefined,
    registrationAuthority: undefined,
    email: undefined,
    legalPerson: undefined,
    legalCertType: undefined,
    legalCertNo: undefined,
    companyType: undefined,
    addressConsistent: undefined,
    actualAddress: undefined,
    userId: undefined,
    applicationStatus: undefined,
    params: {},
  },
  rules: {
    id: [{ required: true, message: "主键ID不能为空", trigger: "blur" }],
    companyName: [
      { required: true, message: "公司名称不能为空", trigger: "blur" },
    ],
    companyCertType: [
      { required: true, message: "公司证件类型不能为空", trigger: "change" },
    ],
    companyCertNo: [
      { required: true, message: "公司证件号码不能为空", trigger: "blur" },
    ],
    establishTime: [
      { required: true, message: "成立时间不能为空", trigger: "blur" },
    ],
    companyAddress: [
      { required: true, message: "公司地址不能为空", trigger: "blur" },
    ],
    registrationAuthority: [
      { required: true, message: "登记机关不能为空", trigger: "blur" },
    ],
    email: [{ required: true, message: "邮箱地址不能为空", trigger: "blur" }],
    legalPerson: [
      { required: true, message: "法人姓名不能为空", trigger: "blur" },
    ],
    legalCertType: [
      { required: true, message: "法人证件类型不能为空", trigger: "change" },
    ],
    legalCertNo: [
      { required: true, message: "法人证件号码不能为空", trigger: "blur" },
    ],
    companyType: [
      { required: true, message: "企业类型不能为空", trigger: "change" },
    ],
    addressConsistent: [
      {
        required: true,
        message: "实际地址是否与注册地一致不能为空",
        trigger: "change",
      },
    ],
    actualAddress: [
      { required: true, message: "实际经营地址不能为空", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公司信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCompany_info(queryParams.value);
  company_infoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  company_infoFormRef.value?.resetFields();
  fileList.value = []; // 重置文件列表
};

/** 监听addressConsistent变化 */
watch(
  () => form.value.addressConsistent,
  (newVal) => {
    // 当选择地址一致(1)时，将实际经营地址设置为公司地址
    if (newVal === "1") {
      form.value.actualAddress = form.value.companyAddress;
    }
    // 当选择地址不一致(2)时，确保实际经营地址必填
    else if (newVal === "2") {
      if (company_infoFormRef.value) {
        company_infoFormRef.value.validateField("actualAddress");
      }
    }
  },
);

/** 监听公司地址变化 */
watch(
  () => form.value.companyAddress,
  (newVal) => {
    if (form.value.addressConsistent === "1") {
      // 当地址一致时，同步更新实际经营地址
      form.value.actualAddress = newVal;
    }
  },
);

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: Company_infoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 文件上传前验证 */
const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  const isImage =
    file.type === "image/jpeg" ||
    file.type === "image/png" ||
    file.type === "image/jpg";
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    proxy?.$modal.msgError("上传文件只能是 JPG/PNG/JPEG 格式!");
    return false;
  }
  if (!isLt5M) {
    proxy?.$modal.msgError("上传照片大小不能超过 5MB!");
    return false;
  }
  return true;
};

/** 处理文件数量超出限制 */
const handleExceed: UploadProps["onExceed"] = () => {
  proxy?.$modal.msgWarning("最多只能上传三张照片");
};

/** 处理文件移除 */
const handleRemove = (file: UploadUserFile) => {
  const index = fileList.value.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    fileList.value.splice(index, 1);
    // 更新文件ID列表
    try {
      const fileTypeMap =
        typeof form.value.fileId === "string"
          ? JSON.parse(form.value.fileId)
          : {};
      // 找到对应的类型并删除
      for (const type in fileTypeMap) {
        if (fileTypeMap[type].uid === file.uid) {
          delete fileTypeMap[type];
          break;
        }
      }
      form.value.fileId = JSON.stringify(fileTypeMap);
    } catch (e) {
      console.error("解析fileId失败", e);
    }
  }
};

/** 自定义文件上传 */
const handleFileUpload = async (options: any) => {
  const { file } = options;
  const formData = new FormData();
  formData.append("file", file);
  formData.append("type", currentFileType.value);

  try {
    loading.value = true;
    // 使用apply.ts中的uploadCompanyFile函数
    const res = await uploadCompanyFile(formData);
    loading.value = false;

    if (res.code === 200 && res.data) {
      // 保存文件类型和对应的fileId
      const fileInfo = res.data;
      let fileTypeMap = {};

      // 处理fileId可能是字符串或对象的情况
      if (form.value.fileId) {
        try {
          fileTypeMap =
            typeof form.value.fileId === "string"
              ? JSON.parse(form.value.fileId)
              : {};
          // 确保解析后的结果是对象
          if (typeof fileTypeMap !== "object" || fileTypeMap === null) {
            fileTypeMap = {};
          }
        } catch (e) {
          // JSON解析失败，重置为空对象
          fileTypeMap = {};
        }
      }

      // 添加到上传文件列表用于显示
      fileList.value.push({
        name: file.name,
        url: fileInfo.fileUrl || "", // 如果没有URL，留空
        uid: file.uid,
      });

      // 保存文件信息到fileTypeMap
      fileTypeMap[currentFileType.value] = {
        fileId: fileInfo.fileId,
        fileUrl: fileInfo.fileUrl,
        uid: file.uid,
      };

      form.value.fileId = JSON.stringify(fileTypeMap);
      proxy?.$modal.msgSuccess("照片上传成功");
    } else {
      proxy?.$modal.msgError(res?.msg || "照片上传失败");
    }
  } catch (error) {
    loading.value = false;
    console.error("照片上传失败", error);
    proxy?.$modal.msgError("照片上传失败");
  }
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  fileList.value = []; // 清空文件列表
  dialog.visible = true;
  dialog.title = "添加公司信息";
};

/** 修改按钮操作 */
const handleUpdate = async (row?: Company_infoVO) => {
  reset();
  fileList.value = []; // 清空文件列表
  const _id = row?.id || ids.value[0];
  const res = await getCompany_info(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公司信息";
};

/** 提交按钮 */
const submitForm = () => {
  company_infoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 特殊处理：当地址不一致时，确保实际经营地址不为空
      if (form.value.addressConsistent === "2" && !form.value.actualAddress) {
        proxy?.$modal.msgError("实际经营地址不能为空");
        return;
      }

      buttonLoading.value = true;
      if (form.value.id) {
        await updateCompany_info(form.value).finally(
          () => (buttonLoading.value = false),
        );
      } else {
        await addCompany_info(form.value).finally(
          () => (buttonLoading.value = false),
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: Company_infoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除公司信息编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delCompany_info(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "salary/company_info/export",
    {
      ...queryParams.value,
    },
    `company_info_${new Date().getTime()}.xlsx`,
  );
};

/** 获取照片URL */
const getPhotoUrl = (fileId: string | undefined): string | null => {
  if (!fileId) return null;

  try {
    // 尝试解析JSON
    const fileObj = JSON.parse(fileId);
    // 如果是对象，获取第一张照片的URL或文件ID
    if (typeof fileObj === "object" && fileObj !== null) {
      const photoTypes = Object.keys(fileObj);
      if (photoTypes.length > 0) {
        const firstPhoto = fileObj[photoTypes[0]];
        // 如果fileUrl为空，使用fileId构建下载链接
        return (
          firstPhoto.fileUrl ||
          `/api/salary/company_info/download/${firstPhoto.fileId}`
        );
      }
    }
    return null;
  } catch (e) {
    // 如果解析失败，可能是旧格式，返回下载链接
    return `/api/salary/company_info/download/${fileId}`;
  }
};

/** 预览照片 */
const previewPhoto = (fileId: string) => {
  // 解析照片信息
  try {
    const fileObj = JSON.parse(fileId);
    if (typeof fileObj === "object" && fileObj !== null) {
      photoList.value = [];
      // 遍历所有照片类型
      Object.keys(fileObj).forEach((type) => {
        const photoData = fileObj[type];
        if (photoData) {
          let title = "照片";
          if (type === "1") title = "营业执照";
          else if (type === "2") title = "法人身份证正面";
          else if (type === "3") title = "法人身份证反面";

          // 如果fileUrl为空，使用fileId构建下载链接
          const url =
            photoData.fileUrl ||
            `/api/salary/company_info/download/${photoData.fileId}`;

          photoList.value.push({
            url: url,
            title: title,
          });
        }
      });

      if (photoList.value.length > 0) {
        photoDialogVisible.value = true;
        return;
      }
    }

    // 旧数据格式或URL为空，尝试使用下载链接
    const url = `/api/salary/company_info/download/${fileId}`;
    photoList.value = [{ url, title: "照片文件" }];
    photoDialogVisible.value = true;
  } catch (e) {
    // 解析失败，使用下载链接
    const url = `/api/salary/company_info/download/${fileId}`;
    photoList.value = [{ url, title: "照片文件" }];
    photoDialogVisible.value = true;
  }
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.photo-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.photo-item {
  width: 100%;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.photo-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 8px 10px;
  font-size: 14px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

/* 搜索表单样式 */
.search-form {
  display: flex;
  flex-wrap: wrap;
  column-gap: 15px;
  row-gap: 10px;

  :deep(.el-form-item) {
    margin-bottom: 10px;
    margin-right: 0;
  }

  :deep(.el-form-item__label) {
    width: 110px;
    font-weight: 500;
    white-space: nowrap;
  }

  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-date-editor) {
    width: 200px;
  }

  .search-buttons {
    margin-top: 10px;
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

@media (min-width: 768px) {
  .photo-gallery {
    .photo-item {
      width: calc(50% - 7.5px);
    }
  }
}

.upload-tip {
  margin-top: 10px;
  margin-bottom: 10px;
}

.file-type-select {
  margin-top: 15px;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
}

:deep(.el-tabs--border-card) {
  box-shadow: none;
  border: 1px solid #dcdfe6;
}

:deep(.el-dialog__body) {
  padding: 10px 20px;
}

:deep(.el-dialog__header) {
  padding-bottom: 10px;
  margin-bottom: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-upload--picture-card) {
  --el-upload-picture-card-size: 120px;
  border-radius: 8px;
  border: 1px dashed var(--el-border-color-darker);
  transition: all 0.3s;

  &:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  border-radius: 8px;
  overflow: hidden;
}

.el-form-item-tips {
  margin-top: 5px;
}

.form-section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 10px 0 0;
}

:deep(.el-divider--horizontal) {
  margin: 10px 0 20px;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}
</style>
