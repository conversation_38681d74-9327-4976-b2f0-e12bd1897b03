<template>
  <div class="channel-page bg-[#fff] min-h-screen px-8 py-6">
    <!-- 顶部标题栏 -->
    <div class="relative flex items-center justify-between pb-2 mb-4">
      <div class="flex items-center space-x-6">
        <span
          class="text-[#7c3aed] text-base cursor-pointer font-medium border-b-2 border-[#7c3aed] pb-1"
          style="color: #409eff"
          >人民币代发渠道</span
        >
      </div>
      <div class="flex items-center">
        <button
          v-hasPermi="['salary:channel:query']"
          class="text-[#7c3aed] text-base font-medium flex items-center space-x-1 mr-4 no-outline no-border no-bg no-underline"
          @click="handleRefresh"
        >
          <span class="flex items-center">
            <el-icon
              :class="{ 'refresh-rotating': isRefreshing }"
              style="color: #409eff"
              ><Refresh
            /></el-icon>
            <span class="ml-1" style="color: #409eff">刷新</span>
          </span>
        </button>
        <button
          v-hasPermi="['salary:channel:add']"
          class="text-[#7c3aed] text-base font-medium flex items-center space-x-1 no-outline no-border no-bg no-underline"
          @click="openDialog()"
        >
          <span style="color: #409eff">＋ 添加渠道</span>
        </button>
      </div>
      <div
        class="absolute left-0 right-0 bottom-0 h-[1px] bg-[#e5e7eb]"
        style="z-index: 0"
      ></div>
    </div>

    <!-- 数据统计 -->
<!--    <div class="flex items-center mt-2 mb-6 justify-between">-->
<!--      <div class="flex items-center space-x-8">-->
<!--        <div class="flex flex-col items-center mx-2">-->
<!--          <span class="text-[#888] text-base mb-1">渠道总数</span>-->
<!--          <span class="text-2xl font-bold text-[#222]">{{-->
<!--            channelList.length-->
<!--          }}</span>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->

    <!-- 渠道列表 -->
    <el-table
      v-loading="loading"
      :data="channelList"
      style="width: 100% ;margin-top: 53px ;"
      border
    >
      <el-table-column prop="id" label="ID" width="200" />
      <el-table-column prop="channel" label="渠道名称" min-width="120" />
      <el-table-column prop="taxPoint" label="税点" min-width="120">
        <template #default="scope"> {{ scope.row.taxPoint }}% </template>
      </el-table-column>
      <el-table-column prop="enable" label="合同状态" width="100" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.enable"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        label="备注"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column prop="createTime" label="创建时间" min-width="160" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button
            v-hasPermi="['salary:channel:edit']"
            type="primary"
            link
            @click="openDialog(scope.row)"
            >编辑</el-button
          >
          <el-button
            v-hasPermi="['salary:channel:remove']"
            type="danger"
            link
            @click="handleDelete(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container mt-4">
      <el-pagination
        v-if="total > 0"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-drawer
      v-model="dialogVisible"
      :title="dialogTitle"
      direction="rtl"
      :size="drawerWidth"
      :close-on-click-modal="false"
      destroy-on-close
      class="custom-drawer"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="drawer-form"
      >
        <!-- 编辑时显示渠道ID -->
        <div v-if="dialogTitle === '编辑渠道'" class="mb-4">
          <div class="bg-[#f5f8ff] p-4 rounded-lg">
            <div class="font-medium text-base mb-2">
              {{ formData.channel || "渠道信息" }}
            </div>
            <div class="text-sm text-[#666]">
              <span>ID: {{ formData.id || "-" }}</span>
            </div>
          </div>
        </div>

        <el-form-item label="渠道名称" prop="channel">
          <el-input v-model="formData.channel" placeholder="请输入渠道名称" />
        </el-form-item>
        <el-form-item label="税点" prop="taxPoint">
          <el-input-number
            v-model="formData.taxPoint"
            :min="0"
            :max="100"
            :precision="2"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        <el-form-item label="是否有效" prop="enable">
          <el-radio-group v-model="formData.enable">
            <el-radio label="1">有效</el-radio>
            <el-radio label="0">无效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="drawer-footer">
          <el-button type="primary" :loading="buttonLoading" @click="submitForm"
            >保存</el-button
          >
          <el-button @click="dialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import {
  listChannel,
  getChannel,
  addChannel,
  updateChannel,
  delChannel,
  updateChannelStatus,
} from "@/api/salary/channel";
import type {
  ChannelVO,
  ChannelForm,
  ChannelQuery,
} from "@/api/salary/channel/types";

// 定义API响应接口
interface ApiResponse<T> {
  code: number;
  msg: string;
  data?: T;
}

interface PageResult<T> {
  total: number;
  rows: T[];
  code?: number;
  msg?: string;
}

const { proxy } = getCurrentInstance();
const loading = ref(false);
const isRefreshing = ref(false);
const buttonLoading = ref(false);
const formRef = ref();

// 查询参数
const queryParams = reactive<ChannelQuery>({
  pageNum: 1,
  pageSize: 10,
  channel: undefined,
  taxPoint: undefined,
  enable: undefined,
});

// 分页参数
const total = ref(0);

// 渠道列表
const channelList = ref<ChannelVO[]>([]);

const dialogVisible = ref(false);
const dialogTitle = ref("");
const formData = reactive<ChannelForm>({
  id: undefined,
  channel: "",
  taxPoint: 0,
  remark: "",
  enable: "0", // 默认有效
});

// 表单校验规则
const rules = {
  channel: [
    { required: true, message: "请输入渠道名称", trigger: "blur" },
    { max: 10, message: "渠道名称长度不能超过10个字符", trigger: "blur" },
  ],
  taxPoint: [{ required: true, message: "请输入税点", trigger: "blur" }],
};

// 获取渠道列表
const getList = async () => {
  try {
    loading.value = true;
    const res = await listChannel(queryParams);
    console.log(res.rows);
    if (res.code === 200 && res.rows) {
      // 处理实际返回的数据结构
      const responseData = res as unknown as PageResult<ChannelVO>;
      channelList.value = responseData.rows || [];
      total.value = responseData.total || 0;
    } else {
      ElMessage.error(res.msg || "获取渠道列表失败");
    }
  } catch (error) {
    console.error("获取渠道列表失败:", error);
    ElMessage.error("获取渠道列表失败");
  } finally {
    loading.value = false;
  }
};

// 处理分页变更
const handlePageChange = (currentPage: number) => {
  queryParams.pageNum = currentPage;
  getList();
};

// 处理每页条数变更
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  queryParams.pageNum = 1;
  getList();
};

// 打开弹窗
async function openDialog(row?: ChannelVO) {
  dialogTitle.value = row ? "编辑渠道" : "新增渠道";

  // 重置表单数据
  formData.id = undefined;
  formData.channel = "";
  formData.taxPoint = 0;
  formData.remark = "";
  formData.enable = "0";

  // 如果是编辑模式，需要获取渠道详情
  if (row) {
    try {
      const res = await getChannel(row.id);
      if (res.code === 200 && res.data) {
        Object.assign(formData, res.data);
      } else {
        ElMessage.error(res.msg || "获取渠道详情失败");
        return;
      }
    } catch (error) {
      console.error("获取渠道详情失败:", error);
      ElMessage.error("获取渠道详情失败");
      return;
    }
  }

  dialogVisible.value = true;
}

// 保存表单
const submitForm = () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (!valid) return;

    buttonLoading.value = true;
    try {
      const apiMethod = formData.id ? updateChannel : addChannel;
      const res = await apiMethod(formData);

      if (res.code === 200) {
        ElMessage.success("操作成功");
        dialogVisible.value = false;
        getList(); // 刷新列表
      } else {
        ElMessage.error(res.msg || "操作失败");
      }
    } catch (error) {
      console.error("保存失败:", error);
      ElMessage.error("操作失败");
    } finally {
      buttonLoading.value = false;
    }
  });
};

// 删除渠道
const handleDelete = async (id: number | string) => {
  try {
    await ElMessageBox.confirm("确认删除该渠道吗？删除后无法恢复", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    loading.value = true;
    const res = await delChannel(id);

    if (res.code === 200) {
      ElMessage.success("删除成功");
      getList(); // 刷新列表
    } else {
      ElMessage.error(res.msg || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败");
    }
  } finally {
    loading.value = false;
  }
};

// 刷新数据
const handleRefresh = async () => {
  if (isRefreshing.value) return; // 防止重复点击
  try {
    isRefreshing.value = true;
    await getList();
    ElMessage.success("数据已刷新");
  } catch (error) {
    console.error("刷新数据失败:", error);
  } finally {
    // 延迟清除旋转状态，保证动画效果完整
    setTimeout(() => {
      isRefreshing.value = false;
    }, 500);
  }
};

// 添加响应式侧边栏宽度
const drawerWidth = computed(() => {
  const width = window.innerWidth;
  if (width < 768) {
    return "100%";
  } else if (width < 1024) {
    return "60%";
  } else if (width < 1440) {
    return "40%";
  } else {
    return "30%";
  }
});

// 处理状态变更
const handleStatusChange = async (row: ChannelVO) => {
  try {
    loading.value = true;
    const res = await updateChannelStatus(row.id, row.enable);

    if (res.code === 200) {
      ElMessage.success("状态更新成功");
    } else {
      ElMessage.error(res.msg || "状态更新失败");
      getList();
    }
  } catch (error) {
    ElMessage.error("更新状态失败");
    getList();
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.channel-page {
  background: #fff;
  min-height: 100vh;
}
.no-border {
  border: none !important;
}
.no-bg {
  background: none !important;
}
button,
.no-outline,
.no-border {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}
.no-underline {
  text-decoration: none !important;
}

/* 添加刷新按钮旋转动画 */
@keyframes refresh-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.refresh-rotating {
  animation: refresh-rotate 1s linear infinite;
}

/* 弹框样式 */
.custom-drawer :deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7eb;
}

.custom-drawer :deep(.el-drawer__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.custom-drawer :deep(.el-drawer__body) {
  padding: 0;
  height: calc(100% - 55px);
  overflow: hidden;
}

.drawer-form {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.drawer-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.drawer-form :deep(.el-form-item__label) {
  font-weight: 500;
}

.drawer-footer {
  position: sticky;
  bottom: 0;
  background-color: #fff;
  padding: 20px;
  margin-bottom: 0;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .drawer-form {
    padding: 16px;
  }

  :deep(.el-form-item__label) {
    width: 100px !important;
  }

  .custom-drawer :deep(.el-drawer__header) {
    padding: 12px 16px;
  }
}

@media screen and (min-width: 769px) and (max-width: 1024px) {
  .drawer-form {
    padding: 18px;
  }
}

@media screen and (min-width: 1025px) {
  .drawer-form {
    padding: 20px;
  }
}

/* 分页样式 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
