<template>
  <div class="usd-workorder-page">
    <el-card shadow="hover" class="mb-[10px]">
      <el-form ref="queryFormRef" :model="filters" :inline="true">
        <div class="flex flex-col w-full">
          <div class="flex flex-nowrap w-full">
            <el-form-item
              label="工单编号"
              prop="workorderId"
              style="width: 20%; margin-right: 20px"
            >
              <el-input
                v-model="filters.workorderId"
                placeholder="请输入工单编号"
                clearable
              />
            </el-form-item>
            <el-form-item
              label="建单时间"
              prop="dateRange"
              style="width: 30%; margin-right: 20px"
            >
              <el-date-picker
                v-model="filters.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                :editable="false"
              />
            </el-form-item>
            <el-form-item
              label="服务公司"
              prop="companyId"
              class="elFormItem"
              style="width: 22%; margin-right: 20px"
            >
              <el-select
                v-model="filters.companyId"
                placeholder="全部"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option
                  v-for="item in companyList"
                  :key="item.id"
                  :label="item.displayName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              prop="workorderStatus"
              style="width: 22%"
            >
              <el-select
                v-model="filters.workorderStatus"
                placeholder="全部"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option v-if="userStore.companyType === '1'" label="待提交" value="0" />
                <el-option v-if="userStore.companyType === '1'" label="船舶公司内部审批驳回" value="1" />
                <el-option v-if="userStore.companyType === '1'" label="船舶公司内部审批中" value="2" />
                <el-option label="待确认" value="3" />
                <el-option label="航运公司内部审批驳回" value="4" />
                <el-option label="航运公司内部审批中" value="5" />
                <el-option label="操作中" value="6" />
                <el-option label="待反馈" value="7" />
                <el-option label="航运公司驳回" value="8" />
                <el-option label="处理完毕" value="9" />
                <el-option label="反馈完毕" value="10" />
                <el-option label="异常处理" value="11" />
                <el-option label="异常处理完毕" value="12" />
              </el-select>
            </el-form-item>
          </div>
          <div class="flex justify-end">
            <el-form-item>
              <el-button
                type="primary"
                style="margin-right: 20px; width: 80px"
                @click="onSearch"
                >查询
              </el-button>
              <el-button style="width: 80px" @click="onReset">重置</el-button>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <!-- 头部操作区 -->
    <div class="summary-header" style="height: 50px; padding: 0 16px">
      <div
        class="actions-wrapper"
        style="display: flex; align-items: center; width: 100%"
      >
        <div
          v-if="showCreateButton"
          class="left-actions"
          style="display: flex; gap: 12px; flex-wrap: nowrap"
        >
          <el-button
            v-hasPermi="['salary:usdWorkorder:add']"
            class="custom-blue-button"
            @click="handleCreateCommand('single')"
          >
            <el-icon>
              <Plus />
            </el-icon>
            单笔转账
          </el-button>
          <el-button
            v-hasPermi="['salary:usdWorkorder:add']"
            class="custom-blue-button"
            @click="handleCreateCommand('batch')"
          >
            <el-icon>
              <Plus />
            </el-icon>
            批量转账
          </el-button>
          <el-button
            v-hasPermi="['salary:usdWorkorder:add']"
            class="custom-blue-button"
            @click="handleCreateCommand('public')"
          >
            <el-icon>
              <Plus />
            </el-icon>
            对公转账
          </el-button>
        </div>
        <div
          class="right-actions"
          style="display: flex; gap: 12px; margin-left: auto"
        >
          <el-button class="custom-blue-button" @click="handleConfigTable">
            <el-icon>
              <Setting />
            </el-icon>
            配置表格
          </el-button>
          <el-button
            v-hasPermi="['salary:usdWorkorder:query']"
            class="custom-blue-button"
            @click="handleRefresh"
          >
            <el-icon>
              <Refresh />
            </el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <UsdWorkorderTable
      ref="usdWorkorderTableRef"
      v-loading="loading"
      :table-data="tableData"
      :loading="loading"
      :default-columns="[
        { prop: 'id', label: '工单编号', minWidth: '120', fixed: 'left' },
        { prop: 'shippingName', label: '船舶公司', minWidth: '150' },
        { prop: 'transportName', label: '航运公司', minWidth: '150' },
        { prop: 'createTime', label: '工单创建时间', minWidth: '120' },
        { prop: 'salary', label: '总金额', minWidth: '120', align: 'right' },
        { prop: 'count', label: '笔数', minWidth: '80', align: 'center' },
        { prop: 'processName', label: '处理人', minWidth: '120' },
        {
          prop: 'commission',
          label: '手续费',
          minWidth: '120',
          align: 'right',
        },
        {
          prop: 'usedCredit',
          label: '授信金额',
          minWidth: '120',
          align: 'right',
          visible: showOperations.isTransport,
        },
        {
          prop: 'workorderStatus',
          label: '状态',
          width: '130',
          type: 'status',
        },
      ]"
      :config-name="'usdWorkorderTable'"
      :is-transport="showOperations.isTransport"
      :is-shipping="showOperations.isShipping"
      :is-dealer="showOperations.isDealer"
      :pagination="true"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      @operation="handleTableOperation"
      @page-change="onPageChange"
      @size-change="onSizeChange"
    />

    <!-- 工单详情抽屉 -->
    <el-drawer
      v-model="detailDrawer.visible"
      direction="rtl"
      size="86%"
      :before-close="handleDrawerClose"
    >
      <template #title>
        <div class="drawer-title-container">
          <span class="drawer-title">{{ detailDrawer.title }}</span>
          <div class="title-anchor-nav">
            <ul class="anchor-menu">
              <li>
                <a
                  class="anchor-link"
                  title="工单明细"
                  @click.prevent="scrollToAnchor('order-details')"
                >
                  <span class="anchor-text">工单明细</span>
                </a>
              </li>
              <li
                v-if="
                  detailDrawer.form.workorderStatus != '0' &&
                  stepsData.length > 0
                "
              >
                <a
                  class="anchor-link"
                  title="审批流程"
                  @click.prevent="scrollToAnchor('approval-process')"
                >
                  <span class="anchor-text">审批流程</span>
                </a>
              </li>
              <li
                v-if="
                  detailDrawer.form.workorderStatus != '0' &&
                  approvalLogData.length > 0
                "
              >
                <a
                  class="anchor-link"
                  title="审批日志"
                  @click.prevent="scrollToAnchor('approval-log')"
                >
                  <span class="anchor-text">审批日志</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </template>
      <div
        class="drawer-content"
        :class="{ 'approval-content': detailDrawer.mode === 'approval' }"
        style="position: relative"
      >
        <!-- 基本信息 -->
        <el-card
          :class="[
            'form-card',
            { 'approval-form-card': detailDrawer.mode === 'approval' },
          ]"
        >
          <el-form
            v-if="
              detailDrawer.mode == 'view' || detailDrawer.mode == 'approval'
            "
            :model="detailDrawer.form"
            label-width="100px"
            class="detail-form"
          >
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="工单编号">
                  <el-input v-model="detailDrawer.originalData.id" disabled />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="船舶公司">
                  <el-input
                    v-model="detailDrawer.originalData.shippingName"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="航运公司">
                  <el-input
                    v-model="detailDrawer.originalData.transportName"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="建单人">
                  <el-input
                    v-model="detailDrawer.originalData.createrName"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="总金额">
                  <el-input
                    v-model="detailDrawer.originalData.salary"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="手续费">
                  <el-input
                    v-model="detailDrawer.originalData.commission"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12" :md="8" :lg="6">
                <el-form-item label="工单状态">
                  <el-input
                    :value="getWorkorderStatusText(detailDrawer.originalData.workorderStatus)"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col
                v-if="showOperations.isTransport"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
              >
                <el-form-item label="授信金额">
                  <el-input
                    v-model="detailDrawer.originalData.usedCredit"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="24" :md="24" :lg="24">
                <el-form-item label="备注">
                  <el-input
                    v-model="detailDrawer.form.remark"
                    :disabled="
                      detailDrawer.mode == 'approval' ||
                      !['3', '6'].includes(
                        detailDrawer.originalData.workorderStatus,
                      ) ||
                      isShippingCompany
                    "
                    type="textarea"
                    :rows="2"
                    placeholder="请输入备注信息"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-form
            v-else
            :model="detailDrawer.form"
            label-width="100px"
            class="detail-form"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="服务公司">
                  <el-select
                    v-if="
                      detailDrawer.mode === 'edit' ||
                      detailDrawer.mode === 'copy'
                    "
                    v-model="detailDrawer.form.companyId"
                    placeholder="请选择服务公司"
                    clearable
                    @change="onCompanyChange"
                  >
                    <el-option
                      v-for="item in companyList"
                      :key="item.id"
                      :label="item.displayName"
                      :value="item.id"
                    />
                  </el-select>
                  <el-input
                    v-else
                    v-model="detailDrawer.form.companyName"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="建单人">
                  <el-input v-model="detailDrawer.form.createrName" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注">
                  <el-input
                    v-model="detailDrawer.form.remark"
                    type="textarea"
                    :disabled="['3'].includes(detailDrawer.originalData.workorderStatus)"
                    :rows="2"
                    placeholder="请输入备注信息"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-button
            v-if="detailDrawer.mode === 'edit' || detailDrawer.mode === 'copy'"
            @click="onAddDetailItem"
            >添加明细
          </el-button>
          <el-button
            v-if="detailDrawer.mode === 'edit' || detailDrawer.mode === 'copy'"
            @click="handleImportTemplate"
            >导入明细
          </el-button>
          <el-button
            v-if="detailDrawer.mode === 'edit' || detailDrawer.mode === 'copy'"
            @click="handleExportTemplate"
            >下载模板
          </el-button>
          <el-button
            v-if="detailDrawer.mode === 'view'"
            @click="handleExportDetail"
            >导出明细
          </el-button>
          <!-- 工单明细 -->
          <el-card
            v-if="detailDrawer.mode !== 'approval'"
            id="order-details"
            class="mt-4"
          >
            <div class="mb-2 font-bold">
              工单明细
              <el-button
                v-if="showBatchModifyButton"
                size="small"
                type="primary"
                :disabled="selectedDetails.length === 0"
                @click="handleBatchModify"
                style="margin-left: 10px;"
              >批量修改状态</el-button>
            </div>
            <el-table
              v-loading="detailDrawer.loading"
              :data="detailDrawer.form.details"
              border
              stripe
              style="width: 100%"
              height="400px"
              :max-height="400"
              @selection-change="handleSelectionChange"
              ref="detailTableRef"
            >
              <el-table-column
                v-if="showBatchModifyButton"
                type="selection"
                width="55"
                fixed="left"
              />
              <el-table-column
                v-for="col in getDetailColumns"
                :key="col.prop"
                :prop="col.prop"
                :label="col.label"
                :width="col.width"
                :fixed="col.fixed"
                :formatter="col.formatter"
              >
                <template
                  v-if="
                    col.prop === 'operations' &&
                    (detailDrawer.mode === 'edit' ||
                      detailDrawer.mode === 'copy')
                  "
                  #default="scope"
                >
                  <el-button
                    type="primary"
                    link
                    @click="onEditDetailItem(scope.row)"
                    >编辑
                  </el-button>
                  <el-button
                    type="danger"
                    link
                    @click="onDeleteDetailItem(scope.row)"
                    >删除
                  </el-button>
                </template>
                <template
                  v-else-if="col.cellSlot === 'verifyCell'"
                  #default="scope"
                >
                  <span v-if="scope.row.verifyStatus === 1">已核对</span>
                  <el-button
                    v-else-if="
                      userStore.companyType === '2' &&
                      scope.row.verifyStatus === 0
                    "
                    type="primary"
                    link
                    @click="onViewDetailItem(scope.row)"
                    >核对
                  </el-button>
                </template>

                <template v-else-if="col.slot" #default="scope">
                  <el-button
                    v-if="
                      // 当工单状态为反馈完成且明细发放状态为失败且没有错误原因或者类型时显示处理异常按钮
                      (detailDrawer.originalData.workorderStatus === '10' &&
                        scope.row.grantStatus == '1' &&
                        (!scope.row.exceptionReason || !scope.row.exceptionType) &&
                        userStore.companyType == '2')
                         ||
                      // 当工单状态为异常待处理且明细发放状态为失败且异常类型为信息错误时显示处理异常按钮
                      (detailDrawer.originalData.workorderStatus === '11' &&
                        scope.row.grantStatus == '1' &&
                        scope.row.exceptionType == '1' &&
                        userStore.companyType == '1' &&
                        (scope.row.feePayertype === '2' || !scope.row.feePayertype)) ||
                      // 当工单状态为异常待处理且明细发放状态为失败且异常类型为银行问题且未上传文件时显示处理异常按钮
                      (detailDrawer.originalData.workorderStatus === '11' &&
                        scope.row.grantStatus == '1' &&
                        scope.row.exceptionType == '2' &&
                        userStore.companyType == '1' &&
                        !scope.row.fileId)
                    "
                    type="primary"
                    link
                    @click="handleDetailException(scope.row)"
                  >
                    处理异常
                  </el-button>
                  <el-button
                    v-if="
                      // 发放中或有fileId时显示修改状态按钮
                      (scope.row.grantStatus === '2' || scope.row.fileId) &&
                      userStore.companyType === '1' &&
                      detailDrawer.originalData.workorderStatus === '7'
                    "
                    v-hasPermi="['salary:workorderDetail:updateGrantStatus']"
                    type="primary"
                    link
                    @click="handleUpdateGrantStatus(scope.row)"
                  >
                    修改状态
                  </el-button>
                  <el-button
                    v-if="scope.row.fileUrl && userStore.companyType === '2'"
                    type="primary"
                    link
                    @click="downloadFile(scope.row)"
                  >
                    下载文件
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 工资总金额显示区域 -->
            <div class="total-amount-container">
              <span class="total-amount-label">工资总额:</span>
              <span class="total-amount-value"
                >${{ totalAmount.toFixed(2) }}</span
              >
              <template v-if="showFeeInfo">
                <span class="total-amount-label ml-4">手续费总额:</span>
                <span class="total-amount-value"
                  >${{ totalFee.toFixed(2) }}</span
                >
              </template>
              <span class="total-amount-label" style="margin-left: 10px" ml-4 v-if="showFeeInfo"
                >总计:</span
              >
              <span class="total-amount-value" v-if="showFeeInfo"
                >${{
                  (
                    parseFloat(totalAmount.toFixed(2)) +
                    parseFloat(totalFee.toFixed(2))
                  ).toFixed(2)
                }}</span
              >
            </div>
          </el-card>
        </el-card>
        <el-card
          v-if="
            detailDrawer.form.workorderStatus != '0' && stepsData.length > 0
          "
          id="approval-process"
          class="mt-4"
          :class="{ 'approval-mt-4': detailDrawer.mode === 'approval' }"
        >
          <div class="mb-2 font-bold">审批流程</div>
          <el-steps :active="stepsActive" align-center finish-status="success">
            <el-step
              v-for="(item, index) in stepsData"
              :key="index"
              :title="item.auditingUserName"
            >
            </el-step>
          </el-steps>
        </el-card>
        <el-card
          v-if="
            detailDrawer.form.workorderStatus != '0' &&
            approvalLogData.length > 0
          "
          id="approval-log"
          class="mt-4"
          :class="{ 'approval-mt-4': detailDrawer.mode === 'approval' }"
        >
          <div class="mb-2 font-bold">审批日志</div>
          <el-table
            :data="approvalLogData"
            style="width: 100%"
            height="400"
            :max-height="400"
          >
            <el-table-column prop="userName" label="审批人" width="180" />
            <!--            <el-table-column prop="userName" label="审批人" width="180" />-->
<!--            <el-table-column prop="companyName" label="审批公司" width="180" />-->
            <el-table-column prop="updateTime" label="审批时间" width="180" />
            <el-table-column prop="status" label="审批状态">
              <template #default="scope">
                <el-tag :type="getApprovalStatusType(scope.row.status)">
                  {{ getApprovalStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="opinion" label="审批意见" />
          </el-table>
        </el-card>
        <!-- 根据不同状态显示不同的底部按钮--对于工单的修改 -->
        <div class="action-buttons drawer-actions">
          <template v-if="detailDrawer.mode == 'process'">
            <template v-if="detailDrawer.originalData.workorderStatus === '6'">
              <!-- 状态为6（操作中）时的按钮 -->
              <el-button type="primary" @click="handleSubmitProcess"
                >操作完成
              </el-button>
            </template>
            <template
              v-else-if="detailDrawer.originalData.workorderStatus === '3'"
            >
              <!-- 状态为3（待确认）时的按钮 -->
              <!-- 经销商公司需要先选择航运公司 -->
<!--              <div v-if="userStore.companyType === '4'" class="mb-4 p-4 bg-gray-50 rounded-lg">-->
<!--                <el-form label-width="120px">-->
<!--                  <el-form-item label="选择航运公司" required>-->
<!--                    <el-select-->
<!--                      v-model="selectedTransportId"-->
<!--                      placeholder="请选择航运公司"-->
<!--                      clearable-->
<!--                      style="width: 300px;"-->
<!--                      :disabled="transportCompanyList.length === 0"-->
<!--                    >-->
<!--                      <el-option-->
<!--                        v-for="item in transportCompanyList"-->
<!--                        :key="item.transportId"-->
<!--                        :label="item.transportName"-->
<!--                        :value="item.transportId"-->
<!--                      />-->
<!--                    </el-select>-->
<!--                    <div class="text-xs text-gray-500 mt-1">-->
<!--                      {{ transportCompanyList.length === 0 ? '暂无可选择的航运公司' : '请先选择要合作的航运公司' }}-->
<!--                    </div>-->
<!--                  </el-form-item>-->
<!--                </el-form>-->
<!--              </div>-->
              <el-button
                type="primary"
                :disabled="hasUnverifiedDetails || (userStore.companyType === '4' && !selectedTransportId)"
                :loading="processLoading"
                @click="handleSubmitProcess"
                >提交办理
              </el-button>
              <el-button
                type="warning"
                :loading="rejectLoading"
                @click="handleReject"
                >驳回
              </el-button>
            </template>
            <template
              v-else-if="detailDrawer.originalData.workorderStatus === '7'"
            >
            </template>
          </template>
          <el-button
            v-if="
              detailDrawer.mode == 'view' &&
              canCompleteWorkorder &&
              userStore.companyType === '2' &&
              detailDrawer.originalData.workorderStatus === '10'
            "
            type="primary"
            @click="handleCompleteWorkorder"
            >工单完成
          </el-button>
          <el-button
            v-if="
              detailDrawer.mode == 'view' &&
              canFeedbackComplete &&
              userStore.companyType === '1' &&
              detailDrawer.originalData.workorderStatus === '7'
            "
            type="primary"
            @click="handleFeedbackComplete"
            >反馈完成
          </el-button>
          <el-button
            v-if="
              detailDrawer.mode == 'view' &&
              canCompleteExceptionHandling &&
              userStore.companyType === '2' &&
              detailDrawer.originalData.workorderStatus === '10'
            "
            type="primary"
            @click="handleCompleteExceptionHandling"
            >完成异常原因处理
          </el-button>
          <el-button
            v-if="
              detailDrawer.mode == 'view' &&
              userStore.companyType === '1' &&
              detailDrawer.originalData.workorderStatus === '11'
            "
            type="primary"
            @click="handleCompleteException"
            >船舶公司处理完成异常
          </el-button>
          <el-button
            v-if="
              detailDrawer.mode == 'view' &&
              userStore.companyType === '2' &&
              detailDrawer.originalData.workorderStatus === '12'
            "
            type="primary"
            @click="handleExceptionHandleError"
            >反馈异常处理
          </el-button>
          <el-button
            v-if="detailDrawer.mode == 'edit' || detailDrawer.mode == 'copy'"
            type="primary"
            :disabled="hasUnverifiedDetails"
            :loading="submitLoading"
            @click="onSubmitDrawer"
            >提交
          </el-button>
          <el-button
            v-if="detailDrawer.mode == 'edit' || detailDrawer.mode == 'copy'"
            type="primary"
            :loading="saveLoading"
            @click="onSaveDrawer"
            >暂存
          </el-button>
          <el-button @click="detailDrawer.visible = false">关闭</el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 明细编辑对话框 -->
    <el-drawer
      v-model="detailItemDialog.visible"
      :title="detailItemDialog.title"
      direction="rtl"
      size="30%"
    >
      <div class="detail-form-container">
        <el-form
          :model="detailItemDialog.data"
          label-width="100px"
          class="detail-dialog-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 编号只有在查看或编辑已有记录时显示 -->
              <el-form-item v-if="detailItemDialog.showId" label="编号">
                <el-input v-model="detailItemDialog.data.id" disabled />
              </el-form-item>
              <el-form-item label="证件号码">
                <el-input
                  v-model="detailItemDialog.data.certNo"
                  :disabled="detailItemDialog.mode === 'view'"
                  @blur="queryCertNoInfo"
                />
              </el-form-item>
              <el-form-item label="船员姓名">
                <el-input
                  v-model="detailItemDialog.data.grantobjectName"
                  :disabled="detailItemDialog.mode === 'view'"
                />
              </el-form-item>
              <el-form-item label="证件类型">
                <el-select
                  v-model="detailItemDialog.data.certType"
                  placeholder="请选择证件类型"
                  :disabled="detailItemDialog.mode === 'view'"
                >
                  <el-option label="居民身份证" value="1" />
                  <el-option label="护照" value="2" />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="detailItemDialog.showAccountSelect"
                label="选择账户"
              >
                <el-select
                  v-model="detailItemDialog.data.selectedAccountId"
                  placeholder="请选择账户"
                  @change="handleAccountChange"
                >
                  <el-option
                    v-for="account in detailItemDialog.accounts"
                    :key="account.accountId"
                    :label="`${account.accountNo}(${account.bankName})`"
                    :value="account.accountId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="职务">
                <el-input
                  v-model="detailItemDialog.data.job"
                  :disabled="detailItemDialog.mode === 'view'"
                />
              </el-form-item>
              <el-form-item label="工资额">
                <el-input
                  v-model="detailItemDialog.data.grantAmount"
                  :disabled="detailItemDialog.mode === 'view'"
                  @blur="formatGrantAmount"
                />
              </el-form-item>
              <el-form-item label="持卡人姓名">
                <el-input
                  v-model="detailItemDialog.data.accountName"
                  :disabled="detailItemDialog.mode === 'view'"
                />
              </el-form-item>
              <el-form-item label="持卡人拼音">
                <el-input
                  v-model="detailItemDialog.data.accountPinyin"
                  :disabled="detailItemDialog.mode === 'view'"
                />
              </el-form-item>
              <el-form-item label="账号">
                <el-input
                  v-model="detailItemDialog.data.accountNo"
                  :disabled="detailItemDialog.mode === 'view'"
                  @blur="queryBankInfo"
                />
              </el-form-item>
              <el-form-item label="开户行">
                <el-input
                  v-model="detailItemDialog.data.bankName"
                  :disabled="detailItemDialog.mode === 'view'"
                />
              </el-form-item>
              <el-form-item label="开户支行">
                <el-input
                  v-model="detailItemDialog.data.depositBranch"
                  :disabled="detailItemDialog.mode === 'view'"
                  @blur="querySwiftCode"
                />
              </el-form-item>
              <el-form-item
                v-if="
                  (detailItemDialog.mode !== 'edit' &&
                    ['4', '5', '6', '7', '8', '9'].includes(
                      detailDrawer.originalData.workorderStatus,
                    )) ||
                  (detailItemDialog.mode === 'view' &&
                    detailDrawer.originalData.workorderStatus === '3')
                "
                label="交易类型"
                required
              >
                <el-select
                  v-model="detailItemDialog.data.grantType"
                  placeholder="请选择交易类型"
                  :disabled="
                    detailItemDialog.mode === 'view' &&
                    detailDrawer.originalData.workorderStatus !== '3'
                  "
                >
                  <el-option label="个人境内" value="1" />
                  <el-option label="个人境外" value="2" />
                  <el-option label="公司境内" value="3" />
                  <el-option label="公司境外" value="4" />
                </el-select>
              </el-form-item>
              <el-form-item label="swiftcode">
                <el-input
                  v-model="detailItemDialog.data.swiftCode"
                  :disabled="detailItemDialog.mode === 'view'"
                />
              </el-form-item>
              <el-form-item label="备注">
                <el-input
                  v-model="detailItemDialog.data.detailRemark"
                  :disabled="detailItemDialog.mode === 'view'"
                  type="textarea"
                  :rows="2"
                />
              </el-form-item>
              <el-form-item label="所在船舶">
                <el-input
                  v-model="detailItemDialog.data.ship"
                  :disabled="detailItemDialog.mode === 'view'"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="drawer-footer">
        <el-button
          v-if="
            detailItemDialog.mode === 'view' &&
            detailDrawer.originalData.workorderStatus == '3'
          "
          @click="handleVerify(detailItemDialog.data)"
          >核对
        </el-button>
        <el-button
          v-if="
            detailItemDialog.mode === 'view' &&
            detailItemDialog.data.fileUrl &&
            userStore.companyType === '2' &&
            detailDrawer.originalData.workorderStatus == '12'
          "
          type="primary"
          @click="downloadFile(detailItemDialog.data)"
          >下载文件
        </el-button>
        <!-- 异常处理状态下显示的保存按钮 -->
        <el-button
          v-if="
            detailDrawer.originalData.workorderStatus === '11' &&
            detailItemDialog.mode === 'edit'
          "
          type="primary"
          @click="onSaveExceptionDetail"
        >
          保存异常信息
        </el-button>
        <!-- 非异常处理状态下显示的常规保存按钮 -->
        <el-button
          v-if="
            detailDrawer.originalData.workorderStatus !== '11' &&
            detailDrawer.originalData.workorderStatus != '3'
          "
          type="primary"
          @click="onSaveDetailItem"
        >
          保存
        </el-button>
        <el-button
          @click="
            () => {
              detailItemDialog.visible = false;
              resetDetailItemDialog();
              // 重新显示异常处理弹框
              if (exceptionDialog.currentDetail) {
                exceptionDialog.visible = true;
              }
            }
          "
          >取消
        </el-button>
      </div>
    </el-drawer>

    <!-- 处理异常弹窗 -->
    <el-dialog
      v-model="exceptionDialog.visible"
      :show-header="false"
      width="500px"
      append-to-body
    >
      <!-- 航运公司的异常处理界面 -->
      <template v-if="!isShippingCompany">
        <div class="exception-form">
          <el-form :model="exceptionDialog.form" label-width="100px">
            <el-form-item label="异常类型">
              <el-radio-group v-model="exceptionDialog.type">
                <el-radio-button label="2">银行审核</el-radio-button>
                <el-radio-button label="1">信息错误</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="异常原因" required>
              <el-input
                v-model="exceptionDialog.form.reason"
                type="textarea"
                :rows="3"
                placeholder="请输入异常原因"
              />
            </el-form-item>
            <!-- 信息错误时显示额外字段 -->
            <template v-if="exceptionDialog.type === '1'">
              <el-form-item label="退汇扣收">
                <el-input
                  v-model="exceptionDialog.form.returnFee"
                  placeholder="请输入退汇扣收金额"
                />
              </el-form-item>
<!--              <el-form-item label="退款手续费">-->
<!--                <el-input-->
<!--                  v-model="exceptionDialog.form.refundFee"-->
<!--                  placeholder="请输入退款手续费"-->
<!--                />-->
<!--              </el-form-item>-->
              <el-form-item label="责任方">
                <el-radio-group v-model="exceptionDialog.form.deductTarget">
                  <el-radio :label="1">航运公司</el-radio>
                  <el-radio :label="2">船舶公司</el-radio>
                </el-radio-group>
              </el-form-item>
            </template>
          </el-form>
        </div>
      </template>

      <!-- 船舶公司的异常处理界面 -->
      <template v-else>
        <!-- 只在有异常原因时显示内容 -->
        <div v-if="exceptionDialog.form.reason">
          <!-- 根据异常类型显示相应的处理标题 -->
          <div class="exception-content">
            <div class="exception-reason-title">
              {{
                exceptionDialog.type === "2" ? "银行问题处理" : "信息错误处理"
              }}
            </div>
          </div>
        </div>
        <div v-else>
          <div class="empty-exception-msg">没有异常信息需要处理</div>
        </div>

        <!-- 表单内容 -->
        <el-form :model="exceptionDialog.form" label-width="100px" class="mt-4">
          <!-- 异常原因展示 -->
          <el-form-item label="异常原因">
            <div class="exception-reason">
              {{ exceptionDialog.form.reason || "暂无异常原因" }}
            </div>
          </el-form-item>

          <!-- 银行问题表单 -->
          <template v-if="exceptionDialog.type === '2' && isShippingCompany && !exceptionDialog.currentDetail?.fileId">
            <el-form-item label="上传处理文件" required>
              <el-upload
                class="upload-demo"
                :headers="exceptionHeaders"
                :action="uploadAction"
                :data="{workorderId: detailDrawer.originalData.id, detailId: exceptionDialog.currentDetail?.id || ''}"
                :before-upload="beforeUpload"
                :on-success="handleExceptionUploadSuccess"
                :on-error="handleExceptionUploadError"
                :limit="1"
              >
                <el-button type="primary">点击上传</el-button>
                <div class="el-upload__tip">仅允许上传Word文件（.doc/.docx），大小不超过5MB</div>
              </el-upload>
            </el-form-item>
          </template>

          <!-- 信息错误表单 -->
          <template v-if="exceptionDialog.type === '1'">
            <!-- 编辑明细按钮 -->
            <el-form-item>
              <el-button type="primary" @click="handleEditDetail">
                编辑明细
              </el-button>
              <div class="form-tip">点击编辑按钮修改明细信息</div>
            </el-form-item>

            <!-- 显示信息修改状态 -->
            <template v-if="exceptionDialog.currentDetail && exceptionDialog.currentDetail.errorCorrection === '1'">
              <el-form-item>
                <div class="form-tip success-tip">已完成信息修改</div>
              </el-form-item>
            </template>

            <!-- 费用支付方类型选择 -->
            <el-form-item label="费用支付方" required>
              <el-radio-group v-model="exceptionDialog.form.feePayertype">
                <el-radio label="2">船舶公司</el-radio>
                <el-radio label="3">个人</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
        </el-form>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exceptionDialog.visible = false">取消</el-button>
          <el-button
            type="primary"
            :disabled="!canSubmit"
            @click="handleSubmitException"
          >
            {{ isShippingCompany ? "提交处理" : "提交" }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog
      v-model="upload.open"
      :title="upload.title"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="
          upload.url + '?workorderId=' + (detailDrawer.originalData?.id || '')
        "
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <Upload />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="handleExportTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件上传隐藏输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept=".xls,.xlsx"
      style="display: none"
      @change="handleNativeFileChange"
    />

    <!-- 导入状态提示 -->
    <el-alert
      v-if="importStatus.show"
      :title="importStatus.message"
      :type="importStatus.type"
      :closable="false"
      class="mb-[10px]"
      show-icon
    />

    <!-- 修改发放状态弹窗 -->
    <el-dialog
      v-model="grantStatusDialog.visible"
      :title="grantStatusDialog.title"
      width="30%"
      append-to-body
    >
      <div class="form-container">
        <el-form
          :model="grantStatusDialog.data"
          label-width="100px"
          class="form-dialog"
        >
          <el-form-item label="发放状态">
            <el-select v-model="grantStatusDialog.status">
              <el-option label="成功" value="0" />
              <el-option label="失败" value="1" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialog-footer">
        <el-button @click="grantStatusDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitUpdateGrantStatus"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 异常处理反馈对话框 -->
    <el-dialog
      v-model="exceptionFeedbackDialog.visible"
      title="反馈异常处理"
      width="30%"
      append-to-body
    >
      <div class="form-container">
        <el-form label-width="120px" class="form-dialog">
          <el-form-item label="异常处理是否有误">
            <el-radio-group v-model="exceptionFeedbackDialog.form.isException">
              <el-radio label="0">处理无误</el-radio>
              <el-radio label="1">处理有误</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="错误信息" v-if="exceptionFeedbackDialog.form.isException === '1'">
            <el-input
              v-model="exceptionFeedbackDialog.form.errorMsg"
              type="textarea"
              rows="3"
              placeholder="请输入错误信息"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exceptionFeedbackDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitExceptionFeedback">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量修改发放状态对话框 -->
    <el-dialog
      v-model="batchModifyDialog.visible"
      title="批量修改发放状态"
      width="30%"
      append-to-body
    >
      <div class="form-container">
        <el-form label-width="100px" class="form-dialog">
          <el-form-item label="发放状态" required>
            <el-select v-model="batchModifyDialog.status" placeholder="请选择状态">
              <el-option label="成功" value="0" />
              <el-option label="失败" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="选中明细">
            <div class="selected-count">已选择 {{ selectedDetails.length }} 条明细</div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchModifyDialog.visible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="batchModifyDialog.loading"
            @click="submitBatchModify">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  onMounted,
  computed,
  nextTick,
  getCurrentInstance
} from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useUserStore } from "@/store/modules/user";
import { useSettingsStore } from "@/store/modules/settings";
import { getPropertyList } from "@/api/salary/usdConfig";
import {
  listUsdWorkorder,
  processWorkorder,
  rejectUsdWorkorder,
  completeUsdWorkorder,
  handleUsdWorkorderException,
  getApprovalProcess,
  updateGrantType,
  updateGrantStatus,
  uploadExceptionFile,
  completeWorkorder,
  feedbackComplete,
  completeExceptionHandling,
  updateExceptionDetail,
  completeException,
  exceptionHandleError,
  getSwiftCodeByBank,
  getBankInfoByCardNumber
} from "@/api/salary/usdWorkorder";
import {
  getWorkorderDetail,
  submitWorkorder,
  tempWorkorder,
  updateVerifyStatus,
} from "@/api/salary/usdWorkorder/detailAndEdit";
import { delWorkorderDetail, batchUpdateGrantStatus } from "@/api/salary/workorderDetail/index"; // 修正导入路径
import { getExamineLog } from "@/api/salary/workorderInfo";
import { getUserplus_infoByCertNo } from "@/api/system/userplus_info";
import UsdWorkorderTable from "./UsdWorkorderTable.vue";
import { globalHeaders } from "@/utils/request";

// 工单表单接口定义
interface WorkorderDetailVO {
  workorder: {
    id: number;
    transportId: string;
    transportName: string;
    createrName: string;
    remark: string;
    createTime: string;
    shippingId: string;
    shippingName: string;
    salary: number;
    commission: number;
    usedCredit: number;
    workorderStatus: string;
    processName?: string; // 添加可选的处理人字段
  };
  detailList: Array<{
    id: number;
    workorderId: string;
    grantobjectId: number;
    grantobjectName: string | null;
    grantAmount: number; // 添加发放金额字段
    relatedAcountid: string;
    grantFee?: number;
    detailCommission?: number;
    adjustAmount?: number;
    grantStatus: string;
    grantType: string;
    remark: string;
    ship: string;
    swiftCode: string | null;
    accountNo?: string;//添加银行账号字段
    bankName?: string;
    accountName?: string;
    accountPinyin?: string;
    verifyStatus?: number;
    certType?: string; // 添加证件类型
    certNo?: string; // 添加证件号码
    job?: string; // 添加职务
    detailRemark?: string; // 添加明细备注字段
    fileId?: string; // 添加文件ID字段
    fileUrl?: string; // 添加文件URL字段
    exceptionReason?: string; // 添加异常原因字段
    exceptionType?: string; // 添加异常类型字段
    errorCorrection?: string; // 添加信息错误是否修改字段 0未修改 1已修改
    feePayertype?: string; // 添加费用支付方类型字段 1航运，2船舶，3个人
  }>;
}

interface WorkorderForm {
  id?: number;
  companyId: string;
  companyName: string;
  createrName: string;
  remark: string;
  createTime: string;
  details: Array<{
    id?: number;
    grantobjectId: number; // 发放对象ID
    grantobjectName: string | null; // 发放对象名称
    grantAmount: number; // 发放金额
    relatedAcountid: string; // 关联账户ID
    grantFee?: number; // 发放手续费
    detailCommission?: number; // 明细手续费
    adjustAmount?: number; // 调整金额
    grantStatus: string; // 发放状态
    grantType: string; // 发放类型
    remark: string; // 备注
    ship: string; // 船名
    swiftCode: string | null; // 银行SWIFT代码
    verifyStatus?: number; // 审核状态
    certType?: string; // 证件类型
    certNo?: string; // 证件号码
    job?: string; // 职务
    accountNo?: string; // 账号
    bankName?: string; // 添加开户行
    depositBranch?: string; // 添加开户支行
    accountName?: string; // 添加持卡人姓名
    accountPinyin?: string; // 添加持卡人拼音
    detailRemark?: string; // 添加明细备注
    fileUrl?: string; // 添加文件URL字段
    delFlag?: string; // 添加删除标记
  }>;
  transferType: string;
  workorderStatus: string;
}

// 获取用户信息
const userStore = useUserStore();
// 获取系统配置信息
const settingsStore = useSettingsStore();
// 获取主题色
const themeColor = computed(() => settingsStore.theme);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const loading = ref(false);
const approvalLogData = ref<any[]>([]);
const stepsData = ref<any[]>([]);
const stepsActive = ref(0);
// 筛选条件
const filters = reactive({
  workorderId: "",
  companyId: "",
  workorderStatus: "",
  dateRange: null as [Date, Date] | null,
});

// 公司列表
const companyList = ref<any[]>([]);
// 航运公司列表（用于经销商选择）
const transportCompanyList = ref<any[]>([]);
// 选择的航运公司ID（经销商使用）
const selectedTransportId = ref<string>("");
// 获取有服务关系的企业列表
const fetchCompanyList = async () => {
  // 如果公司类型为空或没有公司ID，不发送请求
  if (!userStore.companyType || !userStore.companyId) {
    companyList.value = [];
    return;
  }

  try {
    const params: any = {};
    if (userStore.companyType === "1") {
      // 船舶公司登录，获取关联的航运公司列表
      params.shippingId = userStore.companyId;
    } else if (userStore.companyType === "2") {
      // 航运公司登录，获取关联的船舶公司列表
      params.transportId = userStore.companyId;
    }

    const res = await getPropertyList(params);
    if (res.code === 200 && res.data) {
      companyList.value = res.data.map((item: any) => {
        // 根据公司类型设置显示的公司名称
        let displayName = "";
        // 如果是船舶公司登录,显示航运公司名称
        if (userStore.companyType === "1" && item.transportName) {
          displayName = item.transportName;
        }
        // 如果是航运公司登录,显示船舶公司名称
        else if (userStore.companyType === "2" && item.shippingName) {
          displayName = item.shippingName;
        }
        // 默认显示任一不为空的名称
        else {
          displayName = item.transportName || item.shippingName || "未知企业";
        }
        return {
          ...item,
          id:
            userStore.companyType === "1" ? item.transportId : item.shippingId, // 根据登录用户类型选择ID
          displayName,
          companyType:
            userStore.companyType === "1"
              ? "2"
              : userStore.companyType === "2"
                ? "1"
                : null, // 设置公司类型
        };
      });
    }
  } catch (error) {
    ElMessage.error("获取企业列表失败");
  }
};

// 获取航运公司列表（经销商使用）
const fetchTransportCompanyList = async () => {
  // 只有经销商才需要获取航运公司列表
  if (!userStore.companyType || userStore.companyType !== '4' || !userStore.companyId) {
    transportCompanyList.value = [];
    return;
  }

  try {
    const params: any = {
      distributorsId: userStore.companyId
    };

    const res = await getPropertyList(params);
    console.log(res);
    if (res.code === 200 && res.data) {
      transportCompanyList.value = res.data.map((item: any) => {
        return {
          transportId: item.transportId,
          transportName: item.transportName || '未知航运公司',
          ...item
        };
      }).filter((item: any) => item.transportId && item.transportName);
    }
  } catch (error) {
    ElMessage.error("获取航运公司列表失败");
  }
};

// 表格数据
const tableData = ref<any[]>([]);

// 提交和保存按钮的加载状态
const submitLoading = ref(false);
const saveLoading = ref(false);
const processLoading = ref(false);
const rejectLoading = ref(false);

// 格式化日期为后端需要的格式
const formatDateToString = (date: Date | string): string => {
  if (!date) return "";

  // 如果输入是字符串，检查它是否符合要求格式，如果已经符合则直接返回
  if (typeof date === "string") {
    if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(date)) {
      return date;
    }
    // 尝试将字符串转为日期对象
    date = new Date(date);
  }

  // 确保是有效的日期对象
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return "";
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// 查询工单列表
const fetchWorkorderList = async () => {
  loading.value = true;
  try {
    const params: any = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      id: filters.workorderId,
      workorderStatus: filters.workorderStatus,
    };

    // 根据用户类型设置公司ID
    if (userStore.companyType === "1") {
      // 船舶公司登录
      params.shippingId = userStore.companyId;
      if (filters.companyId) {
        params.transportId = filters.companyId;
      }
    } else if (userStore.companyType === "2") {
      // 航运公司登录
      params.transportId = userStore.companyId;
      if (filters.companyId) {
        params.shippingId = filters.companyId;
      }
    }

    // 处理建单时间筛选
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.beginTime = formatDateToString(filters.dateRange[0]);
      params.endTime = formatDateToString(filters.dateRange[1]);
    }

    const res = await listUsdWorkorder(params);
    if (res.code === 200) {
      tableData.value = res.rows || [];
      total.value = res.total || 0;
    } else {
      tableData.value = [];
      total.value = 0;
      ElMessage.error(res.msg || "查询失败");
    }
  } catch (e) {
    tableData.value = [];
    total.value = 0;
    ElMessage.error("查询失败");
  } finally {
    loading.value = false;
  }
};

// 状态相关函数已移至表格组件中

// 格式化证件类型
const formatCertType = (certType: string) => {
  const certTypeMap: Record<string, string> = {
    "1": "居民身份证",
    "2": "护照",
  };
  return certTypeMap[certType] || certType || "未知";
};

// 格式化交易类型
const formatTradeType = (grantType: string) => {
  const tradeTypeMap: Record<string, string> = {
    "1": "个人境内",
    "2": "个人境外",
    "3": "公司境内",
    "4": "公司境外",
  };
  return tradeTypeMap[grantType] || grantType || "未知";
};

// 格式化发放状态
const formatGrantStatus = (grantStatus: string) => {
  const grantStatusMap: Record<string, string> = {
    "0": "成功",
    "1": "失败",
    "2": "发放中",
  };
  return grantStatusMap[grantStatus] || "未知";
};

// 搜索
const onSearch = () => {
  currentPage.value = 1;
  fetchWorkorderList();
};

// 重置
const onReset = () => {
  filters.workorderId = "";
  filters.companyId = "";
  filters.workorderStatus = "";
  filters.dateRange = null;
  currentPage.value = 1;
  fetchWorkorderList();
};

// 分页相关
const onPageChange = (page: number) => {
  currentPage.value = page;
  fetchWorkorderList();
};

const onSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchWorkorderList();
};

// 刷新
const handleRefresh = () => {
  fetchWorkorderList();
  fetchCompanyList();
  // 如果是经销商公司，刷新航运公司列表
  if (userStore.companyType === '4') {
    fetchTransportCompanyList();
  }
  ElMessage.success("数据已刷新");
};

// 工单详情抽屉
const detailDrawer = reactive({
  visible: false,
  loading: false,
  form: {} as WorkorderForm,
  title: "工单详情",
  mode: "view", // view, copy, edit
  type: "", // single, batch, public
  originalData: {} as any,
  importDialogVisible: false, // 添加导入对话框显示状态
  modified: false, // 标记是否有修改
});

// 明细编辑对话框
const detailItemDialog = reactive({
  visible: false,
  mode: "view", // view or edit
  data: {} as any,
  index: -1,
  showId: false, // 控制是否显示ID字段
  accounts: [] as any[], // 存储账户列表
  showAccountSelect: false, // 控制是否显示账户选择下拉框,
  title: "", // 对话框标题
});

// 添加导入状态对象
const importStatus = reactive({
  show: false,
  type: "info", // 'info', 'success', 'error', 'warning'
  message: "",
});

// 查询工单详情
const fetchWorkorderDetail = async (id: string) => {
  detailDrawer.loading = true;
  try {
    const res = await getWorkorderDetail(id);
    if (res.code === 200 && res.data) {
      // 使用类型断言处理API返回数据
      const data = res.data as unknown as WorkorderDetailVO;
      const { workorder, detailList } = data;
      const workorderData = workorder || {};
      const detailListData = detailList || [];

      // 处理明细列表，将后端字段映射为前端展示需要的字段
      const processedDetails = detailListData.map((detail: any) => {
        return {
          id: detail.id,
          grantobjectId: detail.grantobjectId,
          grantobjectName: detail.grantobjectName,
          certType: detail.certType || "",
          certNo: detail.certNo || "",
          job: detail.job || "",
          grantAmount: detail.grantAmount || 0,
          relatedAcountid: detail.relatedAcountid || "",
          grantFee: detail.grantFee,
          detailCommission: detail.detailCommission,
          adjustAmount: detail.adjustAmount,
          grantStatus: detail.grantStatus || "2", // 默认为发放中
          grantType: detail.grantType,
          remark: detail.remark || "",
          ship: detail.ship || "",
          swiftCode: detail.swiftCode || "",
          accountNo: detail.accountNo || "",
          bankName: detail.bankName || "",
          depositBranch: detail.depositBranch || "",
          accountName: detail.accountName || "",
          accountPinyin: detail.accountPinyin || "",
          detailRemark: detail.remark || "", // 添加明细备注字段映射
          verifyStatus: detail.verifyStatus || 0,
          fileId: detail.fileId || "", // 添加文件ID字段
          fileUrl: detail.fileUrl || "", // 添加文件URL字段
          exceptionReason: detail.exceptionReason || "", // 添加异常原因字段
          exceptionType: detail.exceptionType || "", // 添加异常类型字段
          errorCorrection: detail.errorCorrection || "0", // 添加信息错误是否修改字段，默认为0(未修改)
          feePayertype: detail.feePayertype || "", // 添加费用支付方类型字段
        };
      });

      // 构建表单数据
      detailDrawer.form = {
        companyId: workorderData.transportId || "",
        companyName: workorderData.transportName || "",
        createrName: workorderData.createrName || "", // 修正字段名称
        remark: workorderData.remark || "",
        createTime: workorderData.createTime || "",
        details: processedDetails,
        transferType: detailDrawer.type || "public", // 默认为对公转账
        workorderStatus: workorderData.workorderStatus || "", // 添加工单状态字段
      };

      // 存储其他可能需要的字段，但不放入表单
      detailDrawer.originalData = {
        id: workorderData.id || "",
        shippingName: workorderData.shippingName || "",
        transportName: workorderData.transportName || "",
        createrName: workorderData.createrName || "", // 确保建单人正确映射
        processName: workorderData.processName || "",
        workorderStatus: workorderData.workorderStatus || "",
        salary: workorderData.salary || 0,
        commission: workorderData.commission || 0,
        usedCredit: workorderData.usedCredit || 0,
      };
    } else {
      ElMessage.error("获取工单详情失败");
    }
    const resApprovalProcessData = await getApprovalProcess(id);
    const responseApprovalProcessData = resApprovalProcessData.data as any;
    stepsData.value = responseApprovalProcessData.approvalProcessList;
    stepsActive.value = responseApprovalProcessData.currentSort;

    const resExamineLogData = await getExamineLog(id, 0);
    const responseresExamineLogData = resExamineLogData.data as any;
    approvalLogData.value = responseresExamineLogData;
  } catch (error) {
    ElMessage.error("获取工单详情失败");
  } finally {
    detailDrawer.loading = false;
  }
};

// 查看详情按钮
const handleDetail = (row: any) => {
  detailDrawer.visible = true;
  detailDrawer.mode = "view";
  detailDrawer.title = "工单详情";
  detailDrawer.loading = true;

  // 确保抽屉内容滚动到顶部
  nextTick(() => {
    const drawerContent = document.querySelector(".drawer-content");
    if (drawerContent) {
      drawerContent.scrollTop = 0;
    }
  });

  // 获取工单详情
  getWorkorderDetail(row.id)
    .then((res) => {
      if (res.code === 200 && res.data) {
        const data = res.data as unknown as WorkorderDetailVO;
        const { workorder, detailList } = data;

        // 处理明细列表
        const processedDetails = detailList.map((detail: any) => ({
          id: detail.id,
          grantobjectId: detail.grantobjectId,
          grantobjectName: detail.grantobjectName,
          certType: detail.certType || "",
          certNo: detail.certNo || "",
          job: detail.job || "",
          grantAmount: detail.grantAmount || 0,
          relatedAcountid: detail.relatedAcountid,
          grantFee: detail.grantFee,
          detailCommission: detail.detailCommission,
          adjustAmount: detail.adjustAmount,
          grantStatus: detail.grantStatus || "0",
          grantType: detail.grantType,
          remark: detail.remark || "",
          ship: detail.ship || "",
          swiftCode: detail.swiftCode || "",
          accountNo: detail.accountNo || "", // 确保映射accountNo字段
          bankName: detail.bankName || "", // 确保映射bankName字段
          depositBranch: detail.depositBranch || "", // 添加depositBranch映射
          accountName: detail.accountName || "", // 添加accountName映射
          accountPinyin: detail.accountPinyin || "", // 添加accountPinyin映射
          detailRemark: detail.remark || "", // 添加明细备注字段映射
          verifyStatus: 0,
          fileId: detail.fileId || "", // 添加文件ID字段
          fileUrl: detail.fileUrl || "", // 添加文件URL字段
          exceptionType: detail.exceptionType || "",
          exceptionReason: detail.exceptionReason || "",
          errorCorrection: detail.errorCorrection || "0", // 添加信息错误是否修改字段
          feePayertype: detail.feePayertype || "", // 添加费用支付方类型字段
        }));

        // 构建表单数据
        detailDrawer.form = {
          id: workorder.id,
          companyId: workorder.transportId,
          companyName: workorder.transportName,
          createrName: workorder.createrName,
          remark: workorder.remark,
          createTime: workorder.createTime,
          details: processedDetails,
          transferType: "public",
          workorderStatus: workorder.workorderStatus, // 添加工单状态字段
        };

        // 存储原始数据用于显示
        detailDrawer.originalData = {
          id: workorder.id,
          shippingName: workorder.shippingName,
          transportName: workorder.transportName,
          createrName: workorder.createrName,
          processName: workorder.processName || "",
          workorderStatus: workorder.workorderStatus,
          salary: workorder.salary,
          commission: workorder.commission,
          usedCredit: workorder.usedCredit || 0,
        };

        detailDrawer.loading = false;
      } else {
        ElMessage.error("获取工单详情失败");
      }
    })
    .catch((error) => {
      ElMessage.error("获取工单详情失败");
      detailDrawer.loading = false;
    });

  // 获取审批流程和日志
  fetchApprovalData(row.id);
};

// 复制工单按钮
const handleCopy = (row: any) => {
  detailDrawer.visible = true;
  detailDrawer.mode = "copy";
  detailDrawer.title = "复制工单";
  detailDrawer.loading = true;

  // 确保抽屉内容滚动到顶部
  nextTick(() => {
    const drawerContent = document.querySelector(".drawer-content");
    if (drawerContent) {
      drawerContent.scrollTop = 0;
    }
  });

  // 先请求该工单的详情
  getWorkorderDetail(row.id)
    .then((res) => {
      if (res.code === 200 && res.data) {
        const data = res.data as unknown as WorkorderDetailVO;
        const { workorder, detailList } = data;

        // 处理明细列表
        const processedDetails = detailList.map((detail: any) => ({
          grantobjectId: detail.grantobjectId,
          grantobjectName: detail.grantobjectName,
          certType: detail.certType || "",
          certNo: detail.certNo || "",
          job: detail.job || "",
          grantAmount: detail.grantAmount || 0,
          relatedAcountid: detail.relatedAcountid,
          grantFee: detail.grantFee,
          detailCommission: detail.detailCommission,
          adjustAmount: detail.adjustAmount,
          grantStatus: "0",
          grantType: detail.grantType,
          remark: detail.remark || "",
          ship: detail.ship || "",
          swiftCode: detail.swiftCode || "",
          accountNo: detail.accountNo || "", // 确保映射accountNo字段
          bankName: detail.bankName || "", // 确保映射bankName字段
          depositBranch: detail.depositBranch || "", // 确保映射depositBranch字段
          accountName: detail.accountName || "", // 添加accountName映射
          accountPinyin: detail.accountPinyin || "", // 添加accountPinyin映射
          detailRemark: detail.remark || "", // 确保detailRemark字段存在
          fileUrl: detail.fileUrl || "", // 添加文件URL字段
        }));

        // 构建表单数据
        detailDrawer.form = {
          companyId: workorder.transportId,
          companyName: workorder.transportName,
          createrName: userStore.nickname, // 使用当前用户昵称
          remark: "",
          createTime: formatDateToString(new Date()),
          details: processedDetails,
          transferType: "public",
          workorderStatus: "0", // 添加默认工单状态
        };

        detailDrawer.modified = false;
        watchFormChanges();
      } else {
        ElMessage.error("获取工单详情失败");
        detailDrawer.visible = false;
      }
      fetchApprovalData(row.id);
      detailDrawer.loading = false;
    })
    .catch((error) => {
      ElMessage.error("获取工单详情失败");
      detailDrawer.visible = false;
      detailDrawer.loading = false;
    });
};

// 修改新建工单指令处理函数，使用格式化的日期
const handleCreateCommand = (command: string) => {
  // 重置所有相关数据
  detailDrawer.visible = true;
  detailDrawer.mode = "edit";
  detailDrawer.type = command;
  detailDrawer.loading = false;
  detailDrawer.modified = false;

  // 清空审批流程和日志数据
  stepsData.value = [];
  stepsActive.value = 0;
  approvalLogData.value = [];

  // 清空原始数据
  detailDrawer.originalData = {
    id: "",
    shippingName: "",
    transportName: "",
    createrName: "",
    processName: "",
    workorderStatus: "0",
    salary: 0,
    commission: 0,
    usedCredit: 0,
  };

  // 初始化表单数据
  detailDrawer.form = {
    id: undefined,
    companyId: "",
    companyName: "",
    createrName: userStore.nickname,
    remark: "",
    createTime: formatDateToString(new Date()),
    details: [],
    transferType: command,
    workorderStatus: "0",
  };

  // 根据不同的转账类型设置不同的标题
  if (command === "single") {
    detailDrawer.title = "单笔转账";
  } else if (command === "batch") {
    detailDrawer.title = "批量转账";
  } else if (command === "public") {
    detailDrawer.title = "对公转账";
  }

  watchFormChanges();
};

// 新添加明细初始化
const onAddDetailItem = () => {
  detailItemDialog.mode = "edit";
  detailItemDialog.data = {
    grantobjectName: "",
    certType: "",
    certNo: "",
    job: "",
    grantAmount: 0,
    accountName: "",
    accountPinyin: "",
    accountNo: "",
    bankName: "",
    depositBranch: "",
    grantType: "",
    swiftCode: "",
    detailRemark: "",
    ship: "",
    delFlag: "0", // 添加delFlag字段，默认为未删除
    verifyStatus: 0, // 添加verifyStatus字段，默认为未核对
    fileUrl: "", // 添加fileUrl字段
  };
  detailItemDialog.index = -1;
  detailItemDialog.showId = false;
  detailItemDialog.visible = true;
  detailItemDialog.loading = true;
  detailItemDialog.title = "添加明细";

  // 确保抽屉内容滚动到顶部
  nextTick(() => {
    const drawerContent = document.querySelector(".detail-form-container");
    if (drawerContent) {
      drawerContent.scrollTop = 0;
    }
  });
};

// 查看明细项
const onViewDetailItem = (row: any) => {
  // 获取完整的明细数据，确保所有字段都被传递
  const detailIndex = detailDrawer.form.details.findIndex(
    (item) => item.id === row.id,
  );

  if (detailIndex !== -1) {
    // 使用完整的明细数据
    detailItemDialog.data = { ...detailDrawer.form.details[detailIndex] };
  } else {
    // 如果找不到详细信息，使用当前行的数据
    detailItemDialog.data = { ...row };
  }

  detailItemDialog.mode = "view";
  detailItemDialog.showId = true; // 显示明细时显示ID
  detailItemDialog.visible = true;
  detailItemDialog.title = "查看明细";

  // 确保抽屉内容滚动到顶部
  nextTick(() => {
    const drawerContent = document.querySelector(".detail-form-container");
    if (drawerContent) {
      drawerContent.scrollTop = 0;
    }
  });
};

// 编辑明细项
const onEditDetailItem = (row: any) => {
  // 确保从processedDetails字段获取完整的数据
  const fullDetailData = { ...row };
  // 使用引用相等性来确保找到的是同一个对象实例，而不仅仅是相同ID的对象
  const detailIndex = detailDrawer.form.details.findIndex(
    (item) => item === row,
  );

  // 如果通过引用相等性没找到，再尝试通过ID查找
  const fallbackIndex =
    detailIndex === -1
      ? detailDrawer.form.details.findIndex((item) => item.id === row.id)
      : detailIndex;

  // 从detailDrawer.form.details中查找对应的明细数据以获取完整信息
  const detailInOriginal = detailDrawer.form.details[fallbackIndex];

  if (detailInOriginal) {
    // 确保所有必要字段都被复制，使用空字符串作为默认值
    fullDetailData.certType = detailInOriginal.certType || "";
    fullDetailData.certNo = detailInOriginal.certNo || "";
    fullDetailData.job = detailInOriginal.job || "";
    fullDetailData.accountNo = detailInOriginal.accountNo || "";
    fullDetailData.bankName = detailInOriginal.bankName || "";
    fullDetailData.depositBranch = detailInOriginal.depositBranch || "";
    fullDetailData.accountName = detailInOriginal.accountName || "";
    fullDetailData.accountPinyin = detailInOriginal.accountPinyin || "";
    fullDetailData.grantType = detailInOriginal.grantType || "";
    fullDetailData.swiftCode = detailInOriginal.swiftCode || "";
    // 修复备注字段映射问题：优先使用detailRemark字段，如果没有则使用remark字段
    fullDetailData.detailRemark =
      detailInOriginal.detailRemark || detailInOriginal.remark || "";
    fullDetailData.ship = detailInOriginal.ship || "";
  }

  detailItemDialog.data = fullDetailData;
  detailItemDialog.mode = "edit";
  detailItemDialog.visible = true;
  // 保存找到的索引，确保编辑的是正确的项
  detailItemDialog.index = fallbackIndex;
  detailItemDialog.title = "编辑明细";
  // 已保存的记录才显示ID
  detailItemDialog.showId = !!row.id;

  // 确保抽屉内容滚动到顶部
  nextTick(() => {
    const drawerContent = document.querySelector(".detail-form-container");
    if (drawerContent) {
      drawerContent.scrollTop = 0;
    }
  });
};

// 删除明细项
const onDeleteDetailItem = (row: any) => {
  ElMessageBox.confirm("确定要删除该明细吗？", "提示", {
    type: "warning",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(() => {
    if (row.id) {
      // 如果有ID，说明是已经保存到数据库的记录，需要调用API删除
      delWorkorderDetail(row.id)
        .then((res) => {
          if (res.code === 200) {
            // 成功从数据库删除后，从前端列表中移除
            const index = detailDrawer.form.details.findIndex(
              (d: any) => d.id === row.id,
            );
            if (index !== -1) {
              detailDrawer.form.details.splice(index, 1);
            }
            ElMessage.success("删除成功");
            detailDrawer.modified = true; // 标记为已修改
          } else {
            ElMessage.error(res.msg || "删除失败");
          }
        })
        .catch(() => {
          ElMessage.error("删除失败");
        });
    } else {
      // 如果没有ID，说明是新添加的未保存记录，直接从列表中删除
      const index = detailDrawer.form.details.findIndex((d: any) => d === row);
      if (index !== -1) {
        detailDrawer.form.details.splice(index, 1);
      }
      ElMessage.success("删除成功");
      detailDrawer.modified = true; // 标记为已修改
    }
  });
};

// 添加检查证件号是否重复的函数
const checkDuplicateCertNo = (details: any[], currentItem: any = null) => {
  // 创建证件号映射，用于检查重复
  const certNoMap = new Map();
  const duplicates: string[] = [];

  details.forEach((item) => {
    // 如果是正在编辑的项，跳过
    if (currentItem && item.id === currentItem.id) {
      return;
    }

    // 如果certNo存在且不为空
    if (item.certNo) {
      if (certNoMap.has(item.certNo)) {
        duplicates.push(item.certNo);
      } else {
        certNoMap.set(item.certNo, true);
      }
    }
  });

  return duplicates;
};

// 保存明细项 - 添加检查证件号重复的逻辑
const onSaveDetailItem = () => {
  // 检查证件号是否为空
  if (!detailItemDialog.data.certNo) {
    ElMessage.warning("证件号码不能为空");
    return;
  }

  // 编辑现有明细和新增明细时使用不同的检查逻辑
  let duplicates = [];
  if (detailItemDialog.index > -1) {
    // 编辑现有明细时，从列表中排除当前编辑项再检查
    const tempDetails = [...detailDrawer.form.details];
    tempDetails.splice(detailItemDialog.index, 1);
    duplicates = checkDuplicateCertNo([...tempDetails, detailItemDialog.data]);
  } else {
    // 新增明细时，检查所有明细加上新明细
    duplicates = checkDuplicateCertNo([
      ...detailDrawer.form.details,
      detailItemDialog.data,
    ]);
  }

  if (duplicates.length > 0) {
    ElMessage.warning(`发现重复的证件号码: ${duplicates.join(", ")}`);
    // 继续保存，只提示不阻止
  }

  if (detailItemDialog.index > -1) {
    detailDrawer.form.details[detailItemDialog.index] = {
      ...detailItemDialog.data,
      delFlag: detailItemDialog.data.delFlag || "0", // 保持原有的delFlag，如果没有则默认为未删除
      remark: detailItemDialog.data.detailRemark || "", // 确保备注字段正确保存
    };
  } else {
    const newItem = {
      ...detailItemDialog.data,
      delFlag: "0", // 新添加的明细默认为未删除
      remark: detailItemDialog.data.detailRemark || "", // 确保备注字段正确保存
    };
    detailDrawer.form.details.push(newItem);
  }
  ElMessage.success("保存成功");
  detailItemDialog.visible = false;
  resetDetailItemDialog(); // 重置对话框数据
};

// 保存异常明细项 - 添加比较逻辑确保数据有变化
const onSaveExceptionDetail = async () => {
  // 检查证件号是否为空
  if (!detailItemDialog.data.certNo) {
    ElMessage.warning("证件号码不能为空");
    return;
  }

  // 检查必填字段
  if (
    !detailItemDialog.data.grantobjectName ||
    !detailItemDialog.data.accountNo ||
    !detailItemDialog.data.bankName ||
    !detailItemDialog.data.accountName
  ) {
    ElMessage.warning("请填写完整的船员姓名、账号、开户行和持卡人姓名信息");
    return;
  }

  try {
    // 保存当前编辑的明细数据
    const currentDetailData = { ...detailItemDialog.data };

    // 获取原始明细数据
    const originalDetail = detailDrawer.form.details.find(
      (item) => item.id === currentDetailData.id,
    );

    if (!originalDetail) {
      ElMessage.warning("找不到原始明细数据");
      return;
    }

    // 比较关键字段是否有变化
    const hasChanged =
      originalDetail.grantobjectName !== currentDetailData.grantobjectName ||
      originalDetail.certNo !== currentDetailData.certNo ||
      originalDetail.job !== currentDetailData.job ||
      originalDetail.grantAmount !== currentDetailData.grantAmount ||
      originalDetail.accountName !== currentDetailData.accountName ||
      originalDetail.accountPinyin !== currentDetailData.accountPinyin ||
      originalDetail.accountNo !== currentDetailData.accountNo ||
      originalDetail.bankName !== currentDetailData.bankName ||
      originalDetail.swiftCode !== currentDetailData.swiftCode ||
      (originalDetail.detailRemark || originalDetail.remark) !==
        currentDetailData.detailRemark ||
      originalDetail.ship !== currentDetailData.ship;

    if (!hasChanged) {
      ElMessage.warning("明细信息未发生变化，请修改后再提交");
      return;
    }

    // 设置errorCorrection字段为"1"(已修改)
    currentDetailData.errorCorrection = "1";

    // 发送更新异常明细的请求
    const res = await updateExceptionDetail(currentDetailData);

    if (res.code === 200) {
      // 更新本地数据
      detailDrawer.form.details[detailItemDialog.index] = {
        ...currentDetailData,
        delFlag: currentDetailData.delFlag || "0",
        remark: currentDetailData.detailRemark || "",
        errorCorrection: "1", // 确保更新errorCorrection字段
      };

      ElMessage.success("异常明细更新成功");
      detailItemDialog.visible = false;
      resetDetailItemDialog();

      // 重新显示异常处理弹框
      if (exceptionDialog.currentDetail) {
        exceptionDialog.visible = true;
      }

      // 刷新工单列表
      fetchWorkorderList();
    } else {
      ElMessage.error(res.msg || "更新失败");
    }
  } catch (error) {
    ElMessage.error("更新失败");
  }
};

// 计算工资总额
const calculateTotalSalary = (details: any[]): number => {
  if (!details || details.length === 0) return 0;

  // 将所有明细的工资累加，并保留两位小数
  const total = details.reduce((sum, item) => {
    // 确保grantAmount是数字
    const salary = parseFloat(item.grantAmount) || 0;
    return sum + salary;
  }, 0);

  // 格式化为保留2位小数
  return parseFloat(total.toFixed(2));
};

// 计算手续费总额
const calculateTotalFee = (details: any[]): number => {
  if (!details || details.length === 0) return 0;

  // 将所有明细的手续费累加，并保留两位小数
  const total = details.reduce((sum, item) => {
    // 确保detailCommission是数字
    const fee = parseFloat(item.detailCommission) || 0;
    return sum + fee;
  }, 0);
  // 格式化为保留2位小数
  return parseFloat(total.toFixed(2));
};

// 修改提交工单函数，添加航运公司ID和船舶公司ID
const onSubmitDrawer = async () => {
  if (
    detailDrawer.form.companyId === "" ||
    detailDrawer.form.companyId === undefined
  ) {
    ElMessage.warning("请选择服务单位");
    return;
  }
  if (
    detailDrawer.form.companyId === "" ||
    detailDrawer.form.companyId === undefined
  ) {
    ElMessage.warning("请选择服务单位");
    return;
  }
  if (detailDrawer.form.details.length === 0) {
    ElMessage.warning("请至少添加一条明细记录");
    return;
  }

  // 开启提交加载状态
  submitLoading.value = true;

  try {
    // 检查是否有重复的证件号
    const duplicates = checkDuplicateCertNo(detailDrawer.form.details);
    if (duplicates.length > 0) {
      //ElMessage.warning(`发现重复的证件号码: ${duplicates.join(", ")}`);
      // 提示后继续提交，不阻止
    }

    // 确保日期格式正确
    const formattedCreateTime = formatDateToString(detailDrawer.form.createTime);
    // 计算工资总额
    const totalSalary = calculateTotalSalary(detailDrawer.form.details);
    // 设置工单状态为2（船舶公司内部审批中）
    const submitData: any = {
      ...detailDrawer.form,
      createTime: formattedCreateTime,
      workorderStatus: "2",
      salary: totalSalary, // 添加工资总额
      transportId: detailDrawer.form.companyId, // 航运公司ID是用户选择的服务企业ID
      shippingId: userStore.companyId, // 船舶公司ID是当前用户的公司ID
    };

    // 如果是编辑模式，添加工单ID
    if (detailDrawer.mode === "edit" && detailDrawer.originalData?.id) {
      submitData.id = detailDrawer.originalData.id;
    }

    const res = await submitWorkorder(submitData);
    if (res.code === 200) {
      ElMessage.success("提交成功");
      detailDrawer.visible = false;
      fetchWorkorderList(); // 刷新列表
      detailDrawer.modified = false; // 重置修改状态
    } else {
      ElMessage.error(res.msg || "提交失败");
    }
  } catch (error) {
    ElMessage.error("提交失败，请稍后重试");
  } finally {
    // 关闭提交加载状态
    submitLoading.value = false;
  }
};

// 修改暂存工单函数，添加航运公司ID和船舶公司ID
const onSaveDrawer = async () => {
  if (
    detailDrawer.form.companyId === "" ||
    detailDrawer.form.companyId === undefined
  ) {
    ElMessage.warning("请选择服务单位");
    return;
  }

  // 开启暂存加载状态
  saveLoading.value = true;

  try {
    // 检查是否有重复的证件号
    const duplicates = checkDuplicateCertNo(detailDrawer.form.details);
    if (duplicates.length > 0) {
      //ElMessage.warning(`发现重复的证件号码: ${duplicates.join(", ")}`);
      // 提示后继续保存，不阻止
    }
    // 确保日期格式正确
    const formattedCreateTime = formatDateToString(detailDrawer.form.createTime);
    // 计算工资总额
    const totalSalary = calculateTotalSalary(detailDrawer.form.details);
    // 设置工单状态为0（待提交）
    const saveData: any = {
      ...detailDrawer.form,
      createTime: formattedCreateTime,
      workorderStatus: "0",
      salary: totalSalary, // 添加工资总额
      transportId: detailDrawer.form.companyId, // 航运公司ID是用户选择的服务企业ID
      shippingId: userStore.companyId, // 船舶公司ID是当前用户的公司ID
    };

    // 如果是编辑模式，添加工单ID
    if (detailDrawer.mode === "edit" && detailDrawer.originalData?.id) {
      //saveData.id = detailDrawer.originalData.id;
    }
    const res = await tempWorkorder(saveData);
    if (res.code === 200) {
      ElMessage.success("暂存成功");
      detailDrawer.visible = false;
      fetchWorkorderList(); // 刷新列表
      detailDrawer.modified = false; // 重置修改状态
    } else {
      ElMessage.error(res.msg || "暂存失败");
    }
  } catch (error) {
    ElMessage.error("暂存失败，请稍后重试");
  } finally {
    // 关闭暂存加载状态
    saveLoading.value = false;
  }
};

// 办理工单按钮
const handleProcess = (row: any) => {
  detailDrawer.visible = true;
  detailDrawer.mode = "process";
  detailDrawer.title = "办理工单";

  // 重置选择的航运公司ID
  selectedTransportId.value = "";

  // 如果是经销商公司，获取航运公司列表
  if (userStore.companyType === '4') {
    fetchTransportCompanyList();
  }

  fetchWorkorderDetail(row.id);
};

// 编辑工单按钮
const handleEdit = (row: any) => {
  detailDrawer.visible = true;
  detailDrawer.mode = "edit";
  detailDrawer.title = "编辑工单";
  detailDrawer.loading = true;

  // 确保抽屉内容滚动到顶部
  nextTick(() => {
    const drawerContent = document.querySelector(".drawer-content");
    if (drawerContent) {
      drawerContent.scrollTop = 0;
    }
  });

  // 获取工单详情
  getWorkorderDetail(row.id)
    .then((res) => {
      if (res.code === 200 && res.data) {
        const data = res.data as unknown as WorkorderDetailVO;
        const { workorder, detailList } = data;
        // 处理明细列表
        const processedDetails = detailList.map((detail: any) => ({
          id: detail.id,
          grantobjectId: detail.grantobjectId,
          grantobjectName: detail.grantobjectName,
          certType: detail.certType || "", // 添加证件类型
          certNo: detail.certNo || "", // 添加证件号码
          job: detail.job || "", // 添加职务
          grantAmount: detail.grantAmount || 0,
          relatedAcountid: detail.relatedAcountid,
          grantFee: detail.grantFee,
          detailCommission: detail.detailCommission,
          adjustAmount: detail.adjustAmount,
          grantStatus: detail.grantStatus || "0",
          grantType: detail.grantType,
          remark: detail.remark || "",
          ship: detail.ship || "",
          swiftCode: detail.swiftCode || "",
          accountNo: detail.accountNo || "", // 确保映射accountNo字段
          bankName: detail.bankName || "", // 确保映射bankName字段
          depositBranch: detail.depositBranch || "", // 确保映射depositBranch字段
          accountName: detail.accountName || "", // 添加accountName映射
          accountPinyin: detail.accountPinyin || "", // 添加accountPinyin映射
          detailRemark: detail.remark || "", // 添加明细备注字段映射
          fileUrl: detail.fileUrl || "", // 添加文件URL字段
        }));

        // 构建表单数据
        detailDrawer.form = {
          id: workorder.id,
          companyId: workorder.transportId,
          companyName: workorder.transportName,
          createrName: workorder.createrName, // 使用原工单的创建人
          remark: workorder.remark,
          createTime: workorder.createTime,
          details: processedDetails,
          transferType: "public",
          workorderStatus: workorder.workorderStatus, // 添加工单状态
        };

        // 存储原始数据用于比较
        detailDrawer.originalData = { ...workorder };
        detailDrawer.modified = false;
        watchFormChanges();
      } else {
        ElMessage.error("获取工单详情失败");
        detailDrawer.visible = false;
      }
      fetchApprovalData(row.id);
      detailDrawer.loading = false;
    })
    .catch((error) => {
      ElMessage.error("获取工单详情失败");
      detailDrawer.visible = false;
      detailDrawer.loading = false;
    });
};

// 按钮组 - 根据公司类型控制新建按钮显示
const showCreateButton = computed(() => {
  // 船舶公司(1)才能新建工单
  return userStore.companyType === "1";
});

// 操作列中的操作按钮
const showOperations = computed(() => {
  return {
    // 船舶公司能够看到详情和复制按钮
    isShipping: userStore.companyType === "1",
    // 航运公司能够看到办理按钮
    isTransport: userStore.companyType === "2",
    // 经销商公司能够看到反馈按钮
    isDealer: userStore.companyType === "4",
  };
});

// 修改服务单位改变事件处理
const onCompanyChange = (value: string) => {
  // 从companyList中找到对应的公司信息
  const company = companyList.value.find((item) => item.id === value);
  if (company) {
    detailDrawer.form.companyName = company.displayName;
  } else {
    detailDrawer.form.companyId = "";
    detailDrawer.form.companyName = "";
  }
  detailDrawer.modified = true;
};

// 修改文件引用
const uploadRef = ref(null);
const fileInputRef = ref(null);

// 异常处理上传相关变量
const exceptionHeaders = globalHeaders ? globalHeaders() : {};

// 定义文件上传地址
const uploadAction =
  import.meta.env.VITE_APP_BASE_API +
  "/salary/workorderDetail/exception/upload";

// 用户导入参数
const upload = reactive({
  // 是否显示弹出层（工单导入）
  open: false,
  // 弹出层标题（工单导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: globalHeaders ? globalHeaders() : {},
  // 上传的地址
  url:
    import.meta.env.VITE_APP_BASE_API +
    "/salary/workorderDetail/importTemplate",
});

// 修改处理导入按钮点击函数
const handleImportTemplate = () => {
  upload.title = "导入明细";
  upload.open = true;
};

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
  importStatus.show = true;
  importStatus.type = "info";
  importStatus.message = "文件上传中，请稍候...";
};

/** 文件上传成功处理 */
const handleFileSuccess = (response: any) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.clearFiles();
  if (response.code === 200) {
    // 获取导入的明细列表，支持两种数据结构
    const importDetailList = response.data.detailList || response.data;

    if (!importDetailList || !Array.isArray(importDetailList)) {
      ElMessage.error("导入数据格式错误");
      importStatus.type = "error";
      importStatus.message = "导入数据格式错误";
      return;
    }

    // 检查导入数据是否有重复的证件号
    const allDetails = [...detailDrawer.form.details, ...importDetailList];
    const duplicates = checkDuplicateCertNo(allDetails);
    if (duplicates.length > 0) {
      ElMessage.warning(`发现重复的证件号码: ${duplicates.join(", ")}`);
      // 继续导入，只提示不阻止
    }

    // 将解析结果添加到工单明细列表中
    detailDrawer.form.details = [
      ...detailDrawer.form.details,
      ...importDetailList.map((item: any) => ({
        ...item,
        grantStatus: "2", // 默认设置发放状态为发放中
        verifyStatus: 0, // 默认设置核对状态为未核对
        fileUrl: item.fileUrl || "", // 确保fileUrl字段存在
      })),
    ];
    detailDrawer.modified = true;

    // 显示成功状态
    importStatus.type = "success";
    importStatus.message = `${response.msg || "解析成功"}, 已添加 ${importDetailList.length} 条明细`;

    ElMessageBox.alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        "</div>",
      "导入结果",
      {
        dangerouslyUseHTMLString: true,
      },
    );
  } else {
    // 显示错误状态
    importStatus.type = "error";
    importStatus.message = response.msg || "文件解析失败";
    ElMessage.error(response.msg || "导入失败");
  }

  // 3秒后自动隐藏提示
  setTimeout(() => {
    importStatus.show = false;
  }, 3000);
};

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

// 异常上传成功处理
const handleExceptionUploadSuccess = (response: any) => {
  if (response.code === 200) {
    // 处理返回结果，从返回数据中获取文件ID和URL
    const fileId = response.data;
    exceptionDialog.form.attachmentUrl = fileId; // 保存文件ID

    // 如果有当前处理的明细，也为其设置fileId
    if (exceptionDialog.currentDetail) {
      exceptionDialog.currentDetail.fileId = fileId;

      // 同时更新detailDrawer中对应明细的fileId
      if (detailDrawer.form.details && detailDrawer.form.details.length > 0) {
        const detailIndex = detailDrawer.form.details.findIndex(
          (item) => item.id === exceptionDialog.currentDetail.id,
        );
        if (detailIndex !== -1) {
          detailDrawer.form.details[detailIndex].fileId = fileId;
        }
      }
    }

    ElMessage.success("文件上传成功");
  } else {
    ElMessage.error(response.msg || "上传失败");
  }
};

// 异常上传失败处理
const handleExceptionUploadError = (error: any) => {
  ElMessage.error("文件上传失败，请确保文件格式和大小正确");
  exceptionDialog.form.attachmentUrl = ""; // 清空文件ID
};

// 导出模板函数
const { proxy } = getCurrentInstance();
const handleExportTemplate = () => {
  proxy?.download(
    "salary/workorderDetail/exportTemplate",
    {},
    `workorder_template_${new Date().getTime()}.xlsx`,
  );
};

// 导出工单明细功能
const handleExportDetail = () => {
  if (!detailDrawer.form.details || detailDrawer.form.details.length === 0) {
    ElMessage.warning("没有可导出的明细数据");
    return;
  }

  // 使用proxy.download方法导出Excel文件
  const workorderId = detailDrawer.originalData.id;
  proxy?.download(
    "salary/workorderDetail/export",
    { workorderId }, // 将工单ID作为请求参数
    `usd_workorder_detail_${workorderId}_${new Date().getTime()}.xlsx`,
  );
};

// 处理抽屉关闭前的逻辑
const handleDrawerClose = (done: () => void) => {
  if (detailDrawer.modified && detailDrawer.mode !== "detail") {
    ElMessageBox.confirm("您有未保存的修改，是否要关闭？", "提示", {
      confirmButtonText: "关闭",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        done();
      })
      .catch(() => {
        // 取消关闭
      });
  } else {
    done();
  }
};

// 监听表单变化以设置修改标记
const watchFormChanges = () => {
  // 在真实应用中，可以添加表单的深度监听
  // 这里只是模拟，当用户操作表单元素时，设置modified为true
  nextTick(() => {
    const form = document.querySelector(".detail-form");
    if (form) {
      form.addEventListener("change", () => {
        detailDrawer.modified = true;
      });
    }
  });
};

// 获取明细表格列配置
const getDetailColumns = computed(() => {
  const baseColumns = [
    {
      prop: "grantobjectName",
      label: "船员姓名",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "grantAmount",
      label: "工资额",
      width: undefined,
      fixed: undefined,
      formatter: (row: any) => formatAmount(row.grantAmount),
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "grantFee",
      label: "交易手续费金额",
      width: undefined,
      fixed: undefined,
      formatter: (row: any) => formatFeePercent(row.grantFee),
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "accountNo",
      label: "账号",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "bankName",
      label: "开户行",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "depositBranch",
      label: "开户支行",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "swiftCode",
      label: "swiftcode",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "ship",
      label: "所在船舶",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
  ];

  // 完整列配置，包含所有可能的字段（移除了发放状态列）
  const fullColumns = [
    {
      prop: "grantobjectName",
      label: "船员姓名",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "certType",
      label: "证件类型",
      width: undefined,
      fixed: undefined,
      formatter: (row: any) => formatCertType(row.certType),
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "certNo",
      label: "证件号码",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "job",
      label: "职务",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "grantAmount",
      label: "工资额",
      width: undefined,
      fixed: undefined,
      formatter: (row: any) => formatAmount(row.grantAmount),
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "grantFee",
      label: "交易手续费金额",
      width: undefined,
      fixed: undefined,
      formatter: (row: any) => formatFeePercent(row.grantFee),
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "accountName",
      label: "持卡人姓名",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "accountPinyin",
      label: "持卡人拼音",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "accountNo",
      label: "账号",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "bankName",
      label: "开户行",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "depositBranch",
      label: "开户支行",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "grantType",
      label: "交易类型",
      width: undefined,
      fixed: undefined,
      formatter: (row: any) => formatTradeType(row.grantType),
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "swiftCode",
      label: "swiftcode",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "ship",
      label: "所在船舶",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
    {
      prop: "detailRemark",
      label: "备注",
      width: undefined,
      fixed: undefined,
      formatter: undefined,
      slot: undefined,
      cellSlot: undefined,
    },
  ];

  // 发放状态列定义
  const grantStatusColumn = {
    prop: "grantStatus",
    label: "发放状态",
    width: 100,
    fixed: undefined,
    formatter: (row: any) => {
      return formatGrantStatus(row.grantStatus);
    },
    slot: undefined,
    cellSlot: undefined,
  };

  // 在编辑、复制或查看模式下显示完整列
  if (
    detailDrawer.mode === "edit" ||
    detailDrawer.mode === "copy" ||
    detailDrawer.mode === "view"
  ) {
    const columns = [...fullColumns];

    // 在状态为7、9、10、11、12时添加发放状态列
    if (["7", "9", "10", "11", "12"].includes(detailDrawer.originalData.workorderStatus)) {
      columns.push(grantStatusColumn);
    }

    // 在编辑或复制模式下添加操作列
    if (detailDrawer.mode === "edit" || detailDrawer.mode === "copy") {
      columns.push({
        prop: "operations",
        label: "操作",
        width: 120,
        fixed: "right",
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      });
    }

    // 在查看模式下如果状态为7(待反馈)、10(反馈完毕)或11(异常待处理)，添加操作列
    if (
      detailDrawer.mode === "view" &&
      (detailDrawer.originalData.workorderStatus === "7" ||
        detailDrawer.originalData.workorderStatus === "10" ||
        detailDrawer.originalData.workorderStatus === "11" ||
        detailDrawer.originalData.workorderStatus === "12") &&
      (userStore.companyType === "1" || userStore.companyType === "2")
    ) {
      columns.push({
        prop: "operation",
        label: "操作",
        width: 100,
        fixed: "right",
        slot: true, // 使用slot以便显示操作按钮
        formatter: undefined,
        cellSlot: undefined,
      });
    }

    return columns;
  }

  // 状态为4、5、6、7、8、9、10、11、12时显示更多信息
  if (
    ["4", "5", "6", "7", "8", "9", "10", "11", "12"].includes(
      detailDrawer.originalData.workorderStatus,
    )
  ) {
    const columns = [
      {
        prop: "grantobjectName",
        label: "船员姓名",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "certType",
        label: "证件类型",
        width: undefined,
        fixed: undefined,
        formatter: (row: any) => formatCertType(row.certType),
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "certNo",
        label: "证件号码",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "job",
        label: "职务",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "grantAmount",
        label: "工资额",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "grantFee",
        label: "手续费",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "accountName",
        label: "账号名",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "accountPinyin",
        label: "持卡人拼音",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "accountNo",
        label: "账号",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "bankName",
        label: "开户行",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "depositBranch",
        label: "开户支行",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "grantType",
        label: "交易类型",
        width: undefined,
        fixed: undefined,
        formatter: (row: any) => formatTradeType(row.grantType),
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "swiftCode",
        label: "swiftcode",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "ship",
        label: "所在船舶",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "detailRemark",
        label: "备注",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: undefined,
      },
      {
        prop: "grantStatus",
        label: "发放状态",
        width: 100,
        fixed: undefined,
        formatter: (row: any) => {
          return formatGrantStatus(row.grantStatus);
        },
        slot: undefined,
        cellSlot: undefined,
      },
    ];

    // 对于工单状态为10、11、12的，添加异常类型和异常原因列
    if (["10", "11", "12"].includes(detailDrawer.originalData.workorderStatus)) {
      columns.push(
        {
          prop: "exceptionType",
          label: "异常类型",
          width: 100,
          fixed: undefined,
          formatter: (row: any) => formatExceptionType(row.exceptionType),
          slot: undefined,
          cellSlot: undefined,
        },
        {
          prop: "exceptionReason",
          label: "异常原因",
          width: 150,
          fixed: undefined,
          formatter: undefined,
          slot: undefined,
          cellSlot: undefined,
        }
      );
    }

    // 在状态为7(待反馈)或10(反馈完毕)时显示操作列
    if (
      detailDrawer.originalData.workorderStatus === "7" ||
      detailDrawer.originalData.workorderStatus === "10"
    ) {
      columns.push({
        prop: "operation",
        label: "操作",
        width: 100,
        fixed: "right",
        slot: true, // This slot is for the "处理异常" button
        formatter: undefined,
        cellSlot: undefined,
      });
    }

    return columns;
  }

  // 状态为3（待确认）时显示核对状态
  if (
    detailDrawer.originalData.workorderStatus === "3" &&
    userStore.companyType === "2"
  ) {
    return [
      ...baseColumns, // 复用基础列
      {
        prop: "verifyStatus",
        label: "核对状态",
        width: undefined,
        fixed: undefined,
        formatter: undefined,
        slot: undefined,
        cellSlot: "verifyCell", // Use this slot for custom rendering
      },
    ];
  }
  return baseColumns;
});

// 是否有未核对的明细
const hasUnverifiedDetails = computed(() => {
  if (detailDrawer.originalData.workorderStatus !== "3") return false;
  return detailDrawer.form.details.some(
    (detail: any) => detail.verifyStatus !== 1,
  );
});

// 计算表格中所有明细的工资总额
const totalAmount = computed(() => {
  return calculateTotalSalary(detailDrawer.form.details);
});

// 计算表格中所有明细的手续费总额
const totalFee = computed(() => {
  return calculateTotalFee(detailDrawer.form.details);
});

// 是否显示手续费信息（根据工单状态）
const showFeeInfo = computed(() => {
  const status = detailDrawer.originalData.workorderStatus;
  return ["4", "5", "6", "7", "8", "9", "10", "11", "12"].includes(status);
});

// 添加核对状态
const handleVerify = async (row: any) => {
  const index = detailDrawer.form.details.findIndex(
    (item) => item.id === row.id,
  );
  if (index !== -1) {
    // 检查交易类型是否为空
    if (!row.grantType) {
      ElMessage.warning("请选择交易类型");
      return;
    }

    try {
      // 调用API更新核对状态
      const res = await updateVerifyStatus({
        id: row.id,
        verifyStatus: 1,
        grantType: row.grantType,
      });

      if (res.code === 200) {
        // 更新前端数据
        detailDrawer.form.details = detailDrawer.form.details.map(
          (item, idx) => {
            if (idx === index) {
              return {
                ...item,
                verifyStatus: 1,
                grantType: row.grantType, // 确保交易类型被正确保存到明细对象中
              };
            }
            return item;
          },
        );
        ElMessage.success("核对成功");
        detailItemDialog.visible = false;
      } else {
        ElMessage.error(res.msg || "核对失败");
      }
    } catch (error) {
      // 使用后端返回的错误信息
      if (error.response && error.response.data && error.response.data.msg) {
        ElMessage.error(error.response.data.msg);
      } else {
        ElMessage.error("核对状态更新失败");
      }
    }
  }
};

// 根据证件号码查询用户信息
const queryCertNoInfo = async () => {
  const certNo = detailItemDialog.data.certNo;
  if (!certNo) {
    return;
  }
  try {
    const res = await getUserplus_infoByCertNo(certNo, "usd");

    if (res.code === 200 && res.data) {
      const userInfo = res.data;
      // 填充船员基本信息
      detailItemDialog.data.grantobjectName = userInfo.userName || ""; //船员姓名
      detailItemDialog.data.certType = userInfo.certType || ""; //证件类型
      detailItemDialog.data.job = userInfo.job || ""; //职务
      detailItemDialog.data.grantobjectId = userInfo.userId || ""; //船员ID

      // 处理多账户情况
      if (userInfo.accounts && Array.isArray(userInfo.accounts)) {
        detailItemDialog.accounts = userInfo.accounts;

        if (userInfo.accounts.length === 1) {
          // 只有一个账户，直接使用
          const account = userInfo.accounts[0];
          fillAccountInfo(account);
          detailItemDialog.showAccountSelect = false;
        } else if (userInfo.accounts.length > 1) {
          // 多个账户，显示选择下拉框

          detailItemDialog.showAccountSelect = true;
          detailItemDialog.data.selectedAccountId = ""; // 清空选择
          // 确保响应式更新
          nextTick(() => {});
          // 不自动填充账户信息，等待用户选择
        } else {
          // 没有账户
          detailItemDialog.showAccountSelect = false;
          detailItemDialog.accounts = [];
          ElMessage.warning("未找到关联的银行账户信息");
        }

        if (userInfo.accounts.length > 0) {
          ElMessage.success(
            `已自动获取用户信息，找到 ${userInfo.accounts.length} 个账户`,
          );
        }
      } else {
        // 没有账户信息
        detailItemDialog.showAccountSelect = false;
        detailItemDialog.accounts = [];
        ElMessage.warning("请填写银行账户信息");
      }
      if (userInfo.job === undefined || userInfo.job === "") {
        ElMessage.warning("请填写职务");
      }
    }
  } catch (error) {
    detailItemDialog.showAccountSelect = false;
    detailItemDialog.accounts = [];
  }
};

// 账户选择变更处理
const handleAccountChange = (accountId: string) => {
  if (!accountId) return;
  const selectedAccount = detailItemDialog.accounts.find(
    (account) => account.accountId === accountId,
  );
  if (selectedAccount) {
    fillAccountInfo(selectedAccount);
  }
};

// 填充账户信息到表单
const fillAccountInfo = (account: any) => {
  detailItemDialog.data.accountName = account.accountName || ""; //账号名
  detailItemDialog.data.accountPinyin = account.accountPinyin || ""; //持卡人拼音
  detailItemDialog.data.accountNo = account.accountNo || ""; //账号
  detailItemDialog.data.bankName = account.bankName || ""; //开户行
  detailItemDialog.data.depositBranch = account.depositBranch || ""; //开户支行
  detailItemDialog.data.swiftCode = account.swiftCode || ""; //swiftcode
  detailItemDialog.data.relatedAcountid = account.accountId || ""; //账户ID
};

// 提交办理
const handleSubmitProcess = async () => {
  // 在状态为3时检查核对状态
  if (detailDrawer.originalData.workorderStatus === "3") {
    const unverifiedDetails = detailDrawer.form.details.filter(
      (detail) => detail.verifyStatus !== 1,
    );
    if (unverifiedDetails.length > 0) {
      ElMessage.warning("还有未核对的明细，请先完成核对");
      return;
    }
  }

  processLoading.value = true;
  try {
    // 根据不同状态执行不同的操作
    if (detailDrawer.originalData.workorderStatus === "6") {
      // 状态为6时，执行操作完成的逻辑
      const res = await completeUsdWorkorder({
        workorderId: detailDrawer.originalData.id,
        remark: detailDrawer.form.remark,
      });
      if (res.code === 200) {
        await ElMessage.success("操作完成");
      } else {
        await ElMessage.error(res.msg || "操作完成失败");
        return;
      }
    } else {
      // 其他状态执行提交办理逻辑
      const detailsData = detailDrawer.form.details
        .filter((detail) => detail.verifyStatus === 1)
        .map((detail) => {
          // 确保每个字段都有值
          return {
            detailId: detail.id,
            // 如果明细中没有获取到交易类型，则读取detailItemDialog中的交易类型
            grantType:
              detail.grantType || detailItemDialog.data.grantType || "",
          };
        });
      // 构建请求数据
      const requestData: any = {
        workorderId: detailDrawer.originalData.id,
        remark: detailDrawer.form.remark,
        details: detailsData, // 将明细ID和交易类型一起传递给后端
      };

      // 如果是经销商公司，添加选择的航运公司ID
      if (userStore.companyType === '4' && selectedTransportId.value) {
        requestData.transportId = selectedTransportId.value;
      }

      const res = await processWorkorder(requestData);
      if (res.code === 200) {
        await ElMessage.success("办理成功");
      } else {
        await ElMessage.error(res.msg || "办理失败");
        return;
      }
    }
    detailDrawer.visible = false;
    fetchWorkorderList();
  } catch (error) {
    ElMessage.error(
      detailDrawer.originalData.workorderStatus === "6"
        ? "操作失败"
        : "办理失败",
    );
  } finally {
    processLoading.value = false;
  }
};

// 修改驳回处理函数，添加备注
const handleReject = async () => {
  try {
    // 直接弹出输入框让用户输入驳回原因
    const { value: rejectReason } = await ElMessageBox.prompt("请输入驳回原因", "驳回工单", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      inputPlaceholder: "请输入驳回原因",
      inputValidator: (value) => {
        if (!value.trim()) {
          return "驳回原因不能为空";
        }
        return true;
      },
    });

    // 设置加载状态
    rejectLoading.value = true;

    // 用户输入了驳回原因，执行驳回操作
    const res = await rejectUsdWorkorder({
      workorderId: detailDrawer.originalData.id,
      rejectReason: rejectReason.trim(), // 使用用户输入的驳回原因，而不是表单的备注
    });

    if (res.code === 200) {
      ElMessage.success("驳回成功");
      detailDrawer.visible = false;
      fetchWorkorderList();
    } else {
      ElMessage.error(res.msg || "驳回失败");
    }
  } catch (error) {
    // 如果用户取消，error会是 'cancel' 字符串，或者是一个包含错误信息的对象
    if (error !== "cancel") {
      // 只有在不是用户主动取消的情况下才提示错误
      // ElMessage.error("驳回操作失败");
    }
  } finally {
    rejectLoading.value = false;
  }
};

// 添加发放结果数据
ref([
  { result: "成功", remark: "" },
  { result: "失败", remark: "金额不对" },
  { result: "失败", remark: "银行卡号不对" },
]);
// 处理异常弹窗数据
const exceptionDialog = reactive({
  visible: false,
  type: "2", // 2: 银行问题/审核, 1: 信息错误
  form: {
    workorderId: "",
    reason: "", // 异常原因
    returnFee: "0", // 退汇扣收
    refundFee: "", // 退款手续费
    deductTarget: 1, // 责任方，1表示航运公司，2表示船舶公司
    attachmentUrl: "", // 附件URL（船舶公司使用）
    failedDetails: [] as any[], // 初始化 failedDetails
    currentDetailId: "", // 添加当前处理明细ID
    feePayertype: "2", // 费用支付方类型，默认为船舶公司
    errorCorrection: "0", // 信息错误是否修改，默认为未修改
  },
  currentDetail: null as any, // 当前处理的明细
});

// 是否是船舶公司
const isShippingCompany = computed(() => userStore.companyType === "1");

// 是否可以提交
const canSubmit = computed(() => {
  // 航运公司
  if (!isShippingCompany.value) {
    // 银行审核只需要异常原因
    if (exceptionDialog.type === "2") {
      return !!exceptionDialog.form.reason;
    }
    // 信息错误需要填写所有字段
    return !!(
      exceptionDialog.form.reason &&
      exceptionDialog.form.returnFee &&
      exceptionDialog.form.refundFee &&
      exceptionDialog.form.deductTarget
    );
  }

  // 船舶公司
  if (isShippingCompany.value && exceptionDialog.type === "1") {
    // 信息错误时需要选择费用支付方类型
    return !!exceptionDialog.form.feePayertype;
  }

  // 其他情况不需要特殊条件
  return true;
});

// 编辑工单信息
const handleEditWorkorder = () => {
  exceptionDialog.visible = false;
  // 打开工单编辑抽屉
  detailDrawer.visible = true;
  detailDrawer.mode = "edit";
  detailDrawer.title = "编辑工单";
  // 获取最新的工单数据
  fetchWorkorderDetail(detailDrawer.originalData.id);
};

// 编辑明细信息
const handleEditDetail = () => {
  // 暂时关闭异常处理弹窗，但保持状态
  exceptionDialog.visible = false;

  if (exceptionDialog.currentDetail) {
    // 打开明细编辑对话框
    detailItemDialog.data = { ...exceptionDialog.currentDetail };
    detailItemDialog.mode = "edit";
    detailItemDialog.showId = true;
    detailItemDialog.index = detailDrawer.form.details.findIndex(
      (item) => item.id === exceptionDialog.currentDetail.id,
    );
    detailItemDialog.visible = true;
  } else {
    ElMessage.warning("未找到明细数据");
    // 如果没有找到明细数据，重新显示异常处理弹框
    exceptionDialog.visible = true;
  }
};

// 提交异常处理
const handleSubmitException = async () => {
  // 根据公司类型和异常类型校验
  if (!isShippingCompany.value) {
    // 航运公司
    if (!exceptionDialog.form.reason) {
      ElMessage.warning("请输入异常原因");
      return;
    }
    if (exceptionDialog.type === "1") {
      if (
        !exceptionDialog.form.returnFee ||
        !exceptionDialog.form.refundFee ||
        !exceptionDialog.form.deductTarget
      ) {
        ElMessage.warning(
          "信息错误时，退汇扣收、退款手续费和责任方均为必填项",
        );
        return;
      }
    }
  } else {
    // 船舶公司
    if (exceptionDialog.type === "1" && !exceptionDialog.form.feePayertype) {
      ElMessage.warning("请选择费用支付方类型");
      return;
    }
  }

  // 构造提交数据
  const exceptionTypeLabel =
    exceptionDialog.type === "2" ? "银行问题" : "信息错误";
  const reasonWithPrefix = `[${exceptionTypeLabel}] ${exceptionDialog.form.reason || ""}`;

  // 使用异常类型：1-信息错误，2-银行审核
  const exceptionTypeValue = exceptionDialog.type;

  const submitData: any = {
    workorderId: exceptionDialog.form.workorderId,
    type: exceptionDialog.type,
    exceptionType: exceptionTypeValue, // 添加数字类型的异常类型
    reason: reasonWithPrefix, // 使用带类型前缀的原因
    // 以下为航运公司 - 信息错误 时需要的字段
    returnFee: exceptionDialog.form.returnFee,
    refundFee: exceptionDialog.form.refundFee,
    deductTarget: exceptionDialog.form.deductTarget,
    // 传递当前处理的明细ID
    detailId:
      exceptionDialog.form.currentDetailId ||
      (exceptionDialog.currentDetail ? exceptionDialog.currentDetail.id : null),
    // 添加公司类型参数
    companyType: userStore.companyType,
    companyId: userStore.companyId,
    // 添加新字段
    feePayertype: exceptionDialog.form.feePayertype,
    errorCorrection: exceptionDialog.form.errorCorrection,
  };

  // 船舶公司银行问题处理需要添加附件ID
  if (isShippingCompany.value && exceptionDialog.type === "2") {
    // 确保附件URL存在
    if (exceptionDialog.form.attachmentUrl) {
      submitData.attachmentUrl = exceptionDialog.form.attachmentUrl; // 添加上传成功后获取的文件ID
    } else {
      ElMessage.warning("请先上传处理文件");
      return;
    }
  }

  // 如果有当前处理的明细对象，添加完整的明细信息
  if (exceptionDialog.currentDetail) {
    // 确保传递完整的明细信息到后端
    submitData.detailData = exceptionDialog.currentDetail;

    // 如果是船舶公司处理信息错误，更新errorCorrection字段为"1"(已修改)
    if (isShippingCompany.value && exceptionDialog.type === "1") {
      submitData.errorCorrection = "1";

      // 如果当前明细已经被编辑过，也设置为已修改
      if (exceptionDialog.currentDetail.errorCorrection === "1") {
        submitData.errorCorrection = "1";
      }
    }
  }

  // 根据公司类型和异常类型，移除不需要的字段
  if (isShippingCompany.value) {
    delete submitData.returnFee;
    delete submitData.refundFee;
    delete submitData.deductTarget;
    if (exceptionDialog.type === "1") {
      // 船舶公司信息错误，只提交基本信息
      delete submitData.attachmentUrl;
    }

    // 船舶公司处理异常时，确保fileId字段被传递
    if (exceptionDialog.currentDetail && exceptionDialog.currentDetail.fileId) {
      submitData.fileId = exceptionDialog.currentDetail.fileId;
    }
  } else {
    // 航运公司
    delete submitData.attachmentUrl;
    if (exceptionDialog.type === "2") {
      // 航运公司银行问题，移除信息错误相关字段
      delete submitData.returnFee;
      delete submitData.refundFee;
      delete submitData.deductTarget;
    }
  }

  try {
    const res = await handleUsdWorkorderException(submitData);
    if (res.code === 200) {
      ElMessage.success("处理成功");

      // 保存当前工单ID，用于重新打开工单详情
      const workorderId = exceptionDialog.form.workorderId;

      // 关闭异常处理弹窗
      exceptionDialog.visible = false;

      // 刷新工单列表
      fetchWorkorderList();

      // 重新打开工单详情抽屉
      if (workorderId) {
        // 打开工单详情抽屉，设置为查看模式
        detailDrawer.visible = true;
        detailDrawer.mode = "view";
        detailDrawer.title = "工单详情";
        detailDrawer.loading = true;

        // 获取最新的工单详情
        fetchWorkorderDetail(workorderId);
      }
    } else {
      ElMessage.error(res.msg || "处理失败");
    }
  } catch (error) {
    ElMessage.error("处理失败");
  }
};

// 处理单条明细的异常处理函数
const handleDetailException = (row: any) => {
  // 关闭工单详情抽屉
  detailDrawer.visible = false;

  // 确保获取明细的完整字段信息
  // 从失败的明细中获取异常原因（如果有）
  const defaultReason = row.exceptionReason || "";

  // 根据异常类型设置默认的异常处理类型
  // exceptionType: 1-信息错误，2-银行审核
  const defaultType = row.exceptionType === "1" ? "1" : "2";

  // 打开异常处理弹窗
  exceptionDialog.visible = true;

  // 确保弹窗内容滚动到顶部
  nextTick(() => {
    const dialogContent = document.querySelector(".exception-form");
    if (dialogContent) {
      dialogContent.scrollTop = 0;
    }
  });
  exceptionDialog.form = {
    workorderId: detailDrawer.originalData.id,
    reason: defaultReason,
    returnFee: "",
    refundFee: "0",
    deductTarget: 1,
    attachmentUrl: "", // 附件URL初始为空，上传成功后会被设置
    failedDetails: [], // 保留这个字段以兼容类型定义
    currentDetailId: row.id, // 存储当前处理的明细ID
    feePayertype: row.feePayertype || "2", // 默认为船舶公司
    errorCorrection: row.errorCorrection || "0", // 默认为未修改
  };

  // 清除可能存在的之前的文件上传信息
  const uploadComponent = document.querySelector(
    ".upload-demo .el-upload__input",
  );
  if (uploadComponent) {
    (uploadComponent as HTMLInputElement).value = "";
  }

  // 设置当前处理的明细对象，确保所有字段都被传递
  exceptionDialog.currentDetail = row;
  // 根据明细中的异常类型设置默认类型，如果没有则默认为"2"(银行审核)
  exceptionDialog.type = defaultType;
};

// 获取审批流程和日志
const fetchApprovalData = async (id: string) => {
  const resApprovalProcessData = await getApprovalProcess(id);
  const responseApprovalProcessData = resApprovalProcessData.data as any;
  stepsData.value = responseApprovalProcessData.approvalProcessList;
  stepsActive.value = responseApprovalProcessData.currentSort;

  const resExamineLogData = await getExamineLog(id, 0);
  const responseresExamineLogData = resExamineLogData.data as any;
  approvalLogData.value = responseresExamineLogData;
};
// 添加审批状态相关函数
const getApprovalStatusText = (status: string | number) => {
  const statusMap: Record<string, string> = {
    "1": "审批中",
    "2": "审批通过",
    "3": "审批不通过",
  };
  return statusMap[status] || "未知状态";
};

const getApprovalStatusType = (status: string | number) => {
  const typeMap: Record<string, string> = {
    "1": "warning",
    "2": "success",
    "3": "danger",
  };
  return typeMap[status] || "info";
};

// 添加工单状态相关函数
const getWorkorderStatusText = (status: string | number) => {
  const statusMap: Record<string, string> = {
    "0": "待提交",
    "1": "船舶公司内部审批驳回",
    "2": "船舶公司内部审批中",
    "3": "待确认",
    "4": "航运公司内部审批驳回",
    "5": "航运公司内部审批中",
    "6": "操作中",
    "7": "待反馈",
    "8": "航运公司驳回",
    "9": "处理完毕",
    "10": "反馈完毕",
    "11": "异常待处理",
    "12": "异常处理完毕",
  };
  return statusMap[status] || "未知状态";
};

const getWorkorderStatusType = (status: string | number) => {
  const typeMap: Record<string, string> = {
    "0": "info",
    "1": "danger",
    "2": "warning",
    "3": "warning",
    "4": "danger",
    "5": "warning",
    "6": "warning",
    "7": "warning",
    "8": "danger",
    "9": "success",
    "10": "success",
    "11": "danger",
    "12": "success",
  };
  return typeMap[status] || "info";
};

const handleApproval = (row: any) => {
  detailDrawer.visible = true;
  detailDrawer.mode = "approval";
  detailDrawer.title = "审批信息";
  detailDrawer.loading = true;

  // 获取工单详情
  getWorkorderDetail(row.id)
    .then((res) => {
      if (res.code === 200 && res.data) {
        const data = res.data as unknown as WorkorderDetailVO;
        const { workorder, detailList } = data;

        // 处理明细列表
        const processedDetails = detailList.map((detail: any) => ({
          id: detail.id,
          grantobjectId: detail.grantobjectId,
          grantobjectName: detail.grantobjectName,
          certType: detail.certType || "", // 添加证件类型
          certNo: detail.certNo || "", // 添加证件号码
          job: detail.job || "", // 添加职务
          grantAmount: detail.grantAmount || 0,
          relatedAcountid: detail.relatedAcountid,
          grantFee: detail.grantFee,
          detailCommission: detail.detailCommission,
          adjustAmount: detail.adjustAmount,
          grantStatus: detail.grantStatus || "0",
          grantType: detail.grantType,
          remark: detail.remark || "",
          ship: detail.ship || "",
          swiftCode: detail.swiftCode || "",
          accountNo: detail.accountNo || "", // 添加账号
          bankName: detail.bankName || "", // 添加开户行
          accountName: detail.accountName || "", // 添加账号名
          accountPinyin: detail.accountPinyin || "", // 添加持卡人拼音
          detailRemark: detail.remark || "", // 添加明细备注字段
          verifyStatus: detail.verifyStatus || 0,
          fileUrl: detail.fileUrl || "", // 添加文件URL字段
        }));

        // 构建表单数据
        detailDrawer.form = {
          id: workorder.id,
          companyId: workorder.transportId,
          companyName: workorder.transportName,
          createrName: workorder.createrName,
          remark: workorder.remark,
          createTime: workorder.createTime,
          details: processedDetails,
          transferType: "public",
          workorderStatus: workorder.workorderStatus,
        };

        // 存储原始数据用于显示
        detailDrawer.originalData = {
          id: workorder.id,
          shippingName: workorder.shippingName,
          transportName: workorder.transportName,
          createrName: workorder.createrName,
          processName: workorder.processName || "",
          workorderStatus: workorder.workorderStatus,
          salary: workorder.salary,
          commission: workorder.commission,
          usedCredit: workorder.usedCredit || 0,
        };

        detailDrawer.loading = false;
      } else {
        ElMessage.error("获取工单详情失败");
      }
    })
    .catch((error) => {
      ElMessage.error("获取工单详情失败");
      detailDrawer.loading = false;
    });

  // 获取审批流程和日志
  fetchApprovalData(row.id);
};

// 格式化工资额为两位小数
const formatGrantAmount = () => {
  if (detailItemDialog.data.grantAmount) {
    // 将输入值转换为数字，并保留两位小数
    const formattedValue = parseFloat(detailItemDialog.data.grantAmount).toFixed(2);
    // 检查是否为有效数字
    if (!isNaN(formattedValue)) {
      detailItemDialog.data.grantAmount = formattedValue;
    }
  }
};

// 通用金额格式化函数，用于处理空值显示
const formatAmount = (value: any) => {
  return value !== undefined && value !== null && value !== '' ? value : '/';
};

// 格式化交易手续费金额，添加美元符号
const formatFeePercent = (value: any) => {
  return value !== undefined && value !== null && value !== '' ? `$${value}` : '/';
};

onMounted(() => {
  fetchCompanyList();
  fetchWorkorderList();
  // 如果是经销商公司，获取航运公司列表
  if (userStore.companyType === '4') {
    fetchTransportCompanyList();
  }
});

// 重置明细对话框数据
const resetDetailItemDialog = () => {
  detailItemDialog.data = {
    grantobjectName: "",
    certType: "",
    certNo: "",
    job: "",
    grantAmount: 0,
    accountName: "",
    accountPinyin: "",
    accountNo: "",
    bankName: "",
    depositBranch: "",
    grantType: "",
    swiftCode: "",
    detailRemark: "",
    ship: "",
    verifyStatus: 0,
    fileUrl: "",
  };
  detailItemDialog.index = -1;
  detailItemDialog.showId = false;
  detailItemDialog.showAccountSelect = false;
  detailItemDialog.accounts = [];
  detailItemDialog.title = "";
};

// 表格操作处理
const handleTableOperation = ({ action, row }) => {
  switch (action) {
    case "detail":
      handleDetail(row);
      break;
    case "edit":
      handleEdit(row);
      break;
    case "copy":
      handleCopy(row);
      break;
    case "process":
      handleProcess(row);
      break;
    case "approval":
      handleApproval(row);
      break;
    default:
      console.warn("未知操作类型:", action);
  }
};

// 配置表格按钮处理函数
const handleConfigTable = () => {
  // 直接获取UsdWorkorderTable组件实例并调用其handleConfigClick方法
  // 这里使用$refs方式访问子组件实例
  if (usdWorkorderTableRef.value) {
    usdWorkorderTableRef.value.handleConfigClick();
  }
};

// 添加对UsdWorkorderTable组件的引用
const usdWorkorderTableRef = ref(null);

// 锚点导航相关函数

// 锚点导航滚动方法
const scrollToAnchor = (elementId: string) => {
  const element = document.getElementById(elementId);
  if (element) {
    // 获取抽屉内容区域
    const drawerContent = document.querySelector(".drawer-content");
    if (drawerContent) {
      // 计算元素距离抽屉内容顶部的距离
      const offsetTop = element.offsetTop;
      // 平滑滚动到目标位置
      drawerContent.scrollTo({
        top: offsetTop - 20, // 减去一点偏移量，使视图更好
        behavior: "smooth",
      });
    }
  }
};

// 修改发放状态弹窗
const grantStatusDialog = reactive({
  visible: false,
  title: "修改发放状态",
  data: null as any,
  status: "0", // 默认为成功
});

// 批量修改发放状态相关变量
const batchModifyDialog = reactive({
  visible: false,
  status: "0", // 默认为成功
  loading: false
});

// 表格引用
const detailTableRef = ref(null);

// 选中的明细列表
const selectedDetails = ref<any[]>([]);

// 处理修改发放状态
const handleUpdateGrantStatus = (row: any) => {
  grantStatusDialog.visible = true;
  grantStatusDialog.data = row;
  grantStatusDialog.status = "0"; // 默认设置为成功
};

// 提交修改发放状态
const submitUpdateGrantStatus = async () => {
  try {
    // 调用更新发放状态API
    const res = await updateGrantStatus({
      id: grantStatusDialog.data.id,
      grantStatus: grantStatusDialog.status,
    });
    if (res.code === 200) {
      // 更新本地数据
      const index = detailDrawer.form.details.findIndex(
        (item) => item.id === grantStatusDialog.data.id,
      );

      if (index !== -1) {
        detailDrawer.form.details[index].grantStatus = grantStatusDialog.status;
      }

      ElMessage.success("发放状态更新成功");
      grantStatusDialog.visible = false;

      // 如果所有明细都已更新状态，可以考虑刷新工单列表
      const allUpdated = !detailDrawer.form.details.some(
        (item) => item.grantStatus === "2",
      );

      if (allUpdated) {
        fetchWorkorderList();
      }
    } else {
      ElMessage.error(res.msg || "更新发放状态失败");
    }
  } catch (error) {
    ElMessage.error("更新发放状态失败");
  }
};

// 处理表格选择变更
const handleSelectionChange = (selection: any[]) => {
  selectedDetails.value = selection;
};

// 显示批量修改对话框
const handleBatchModify = () => {
  if (selectedDetails.value.length === 0) {
    ElMessage.warning("请至少选择一条明细记录");
    return;
  }
  batchModifyDialog.visible = true;
  batchModifyDialog.status = "0"; // 默认设置为成功
};

// 提交批量修改状态
const submitBatchModify = async () => {
  if (selectedDetails.value.length === 0) {
    ElMessage.warning("请至少选择一条明细记录");
    return;
  }
  try {
    batchModifyDialog.loading = true;
    // 获取所有选中明细的ID
    const detailIds = selectedDetails.value.map(item => item.id);
    const res = await batchUpdateGrantStatus(detailIds, batchModifyDialog.status);
    if (res.code === 200) {
      ElMessage.success(`已成功修改发放状态`);
      // 更新本地数据
      selectedDetails.value.forEach(selected => {
        const index = detailDrawer.form.details.findIndex(item => item.id === selected.id);
        if (index !== -1) {
          detailDrawer.form.details[index].grantStatus = batchModifyDialog.status;
        }
      });
      batchModifyDialog.visible = false;
      // 清空选择数组
      selectedDetails.value = [];

      // 清除表格选择状态
      if (detailTableRef.value) {
        detailTableRef.value.clearSelection();
      }
    } else {
      ElMessage.error(res.msg || "批量更新发放状态失败");
    }
  } catch (error) {
    ElMessage.error("批量更新发放状态失败");
  } finally {
    batchModifyDialog.loading = false;
  }
};

// 添加文件上传前的验证函数
const beforeUpload = (file: any) => {
  // 检查文件类型
  const isWord =
    file.type === "application/msword" ||
    file.type ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
  if (!isWord) {
    ElMessage.error("只能上传Word文件（.doc/.docx）!");
    return false;
  }

  // 检查文件大小
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error("上传文件大小不能超过5MB!");
    return false;
  }

  // 如果通过验证，设置上传地址
  return true;
};

// 添加"工单完成"按钮并实现功能
const handleCompleteWorkorder = async () => {
  try {
    await ElMessageBox.confirm("确定要完成该工单吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await completeWorkorder(detailDrawer.originalData.id);
    if (res.code === 200) {
      ElMessage.success("工单完成成功");
      detailDrawer.visible = false;
      fetchWorkorderList(); // 刷新工单列表
    } else {
      ElMessage.error(res.msg || "工单完成失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("工单完成操作失败");
    }
  }
};

// 添加"反馈完成"按钮并实现功能
const handleFeedbackComplete = async () => {
  try {
    await ElMessageBox.confirm("确定要反馈完成该工单吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await feedbackComplete(detailDrawer.originalData.id);
    if (res.code === 200) {
      ElMessage.success("反馈完成成功");
      detailDrawer.visible = false;
      fetchWorkorderList(); // 刷新工单列表
    } else {
      ElMessage.error(res.msg || "反馈完成失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("反馈完成操作失败");
    }
  }
};

// 添加"完成异常原因处理"按钮并实现功能
const handleCompleteExceptionHandling = async () => {
  try {
    await ElMessageBox.confirm("确定要完成异常原因处理吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await completeExceptionHandling(detailDrawer.originalData.id);
    if (res.code === 200) {
      ElMessage.success("异常处理完成成功");
      detailDrawer.visible = false;
      fetchWorkorderList(); // 刷新工单列表
    } else {
      ElMessage.error(res.msg || "异常处理完成失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("异常处理完成操作失败");
    }
  }
};

// 添加"处理完成"按钮并实现功能，用于船舶公司处理完成异常
const handleCompleteException = async () => {
  try {
    await ElMessageBox.confirm("确定要完成异常处理吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const res = await completeException(detailDrawer.originalData.id);
    if (res.code === 200) {
      ElMessage.success("异常处理完成成功");
      detailDrawer.visible = false;
      fetchWorkorderList(); // 刷新工单列表
    } else {
      ElMessage.error(res.msg || "异常处理完成失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("异常处理完成操作失败");
    }
  }
};

// 下载异常处理文件
const downloadFile = (row: any) => {
  if (row.fileUrl) {
    // 检查是否有多个文件URL（以逗号分隔）
    if (row.fileUrl.includes(",")) {
      // 拆分为多个URL
      const fileUrls = row.fileUrl.split(",");

      // 创建隐藏的iframe来处理下载，避免浏览器弹窗限制
      const handleDownload = (url: string) => {
        // 创建一个隐藏的iframe
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        document.body.appendChild(iframe);

        // 设置iframe的src为文件URL
        iframe.src = url.trim();

        // 一段时间后移除iframe
        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 5000);
      };

      // 依次下载每个文件
      fileUrls.forEach((url, index) => {
        if (url.trim()) {
          handleDownload(url.trim());
        }
      });

      ElMessage.success(`${fileUrls.length}个文件开始下载`);
    } else {
      // 单个文件URL的情况
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      document.body.appendChild(iframe);
      iframe.src = row.fileUrl;

      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 5000);

      ElMessage.success("文件开始下载");
    }
  } else {
    ElMessage.warning("文件链接不存在");
  }
};

// 添加"工单完成"按钮的显示条件
const canCompleteWorkorder = computed(() => {
  // 检查工单明细的条件：
  // 1. 明细列表不为空
  // 2. 所有明细的发放状态都是成功(0)，不包含失败(1)或发放中(2)
  if (!detailDrawer.form.details || detailDrawer.form.details.length === 0) {
    return false;
  }

  // 检查所有明细是否都是成功状态
  return detailDrawer.form.details.every((detail) => detail.grantStatus === "0");
});

// 添加"反馈完成"按钮的显示条件
const canFeedbackComplete = computed(() => {
  // 检查条件：
  // 1. 工单状态为7（待反馈）
  // 2. 明细状态不能有发放中(2)
  // 3. 用户公司类型为1（船舶公司）
  if (
    detailDrawer.originalData.workorderStatus !== "7" ||
    userStore.companyType !== "1" ||
    !detailDrawer.form.details ||
    detailDrawer.form.details.length === 0
  ) {
    return false;
  }

  // 确保所有明细的发放状态不是"发放中"
  return !detailDrawer.form.details.some(
    (detail) => detail.grantStatus === "2",
  );
});

// 添加"完成异常原因处理"按钮的显示条件
const canCompleteExceptionHandling = computed(() => {
  // 检查条件：
  // 1. 工单状态为10（反馈完毕）
  // 2. 用户公司类型为2（航运公司）
  // 3. 所有失败明细都存在异常类型与异常原因
  if (
    detailDrawer.originalData.workorderStatus !== "10" ||
    userStore.companyType !== "2" ||
    !detailDrawer.form.details ||
    detailDrawer.form.details.length === 0
  ) {
    return false;
  }

  // 获取所有失败的明细
  const failedDetails = detailDrawer.form.details.filter(
    (detail) => detail.grantStatus === "1",
  );

  // 如果没有失败明细，则不显示按钮
  if (failedDetails.length === 0) {
    return false;
  }

  // 检查所有失败明细是否都有异常类型和异常原因
  return failedDetails.every(
    (detail) => detail.exceptionType && detail.exceptionReason,
  );
});

// 添加异常处理反馈对话框
const exceptionFeedbackDialog = reactive({
  visible: false,
  form: {
    isException: '0',  // 默认无误
    errorMsg: ''
  }
});

// 是否显示批量修改按钮
const showBatchModifyButton = computed(() => {
  // 仅在以下条件都满足时显示批量修改按钮:
  // 1. 工单状态为7（待反馈）
  // 2. 用户公司类型为1（船舶公司）
  // 3. 查看模式
  return (
    detailDrawer.originalData.workorderStatus === "7" &&
    userStore.companyType === "1" &&
    detailDrawer.mode === "view"
  );
});

// 添加"反馈异常处理"按钮并实现功能
const handleExceptionHandleError = () => {
  // 重置表单数据
  exceptionFeedbackDialog.form.isException = '0';
  exceptionFeedbackDialog.form.errorMsg = '';
  // 打开对话框
  exceptionFeedbackDialog.visible = true;
};

// 提交异常处理反馈
const submitExceptionFeedback = async () => {
  // 表单验证
  if (exceptionFeedbackDialog.form.isException === '1' && !exceptionFeedbackDialog.form.errorMsg) {
    ElMessage.warning('请输入错误信息');
    return;
  }

  try {
    const res = await exceptionHandleError(
      detailDrawer.originalData.id,
      exceptionFeedbackDialog.form.isException,
      exceptionFeedbackDialog.form.errorMsg
    );

    if (res.code === 200) {
      ElMessage.success(exceptionFeedbackDialog.form.isException === '1' ? '已反馈异常处理有误' : '已确认异常处理无误');
      exceptionFeedbackDialog.visible = false;
      detailDrawer.visible = false;
      fetchWorkorderList(); // 刷新工单列表
    } else {
      ElMessage.error(res.msg || '反馈失败');
    }
  } catch (error) {
    ElMessage.error('反馈异常处理操作失败');
  }
};

// 查询swiftcode
const querySwiftCode = async () => {
  // 如果开户支行和开户行都有值，才进行查询
  if (detailItemDialog.data.depositBranch && detailItemDialog.data.bankName) {
    try {
      const res = await getSwiftCodeByBank(
        detailItemDialog.data.depositBranch,
        detailItemDialog.data.bankName
      );
      if (res.code === 200 && res.data) {
        // 设置swiftCode
        detailItemDialog.data.swiftCode = res.data || "";
        if (res.data) {
          ElMessage.success("已自动获取swiftCode");
        }
      }
    } catch (error) {
      //onsole.error("获取swiftCode失败:", error);
    }
  }
};

// 查询银行信息
const queryBankInfo = async () => {
  // 如果银行账号有值，才进行查询
  if (detailItemDialog.data.accountNo) {
    try {
      const res = await getBankInfoByCardNumber(detailItemDialog.data.accountNo);
      if (res.code === 200 && res.data) {
        // 设置开户行
        if(res.data != detailItemDialog.data.bankName){
          detailItemDialog.data.swiftCode = "";
        }
        detailItemDialog.data.bankName = res.data || "";
        if (res.data) {
          ElMessage.success("已自动获取开户行信息");
        }
      }
    } catch (error) {
      ElMessage.error("获取银行信息失败");
    }
  }
};

// 添加异常类型格式化函数
const formatExceptionType = (exceptionType: string) => {
  const exceptionTypeMap: Record<string, string> = {
    "1": "信息错误",
    "2": "银行审核",
  };
  return exceptionTypeMap[exceptionType] || "未知";
};

// 格式化工单状态
</script>

<style scoped lang="scss">
.usd-workorder-page {
  padding: 24px;
  background: var(--el-bg-color, #fff);
  border-radius: 4px;
  transition: background-color 0.3s ease;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 24px;
    background-color: v-bind('themeColor');
    border-radius: 2px 0 0 2px;
  }

  :deep(.el-card) {
    background: var(--el-bg-color, #fff);
    border-color: var(--brder-color, #e8e8e8);
    transition: background-color 0.3s ease;
  }
}

html.dark {
  .usd-workorder-page {
    background: var(--el-bg-color);
    color: var(--el-text-color-primary);

    :deep(.summary-header) {
      background-color: var(--subMenuBg, #1f2d3d);
    }
  }
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.header-info {
  display: flex;
  gap: 24px;
}

.info-item {
  font-size: 14px;
  color: #333;

  .value {
    font-weight: bold;
    color: #409eff;
    font-size: 16px;
  }
}

.action-buttons {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;

  .el-button.el-button--primary {
    background-color: v-bind('themeColor');
    border-color: v-bind('themeColor');

    &:hover, &:focus {
      background-color: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }
  }
}

/* 分页样式已移至表格组件 */

.mt-4 {
  margin-bottom: 70px;
}

.mb-2 {
  margin-bottom: 8px;
}

.font-bold {
  font-weight: bold;
}

.form-card {
  margin-bottom: 16px;
}

.drawer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: var(--el-bg-color, #fff);
  border-top: 1px solid var(--brder-color, #dcdfe6);
  text-align: right;
  justify-content: flex-end;
  z-index: 999;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  margin-left: 14%; /* 补偿抽屉的左侧位置 */
  width: 86%;

  .el-button.el-button--primary {
    background-color: v-bind('themeColor');
    border-color: v-bind('themeColor');

    &:hover, &:focus {
      background-color: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }
  }
}

.el-drawer__body {
  padding-bottom: 70px;
  overflow: auto;
}

.detail-form {
  .el-form-item {
    margin-bottom: 18px;
  }
}

.center-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin: 20px;
}

.custom-blue-button {
  background-color: #fff;
  color: #409eff;
  border-color: #409eff;
  width: 100px;

  &:hover {
    background-color: #ecf5ff;
    color: #409eff;
    border-color: #409eff;
  }
}

.detail-operations {
  display: flex;
  gap: 12px;
}

.upload-info {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;

  .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }

  &.success {
    background-color: #f0f9eb;
    color: #67c23a;
    border: 1px solid #e1f3d8;
  }

  &.error {
    background-color: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fde2e2;
  }

  &.warning {
    background-color: #fdf6ec;
    color: #e6a23c;
    border: 1px solid #faecd8;
  }

  &.info {
    background-color: #edf2fc;
    color: #409eff;
    border: 1px solid #d9ecff;
  }
}

/* 表格容器样式 */
.el-card.mt-4 {
  display: flex;
  flex-direction: column;
  max-height: 600px;
  overflow: visible;
}

/* 表格包装器样式，使其高度自适应 */
.el-card.mt-4 > .el-card__body {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 表格样式 */
.el-table {
  flex: 1;
  overflow: auto;
}

.drawer-content {
  height: 100%;
  overflow-y: auto;
  padding-bottom: 70px;
}

.exception-tabs {
  .tab-buttons {
    display: flex;
    width: 100%;
    border-radius: 4px;
    overflow: hidden;

    .tab-button {
      flex: 1;
      text-align: center;
      padding: 12px 0;
      background-color: #f5f7fa;
      color: #606266;
      cursor: pointer;
      transition: all 0.3s;

      &.active {
        background-color: #409eff;
        color: #fff;
      }

      &:hover:not(.active) {
        background-color: #e6e8eb;
      }
    }
  }
}

.mt-4 {
  margin-top: 16px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.success-tip {
  color: #67c23a;
  font-weight: bold;
}

.empty-exception-msg {
  text-align: center;
  padding: 30px 0;
  color: #909399;
  font-size: 14px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.exception-content {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.exception-reason-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
  font-size: 14px;
}

.upload-demo {
  .el-upload__tip {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }
}

.exception-form {
  padding: 20px 0;
}

.exception-reason {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 80px;
  max-height: 120px;
  line-height: 1.5;
  color: #606266;
  overflow-y: auto;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: #fff;
  border-top: 1px solid #dcdfe6;
  text-align: right;
  z-index: 10;
}

.approval-mt-4 {
  margin-top: 4px !important;
  margin-bottom: 8px !important;
}

.approval-content {
  padding-bottom: 20px;
}

.approval-form-card {
  margin-bottom: 4px !important;
}

.action-buttons.drawer-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 0;
  gap: 10px;
}

.total-amount-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 16px;
  font-size: 14px;
  color: #606266;

  .total-amount-label {
    margin-right: 8px;
  }

  .total-amount-value {
    font-weight: bold;
  }

  .ml-4 {
    margin-left: 16px;
  }
}

.detail-dialog-form {
  padding-bottom: 50px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: #fff;
  border-top: 1px solid #dcdfe6;
  text-align: right;
  z-index: 10;
}

.detail-form-container {
  height: 100%;
  overflow-y: auto;
}

/* 新增的抽屉标题导航样式 */
.drawer-title-container {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: -5px;
}

.drawer-title {
  font-size: 16px;
  font-weight: bold;
  margin-right: 15px;
  white-space: nowrap;
}

.title-anchor-nav {
  flex: 1;
}

.title-anchor-nav .anchor-menu {
  display: flex;
  gap: 8px;
  padding: 0;
  margin: 0;
  flex-wrap: wrap;
}

.title-anchor-nav .anchor-link {
  padding: 3px 10px;
  font-size: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-radius: 15px;
  height: 26px;
}

/* 顶部锚点导航样式 */
  .top-anchor-nav {
    margin: 10px 0 20px;
    border-radius: 10px;
    padding: 12px 20px;
    text-align: center;
    background-color: #f0f7ff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 100%;
    border: 1px solid #e0e9f6;
  }

.anchor-menu {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0 auto;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

.anchor-menu li {
  margin: 0;
  padding: 0;
}

.anchor-link {
  display: inline-block;
  padding: 10px 20px;
  color: #606266;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  border-radius: 20px;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid transparent;
}

.anchor-link:hover,
.anchor-link.active {
  color: #409eff;
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  font-weight: 500;
  transform: translateY(-2px);
}

.anchor-text {
  white-space: nowrap;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-count {
  padding: 8px 12px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}
</style>
