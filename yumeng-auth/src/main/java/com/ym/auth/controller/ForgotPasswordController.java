package com.ym.auth.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.ym.common.core.domain.model.LoginBody;
import com.ym.common.json.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ym.auth.form.EmailCodeForm;
import com.ym.auth.form.ForgotPasswordForm;
import com.ym.auth.service.ForgotPasswordService;
import com.ym.common.core.domain.R;

/**
 * 忘记密码控制器
 *
 *
 */
@Slf4j
@RequiredArgsConstructor
@RestController
public class ForgotPasswordController {

    private final ForgotPasswordService forgotPasswordService;

    /**
     * 发送重置密码验证码
     *
     * @param body 邮箱验证码表单
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/email/code")
    public R<Void> sendEmailCode(@RequestBody String body) {
        EmailCodeForm form = JsonUtils.parseObject(body, EmailCodeForm.class);
        log.warn(form.toString());
        forgotPasswordService.sendEmailCode(form);
        log.info("发送重置密码验证码");
        return R.ok();
    }

    /**
     * 重置密码
     *
     * @param body 重置密码表单
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/resetPassword")
    public R<Void> resetPassword(@RequestBody String body) {
        //前端加密了，下面是解密
        ForgotPasswordForm form = JsonUtils.parseObject(body, ForgotPasswordForm.class);
        forgotPasswordService.resetPassword(form);
        log.info("重置密码");
        return R.ok();
    }
}
