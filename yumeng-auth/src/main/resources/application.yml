# Tomcat
server:
  port: 9210

# Spring
spring:
  application:
    # 应用名称
    name: yumeng-auth
  profiles:
    # 环境配置
    active: @profiles.active@

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: @nacos.server@
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        # 注册组
        group: @nacos.discovery.group@
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: @nacos.config.group@
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:${spring.application.name}.yml
wechat:
    appid: wxb01e9f5aee360376
    secret: bc23875d9cc0bf1e3d8c26a7c04d1277
