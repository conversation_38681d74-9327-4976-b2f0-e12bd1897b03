<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ym</groupId>
        <artifactId>ship-Integrated-management-api</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yumeng-auth</artifactId>

    <description>
        yumeng-auth 认证授权中心
    </description>

    <dependencies>
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-captcha</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-sentinel</artifactId>
        </dependency>

        <!-- yumeng Common Security-->
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-social</artifactId>
        </dependency>

        <!-- yumeng Common Log -->
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-api-resource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-api-system</artifactId>
        </dependency>
        <!-- 自定义负载均衡(多团队开发使用) -->
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>

        <!-- ELK 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.ym</groupId>-->
<!--            <artifactId>yumeng-common-logstash</artifactId>-->
<!--        </dependency>-->

        <!-- skywalking 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.ym</groupId>-->
<!--            <artifactId>yumeng-common-skylog</artifactId>-->
<!--        </dependency>-->

        <!-- prometheus 监控 -->
<!--        <dependency>-->
<!--            <groupId>com.ym</groupId>-->
<!--            <artifactId>yumeng-common-prometheus</artifactId>-->
<!--        </dependency>-->

    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.2.11</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
