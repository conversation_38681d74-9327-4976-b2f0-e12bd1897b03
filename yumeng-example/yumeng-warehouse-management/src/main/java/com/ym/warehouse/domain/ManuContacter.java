package com.ym.warehouse.domain;

import com.ym.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 生产商联系人对象 manu_contacter
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("manu_contacter")
public class ManuContacter extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 生产商id
     */
    private Long manufacturerId;

    /**
     * 生产联系人
     */
    private String contacter;

    /**
     * 生产联系人手机
     */
    private String telphone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 地址
     */
    private String address;

    /**
     * 删除标识
     */
    @TableLogic
    private Long delFlag;


}
