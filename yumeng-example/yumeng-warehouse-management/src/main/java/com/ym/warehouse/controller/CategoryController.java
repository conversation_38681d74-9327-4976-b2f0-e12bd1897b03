package com.ym.warehouse.controller;

import java.util.List;

import com.ym.warehouse.domain.bo.CategoryBo;
import com.ym.warehouse.service.ICategoryService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ym.common.idempotent.annotation.RepeatSubmit;
import com.ym.common.log.annotation.Log;
import com.ym.common.web.core.BaseController;
import com.ym.common.core.domain.R;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.log.enums.BusinessType;
import com.ym.common.excel.utils.ExcelUtil;
import com.ym.warehouse.domain.vo.CategoryVo;

/**
 * 商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系
 * 前端访问路由地址为:/warehouse/category
 *
 * <AUTHOR>
 * @date 2025-06-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/category")
public class CategoryController extends BaseController {

    private final ICategoryService categoryService;

    /**
     * 查询商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系列表
     */
    @SaCheckPermission("warehouse:category:list")
    @GetMapping("/list")
    public R<List<CategoryVo>> list(CategoryBo bo) {
        List<CategoryVo> list = categoryService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 导出商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系列表
     */
    @SaCheckPermission("warehouse:category:export")
    @Log(title = "商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CategoryBo bo, HttpServletResponse response) {
        List<CategoryVo> list = categoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系", CategoryVo.class, response);
    }

    /**
     * 获取商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("warehouse:category:query")
    @GetMapping("/{id}")
    public R<CategoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(categoryService.queryById(id));
    }

    /**
     * 新增商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系
     * 限制商品类目最多为三级目录
     */
    @SaCheckPermission("warehouse:category:add")
    @Log(title = "商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CategoryBo bo) {
        log.info("新增商品类目:{}", bo);
        // 检查是否超过三级目录限制
        if (!categoryService.checkCategoryLevel(bo.getParentId())) {
            return R.fail("商品类目最多支持三级目录");
        }
        return toAjax(categoryService.insertByBo(bo));
    }

    /**
     * 修改商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系
     * 限制商品类目最多为三级目录
     */
    @SaCheckPermission("warehouse:category:edit")
    @Log(title = "商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CategoryBo bo) {
        log.info("修改商品类目:{}", bo);
        // 检查是否存在循环引用
        if (!categoryService.checkCircularReference(bo.getId(), bo.getParentId())) {
            return R.fail("不能设置当前节点的子节点为父节点");
        }
        if (!categoryService.checkCategoryLevel(bo.getParentId())) {
            return R.fail("商品类目最多支持三级目录");
        }
        return toAjax(categoryService.updateByBo(bo));
    }

    /**
     * 删除商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("warehouse:category:remove")
    @Log(title = "商品类目，类目和商品(spu)是一对多关系，类目与品牌是多对多关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        log.info("删除商品类目:{}", ids);
        return toAjax(categoryService.deleteWithValidByIds(List.of(ids), true));
    }

}
