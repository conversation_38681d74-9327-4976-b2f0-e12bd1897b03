package com.ym.warehouse.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ym.common.idempotent.annotation.RepeatSubmit;
import com.ym.common.log.annotation.Log;
import com.ym.common.web.core.BaseController;
import com.ym.common.mybatis.core.page.PageQuery;
import com.ym.common.core.domain.R;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.log.enums.BusinessType;
import com.ym.common.excel.utils.ExcelUtil;
import com.ym.warehouse.domain.vo.WarehouseVo;
import com.ym.warehouse.domain.bo.WarehouseBo;
import com.ym.warehouse.service.IWarehouseService;
import com.ym.common.mybatis.core.page.TableDataInfo;

/**
 * 仓库
 * 前端访问路由地址为:/warehouse/warehouse
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/warehouse")
public class WarehouseController extends BaseController {

    private final IWarehouseService warehouseService;

    /**
     * 查询仓库列表
     */
    @SaCheckPermission("warehouse:warehouse:list")
    @GetMapping("/list")
    public TableDataInfo<WarehouseVo> list(WarehouseBo bo, PageQuery pageQuery) {
        return warehouseService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出仓库列表
     */
    @SaCheckPermission("warehouse:warehouse:export")
    @Log(title = "仓库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WarehouseBo bo, HttpServletResponse response) {
        List<WarehouseVo> list = warehouseService.queryList(bo);
        ExcelUtil.exportExcel(list, "仓库", WarehouseVo.class, response);
    }

    /**
     * 获取仓库详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("warehouse:warehouse:query")
    @GetMapping("/{id}")
    public R<WarehouseVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(warehouseService.queryById(id));
    }

    /**
     * 新增仓库
     */
    @SaCheckPermission("warehouse:warehouse:add")
    @Log(title = "仓库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WarehouseBo bo) {
        return toAjax(warehouseService.insertByBo(bo));
    }

    /**
     * 修改仓库
     */
    @SaCheckPermission("warehouse:warehouse:edit")
    @Log(title = "仓库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WarehouseBo bo) {
        return toAjax(warehouseService.updateByBo(bo));
    }

    /**
     * 删除仓库
     *
     * @param ids 主键串
     */
    @SaCheckPermission("warehouse:warehouse:remove")
    @Log(title = "仓库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(warehouseService.deleteWithValidByIds(List.of(ids), true));
    }
}
