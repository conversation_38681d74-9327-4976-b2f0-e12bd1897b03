package com.ym.warehouse.controller;

import java.util.List;

import com.ym.warehouse.service.IManuContacterService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ym.common.idempotent.annotation.RepeatSubmit;
import com.ym.common.log.annotation.Log;
import com.ym.common.web.core.BaseController;
import com.ym.common.mybatis.core.page.PageQuery;
import com.ym.common.core.domain.R;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.log.enums.BusinessType;
import com.ym.common.excel.utils.ExcelUtil;
import com.ym.warehouse.domain.vo.ManufacturerVo;
import com.ym.warehouse.domain.bo.ManufacturerBo;
import com.ym.warehouse.service.IManufacturerService;
import com.ym.common.mybatis.core.page.TableDataInfo;

/**
 * 生产商
 * 前端访问路由地址为:/warehouse/manufacturer
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/manufacturer")
public class ManufacturerController extends BaseController {

    private final IManufacturerService manufacturerService;
    private final IManuContacterService manuContacterService;

    /**
     * 查询生产商列表
     */
    @SaCheckPermission("warehouse:manufacturer:list")
    @GetMapping("/list")
    public TableDataInfo<ManufacturerVo> list(ManufacturerBo bo, PageQuery pageQuery) {
        return manufacturerService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出生产商列表
     */
    @SaCheckPermission("warehouse:manufacturer:export")
    @Log(title = "生产商", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ManufacturerBo bo, HttpServletResponse response) {
        List<ManufacturerVo> list = manufacturerService.queryList(bo);
        ExcelUtil.exportExcel(list, "生产商", ManufacturerVo.class, response);
    }

    /**
     * 获取生产商详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("warehouse:manufacturer:query")
    @GetMapping("/{id}")
    public R<ManufacturerVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        ManufacturerVo manufacturerVo = manufacturerService.queryById(id);
        manufacturerVo.setManuContacterVos(manuContacterService.queryByFacturerId(manufacturerVo.getId()));
        return R.ok(manufacturerVo);
    }

    /**
     * 新增生产商与其联系人
     */
    @SaCheckPermission("warehouse:manufacturer:add")
    @Log(title = "生产商", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ManufacturerBo bo) {
        return toAjax(manufacturerService.insertByBo(bo));
    }

    /**
     * 修改生产商与联系人
     */
    @SaCheckPermission("warehouse:manufacturer:edit")
    @Log(title = "生产商", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ManufacturerBo bo) {
        return toAjax(manufacturerService.updateByBo(bo));
    }

    /**
     * 删除生产商
     *
     * @param ids 主键串
     */
    @SaCheckPermission("warehouse:manufacturer:remove")
    @Log(title = "生产商", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(manufacturerService.deleteWithValidByIds(List.of(ids), true));
    }
}
