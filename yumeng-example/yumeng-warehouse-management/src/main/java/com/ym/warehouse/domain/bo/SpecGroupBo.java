package com.ym.warehouse.domain.bo;

import com.ym.warehouse.domain.SpecGroup;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 规格参数的分组，每个商品分类下有多个规格参数组业务对象 spec_group
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SpecGroup.class, reverseConvertGenerate = false)
public class SpecGroupBo extends BaseEntity {

    /**
     * 规格组Id
     */
    @NotNull(message = "规格组Id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商品分类Id (一个分类下有多个规格组)
     */
    @NotNull(message = "商品分类Id (一个分类下有多个规格组)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long categoryId;

    /**
     * 规格组名称
     */
    @NotBlank(message = "规格组名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;


}
