package com.ym.warehouse.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ym.common.idempotent.annotation.RepeatSubmit;
import com.ym.common.log.annotation.Log;
import com.ym.common.web.core.BaseController;
import com.ym.common.mybatis.core.page.PageQuery;
import com.ym.common.core.domain.R;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.log.enums.BusinessType;
import com.ym.common.excel.utils.ExcelUtil;
import com.ym.warehouse.domain.vo.SpecGroupVo;
import com.ym.warehouse.domain.bo.SpecGroupBo;
import com.ym.warehouse.service.ISpecGroupService;
import com.ym.common.mybatis.core.page.TableDataInfo;

/**
 * 规格参数的分组，每个商品分类下有多个规格参数组
 * 前端访问路由地址为:/warehouse/specGroup
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/specGroup")
public class SpecGroupController extends BaseController {

    private final ISpecGroupService specGroupService;

    /**
     * 查询规格参数的分组，每个商品分类下有多个规格参数组列表
     */
    @SaCheckPermission("warehouse:specGroup:list")
    @GetMapping("/list")
    public TableDataInfo<SpecGroupVo> list(SpecGroupBo bo, PageQuery pageQuery) {
        if (bo.getCategoryId() == null ||bo.getCategoryId() == 0){
            bo.setCategoryId(null);
        }
        return specGroupService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出规格参数的分组，每个商品分类下有多个规格参数组列表
     */
    @SaCheckPermission("warehouse:specGroup:export")
    @Log(title = "规格参数的分组，每个商品分类下有多个规格参数组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SpecGroupBo bo, HttpServletResponse response) {
        List<SpecGroupVo> list = specGroupService.queryList(bo);
        ExcelUtil.exportExcel(list, "规格参数的分组，每个商品分类下有多个规格参数组", SpecGroupVo.class, response);
    }

    /**
     * 获取规格参数的分组，每个商品分类下有多个规格参数组详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("warehouse:specGroup:query")
    @GetMapping("/{id}")
    public R<SpecGroupVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(specGroupService.queryById(id));
    }

    /**
     * 新增规格参数的分组，每个商品分类下有多个规格参数组
     */
    @SaCheckPermission("warehouse:specGroup:add")
    @Log(title = "规格参数的分组，每个商品分类下有多个规格参数组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SpecGroupBo bo) {
        if (bo.getCategoryId() == null ||bo.getCategoryId() <= 0){
            return R.fail("请选择商品分类");
        }
        return toAjax(specGroupService.insertByBo(bo));
    }

    /**
     * 修改规格参数的分组，每个商品分类下有多个规格参数组
     */
    @SaCheckPermission("warehouse:specGroup:edit")
    @Log(title = "规格参数的分组，每个商品分类下有多个规格参数组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SpecGroupBo bo) {
        return toAjax(specGroupService.updateByBo(bo));
    }

    /**
     * 删除规格参数的分组，每个商品分类下有多个规格参数组
     *
     * @param ids 主键串
     */
    @SaCheckPermission("warehouse:specGroup:remove")
    @Log(title = "规格参数的分组，每个商品分类下有多个规格参数组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(specGroupService.deleteWithValidByIds(List.of(ids), true));
    }
}
