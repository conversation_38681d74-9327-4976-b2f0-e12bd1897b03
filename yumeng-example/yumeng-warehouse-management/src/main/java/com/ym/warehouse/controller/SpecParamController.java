package com.ym.warehouse.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ym.common.idempotent.annotation.RepeatSubmit;
import com.ym.common.log.annotation.Log;
import com.ym.common.web.core.BaseController;
import com.ym.common.mybatis.core.page.PageQuery;
import com.ym.common.core.domain.R;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.log.enums.BusinessType;
import com.ym.common.excel.utils.ExcelUtil;
import com.ym.warehouse.domain.vo.SpecParamVo;
import com.ym.warehouse.domain.bo.SpecParamBo;
import com.ym.warehouse.service.ISpecParamService;
import com.ym.common.mybatis.core.page.TableDataInfo;

/**
 * 规格参数组下的参数名
 * 前端访问路由地址为:/warehouse/param
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/param")
public class SpecParamController extends BaseController {

    private final ISpecParamService specParamService;

    /**
     * 查询规格参数组下的参数名列表
     */
    @SaCheckPermission("warehouse:param:list")
    @GetMapping("/list")
    public TableDataInfo<SpecParamVo> list(SpecParamBo bo, PageQuery pageQuery) {
        return specParamService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出规格参数组下的参数名列表
     */
    @SaCheckPermission("warehouse:param:export")
    @Log(title = "规格参数组下的参数名", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SpecParamBo bo, HttpServletResponse response) {
        List<SpecParamVo> list = specParamService.queryList(bo);
        ExcelUtil.exportExcel(list, "规格参数组下的参数名", SpecParamVo.class, response);
    }

    /**
     * 获取规格参数组下的参数名详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("warehouse:param:query")
    @GetMapping("/{id}")
    public R<SpecParamVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(specParamService.queryById(id));
    }

    /**
     * 新增规格参数组下的参数名
     */
    @SaCheckPermission("warehouse:param:add")
    @Log(title = "规格参数组下的参数名", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SpecParamBo bo) {
        return toAjax(specParamService.insertByBo(bo));
    }

    /**
     * 修改规格参数组下的参数名
     */
    @SaCheckPermission("warehouse:param:edit")
    @Log(title = "规格参数组下的参数名", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SpecParamBo bo) {
        return toAjax(specParamService.updateByBo(bo));
    }

    /**
     * 删除规格参数组下的参数名
     *
     * @param ids 主键串
     */
    @SaCheckPermission("warehouse:param:remove")
    @Log(title = "规格参数组下的参数名", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(specParamService.deleteWithValidByIds(List.of(ids), true));
    }
}
