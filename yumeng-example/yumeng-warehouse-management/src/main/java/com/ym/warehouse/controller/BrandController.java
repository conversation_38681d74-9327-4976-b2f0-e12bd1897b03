package com.ym.warehouse.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ym.common.idempotent.annotation.RepeatSubmit;
import com.ym.common.log.annotation.Log;
import com.ym.common.web.core.BaseController;
import com.ym.common.mybatis.core.page.PageQuery;
import com.ym.common.core.domain.R;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.log.enums.BusinessType;
import com.ym.common.excel.utils.ExcelUtil;
import com.ym.warehouse.domain.vo.BrandVo;
import com.ym.warehouse.domain.bo.BrandBo;
import com.ym.warehouse.service.IBrandService;
import com.ym.common.mybatis.core.page.TableDataInfo;

/**
 * 品牌，一个品牌下有多个商品（spu），一对多关系
 * 前端访问路由地址为:/warehouse/brand
 *
 * <AUTHOR>
 * @date 2025-06-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/brand")
public class BrandController extends BaseController {

    private final IBrandService brandService;

    /**
     * 查询品牌，一个品牌下有多个商品（spu），一对多关系列表
     */
    @SaCheckPermission("warehouse:brand:list")
    @GetMapping("/list")
    public TableDataInfo<BrandVo> list(BrandBo bo, PageQuery pageQuery) {
        return brandService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出品牌，一个品牌下有多个商品（spu），一对多关系列表
     */
    @SaCheckPermission("warehouse:brand:export")
    @Log(title = "品牌，一个品牌下有多个商品（spu），一对多关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BrandBo bo, HttpServletResponse response) {
        List<BrandVo> list = brandService.queryList(bo);
        ExcelUtil.exportExcel(list, "品牌", BrandVo.class, response);
    }

    /**
     * 获取品牌，一个品牌下有多个商品（spu），一对多关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("warehouse:brand:query")
    @GetMapping("/{id}")
    public R<BrandVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(brandService.queryById(id));
    }

    /**
     * 新增品牌，一个品牌下有多个商品（spu），一对多关系
     */
    @SaCheckPermission("warehouse:brand:add")
    @Log(title = "品牌，一个品牌下有多个商品（spu），一对多关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BrandBo bo) {
        return toAjax(brandService.insertByBo(bo));
    }

    /**
     * 修改品牌，一个品牌下有多个商品（spu），一对多关系
     */
    @SaCheckPermission("warehouse:brand:edit")
    @Log(title = "品牌，一个品牌下有多个商品（spu），一对多关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BrandBo bo) {
        return toAjax(brandService.updateByBo(bo));
    }

    /**
     * 删除品牌，一个品牌下有多个商品（spu），一对多关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("warehouse:brand:remove")
    @Log(title = "品牌，一个品牌下有多个商品（spu），一对多关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(brandService.deleteWithValidByIds(List.of(ids), true));
    }
}
