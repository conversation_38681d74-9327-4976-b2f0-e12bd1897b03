package com.ym.warehouse.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ym.common.idempotent.annotation.RepeatSubmit;
import com.ym.common.log.annotation.Log;
import com.ym.common.web.core.BaseController;
import com.ym.common.mybatis.core.page.PageQuery;
import com.ym.common.core.domain.R;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.log.enums.BusinessType;
import com.ym.common.excel.utils.ExcelUtil;
import com.ym.warehouse.domain.vo.CategoryBrandVo;
import com.ym.warehouse.domain.bo.CategoryBrandBo;
import com.ym.warehouse.service.ICategoryBrandService;
import com.ym.common.mybatis.core.page.TableDataInfo;

/**
 * 商品分类和品牌的中间，两者是多对多关系
 * 前端访问路由地址为:/warehouse/CategoryBrand
 *
 * <AUTHOR>
 * @date 2025-06-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/CategoryBrand")
public class CategoryBrandController extends BaseController {

    private final ICategoryBrandService categoryBrandService;

    /**
     * 查询商品分类和品牌的中间，两者是多对多关系列表
     */
    @SaCheckPermission("warehouse:CategoryBrand:list")
    @GetMapping("/list")
    public TableDataInfo<CategoryBrandVo> list(CategoryBrandBo bo, PageQuery pageQuery) {
        return categoryBrandService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商品分类和品牌的中间，两者是多对多关系列表
     */
    @SaCheckPermission("warehouse:CategoryBrand:export")
    @Log(title = "商品分类和品牌的中间，两者是多对多关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CategoryBrandBo bo, HttpServletResponse response) {
        List<CategoryBrandVo> list = categoryBrandService.queryList(bo);
        ExcelUtil.exportExcel(list, "商品分类和品牌的中间，两者是多对多关系", CategoryBrandVo.class, response);
    }

    /**
     * 获取商品分类和品牌的中间，两者是多对多关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("warehouse:CategoryBrand:query")
    @GetMapping("/{id}")
    public R<CategoryBrandVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(categoryBrandService.queryById(id));
    }

    /**
     * 新增商品分类和品牌的中间，两者是多对多关系
     */
    @SaCheckPermission("warehouse:CategoryBrand:add")
    @Log(title = "商品分类和品牌的中间，两者是多对多关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CategoryBrandBo bo) {
        return toAjax(categoryBrandService.insertByBo(bo));
    }

    /**
     * 修改商品分类和品牌的中间，两者是多对多关系
     */
    @SaCheckPermission("warehouse:CategoryBrand:edit")
    @Log(title = "商品分类和品牌的中间，两者是多对多关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CategoryBrandBo bo) {
        return toAjax(categoryBrandService.updateByBo(bo));
    }

    /**
     * 删除商品分类和品牌的中间，两者是多对多关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("warehouse:CategoryBrand:remove")
    @Log(title = "商品分类和品牌的中间，两者是多对多关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(categoryBrandService.deleteWithValidByIds(List.of(ids), true));
    }
}
