package com.ym.warehouse.service;

import com.ym.warehouse.domain.Warehouse;
import com.ym.warehouse.domain.vo.WarehouseVo;
import com.ym.warehouse.domain.bo.WarehouseBo;
import com.ym.common.mybatis.core.page.TableDataInfo;
import com.ym.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 仓库Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IWarehouseService {

    /**
     * 查询仓库
     *
     * @param id 主键
     * @return 仓库
     */
    WarehouseVo queryById(Long id);

    /**
     * 分页查询仓库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 仓库分页列表
     */
    TableDataInfo<WarehouseVo> queryPageList(WarehouseBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的仓库列表
     *
     * @param bo 查询条件
     * @return 仓库列表
     */
    List<WarehouseVo> queryList(WarehouseBo bo);

    /**
     * 新增仓库
     *
     * @param bo 仓库
     * @return 是否新增成功
     */
    Boolean insertByBo(WarehouseBo bo);

    /**
     * 修改仓库
     *
     * @param bo 仓库
     * @return 是否修改成功
     */
    Boolean updateByBo(WarehouseBo bo);

    /**
     * 校验并批量删除仓库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
