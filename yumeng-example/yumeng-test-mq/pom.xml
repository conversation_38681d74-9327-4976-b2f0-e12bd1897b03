<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ym</groupId>
        <artifactId>yumeng-example</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yumeng-test-mq</artifactId>

    <description>
        yumeng-test-mq 案例项目
    </description>

    <dependencies>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-tenant</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ym</groupId>
                    <artifactId>yumeng-common-mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.2.11</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
