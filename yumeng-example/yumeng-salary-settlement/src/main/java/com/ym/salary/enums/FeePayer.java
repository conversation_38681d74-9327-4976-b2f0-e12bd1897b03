package com.ym.salary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 审批类型
 * @date 2025/5/24 20:19
 */
@Getter
@AllArgsConstructor
public enum FeePayer {


    /**
     * 航运公司支付
     */
    TransportPay("1", "航运公司支付"),
    /**
     * 船舶公司支付
     */
    shippingPay("2", "船舶公司支付"),
    /**
     * 个人支付
     */
    personalPay("3", "个人支付");

    private final String code;
    private final String info;
}
