package com.ym.salary.domain.bo;

import com.ym.salary.domain.RmbWorkorderDetail;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * 人民币代发工单明细业务对象 rmb_workorder_detail
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = RmbWorkorderDetail.class, reverseConvertGenerate = false)
public class RmbWorkorderDetailBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 关联的人民币工单id，必填
     */
    @NotNull(message = "关联的人民币工单id，必填不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long workoderId;

    /**
     * 船员id 或 企业id
     */
    private Long grantobjectId;
    /**
     * 船员姓名
     */
    private String grantobjectName;
    /**
     * 船员证件类型，枚举：1 身份证  2 护照
     */
    private String certType;
    /**
     * 船员证件号码
     */
    private String certNo;

    /**
     * 涉及交易的：船员或企业默认的银行卡账号id； 注意： 做关联查询的时候，银行卡删除状态不做过滤
     */
    private Long relatedAcountid;
    /**
     * 银行卡账号
     */
    @NotBlank(message = "银行卡账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountNo;
    /**
     * 开户行
     */
    @NotBlank(message = "开户行不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bankName;

    /**
     * 开户支行
     */
    @NotBlank(message = "开户支行不能为空", groups = { AddGroup.class, EditGroup.class })
    private String depositBranch;

    /**
     * 持卡人姓名
     */
    @NotBlank(message = "持卡人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountName;

    /**
     * 持卡人拼音
     */
    @NotBlank(message = "持卡人拼音不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountPinyin;

    /**
     * 职务
     */
    private String job;


    /**
     * 发放金额（RMB）
     */
    @NotNull(message = "发放金额（RMB）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal grantAmount;

    /**
     * 发放类型-手续费类型; 1 个人境内   2 公司境内
     */
    private String grantType;

    /**
     * 税金
     */
    private BigDecimal taxes;

    /**
     * 派遣服务费
     */
    private BigDecimal dispatchFee;

    /**
     * 社保服务费
     */
    private BigDecimal securityFee;

    /**
     * 所在船舶，非必填
     */
    private String ship;

    /**
     * 发放状态  0-成功   1-失败 2待发放
     */
    private String grantStatus;

    /**
     * 异常原因
     */
    private String exceptionReason;
    /**
     * 核对状态
     */
    private Integer verifyStatus;
    /**
     * 信息错误是否修改 0未修改 1已修改
     */
    private String errorCorrection;
    /**
     * 备注
     */
    private String remark;
}
