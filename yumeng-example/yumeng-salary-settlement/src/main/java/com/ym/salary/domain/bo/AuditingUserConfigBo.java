package com.ym.salary.domain.bo;

import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/18 15:35
 */
@Data
public class AuditingUserConfigBo {

    /**
     * 审核人ID
     */
    @NotNull(message = "审核人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long auditingUserId;

    /**
     * 审批人姓名
     */
    @NotBlank(message = "审批人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String auditingUserName;


    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer sort;


}
