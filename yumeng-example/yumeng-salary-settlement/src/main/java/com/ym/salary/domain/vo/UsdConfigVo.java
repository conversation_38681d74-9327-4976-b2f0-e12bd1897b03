package com.ym.salary.domain.vo;

import com.ym.salary.domain.UsdConfig;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;



/**
 * 美金结算配置视图对象 usd_config
 *
 * <AUTHOR>
 * @date 2025-05-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UsdConfig.class)
public class UsdConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 航运公司id
     */
    @ExcelProperty(value = "航运公司id")
    private Long transportId;

    /**
     * 航运公司名称
     */
    @ExcelProperty(value = "航运公司名称")
    private String transportName;

    /**
     * 船舶公司id
     */
    @ExcelProperty(value = "船舶公司id")
    private Long shippingId;

    /**
     * 船舶公司名称
     */
    @ExcelProperty(value = "船舶公司名称")
    private String shippingName;

    /**
     * 个人境内->手续费
     */
    @ExcelProperty(value = "个人境内->手续费")
    private BigDecimal personDomestic;

    /**
     * 个人境外->手续费
     */
    @ExcelProperty(value = "个人境外->手续费")
    private BigDecimal personAbroad;

    /**
     * 公司境内 ->手续费
     */
    @ExcelProperty(value = "公司境内 ->手续费")
    private BigDecimal companyDomestic;

    /**
     * 公司境外 ->手续费
     */
    @ExcelProperty(value = "公司境外 ->手续费")
    private BigDecimal companyAbroad;

    /**
     * 银行收取的手续费
     */
    @ExcelProperty(value = "银行收取的手续费")
    private BigDecimal bankCharges;

    /**
     * 银行退款手续费
     */
    @ExcelProperty(value = "银行退款手续费")
    private BigDecimal refundHandlingFee;

    /**
     * 即时可用余额
     */
    @ExcelProperty(value = "即时可用余额")
    private BigDecimal balance;

    /**
     * 冻结总金额
     */
    @ExcelProperty(value = "冻结总金额")
    private BigDecimal freeze;

    /**
     * 最大授信额度
     */
    @ExcelProperty(value = "最大授信额度")
    private BigDecimal maxCredit;

    /**
     * 已用授信额度
     */
    @ExcelProperty(value = "已用授信额度")
    private BigDecimal usedCredit;

    /**
     * 状态0正常 1封存 2待确认
     */
    @ExcelProperty(value = "状态")
    @ExcelDictFormat(readConverterExp = "0=正常,1=封存,2=待确认")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
