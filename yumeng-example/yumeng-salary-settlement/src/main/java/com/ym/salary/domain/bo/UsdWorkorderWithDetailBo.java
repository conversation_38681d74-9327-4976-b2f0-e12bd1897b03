package com.ym.salary.domain.bo;

import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 美金代发工单与明细业务对象
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UsdWorkorderWithDetailBo extends BaseEntity {

    /**
     * 工单ID
     */
    @NotNull(message = "工单ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 运输公司ID
     */
    private Long transportId;

    /**
     * 船舶公司ID
     */
    private Long shippingId;

    /**
     * 工单状态
     */
    private String workorderStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 工单明细列表
     */
    private List<UsdWorkorderDetailBo> details;
} 