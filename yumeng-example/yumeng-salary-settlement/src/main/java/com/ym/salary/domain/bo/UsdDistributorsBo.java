package com.ym.salary.domain.bo;

import com.ym.salary.domain.UsdDistributors;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 美元航运分销商业务对象 usd_distributors
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UsdDistributors.class, reverseConvertGenerate = false)
public class UsdDistributorsBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 航运id
     */
    @NotNull(message = "航运id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long transportId;

    /**
     * 分销商id
     */
    @NotNull(message = "分销商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long distributorsId;

    /**
     * 美金代发账号
     */
    @NotBlank(message = "美金代发账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String account;

    /**
     * 个人境内->手续费
     */
    @NotNull(message = "个人境内->手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal personDomestic;

    /**
     * 个人境外->手续费
     */
    @NotNull(message = "个人境外->手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal personAbroad;

    /**
     * 公司境内->手续费
     */
    @NotNull(message = "公司境内->手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal companyDomestic;

    /**
     * 公司境外->手续费
     */
    @NotNull(message = "公司境外->手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal companyAbroad;

    /**
     * 银行收取的手续费
     */
    private BigDecimal bankCharges;

    /**
     * 即时可用余额，默认值为0，可能因欠账导致为负值
     */
    private BigDecimal balance;

    /**
     * 冻结金额，默认值为0
     */
    private BigDecimal freeze;

    /**
     * 最大授信额度，即欠账最大额度，默认值为0，修改时不得小于已用授信额度
     */
    @NotNull(message = "最大授信额度不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal maxCredit;

    /**
     * 已用授信额度（即欠账金额），默认值为0
     */
    @NotNull(message = "已用授信额度不能为空", groups = {EditGroup.class })
    private BigDecimal usedCredit;

    /**
     * 状态0正常 1封存 2待确认
     */
    private String status;

    /**
     * 备注
     */
    private String remark;


}
