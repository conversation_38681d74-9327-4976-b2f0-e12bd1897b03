package com.ym.salary.domain.vo;

import java.math.BigDecimal;
import com.ym.salary.domain.GrantChannel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 代发渠道 视图对象 grant_channel
 *
 * <AUTHOR>
 * @date 2025-05-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = GrantChannel.class)
public class GrantChannelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 渠道名
     */
    @ExcelProperty(value = "渠道名")
    private String channel;

    /**
     * 税点
     */
    @ExcelProperty(value = "税点")
    private BigDecimal taxPoint;

    /**
     * 是否有效
     */
    @ExcelProperty(value = "是否有效  ")
    private String enable;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
