package com.ym.salary.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

import java.io.Serial;

/**
 * 美金代发工单明细对象 usd_workorder_detail
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("usd_workorder_detail")
public class UsdWorkorderDetail extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联的美金工单id，必填
     */
    @TableField("workoder_id")
    private Long workoderId;

    /**
     * 船员id 或 企业id
     */
    private Long grantobjectId;

    /**
     * 船员姓名
     */
    private String grantobjectName;
    /**
     * 船员证件类型，枚举：1 身份证  2 护照
     */
    private String certType;
    /**
     * 船员证件号码
     */
    private String certNo;
    /**
     * 涉及交易的：船员或企业默认的银行卡账号id； 注意： 做关联查询的时候，银行卡删除状态不做过滤
     */
    private Long relatedAcountid;

    /**
     * 银行卡账号
     */
    private String accountNo;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 开户支行
     */
    private String depositBranch;

    /**
     * 持卡人姓名
     */
    private String accountName;

    /**
     * 持卡人拼音
     */
    private String accountPinyin;

    /**
     * 职务
     */
    private String job;

    /**
     * 发放金额（USD）
     */
    private BigDecimal grantAmount;

    /**
     * 发放类型-手续费类型; 1 个人境内   2 个人境外  3 公司境内  4 公司境外
     */
    private String grantType;

    /**
     * 发放手续费金额(USD)
     */
    private BigDecimal grantFee;

    /**
     * 重发次数，默认为0
     */
    private String resendTimes;

    /**
     * 调账金额，可能是负值
     */
    private BigDecimal adjustAmount;

    /**
     * swiftcode值
     */
    private String swiftCode;

    /**
     * 所在船舶，非必填
     */
    private String ship;

    /**
     * 发放状态  0-成功   1-失败  2-待发放
     */
    private String grantStatus;
    /**
     * 异常类型
     */
    private String exceptionType;
    /**
     * 异常原因
     */
    private String exceptionReason;
    /**
     * 异常补充材料
     */
    private String fileId;
    /**
     * 审核状态
     */
    private Integer verifyStatus;
    /**
     * 银行手续费
     */
    private BigDecimal bankCharges;

    /**
     *     return_fee       decimal(10, 2)         null comment '退汇行收费',
     *     fee_payertype    varchar(1)             null comment '费用支付方类型 1航运，2船舶 3个人',
     */
    /**
     * 退汇行收费
     */
    private BigDecimal returnFee;
    /**
     * 费用支付方类型 1航运，2船舶 3个人
     */
    @TableField("fee_payertype")
    private String feePayertype;
    /**
     * 信息错误是否修改 0未修改 1已修
     */

    private String errorCorrection;


    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识
     */
    @TableLogic
    private Long delFlag;

}
