package com.ym.salary.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.salary.domain.AuthCode;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 授权码视图对象 auth_code
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AuthCode.class)
public class AuthCodeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 授权码
     */
    @ExcelProperty(value = "授权码")
    private String code;


    /**
     * 生成人ID
     */
    @ExcelProperty(value = "生成人ID")
    private Long generatorId;
    @ExcelProperty(value = "生成人名称")
    private String generatorName;

    /**
     * 金额
     */
    @ExcelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    private Date expireTime;

    /**
     * 原目标配置ID
     */
    @ExcelProperty(value = "原目标配置ID")
    private Long sourceId;

    /**
     * 目标配置ID
     */
    @ExcelProperty(value = "目标配置ID")
    private Long targetId;

    /**
     * 原公司流水ID
     */
    @ExcelProperty(value = "原公司流水ID")
    private Long sourceFlowId;

    /**
     * 目标公司流水ID
     */
    @ExcelProperty(value = "目标公司流水ID")
    private Long targetFlowId;

    /**
     * （0未使用 1已使用 2已过期 3待确认）
     */
    @ExcelProperty(value = "状态")
    private Long status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
