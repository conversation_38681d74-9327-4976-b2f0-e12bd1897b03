package com.ym.salary.util;

import com.ym.common.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Random;

/**
 * 验证码工具类
 * 提供验证码的生成、存储和验证功能
 * 验证码过期时间为5分钟
 *
 * <AUTHOR>
 */
@Slf4j
public class VerificationCodeUtil {

    /**
     * 验证码Redis键前缀
     */
    private static final String VERIFICATION_CODE_KEY_PREFIX = "verification_code:";

    /**
     * 验证码过期时间（分钟）
     */
    private static final int CODE_EXPIRE_MINUTES = 5;

    /**
     * 验证码类型枚举
     */
    public enum CodeType {
        /**
         * 数字验证码
         */
        NUMBER,
        
        /**
         * 字母验证码
         */
        LETTER,
        
        /**
         * 混合验证码（数字+字母）
         */
        MIXED
    }

    /**
     * 默认验证码长度
     */
    private static final int DEFAULT_CODE_LENGTH = 6;

    /**
     * 随机数生成器
     */
    private static final Random RANDOM = new Random();
    
    /**
     * 生成验证码
     *
     * @param codeType 验证码类型
     * @return 生成的验证码
     */
    public static String generateCode(CodeType codeType) {
        return generateCode(codeType, DEFAULT_CODE_LENGTH);
    }

    /**
     * 生成验证码
     *
     * @param codeType 验证码类型
     * @param length 验证码长度
     * @return 生成的验证码
     */
    public static String generateCode(CodeType codeType, int length) {
        if (length <= 0) {
            length = DEFAULT_CODE_LENGTH;
        }
        
        StringBuilder code = new StringBuilder();
        
        switch (codeType) {
            case NUMBER:
                generateNumberCode(code, length);
                break;
            case LETTER:
                generateLetterCode(code, length);
                break;
            case MIXED:
                generateMixedCode(code, length);
                break;
            default:
                generateNumberCode(code, length);
        }
        
        return code.toString();
    }
    
    /**
     * 生成数字验证码
     *
     * @param code 验证码构建器
     * @param length 验证码长度
     */
    private static void generateNumberCode(StringBuilder code, int length) {
        for (int i = 0; i < length; i++) {
            code.append(RANDOM.nextInt(10));
        }
    }
    
    /**
     * 生成字母验证码
     *
     * @param code 验证码构建器
     * @param length 验证码长度
     */
    private static void generateLetterCode(StringBuilder code, int length) {
        for (int i = 0; i < length; i++) {
            // 0-25是26个字母，加上'A'或'a'的ASCII码得到大写或小写字母
            boolean isUpperCase = RANDOM.nextBoolean();
            char base = isUpperCase ? 'A' : 'a';
            code.append((char) (base + RANDOM.nextInt(26)));
        }
    }
    
    /**
     * 生成混合验证码（数字+字母）
     *
     * @param code 验证码构建器
     * @param length 验证码长度
     */
    private static void generateMixedCode(StringBuilder code, int length) {
        String characters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        for (int i = 0; i < length; i++) {
            code.append(characters.charAt(RANDOM.nextInt(characters.length())));
        }
    }
    
    /**
     * 生成验证码并存储到Redis
     *
     * @param type 业务类型
     * @param userId 用户ID
     * @param codeType 验证码类型
     * @return 生成的验证码
     */
    public static String generateAndStoreCode(String type, String userId, CodeType codeType) {
        return generateAndStoreCode(type, userId, codeType, DEFAULT_CODE_LENGTH);
    }
    
    /**
     * 生成验证码并存储到Redis
     *
     * @param type 业务类型
     * @param userId 用户ID
     * @param codeType 验证码类型
     * @param length 验证码长度
     * @return 生成的验证码
     */
    public static String generateAndStoreCode(String type, String userId, CodeType codeType, int length) {
        // 生成验证码
        String code = generateCode(codeType, length);
        
        // 存储到Redis
        storeCode(type, userId, code);
        
        return code;
    }
    
    /**
     * 将验证码存储到Redis
     *
     * @param type 业务类型
     * @param userId 用户ID
     * @param code 验证码
     */
    public static void storeCode(String type, String userId, String code) {
        // 构建Redis键，格式为: verification_code:业务类型:用户ID
        String key = buildKey(type, userId);
        
        // 存储验证码到Redis，设置过期时间为5分钟
        RedisUtils.setCacheObject(key, code, Duration.ofMinutes(CODE_EXPIRE_MINUTES));
        
        log.info("验证码已存储, key: {}, 过期时间: {}分钟", key, CODE_EXPIRE_MINUTES);
    }
    
    /**
     * 构建Redis键
     *
     * @param type 业务类型
     * @param userId 用户ID
     * @return Redis键
     */
    private static String buildKey(String type, String userId) {
        return VERIFICATION_CODE_KEY_PREFIX + type + ":" + userId;
    }
    
    /**
     * 验证码校验
     *
     * @param type 业务类型
     * @param userId 用户ID
     * @param inputCode 用户输入的验证码
     * @return 校验结果，true:校验通过；false:校验失败
     */
    public static boolean verifyCode(String type, String userId, String inputCode) {
        return verifyCode(type, userId, inputCode, true);
    }
    
    /**
     * 验证码校验
     *
     * @param type 业务类型
     * @param userId 用户ID
     * @param inputCode 用户输入的验证码
     * @param caseSensitive 是否区分大小写
     * @return 校验结果，true:校验通过；false:校验失败
     */
    public static boolean verifyCode(String type, String userId, String inputCode, boolean caseSensitive) {
        if (inputCode == null || inputCode.isEmpty()) {
            log.warn("验证失败: 输入的验证码为空");
            return false;
        }
        
        // 获取Redis中存储的验证码
        String key = buildKey(type, userId);
        String storedCode = RedisUtils.getCacheObject(key);
        
        // 验证码不存在或已过期
        if (storedCode == null || storedCode.isEmpty()) {
            log.warn("验证失败: 验证码不存在或已过期, key: {}", key);
            return false;
        }
        
        // 校验验证码
        boolean isValid;
        if (caseSensitive) {
            isValid = storedCode.equals(inputCode);
        } else {
            isValid = storedCode.equalsIgnoreCase(inputCode);
        }
        
        // 验证通过后，删除验证码
        if (isValid) {
            RedisUtils.deleteObject(key);
            log.info("验证码校验通过并已删除, key: {}", key);
        } else {
            log.warn("验证失败: 验证码不匹配, key: {}, 输入: {}, 实际: {}", key, inputCode, storedCode);
        }
        
        return isValid;
    }
    
    /**
     * 获取存储的验证码（不会删除）
     *
     * @param type 业务类型
     * @param userId 用户ID
     * @return 存储的验证码，如果验证码不存在或已过期，返回null
     */
    public static String getStoredCode(String type, String userId) {
        String key = buildKey(type, userId);
        return RedisUtils.getCacheObject(key);
    }
    
    /**
     * 删除存储的验证码
     *
     * @param type 业务类型
     * @param userId 用户ID
     */
    public static void removeCode(String type, String userId) {
        String key = buildKey(type, userId);
        RedisUtils.deleteObject(key);
        log.info("验证码已删除, key: {}", key);
    }
}