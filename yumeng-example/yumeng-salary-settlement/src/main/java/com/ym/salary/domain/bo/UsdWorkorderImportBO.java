package com.ym.salary.domain.bo;

import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 美金工单导入业务对象
 *
 */
@Data
public class UsdWorkorderImportBO extends BaseEntity {

    /**
     * 关联的美金工单id，必填
     */
    @NotNull(message = "关联的美金工单id不能为空", groups = { EditGroup.class })
    private Long workorderId;

    /**
     * Excel文件
     */
    @NotNull(message = "Excel文件不能为空", groups = { AddGroup.class })
    private MultipartFile file;

    /**
     * 船舶公司ID
     */
    private Long shippingId;

    /**
     * 航运公司ID
     */
    private Long transportId;

    /**
     * 更新现有记录
     */
    private Boolean updateExist;

}
