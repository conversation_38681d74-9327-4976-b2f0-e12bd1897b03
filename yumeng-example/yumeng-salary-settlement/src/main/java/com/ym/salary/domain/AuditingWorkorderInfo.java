package com.ym.salary.domain;

import com.ym.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 审批信息对象 auditing_workorder_info
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("auditing_workorder_info")
public class AuditingWorkorderInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 审批人Id
     */
    private Long userId;

    /**
     * 审批人姓名
     */
    private String userName;


    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 状态（1:审批中；2:审批通过；审批不通过:3）
     */
    private Integer status;

    /**
     * 审核意见
     */
    private String opinion;

    /**
     * 审批顺序
     */
    private Integer sort;

    /**
     * 是否待办【1，未办  2，已办】
     */
    private Integer doAction;

    /**
     * 删除标识
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核业务类型
     */
    private Integer businessType;
}
