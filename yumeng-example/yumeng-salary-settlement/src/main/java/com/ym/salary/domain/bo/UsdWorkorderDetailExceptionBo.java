package com.ym.salary.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 美金代发工单明细异常处理业务对象
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Data
public class UsdWorkorderDetailExceptionBo {

    @NotNull(message = "明细ID不能为空")
    private Long detailId;

    @NotBlank(message = "异常类型不能为空")
    private String type; // 1：信息错误，2：银行审核

    private String reason; // 异常原因（航运公司必填）

    private String fileId; // 异常补充材料ID

    private BigDecimal returnFee; // 退汇扣收金额（航运公司处理信息错误时必填）

    private BigDecimal refundFee; // 退款手续费（航运公司处理信息错误时必填）

    private String deductTarget; // 扣款对象：transport（航运公司）或shipping（船舶公司）（航运公司处理信息错误时必填）
}
