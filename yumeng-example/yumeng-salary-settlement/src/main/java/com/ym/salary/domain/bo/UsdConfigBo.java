package com.ym.salary.domain.bo;

import com.ym.salary.domain.UsdConfig;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 美金结算配置业务对象 usd_config
 *
 * <AUTHOR>
 * @date 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UsdConfig.class, reverseConvertGenerate = false)
public class UsdConfigBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 航运公司id
     */
    @NotNull(message = "航运公司id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long transportId;

    /**
     * 船舶公司id
     */
    private Long shippingId;

    /**
     * 个人境内->手续费
     */
    @NotNull(message = "个人境内->手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal personDomestic;

    /**
     * 个人境外->手续费
     */
    @NotNull(message = "个人境外->手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal personAbroad;

    /**
     * 公司境内 ->手续费
     */
    @NotNull(message = "公司境内 ->手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal companyDomestic;

    /**
     * 公司境外 ->手续费
     */
    @NotNull(message = "公司境外 ->手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal companyAbroad;

    /**
     * 银行收取的手续费
     */
    private BigDecimal bankCharges;
    /**
     * 银行退款手续费
     */
    private BigDecimal refundHandlingFee;

    /**
     * 即时可用余额
     */
    @NotNull(message = "即时可用余额不能为空", groups = {  EditGroup.class })
    private BigDecimal balance;

    /**
     * 冻结总金额
     */
    @NotNull(message = "冻结总金额不能为空", groups = {  EditGroup.class })
    private BigDecimal freeze;

    /**
     * 最大授信额度
     */
    @NotNull(message = "最大授信额度不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal maxCredit;

    /**
     * 已用授信额度
     */
    @NotNull(message = "已用授信额度不能为空", groups = { EditGroup.class })
    private BigDecimal usedCredit;

    /**
     * 状态0正常 1封存 2待确认
     */
    private String status;

    /**
     * 备注
     */
    private String remark;


}
