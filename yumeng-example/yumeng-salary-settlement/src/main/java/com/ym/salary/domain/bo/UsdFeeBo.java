package com.ym.salary.domain.bo;

import com.ym.salary.domain.UsdFee;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 美金代发手续费记录 业务对象 usd_fee
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UsdFee.class, reverseConvertGenerate = false)
public class UsdFeeBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 美金工单id，必填
     */
    @NotNull(message = "美金工单id，必填不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long workoderId;

    /**
     * 美金工单明细id，必填
     */
    @NotNull(message = "美金工单明细id，必填不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long workoderDetailId;

    /**
     * 手续费
     */
    @NotNull(message = "手续费不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal fee;

    /**
     * 费用支付方，三类：船舶公司名字、航运公司名字、个人名称
     */
    @NotBlank(message = "费用支付方，三类：船舶公司名字、航运公司名字、个人名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String feePayer;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
