package com.ym.salary.domain.bo;

import com.ym.salary.domain.UsdFlow;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 美金交易流水业务对象 usd_flow
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UsdFlow.class, reverseConvertGenerate = false)
public class UsdFlowBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 航运公司id
     */
    @NotNull(message = "航运公司id不能为空", groups = {EditGroup.class })
    private Long transportId;

    /**
     * 船舶公司id
     */
    @NotNull(message = "船舶公司id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long shippingId;

    /**
     * 交易类型
     */
    @NotBlank(message = "交易类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String transactionType;

    /**
     * 交易金额
     */
    @NotNull(message = "交易金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal transactionAmount;

    /**
     * 交易时间
     */
    @NotNull(message = "交易时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date transactionTime;

    /**
     * 关联工单id
     */
    @NotNull(message = "关联工单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long usdworkoderId;

    /**
     * 账户余额
     */
    @NotNull(message = "账户余额不能为空", groups = { EditGroup.class })
    private BigDecimal accountBalance;

    /**
     * 交易用途
     */
    @NotBlank(message = "交易用途不能为空", groups = { AddGroup.class, EditGroup.class })
    private String purpose;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

}
