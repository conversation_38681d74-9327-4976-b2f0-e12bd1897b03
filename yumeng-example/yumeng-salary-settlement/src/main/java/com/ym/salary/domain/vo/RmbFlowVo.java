package com.ym.salary.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.salary.domain.RmbFlow;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 人民币交易流水视图对象 rmb_flow
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RmbFlow.class)
public class RmbFlowVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 航运公司id
     */
    @ExcelProperty(value = "航运公司id")
    private Long transportId;

    /**
     * 船舶公司id
     */
    @ExcelProperty(value = "船舶公司id")
    private Long shippingId;

    /**
     * 交易类型    1、出账   2、入账
     */
    @ExcelProperty(value = "交易类型    1、出账   2、入账")
    private String transactionType;

    /**
     * 交交易总金额，举例如下：
     */
    @ExcelProperty(value = "交易金额")
    private BigDecimal transactionAmount;

    /**
     * 交易时间,也就是创建时间
     */
    @ExcelProperty(value = "交易时间,也就是创建时间")
    private Date transactionTime;

    /**
     * 关联的人民币工单id
     */
    @ExcelProperty(value = "关联的人民币工单id ")
    private Long usdworkoderId;

    /**
     * 船舶公司人民币余额
     */
    @ExcelProperty(value = "船舶公司人民币余额")
    private BigDecimal accountBalance;

    /**
     * 交易用途
     */
    @ExcelProperty(value = "交易用途")
    private String purpose;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
