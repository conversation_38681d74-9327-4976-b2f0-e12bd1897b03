package com.ym.salary.domain.bo;

import com.ym.salary.domain.AuditingWorkorderInfo;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 审批信息业务对象 auditing_workorder_info
 *
 * <AUTHOR>
 * @date 2025-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AuditingWorkorderInfo.class, reverseConvertGenerate = false)
public class AuditingWorkorderInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 审批人Id
     */
    @NotNull(message = "审批人Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 审批人姓名
     */
    @NotBlank(message = "审批人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long businessId;

    /**
     * 状态（1:审批中；2:审批通过；审批不通过:3）
     */
    @NotNull(message = "状态（1:审批中；2:审批通过；审批不通过:3）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer status;

    /**
     * 审核意见
     */
    @NotBlank(message = "审核意见不能为空", groups = { AddGroup.class, EditGroup.class })
    private String opinion;

    /**
     * 审批顺序
     */
    @NotNull(message = "审批顺序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer sort;

    /**
     * 是否待办【1，未办  2，已办】
     */
    @NotNull(message = "是否待办【1，未办  2，已办】不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer doAction;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;
    /**
     * 审核业务类型
     */
    private Integer businessType;


}
