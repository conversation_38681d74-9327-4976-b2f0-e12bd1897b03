package com.ym.salary.service;

import com.ym.salary.domain.AuthCode;
import com.ym.salary.domain.vo.AuthCodeVo;
import com.ym.salary.domain.bo.AuthCodeBo;
import com.ym.common.mybatis.core.page.TableDataInfo;
import com.ym.common.mybatis.core.page.PageQuery;
import com.ym.common.core.domain.R;

import java.util.Collection;
import java.util.List;

/**
 * 授权码Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IAuthCodeService {

    /**
     * 查询授权码
     *
     * @param id 主键
     * @return 授权码
     */
    AuthCodeVo queryById(Long id);

    /**
     * 分页查询授权码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 授权码分页列表
     */
    TableDataInfo<AuthCodeVo> queryPageList(AuthCodeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的授权码列表
     *
     * @param bo 查询条件
     * @return 授权码列表
     */
    List<AuthCodeVo> queryList(AuthCodeBo bo);

    /**
     * 新增授权码
     *
     * @param bo 授权码
     * @return 是否新增成功
     */
    Boolean insertByBo(AuthCodeBo bo);

    /**
     * 修改授权码
     *
     * @param bo 授权码
     * @return 是否修改成功
     */
    Boolean updateByBo(AuthCodeBo bo);

    /**
     * 校验并批量删除授权码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
    
    /**
     * 验证授权码
     *
     * @param code 授权码
     * @return 授权码是否有效
     */
    Boolean verifyAuthCode(String code);
    
    /**
     * 根据授权码获取授权码信息
     *
     * @param code 授权码
     * @return 授权码信息
     */
    AuthCodeVo getAuthCodeByCode(String code);
    
    /**
     * 使用授权码
     *
     * @param code 授权码
     * @return 是否使用成功
     */
    Boolean useAuthCode(String code,Long targetId);

    /**
     * 确认授权码转账
     *
     * @param id 授权码ID
     * @return 是否确认成功
     */
    Boolean confirmAuthCode(Long id);
}
