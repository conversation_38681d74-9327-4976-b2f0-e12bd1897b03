package com.ym.salary.domain.bo;

import com.ym.salary.domain.GrantChannel;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 代发渠道 业务对象 grant_channel
 *
 * <AUTHOR>
 * @date 2025-05-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = GrantChannel.class, reverseConvertGenerate = false)
public class GrantChannelBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 渠道名
     */
    @NotBlank(message = "渠道名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channel;

    /**
     * 税点
     */
    @NotNull(message = "税点不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal taxPoint;

    /**
     * 是否有效  
     */
    @NotBlank(message = "是否有效  不能为空", groups = { AddGroup.class, EditGroup.class })
    private String enable;

    /**
     * 备注
     */
    private String remark;


}
