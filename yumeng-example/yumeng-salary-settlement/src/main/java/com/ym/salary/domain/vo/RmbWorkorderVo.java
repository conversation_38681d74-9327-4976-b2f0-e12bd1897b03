package com.ym.salary.domain.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.salary.domain.RmbWorkorder;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 人民币代发工单视图对象 rmb_workorder
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RmbWorkorder.class)
public class RmbWorkorderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 航运公司id
     */
    @ExcelProperty(value = "航运公司id")
    private Long transportId;
    @ExcelProperty(value = "航运公司名字")
    private String transportName;
    /**
     * 船舶公司id
     */
    @ExcelProperty(value = "船舶公司id")
    private Long shippingId;
    @ExcelProperty(value = "船舶公司名字")
    private String shippingName;

    /**
     * 建单人id（属于船舶公司）
     */
    @ExcelProperty(value = "建单人id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "属=于船舶公司")
    private Long createrId;
    @ExcelProperty(value = "建单人名字")
    private String createrName;

    /**
     * 当前处理人id（工单的一个步骤）（属于航运公司）
     */
    @ExcelProperty(value = "当前处理人id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "工=单的一个步骤")
    private Long processId;
    @ExcelProperty(value = "当前处理人名字")
    private String processName;

    /**
     * 当前处理时间（工单的一个步骤）
     */
    @ExcelProperty(value = "当前处理时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "工=单的一个步骤")
    private Date processTime;

    /**
     * 工资总额
     */
    @ExcelProperty(value = "工资总额")
    private BigDecimal salary;

    /**
     * 税金总额
     */
    @ExcelProperty(value = "税金总额")
    private BigDecimal taxes;

    /**
     * 派遣服务费总额
     */
    @ExcelProperty(value = "派遣服务费总额")
    private BigDecimal dispatchFee;

    /**
     * 社保服务费总额
     */
    @ExcelProperty(value = "社保服务费总额")
    private BigDecimal securityFee;

    /**
     * 本工单的冻结金额
     */
    @ExcelProperty(value = "本工单的冻结金额")
    private BigDecimal freeze;

    /**
     * 本工单使用的授信金额
     */
    @ExcelProperty(value = "本工单使用的授信金额")
    private BigDecimal usedCredit;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
    @ExcelProperty(value = "工单笔数")
    private Integer count;

    /**
     * 工单状态
     */
    @ExcelProperty(value = "工单状态")
    private String workorderStatus;

    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
