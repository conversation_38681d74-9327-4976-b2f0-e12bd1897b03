package com.ym.salary.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.salary.domain.UsdFlow;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;



/**
 * 美金交易流水视图对象 usd_flow
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UsdFlow.class)
public class UsdFlowVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 航运公司id
     */
    @ExcelProperty(value = "航运公司id")
    private Long transportId;
    private String transportName;

    /**
     * 船舶公司id
     */
    @ExcelProperty(value = "船舶公司id")
    private Long shippingId;
    private String shippingName;

    /**
     * 交易类型
     */
    @ExcelProperty(value = "交易类型")
    private String transactionType;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额")
    private BigDecimal transactionAmount;

    /**
     * 交易时间
     */
    @ExcelProperty(value = "交易时间")
    private Date transactionTime;

    /**
     * 关联工单id
     */
    @ExcelProperty(value = "关联工单id")
    private Long usdworkoderId;

    /**
     * 账户余额
     */
    @ExcelProperty(value = "账户余额")
    private BigDecimal accountBalance;

    /**
     * 交易用途
     */
    @ExcelProperty(value = "交易用途")
    private String purpose;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
