package com.ym.salary.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ExceptionHandleWorkorderBo {

    @NotNull(message = "工单ID不能为空")
    private Long workorderId;

    @NotBlank(message = "异常类型不能为空")
    private String type; // bank：银行问题，info：信息错误

    private String reason; // 异常原因（航运公司必填）

    private String attachmentUrl; // 上传的处理文件URL（船舶公司处理银行问题时必填）

    /**
     * 退汇扣收金额
     */
    private BigDecimal returnFee; // 退汇扣收金额（航运公司处理信息错误时必填）

    /**
     * 退款手续费
     */
    private BigDecimal refundFee; // 退款手续费（航运公司处理信息错误时必填）

    private String deductTarget; // 扣款对象：transport（航运公司）或shipping（船舶公司）（航运公司处理信息错误时必填）

    private String fileId; // 附件ID

    private Long detailId; // 失败的明细记录数组

    private String feePayertype; // 费用支付类型
}
