package com.ym.salary.domain.bo;

import com.ym.salary.domain.AuditingWorkorderConfig;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.List;

/**
 * 审批配置业务对象 auditing_workorder_config
 *
 * <AUTHOR>
 * @date 2025-05-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AuditingWorkorderConfig.class, reverseConvertGenerate = false)
public class AuditingWorkorderConfigBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 审核业务类型
     */
    private Integer businessType;

    /**
     * 审核人ID
     */
    private Long auditingUserId;

    /**
     * 审批人姓名
     */
    private String auditingUserName;

    /**
     * 上一级审批人ID
     */
    private Long previousApproverId;

    /**
     * 上一级审批人姓名
     */
    private String previousApproverName;

    /**
     * 下一级审批人ID
     */
    private Long nextApproverId;

    /**
     * 下一级审批人姓名
     */
    private String nextApproverName;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 备注
     */
    private String remark;

    private List<AuditingUserConfigBo> auditingUserConfigBoList;
}
