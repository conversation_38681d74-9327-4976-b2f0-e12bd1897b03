package com.ym.salary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 工资待发工单状态
 * @date 2025/5/24 21:11
 */
@Getter
@AllArgsConstructor
public enum WorkOrderStatus {

    TO_BE_SUBMITTED("0", "待提交"),
    SHIP_COMPANY_REJECTED("1", "船舶公司内部审批驳回"),
    SHIP_COMPANY_APPROVING("2", "船舶公司内部审批中"),
    TO_BE_CONFIRMED("3", "待确认"),
    SHIPPING_COMPANY_REJECTED("4", "航运公司内部审批驳回"),
    SHIPPING_COMPANY_APPROVING("5", "航运公司内部审批中"),
    IN_PROGRESS("6", "操作中"),
    TO_BE_FEEDBACK("7", "待反馈"),
    SHIPPING_COMPANY_REJECTED_FEEDBACK("8", "航运公司驳回"),
    COMPLETED("9", "处理完毕"),
    Feedback_Completed("10","反馈完毕"),
    Exception_Handling("11","异常待处理"),
    Exception_Handled("12","异常处理完毕");

    private final String code;
    private final String info;
}
