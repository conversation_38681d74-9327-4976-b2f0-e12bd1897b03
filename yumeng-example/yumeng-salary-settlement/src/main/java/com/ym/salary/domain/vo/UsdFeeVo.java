package com.ym.salary.domain.vo;

import java.math.BigDecimal;
import com.ym.salary.domain.UsdFee;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 美金代发手续费记录 视图对象 usd_fee
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UsdFee.class)
public class UsdFeeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 美金工单id，必填
     */
    @ExcelProperty(value = "美金工单id，必填")
    private Long workoderId;

    /**
     * 美金工单明细id，必填
     */
    @ExcelProperty(value = "美金工单明细id，必填")
    private Long workoderDetailId;

    /**
     * 手续费
     */
    @ExcelProperty(value = "手续费")
    private BigDecimal fee;

    /**
     * 费用支付方，三类：船舶公司名字、航运公司名字、个人名称
     */
    @ExcelProperty(value = "费用支付方，三类：船舶公司名字、航运公司名字、个人名称")
    private String feePayer;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
