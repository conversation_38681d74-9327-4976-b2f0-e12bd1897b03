package com.ym.salary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 验证码操作类型枚举
 * 
 * <AUTHOR>
 * @date 2025/7/16
 */
@Getter
@AllArgsConstructor
public enum VerificationCodeType {
    
    /**
     * 美金资金内部划拨
     */
    USD_FUND_INTERNAL_TRANSFER("1","USD_TRANSFER", "美金资金内部划拨");

    /**
     * 操作码类型编码
     */
    private final String code;
    /**
     * 操作码类型
     */
    private final String type;
    
    /**
     * 操作码类型描述
     */
    private final String info;
} 