package com.ym.salary.domain.bo;

import com.ym.salary.domain.RmbConfig;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 人民币结算配置业务对象 rmb_config
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = RmbConfig.class, reverseConvertGenerate = false)
public class RmbConfigBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 航运公司id
     */
    @NotNull(message = "航运公司id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long transportId;

    /**
     * 船舶公司id
     */
    @NotNull(message = "船舶公司id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long shippingId;

    /**
     * 个人境内服务费 - 派遣服务费
     */
    @NotNull(message = "个人境内服务费不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long personServicefee;

    /**
     * 个人境内
     */
    @NotNull(message = "个人境内不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long personManagefee;

    /**
     *  公司境内
     */
    @NotNull(message = " 公司境内不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long companyServicefee;

    /**
     *  公司境内
     */
    @NotNull(message = " 公司境内不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long companyManagefee;

    /**
     * 即时可用余额
     */
    @NotNull(message = "即时可用余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal balance;

    /**
     * 冻结总金额
     */
    @NotNull(message = "冻结总金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal freeze;

    /**
     * 最大授信额度，即欠账最大额度，默认值为0，修改时不得小于已用授信额度
     */
    @NotNull(message = "最大授信额度不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal maxCredit;

    /**
     * 已用授信额度（即欠账金额），默认值为0
     */
    @NotNull(message = "已用授信额度不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal usedCredit;

    /**
     * 状态0正常 1封存 2待确认
     */
    private String status;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
