package com.ym.salary.domain.bo;

import com.ym.salary.domain.UsdWorkorder;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * 美金代发工单业务对象 usd_workorder
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UsdWorkorder.class, reverseConvertGenerate = false)
public class UsdWorkorderBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 航运公司
     */
    @NotNull(message = "航运公司不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long transportId;

    /**
     * 船舶公司
     */
    @NotNull(message = "船舶公司不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long shippingId;

    /**
     * 建单人
     */
    @NotNull(message = "建单人不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createrId;

    /**
     * 当前处理人
     */
    @NotNull(message = "当前处理人不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long processId;

    /**
     * 当前处理时间
     */
    @NotNull(message = "当前处理时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date processTime;

    /**
     * 工资总额
     */
    @NotNull(message = "工资总额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal salary;

    /**
     * 手续费总额
     */
    @NotNull(message = "手续费总额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal commission;

    /**
     * 冻结金额
     */
    @NotNull(message = "冻结金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal freeze;

    /**
     * 已用授信金额
     */
    @NotNull(message = "已用授信金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal usedCredit;
    /**
     * 工单状态
     */
    private String workorderStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

}
