package com.ym.salary.domain.bo;

import com.ym.salary.domain.RmbFlow;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 人民币交易流水业务对象 rmb_flow
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = RmbFlow.class, reverseConvertGenerate = false)
public class RmbFlowBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 航运公司id
     */
    @NotNull(message = "航运公司id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long transportId;

    /**
     * 船舶公司id
     */
    @NotNull(message = "船舶公司id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long shippingId;

    /**
     * 交易类型    1、出账   2、入账
     */
    @NotBlank(message = "交易类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String transactionType;

    /**
     * 交交易总金额，举例如下：
     */
    @NotNull(message = "交易总金额，", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal transactionAmount;

    /**
     * 交易时间,也就是创建时间
     */
    @NotNull(message = "交易时间,也就是创建时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date transactionTime;

    /**
     * 关联的人民币工单id
     */
    @NotNull(message = "关联的人民币工单id 不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long usdworkoderId;

    /**
     * 船舶公司人民币余额
     */
    //@NotNull(message = "船舶公司人民币余额不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal accountBalance;

    /**
     * 交易用途 1.工资 2.税金3.社保服务费4.派遣服务费
     */
    @NotBlank(message = "交易用途不能为空", groups = {AddGroup.class, EditGroup.class})
    private String purpose;

    /**
     * 备注
     */
    //@NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;


}
