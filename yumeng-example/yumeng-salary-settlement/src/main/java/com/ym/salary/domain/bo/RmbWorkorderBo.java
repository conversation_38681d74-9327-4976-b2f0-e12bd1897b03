package com.ym.salary.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.salary.domain.RmbWorkorder;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 人民币代发工单业务对象 rmb_workorder
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = RmbWorkorder.class, reverseConvertGenerate = false)
public class RmbWorkorderBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 航运公司id
     */
    @NotNull(message = "航运公司id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long transportId;
    @ExcelProperty(value = "航运公司名字")
    private String transportName;

    /**
     * 船舶公司id
     */
    @NotNull(message = "船舶公司id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long shippingId;

    @ExcelProperty(value = "船舶公司名字")
    private String shippingName;

    /**
     * 建单人id（属于船舶公司）
     */
    @NotNull(message = "建单人id（属于船舶公司）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createrId;

    @ExcelProperty(value = "建单人名字")
    private String createrName;

    /**
     * 当前处理人id（工单的一个步骤）（属于航运公司）
     */
    @NotNull(message = "当前处理人id（工单的一个步骤）（属于航运公司）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long processId;

    @ExcelProperty(value = "当前处理人名字")
    private String processName;

    /**
     * 当前处理时间（工单的一个步骤）
     */
    @NotNull(message = "当前处理时间（工单的一个步骤）不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd-HH:mm:ss")
    private Date processTime;

    /**
     * 工资总额
     */
    @NotNull(message = "工资总额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal salary;

    /**
     * 税金总额
     */
    @NotNull(message = "税金总额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal taxes;

    /**
     * 派遣服务费总额
     */
    @NotNull(message = "派遣服务费总额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal dispatchFee;

    /**
     * 社保服务费总额
     */
    @NotNull(message = "社保服务费总额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal securityFee;

    /**
     * 本工单的冻结金额
     */
    @NotNull(message = "本工单的冻结金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal freeze;

    /**
     * 本工单使用的授信金额
     */
    @NotNull(message = "本工单使用的授信金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal usedCredit;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    /**
     * 工单状态
     */
    private String workorderStatus;

    /**
     * 开始时间
     */
    private String beginTime;
    /**
     * 结束时间
     */
    private String endTime;

}
