package com.ym.salary.domain.bo;

import com.ym.salary.domain.AuthCode;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 授权码业务对象 auth_code
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AuthCode.class, reverseConvertGenerate = false)
public class AuthCodeBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 授权码
     */
    @NotBlank(message = "授权码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 生成人ID
     */
    @NotNull(message = "生成人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long generatorId;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 过期时间
     */
    @NotNull(message = "过期时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date expireTime;

    /**
     * 原目标配置ID
     */
    @NotNull(message = "原目标配置ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sourceId;

    /**
     * 目标配置ID
     */
    private Long targetId;

    /**
     * 原公司流水ID
     */
    private Long sourceFlowId;

    /**
     * 目标公司流水ID
     */
    private Long targetFlowId;
    /**
     * （0未使用 1已使用 2已过期 3待确认）
     */
    @NotNull(message = "状态", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * 备注
     */
    private String remark;


}
