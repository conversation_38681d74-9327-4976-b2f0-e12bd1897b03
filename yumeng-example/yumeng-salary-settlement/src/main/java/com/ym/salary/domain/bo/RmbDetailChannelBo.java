package com.ym.salary.domain.bo;

import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 人民币代发明细渠道业务对象 RmbDetailChannelBo
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RmbDetailChannelBo {

    /**
     * 人民币工单明细id
     */
    @NotNull(message = "明细ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long rmbdetailId;

    /**
     * 交易类型
     */
    @NotBlank(message = "交易类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String grantType;

    /**
     * 渠道列表
     */
    @NotEmpty(message = "渠道列表不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<ChannelInfo> channels;

    /**
     * 渠道信息
     */
    @Data
    public static class ChannelInfo {
        /**
         * 渠道ID
         */
        @NotNull(message = "渠道ID不能为空", groups = { AddGroup.class, EditGroup.class })
        private Long channelId;

        /**
         * 渠道金额
         */
        @NotNull(message = "渠道金额不能为空", groups = { AddGroup.class, EditGroup.class })
        @DecimalMin(value = "0", message = "渠道金额必须大于等于0", groups = { AddGroup.class, EditGroup.class })
        private BigDecimal channelAmount;
    }
} 