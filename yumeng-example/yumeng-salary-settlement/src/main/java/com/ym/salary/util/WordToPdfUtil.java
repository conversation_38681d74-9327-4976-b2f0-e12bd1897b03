package com.ym.salary.util;


import com.aspose.words.Document;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.HackLoopTableRenderPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

@Component
public class WordToPdfUtil {

    @Autowired
    private  ResourceLoader resourceLoader;
    /**
     * 许可证字符串
     */
    private static final String LICENSE_STR = "<License>" +
            "<Data>" +
            "<Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products>" +
            "<EditionType>Enterprise</EditionType>" +
            "<SubscriptionExpiry>20991231</SubscriptionExpiry>" +
            "<LicenseExpiry>20991231</LicenseExpiry>" +
            "<SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber>" +
            "</Data>" +
            "<Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature>" +
            "</License>";

    public  ByteArrayOutputStream wordToPdf(Map<String, Object> datas,String name) throws IOException {
        HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy();
        Configure config = Configure.newBuilder().bind("list", policy).build();
        // 加载 Word 模板
        Resource resource = resourceLoader.getResource("classpath:static/"+name);

        XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream(),config);
        // 填充数据到 Word 文档
        template.render(datas);
        ByteArrayOutputStream fos = new ByteArrayOutputStream();
        try {
            template.write(fos);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        byte[] bytes = fos.toByteArray();
        ByteArrayInputStream byteArrayInputStream  = new ByteArrayInputStream(bytes);
        return WordToPdfUtil.convert(byteArrayInputStream);
    }

    /**
     * word 转 pdf
     *
     * @param inputStream word文件路径
     */
    public static ByteArrayOutputStream convert(InputStream inputStream) {
        ByteArrayOutputStream fileOutputStream = new ByteArrayOutputStream();
        try {
            setLicense();
            Document doc = new Document(inputStream);
            doc.save(fileOutputStream, SaveFormat.PDF);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                assert fileOutputStream != null;
                fileOutputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        return fileOutputStream;
    }



    /**
     * 设置 license 去除水印
     */
    private static void setLicense() {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(LICENSE_STR.getBytes());
        License license = new License();
        try {
            license.setLicense(byteArrayInputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
