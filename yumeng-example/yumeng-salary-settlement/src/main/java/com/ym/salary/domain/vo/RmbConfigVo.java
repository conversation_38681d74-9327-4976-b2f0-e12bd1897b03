package com.ym.salary.domain.vo;

import java.math.BigDecimal;
import com.ym.salary.domain.RmbConfig;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 人民币结算配置视图对象 rmb_config
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RmbConfig.class)
public class RmbConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 航运公司id
     */
    @ExcelProperty(value = "航运公司id")
    private Long transportId;
    @ExcelProperty(value = "航运公司名称")
    private String transportName;

    /**
     * 船舶公司id
     */
    @ExcelProperty(value = "船舶公司id")
    private Long shippingId;
    @ExcelProperty(value = "船舶公司名称")
    private String shippingName;

    /**
     * 个人境内服务费 - 派遣服务费
     */
    @ExcelProperty(value = "个人境内服务费 - 派遣服务费")
    private Long personServicefee;

    /**
     * 个人境内
     */
    @ExcelProperty(value = "个人境内")
    private Long personManagefee;

    /**
     *  公司境内
     */
    @ExcelProperty(value = " 公司境内")
    private Long companyServicefee;

    /**
     *  公司境内
     */
    @ExcelProperty(value = " 公司境内")
    private Long companyManagefee;

    /**
     * 即时可用余额
     */
    @ExcelProperty(value = "即时可用余额")
    private BigDecimal balance;

    /**
     * 冻结总金额
     */
    @ExcelProperty(value = "冻结总金额")
    private BigDecimal freeze;

    /**
     * 最大授信额度，即欠账最大额度，默认值为0，修改时不得小于已用授信额度
     */
    @ExcelProperty(value = "最大授信额度，即欠账最大额度，默认值为0，修改时不得小于已用授信额度")
    private BigDecimal maxCredit;

    /**
     * 已用授信额度（即欠账金额），默认值为0
     */
    @ExcelProperty(value = "已用授信额度", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "即=欠账金额")
    private BigDecimal usedCredit;

    /**
     * 状态0正常 1封存 2待确认
     */
    @ExcelProperty(value = "状态")
    @ExcelDictFormat(readConverterExp = "0=正常,1=封存,2=待确认")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
