package com.ym.salary.domain.bo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ym.salary.domain.UsdWorkorderDetail;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 美金代发工单明细业务对象 usd_workorder_detail
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UsdWorkorderDetail.class, reverseConvertGenerate = false)
public class UsdWorkorderDetailBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 关联的美金工单id，必填
     */
    private Long workoderId;

    /**
     * 船员id 或 企业id
     */
    private Long grantobjectId;
    /**
     * 船员姓名
     */
    private String grantobjectName;
    /**
     * 船员证件类型，枚举：1 身份证  2 护照
     */
    private String certType;
    /**
     * 船员证件号码
     */
    private String certNo;

    /**
     * 涉及交易的：船员或企业默认的银行卡账号id； 注意： 做关联查询的时候，银行卡删除状态不做过滤
     */
    private Long relatedAcountid;
    /**
     * 银行卡账号
     */
    private String accountNo;
    /**
     * 开户行
     */
    private String bankName;

    /**
     * 开户支行
     */
    private String depositBranch;

    /**
     * 持卡人姓名
     */
    private String accountName;

    /**
     * 持卡人拼音
     */
    private String accountPinyin;

    /**
     * 职务
     */
    private String job;


    /**
     * 发放金额（USD）
     */
    @NotNull(message = "发放金额不能为空", groups = { AddGroup.class, EditGroup.class })
    @Digits(integer = 10, fraction = 2, message = "发放金额必须是有效的数字，最多10位整数和2位小数", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal grantAmount;

    /**
     * 发放类型-手续费类型; 1 个人境内   2 个人境外  3 公司境内  4 公司境外
     */
    private String grantType;

    /**
     * 发放手续费
     */
    @Digits(integer = 10, fraction = 2, message = "发放手续费必须是有效的数字，最多10位整数和2位小数", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal grantFee;

    /**
     * 调账金额，可能是负值
     */
    @Digits(integer = 10, fraction = 2, message = "调账金额必须是有效的数字，最多10位整数和2位小数", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal adjustAmount;

    /**
     * swiftcode值
     */
    private String swiftCode;

    /**
     * 所在船舶，非必填
     */
    private String ship;

    /**
     * 发放状态  0-成功   1-失败
     */
    private String grantStatus;
    /**
     * 异常类型
     */
    private String exceptionType;
    /**
     * 异常原因
     */
    private String exceptionReason;
    /**
     * 异常补充材料
     */
    private Long fileId;

    private Integer verifyStatus;

    /**
     * 备注
     */
    private String remark;
    @TableLogic
    private Long delFlag;
}
