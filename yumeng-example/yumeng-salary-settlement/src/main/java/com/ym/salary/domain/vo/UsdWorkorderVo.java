package com.ym.salary.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.salary.domain.UsdWorkorder;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 美金代发工单视图对象 usd_workorder
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UsdWorkorder.class)
public class UsdWorkorderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 航运公司
     */
    @ExcelProperty(value = "航运公司")
    private Long transportId;
    @ExcelProperty(value = "航运公司名字")
    private String transportName;

    /**
     * 船舶公司
     */
    @ExcelProperty(value = "船舶公司")
    private Long shippingId;
    @ExcelProperty(value = "船舶公司名字")
    private String shippingName;


    /**
     * 建单人
     */
    @ExcelProperty(value = "建单人")
    private Long createrId;

    @ExcelProperty(value = "建单人名字")
    private String createrName;
    /**
     * 当前处理人
     */
    @ExcelProperty(value = "当前处理人")
    private Long processId;

    @ExcelProperty(value = "当前处理人名字")
    private String processName;

    /**
     * 当前处理时间
     */
    @ExcelProperty(value = "当前处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd-HH:mm:ss")
    private Date processTime;

    /**
     * 工资总额
     */
    @ExcelProperty(value = "工资总额")
    private BigDecimal salary;

    /**
     * 手续费总额
     */
    @ExcelProperty(value = "手续费总额")
    private BigDecimal commission;

    /**
     * 冻结金额
     */
    @ExcelProperty(value = "冻结金额")
    private BigDecimal freeze;

    /**
     * 已用授信金额
     */
    @ExcelProperty(value = "已用授信金额")
    private BigDecimal usedCredit;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "工单状态")
    private String workorderStatus;

    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelProperty(value = "工单笔数")
    private Integer count;


}
