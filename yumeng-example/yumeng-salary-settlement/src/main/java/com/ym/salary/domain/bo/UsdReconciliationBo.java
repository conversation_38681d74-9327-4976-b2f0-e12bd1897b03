package com.ym.salary.domain.bo;

import com.ym.salary.domain.UsdReconciliation;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 美元对账单业务对象 usd_reconciliation
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UsdReconciliation.class, reverseConvertGenerate = false)
public class UsdReconciliationBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 航运公司id
     */
    @NotNull(message = "航运公司id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long transportId;

    /**
     * 船舶公司id
     */
    @NotNull(message = "船舶公司id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long shippingId;

    /**
     * 期初余额
     */
    @NotNull(message = "期初余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long openBalance;

    /**
     * 期末余额
     */
    @NotNull(message = "期末余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long closeBalance;

    /**
     * 本月收入金额
     */
    @NotNull(message = "本月收入金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long incomeAmount;

    /**
     * 本月支出金额
     */
    @NotNull(message = "本月支出金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long expenseAmount;

    /**
     * 对账周期（年月）
     */
    @NotNull(message = "对账周期（年月）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date period;

    /**
     * 确认状态（1-确认 0-未确认）
     */
    @NotBlank(message = "确认状态（1-确认 0-未确认）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 确认人（船舶公司）
     */
    @NotNull(message = "确认人（船舶公司）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long confirmer;


}
