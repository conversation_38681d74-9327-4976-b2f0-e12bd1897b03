package com.ym.salary.dubbo;

import com.ym.salary.api.RemoteUsdConfigService;
import com.ym.salary.domain.bo.UsdConfigBo;
import com.ym.salary.service.IUsdConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 配置服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteUsdConfigServiceImpl implements RemoteUsdConfigService {
    
    private final IUsdConfigService usdConfigService;
    
    /**
     * 在配置表中插入一条数据，其中航运id=船舶id
     * @param companyId 公司ID
     * @return 是否成功
     */
    @Override
    public boolean initTransportCompany(Long companyId) {
        if (companyId == null) {
            log.error("初始化配置失败：公司ID为空");
            return false;
        }
        
        try {
            // 创建美金配置
            UsdConfigBo usdConfigBo = new UsdConfigBo();
            // 设置航运ID和船舶ID都为companyId
            usdConfigBo.setTransportId(companyId);
            usdConfigBo.setShippingId(companyId);
            
            // 设置默认值
            usdConfigBo.setPersonDomestic(BigDecimal.ZERO);
            usdConfigBo.setPersonAbroad(BigDecimal.ZERO);
            usdConfigBo.setCompanyDomestic(BigDecimal.ZERO);
            usdConfigBo.setCompanyAbroad(BigDecimal.ZERO);
            usdConfigBo.setBankCharges(BigDecimal.ZERO);
            usdConfigBo.setRefundHandlingFee(BigDecimal.ZERO);
            usdConfigBo.setBalance(BigDecimal.ZERO);
            usdConfigBo.setFreeze(BigDecimal.ZERO);
            usdConfigBo.setMaxCredit(BigDecimal.ZERO);
            usdConfigBo.setUsedCredit(BigDecimal.ZERO);
            usdConfigBo.setRemark("系统自动初始化");
            
            // 插入美金配置
            Boolean usdResult = usdConfigService.insertByBo(usdConfigBo);
            
            log.info("初始化美金配置{}：航运ID=船舶ID={}", usdResult ? "成功" : "失败", companyId);
            return usdResult;
        } catch (Exception e) {
            log.error("初始化配置异常: {}", e.getMessage(), e);
            return false;
        }
    }

}