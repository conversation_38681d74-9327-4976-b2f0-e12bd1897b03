package com.ym.system.api;

/**
 * 待办任务服务
 *
 * <AUTHOR>
 */
public interface RemoteTodoInfoService {
    
    /**
     * 根据权限创建待办任务
     *
     * @param userId 提交人ID
     * @param userName 提交人名称（公司名称）
     * @param examineUserCompanyId 目标人公司id
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param msg 提示消息
     * @param permission 权限
     * @return 是否创建成功
     */
    boolean createHasPermissionTodoInfo(Long userId, String userName,Long examineUserCompanyId, Long businessId, Long businessType, String msg ,String  permission);
    
    /**
     * 更新待办任务状态
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param permission 权限
     * @return 是否更新成功
     */
    boolean updateTodoStatus(Long businessId, Long businessType,String permission);
} 