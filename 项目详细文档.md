# 船舶综合管理系统API项目详细文档

## 项目概述

### 项目基本信息
- **项目名称**: ship-Integrated-management-api
- **项目描述**: 船舶综合管理系统微服务API
- **技术架构**: Spring Cloud Alibaba微服务架构
- **开发语言**: Java
- **构建工具**: Maven
- **版本管理**: Git

### 项目特点
- 基于Spring Cloud Alibaba的微服务架构
- 采用Nacos作为服务注册中心和配置中心
- 使用Dubbo进行服务间通信
- 集成多种中间件：Redis、MySQL、RabbitMQ等
- 支持多租户、数据权限、分布式事务等企业级特性

## 技术栈

### 核心框架
- **Spring Boot**: 3.2.11
- **Spring Cloud Alibaba**: 2023.x
- **Dubbo**: 3.x
- **Nacos**: 2.2.2
- **Seata**: 分布式事务
- **Sentinel**: 流量控制和熔断降级

### 数据存储
- **MySQL**: 8.0.33 - 主数据库
- **Redis**: 6.2.12 - 缓存和分布式锁
- **MyBatis-Plus**: ORM框架

### 中间件
- **RabbitMQ**: 消息队列
- **Kafka**: 大数据量消息处理
- **RocketMQ**: 分布式消息
- **Elasticsearch**: 日志分析和搜索

### 监控运维
- **Skywalking**: 分布式链路追踪
- **Prometheus**: 监控指标收集
- **Grafana**: 监控数据可视化
- **ELK Stack**: 日志收集和分析

### 开发工具
- **Knife4j**: API文档生成
- **XXL-JOB**: 分布式任务调度
- **Sa-Token**: 权限认证框架

## 项目架构

### 微服务模块结构

```
ship-Integrated-management-api/
├── yumeng-auth/                    # 认证授权中心
├── yumeng-gateway/                 # 网关服务
├── yumeng-modules/                 # 业务模块
│   ├── yumeng-system/             # 系统管理模块
│   ├── yumeng-gen/                # 代码生成模块
│   ├── yumeng-job/                # 定时任务模块
│   ├── yumeng-resource/           # 资源管理模块
│   └── yumeng-workflow/           # 工作流模块
├── yumeng-example/                 # 示例模块
│   ├── yumeng-demo/               # 演示模块
│   ├── yumeng-test-mq/            # 消息队列测试
│   ├── yumeng-salary-settlement/  # 薪资结算模块
│   └── yumeng-warehouse-management/ # 仓库管理模块
├── yumeng-ai/                      # AI模块
│   ├── yumeng-chat/               # AI聊天服务
│   ├── yumeng-chat-api/           # 聊天API
│   └── yumeng-knowledge-api/      # 知识库API
├── yumeng-visual/                  # 可视化模块
│   ├── yumeng-monitor/            # 监控服务
│   ├── yumeng-nacos/              # Nacos服务
│   ├── yumeng-seata-server/       # Seata服务
│   ├── yumeng-sentinel-dashboard/ # Sentinel控制台
│   └── yumeng-snailjob-server/    # 任务调度服务
├── yumeng-ship-api/               # 船舶相关API
└── yumeng-client-api/             # 客户端API
```

### 服务间通信架构

```mermaid
graph TB
    A[前端应用] --> B[网关服务 yumeng-gateway:8080]
    B --> C[认证中心 yumeng-auth]
    B --> D[系统管理 yumeng-system]
    B --> E[资源管理 yumeng-resource]
    B --> F[工作流 yumeng-workflow]
    B --> G[代码生成 yumeng-gen]
    B --> H[AI聊天 yumeng-chat]
    
    C --> I[Nacos注册中心:8848]
    D --> I
    E --> I
    F --> I
    G --> I
    H --> I
    
    D --> J[MySQL数据库:3306]
    D --> K[Redis缓存:6379]
    E --> J
    F --> J
    
    L[Seata事务协调器:8091] --> J
    M[Sentinel控制台:8858] --> N[流量控制]
    O[XXL-JOB调度中心:8800] --> P[定时任务]
```

## 核心模块详解

### 1. 认证授权中心 (yumeng-auth)
**功能**: 统一认证授权、Token管理、用户登录
**端口**: 9200
**主要特性**:
- 基于Sa-Token的认证框架
- 支持多种登录方式
- JWT Token管理
- 权限验证和授权

### 2. 网关服务 (yumeng-gateway)
**功能**: 统一入口、路由转发、负载均衡、限流熔断
**端口**: 8080
**主要特性**:
- Spring Cloud Gateway
- 动态路由配置
- 请求日志记录
- 接口限流和熔断

### 3. 系统管理模块 (yumeng-system)
**功能**: 用户管理、角色管理、权限管理、部门管理、字典管理
**主要特性**:
- 用户CRUD操作
- 角色权限分配
- 部门层级管理
- 数据字典维护
- 多租户支持

### 4. 工作流模块 (yumeng-workflow)
**功能**: 流程定义、流程实例管理、任务处理
**主要特性**:
- 基于Activiti/Flowable
- 流程图设计
- 任务分配和处理
- 流程监控

### 5. AI模块 (yumeng-ai)
**功能**: AI聊天、知识库管理、智能问答
**主要特性**:
- 集成OpenAI API
- 聊天会话管理
- 知识库问答
- 模型配置管理

### 6. 薪资结算模块 (yumeng-salary-settlement)
**功能**: 薪资计算、工单管理、代发服务
**主要特性**:
- 人民币代发工单
- 美金代发工单
- 薪资计算引擎
- 银行账户管理

### 7. 仓库管理模块 (yumeng-warehouse-management)
**功能**: 仓库管理、库存管理、出入库管理
**主要特性**:
- 仓库信息管理
- 库存实时监控
- 出入库记录
- 库存预警

## 数据库设计

### 主要数据库
1. **yumeng-cloud**: 系统核心数据库
2. **yumeng-job**: 定时任务数据库
3. **yumeng-workflow**: 工作流数据库
4. **yumeng-salary**: 薪资结算数据库
5. **yumeng-warehouse**: 仓库管理数据库
6. **yumeng-seata**: 分布式事务数据库

### 核心表结构
- **sys_user**: 用户表
- **sys_role**: 角色表
- **sys_dept**: 部门表
- **sys_menu**: 菜单权限表
- **sys_dict_data**: 字典数据表
- **rmb_workorder**: 人民币代发工单表
- **usd_workorder**: 美金代发工单表
- **warehouse**: 仓库信息表

## 配置管理

### Nacos配置中心
- **地址**: localhost:8848
- **用户名/密码**: nacos/yumeng@2025!
- **配置分组**: DEFAULT_GROUP

### 主要配置文件
- `datasource.yml`: 数据源配置
- `yumeng-gateway.yml`: 网关路由配置
- `yumeng-ai.yml`: AI模块配置
- `redis.yml`: Redis配置
- `rabbitmq.yml`: RabbitMQ配置

## 部署架构

### Docker容器化部署
项目支持Docker容器化部署，主要服务包括：
- MySQL数据库容器
- Redis缓存容器
- Nacos注册中心容器
- 各微服务应用容器

### 环境配置
- **开发环境**: dev
- **生产环境**: prod
- **测试环境**: test

## 安全特性

### 认证授权
- 基于Sa-Token的权限框架
- JWT Token认证
- 细粒度权限控制
- 接口级权限验证

### 数据安全
- 敏感数据加密存储
- 数据权限过滤
- 多租户数据隔离
- API接口加密传输

### 系统安全
- 接口防重复提交
- 接口限流保护
- 参数校验和过滤
- 全局异常处理

## 监控运维

### 应用监控
- **Skywalking**: 分布式链路追踪
- **Prometheus**: 指标监控
- **Grafana**: 监控大盘

### 日志管理
- **ELK Stack**: 日志收集分析
- **Logback**: 应用日志框架
- 分布式日志追踪

### 健康检查
- Spring Boot Actuator
- 服务健康状态监控
- 自动故障恢复

## 开发规范

### 代码规范
- 统一的代码风格
- 完善的注释文档
- 规范的异常处理
- 统一的返回结果格式

### 接口规范
- RESTful API设计
- 统一的响应格式
- 完善的接口文档
- 版本控制支持

### 数据库规范
- 统一的命名规范
- 合理的索引设计
- 数据库版本管理
- 备份恢复策略

## 扩展特性

### 多租户支持
- 租户数据隔离
- 租户配置管理
- 动态租户切换

### 国际化支持
- 多语言消息
- 时区处理
- 本地化配置

### 缓存策略
- 多级缓存架构
- 缓存预热机制
- 缓存同步策略

## 性能优化

### 数据库优化
- 连接池配置优化
- SQL查询优化
- 索引优化策略

### 缓存优化
- Redis集群部署
- 缓存穿透防护
- 缓存雪崩防护

### 服务优化
- 异步处理机制
- 批量操作优化
- 资源池化管理

## API接口文档

### 认证相关接口
- `POST /auth/login` - 用户登录
- `POST /auth/logout` - 用户登出
- `POST /auth/refresh` - 刷新Token
- `GET /auth/info` - 获取用户信息

### 系统管理接口
- `GET /system/user/list` - 用户列表查询
- `POST /system/user` - 新增用户
- `PUT /system/user` - 修改用户
- `DELETE /system/user/{userIds}` - 删除用户
- `GET /system/role/list` - 角色列表查询
- `POST /system/role` - 新增角色
- `PUT /system/role` - 修改角色
- `DELETE /system/role/{roleIds}` - 删除角色

### 工作流接口
- `GET /workflow/process/list` - 流程定义列表
- `POST /workflow/process/start` - 启动流程实例
- `GET /workflow/task/list` - 任务列表查询
- `POST /workflow/task/complete` - 完成任务

### AI聊天接口
- `POST /ai/chat/send` - 发送聊天消息
- `GET /ai/chat/history` - 获取聊天历史
- `POST /ai/session/create` - 创建聊天会话
- `DELETE /ai/session/{sessionId}` - 删除聊天会话

### 薪资结算接口
- `GET /salary/rmb/workorder/list` - 人民币工单列表
- `POST /salary/rmb/workorder` - 创建人民币工单
- `PUT /salary/rmb/workorder` - 更新人民币工单
- `GET /salary/usd/workorder/list` - 美金工单列表
- `POST /salary/usd/workorder` - 创建美金工单

### 仓库管理接口
- `GET /warehouse/list` - 仓库列表查询
- `POST /warehouse` - 新增仓库
- `PUT /warehouse` - 修改仓库信息
- `DELETE /warehouse/{ids}` - 删除仓库
- `GET /warehouse/{id}` - 获取仓库详情

## 业务流程

### 用户登录流程
1. 用户提交登录信息到网关
2. 网关转发请求到认证中心
3. 认证中心验证用户凭据
4. 生成JWT Token并返回
5. 后续请求携带Token进行身份验证

### 工单处理流程
1. 用户创建工单申请
2. 系统启动工作流实例
3. 按流程节点分配任务
4. 相关人员处理任务
5. 完成所有节点后工单结束

### 薪资结算流程
1. 创建薪资结算工单
2. 录入员工薪资明细
3. 系统自动计算税费
4. 财务审核确认
5. 银行代发处理
6. 生成结算报告

## 常用注解说明

### 微服务注解
- `@EnableDubbo`: 启用Dubbo服务
- `@DubboService`: 标记服务提供者
- `@DubboReference`: 注入远程服务引用
- `@EnableDiscoveryClient`: 启用服务发现

### 权限安全注解
- `@SaCheckPermission`: 权限校验
- `@ApiEncrypt`: API加密传输
- `@EncryptField`: 字段加密存储
- `@RepeatSubmit`: 防重复提交

### 数据操作注解
- `@DS`: 动态数据源切换
- `@DataPermission`: 数据权限过滤
- `@Transactional`: 事务管理
- `@DSTransactional`: 多数据源事务

### 缓存注解
- `@Cacheable`: 缓存查询结果
- `@CacheEvict`: 清除缓存
- `@CachePut`: 更新缓存

### 限流熔断注解
- `@RateLimiter`: 接口限流
- `@SentinelResource`: Sentinel资源保护
- `@Idempotent`: 接口幂等性

### 定时任务注解
- `@Scheduled`: Spring定时任务
- `@XxlJob`: XXL-JOB分布式任务

## 配置文件详解

### application.yml核心配置
```yaml
server:
  port: 8080

spring:
  application:
    name: yumeng-gateway
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      discovery:
        server-addr: @nacos.server@
        group: @nacos.discovery.group@
        username: @nacos.username@
        password: @nacos.password@
      config:
        server-addr: @nacos.server@
        group: @nacos.config.group@
        username: @nacos.username@
        password: @nacos.password@
```

### 数据源配置示例
```yaml
spring:
  datasource:
    dynamic:
      primary: master
      strict: true
      datasource:
        master:
          type: com.zaxxer.hikari.HikariDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ****************************************
          username: root
          password: password
```

## 部署指南

### 本地开发环境搭建
1. 安装JDK 17+
2. 安装Maven 3.6+
3. 安装MySQL 8.0+
4. 安装Redis 6.0+
5. 安装Nacos 2.2.2
6. 导入项目并配置数据库连接
7. 启动Nacos服务
8. 依次启动各微服务

### Docker部署
```bash
# 构建镜像
docker build -t yumeng/yumeng-gateway:latest .

# 运行容器
docker run -d --name yumeng-gateway \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  yumeng/yumeng-gateway:latest
```

### 生产环境部署
1. 准备生产环境服务器
2. 安装Docker和Docker Compose
3. 配置生产环境参数
4. 使用docker-compose部署
5. 配置负载均衡和反向代理
6. 设置监控和日志收集

## 故障排查

### 常见问题
1. **服务注册失败**: 检查Nacos连接配置
2. **数据库连接异常**: 检查数据源配置和网络连通性
3. **Redis连接失败**: 检查Redis服务状态和配置
4. **接口调用超时**: 检查服务间网络和负载情况
5. **权限验证失败**: 检查Token有效性和权限配置

### 日志查看
- 应用日志位置: `./logs/{service-name}/`
- 错误日志: `error.log`
- 访问日志: `access.log`
- 系统日志: `system.log`

### 性能监控
- JVM监控: 通过Actuator端点查看
- 数据库监控: 通过Druid监控页面
- Redis监控: 通过Redis-cli命令
- 接口性能: 通过Skywalking链路追踪

## 开发指南

### 新增微服务步骤
1. 创建Maven模块
2. 配置pom.xml依赖
3. 创建启动类并添加注解
4. 配置application.yml
5. 实现业务逻辑
6. 添加到父pom.xml的modules中
7. 配置网关路由规则

### 代码生成使用
1. 访问代码生成模块
2. 导入数据库表结构
3. 配置生成参数
4. 生成代码文件
5. 集成到项目中

### 接口开发规范
1. 使用RESTful风格
2. 统一返回结果格式
3. 添加接口文档注解
4. 实现参数校验
5. 添加权限控制
6. 记录操作日志

## 版本更新记录

### v2.2.2 (当前版本)
- 升级Spring Boot到3.2.11
- 集成AI聊天功能
- 优化薪资结算流程
- 增强安全防护机制
- 完善监控体系

### 后续规划
- 支持Kubernetes部署
- 增加更多AI功能
- 优化性能和稳定性
- 完善文档和示例
- 增加单元测试覆盖率

## 联系方式

### 开发团队
- 项目负责人: luoming
- 技术架构师: [待补充]
- 开发工程师: [待补充]

### 技术支持
- 邮箱: <EMAIL>
- 文档地址: [待补充]
- 问题反馈: GitLab Issues

---

*本文档最后更新时间: 2025-08-01*
