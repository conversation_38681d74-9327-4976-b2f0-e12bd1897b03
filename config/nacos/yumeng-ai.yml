# yumeng-ai 模块配置

# AI 相关配置
ai:
  chat:
    # OpenAI 配置
    openai:
      api-host: https://api.openai.com
      api-key: your-openai-api-key
    # 其他 AI 服务配置
    services:
      # 可以添加其他 AI 服务配置
      default: openai

# 数据源配置
spring:
  datasource:
    dynamic:
      seata: false
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.ai.url}
          username: ${datasource.ai.username}
          password: ${datasource.ai.password}
