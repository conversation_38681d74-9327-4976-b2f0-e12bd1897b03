<template>
  <view class="container">
    <form @submit="submit">
      <view class="header">
        <button
          type="balanced"
          open-type="chooseAvatar"
          @chooseavatar="onChooseavatar"
        >
          <image :src="form.avatarUrl" class="avatar"></image>
        </button>
        <view class="phone">{{ form.phone }}</view>
      </view>
      <view class="content">
        <view class="form-item">
          <label>工作名称</label>
          <input
            type="nickname"
            :value="form.nickname"
            @blur="bindblur"
            placeholder-style="color: #c8c9cc;"
            placeholder="请输入你的工作名称"
            @input="bindinput"
          />
        </view>
      </view>

      <view class="submit-wrapper">
        <text class="submit" @tap="submit">保存</text>
      </view>
    </form>
  </view>
</template>
<script>
import { uploadAvatar } from "@/api/login";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      userName: "",
      sexs: [
        {
          text: "男",
        },
        {
          text: "女",
        },
      ],
      form: {
        avatarUrl: "",
        nickname: "",
        // sex: '',
        // birthday: '',
        phone: "",
      },
      oldAvaUrl: "",
      isAvatarFile: false,
      rules: {},
    };
  },
  onLoad(params) {
    this.form.phone = params.phone;
  },
  onShow() {},
  computed: {},
  methods: {
    // bindblur(e) {
    //   this.form.nickname = e.detail.value; // 获取微信昵称
    // },
    // bindinput(e) {
    //   this.form.nickname = e.detail.value; // 获取微信昵称
    // },
    onChooseavatar(e) {
      this.form.avatarUrl = e.detail.avatarUrl;
    },
    async submit() {
      let avatarUrl = null;
      if (this.isAvatarFile) {
        avatarUrl = this.form.avatarUrl;
      }
      const title = this.form.nickname;
      if (this.form.nickname === "") {
        this.$modal.msgError("请输入您工作名称！");
      } else if (this.form.avatarUrl === "") {
        this.$modal.msgError("请上传您的头像！");
      } else {
        let data = {
          name: "avatarfile",
          filePath: this.form.avatarUrl,
        };
        uploadAvatar(data).then((res) => {
          let params = {
            phoneNumber: this.form.phone,
            avatar: res.data.ossId,
            nickName: this.form.nickname,
            clientId: getApp().globalData.config.clientId,
            grantType: getApp().globalData.config.grantType,
          };
          this.$store
            .dispatch("Login", params)
            .then(() => {
              this.$modal.closeLoading();
              this.loginSuccess();
            })
            .catch(() => {
              if (this.captchaEnabled) {
                this.getCode();
              }
            });
        });
      }
    },
    // 登录成功后，处理函数
    loginSuccess(result) {
      // 设置用户信息
      this.$store.dispatch("GetInfo").then((res) => {
        this.$tab.reLaunch("/pages/index");
      });
    },
  },
};
</script>
<style>
.uni-date {
  width: 200rpx !important;
  flex: unset !important;
  text-align: right;
}
</style>
<style lang="less" scoped>
@theme-color: #55aaff;

.container {
  padding: 30rpx 20rpx;
}

.header,
.content {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
}

.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20rpx;

  button {
    margin: 0;
    padding: 0;
    background-color: #fff;
    width: 150rpx;
    height: 150rpx;

    .avatar {
      width: 100%;
      height: 100%;
    }
  }

  .phone {
    color: #666666;
    font-size: 28rpx;
  }
}

.content {
  margin-top: 25rpx;

  .form-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f9f9fa;

    input {
      text-align: right;
      width: 200rpx;
    }
  }

  .sex {
    display: flex;
    flex-direction: row;
    gap: 15rpx;

    text {
      padding: 10rpx 50rpx;
      background-color: #fff;
      border: 1rpx solid #a4adb3;
      border-radius: 100rpx;
    }

    .sex-active {
      background-color: @theme-color;
      color: #fff;
      border: 1rpx solid #fff;
    }
  }
}

.submit-wrapper {
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
  bottom: 150rpx;
  padding: 0 10%;
  text-align: center;
  color: #fff;

  .submit {
    display: block;
    width: 100%;
    background-color: @theme-color;
    padding: 25rpx 0;
    border-radius: 100rpx;
  }
}
</style>
