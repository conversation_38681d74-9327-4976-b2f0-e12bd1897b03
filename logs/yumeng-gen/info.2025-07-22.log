2025-07-22 08:41:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 08:41:39 [main] INFO  com.ym.gen.YmGenApplication - Starting YmGenApplication using Java 17.0.10 with PID 5752 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-gen\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 08:41:39 [main] INFO  com.ym.gen.YmGenApplication - The following 1 profile is active: "dev"
2025-07-22 08:41:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gen.yml, group=DEFAULT_GROUP] success
2025-07-22 08:41:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 08:41:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 08:41:43 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 08:41:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 08:41:43 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 08:41:44 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-07-22 08:41:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 08:41:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 08:41:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 08:41:45 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 08:41:45 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 08:41:45 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 08:41:46 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 08:41:46 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 08:41:52 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 08:41:52 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 08:41:52 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 08:41:52 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 08:41:52 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gen ************:9202 register finished
2025-07-22 08:41:54 [main] INFO  com.ym.gen.YmGenApplication - Started YmGenApplication in 17.022 seconds (process running for 17.968)
2025-07-22 08:41:54 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gen.yml, group=DEFAULT_GROUP
2025-07-22 08:41:54 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 08:41:54 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 08:41:55 [RMI TCP Connection(1)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 08:57:47 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145867781-38851913][thread:383][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_TYPE = ?)
]
[param:
param0=yumeng-salary(java.lang.String)
param1=BASE TABLE(java.lang.String)
];
2025-07-22 08:57:47 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145867781-38851913][thread:383][ds:default-master][action:select][执行耗时:00:00:00.069]
2025-07-22 08:57:47 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145867781-38851913][thread:383][ds:default-master][action:select][封装耗时:00:00:00.013][封装行数:18]
2025-07-22 08:57:47 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145867781-38851913][thread:383][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_TYPE = ?)
]
[param:
param0=yumeng-salary(java.lang.String)
param1=BASE TABLE(java.lang.String)
];
2025-07-22 08:57:47 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145867781-38851913][thread:383][ds:default-master][action:select][执行耗时:00:00:00.030]
2025-07-22 08:57:47 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145867781-38851913][thread:383][ds:default-master][action:select][封装耗时:00:00:00.002][封装行数:18]
2025-07-22 08:57:47 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145867781-38851913][thread:383][ds:default-master][tables][catalog:null][schema:SCHEMA:yumeng-salary][pattern:null][type:1][result:18][执行耗时:00:00:00.214]
2025-07-22 08:57:51 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145871134-73353653][thread:383][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_TYPE = ?)
]
[param:
param0=yumeng-salary(java.lang.String)
param1=BASE TABLE(java.lang.String)
];
2025-07-22 08:57:51 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145871134-73353653][thread:383][ds:default-master][action:select][执行耗时:00:00:00.036]
2025-07-22 08:57:51 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145871134-73353653][thread:383][ds:default-master][action:select][封装耗时:00:00:00.002][封装行数:18]
2025-07-22 08:57:51 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145871134-73353653][thread:383][ds:default-master][action:select][cmd:
SELECT * FROM information_schema.TABLES
WHERE (TABLE_SCHEMA = ? AND TABLE_TYPE = ?)
]
[param:
param0=yumeng-salary(java.lang.String)
param1=BASE TABLE(java.lang.String)
];
2025-07-22 08:57:51 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145871134-73353653][thread:383][ds:default-master][action:select][执行耗时:00:00:00.100]
2025-07-22 08:57:51 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145871134-73353653][thread:383][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:18]
2025-07-22 08:57:51 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145871134-73353653][thread:383][ds:default-master][tables][catalog:null][schema:SCHEMA:yumeng-salary][pattern:null][type:1][result:18][执行耗时:00:00:00.145]
2025-07-22 08:57:52 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145872235-90032122][thread:383][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.COLUMNS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY TABLE_NAME ASC, ORDINAL_POSITION ASC
]
[param:
param0=yumeng-salary(java.lang.String)
param1=auth_code(java.lang.String)
];
2025-07-22 08:57:52 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145872235-90032122][thread:383][ds:default-master][action:select][执行耗时:00:00:00.032]
2025-07-22 08:57:52 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145872235-90032122][thread:383][ds:default-master][action:select][封装耗时:00:00:00.001][封装行数:16]
2025-07-22 08:57:52 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145872235-90032122][thread:383][ds:default-master][columns][catalog:null][schema:SCHEMA:yumeng-salary][table:TABLE:auth_code][total:16][根据metadata解析:0][根据系统表查询:16][根据驱动内置接口补充:0][执行耗时:00:00:00.042]
2025-07-22 08:57:52 [XNIO-1 task-3] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753145872235-90032122][thread:383][ds:default-master][columns][catalog:null][schema:SCHEMA:yumeng-salary][table:TABLE:auth_code][total:16][根据metadata解析:0][根据系统表查询:16][根据根据驱动内置接口补充:0][执行耗时:00:00:00.042]
2025-07-22 08:57:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 08:57:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[135ms]
2025-07-22 08:59:04 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 08:59:04 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 08:59:04 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 08:59:04 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 08:59:04 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 08:59:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 08:59:04 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 08:59:05 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 08:59:05 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 08:59:05 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 08:59:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 08:59:13 [main] INFO  com.ym.gen.YmGenApplication - Starting YmGenApplication using Java 17.0.10 with PID 4088 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-gen\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 08:59:13 [main] INFO  com.ym.gen.YmGenApplication - The following 1 profile is active: "dev"
2025-07-22 08:59:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gen.yml, group=DEFAULT_GROUP] success
2025-07-22 08:59:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 08:59:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 08:59:17 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 08:59:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 08:59:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 08:59:18 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@595626b8
2025-07-22 08:59:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 08:59:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 08:59:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 08:59:19 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 08:59:20 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 08:59:20 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 08:59:20 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 08:59:21 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 08:59:27 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 08:59:27 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 08:59:27 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 08:59:27 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 08:59:27 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gen ************:9202 register finished
2025-07-22 08:59:29 [main] INFO  com.ym.gen.YmGenApplication - Started YmGenApplication in 17.664 seconds (process running for 19.123)
2025-07-22 08:59:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gen.yml, group=DEFAULT_GROUP
2025-07-22 08:59:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 08:59:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 08:59:29 [RMI TCP Connection(2)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 09:02:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 09:02:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[56ms]
2025-07-22 09:02:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 09:02:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:49 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:49 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:52 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:52 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:54 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:54 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:56 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:56 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:58 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:58 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:00 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:00 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:02 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:02 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:04 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:04 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:07 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:07 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:09 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:09 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:11 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:11 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:14 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:14 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:20 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:20 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:20 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xe2a9b0bc, L:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:3964 - R:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881]], dubbo version: 3.2.14, current host: ************
2025-07-22 11:04:21 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:21 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:21 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 29 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:21 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 29 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:22 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:22 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:22 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 29 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:22 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 29 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:22 [NettyClientWorker-5-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:3964 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 11:04:22 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.14, current host: ************
2025-07-22 11:04:22 [SpringApplicationShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.14, current host: ************
2025-07-22 11:04:22 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](yumeng-gen), dubbo version: 3.2.14, current host: ************
2025-07-22 11:04:23 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:23 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 29 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:25 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:25 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:25 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 30 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:25 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 30 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:25 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:25 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:25 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 30 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:25 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 30 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:26 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:26 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 30 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:28 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:28 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:28 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 31 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:28 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 31 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:28 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:28 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:28 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 31 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:28 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 31 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:29 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:29 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 31 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:31 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:31 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:31 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 32 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:31 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 32 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:31 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:31 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:31 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 32 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:31 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 32 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:32 [dubbo-client-heartbeat-reconnect-thread-1] INFO  o.a.d.r.e.s.h.ReconnectTimerTask -  [DUBBO] Initial connection to HeaderExchangeClient [channel=org.apache.dubbo.remoting.transport.netty4.NettyClient [/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:3964 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881]], dubbo version: 3.2.14, current host: ************
2025-07-22 11:04:32 [dubbo-client-heartbeat-reconnect-thread-1] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xe2a9b0bc, L:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:3964 ! R:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881], dubbo version: 3.2.14, current host: ************
2025-07-22 11:04:32 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:32 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 32 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:34 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:34 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:34 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 33 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:34 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 33 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:34 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:34 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:34 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 33 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:34 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 33 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:35 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:35 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 33 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 34 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 34 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 34 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 34 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 34 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 35 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 35 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 35 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 35 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 35 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 36 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 36 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 36 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 36 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3456a53c-198e-49aa-b57e-384052f89368] Fail to connect server, after trying 36 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [5e6864c4-31f6-40de-ad3e-65be15cbaf69_config-0] Fail to connect server, after trying 37 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8292f9c1-e8a7-4315-8eb8-40271f95bca6] Fail to connect server, after trying 37 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [82479380-f6e2-48cc-965f-4cc12ceffd23] Fail to connect server, after trying 37 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [d94d5140-6c74-4d89-957c-17979e0ce7aa_config-0] Fail to connect server, after trying 37 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 14:02:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 14:02:00 [main] INFO  com.ym.gen.YmGenApplication - Starting YmGenApplication using Java 17.0.10 with PID 39432 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-gen\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 14:02:00 [main] INFO  com.ym.gen.YmGenApplication - The following 1 profile is active: "dev"
2025-07-22 14:02:01 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gen.yml, group=DEFAULT_GROUP] success
2025-07-22 14:02:01 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 14:02:01 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 14:02:05 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 14:02:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 14:02:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 14:02:06 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1c9fa12
2025-07-22 14:02:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 14:02:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 14:02:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 14:02:07 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 14:02:08 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 14:02:08 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 14:02:08 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 14:02:08 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 14:02:15 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 14:02:15 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 14:02:15 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 14:02:15 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 14:02:15 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gen 192.168.124.10:9202 register finished
2025-07-22 14:02:17 [main] INFO  com.ym.gen.YmGenApplication - Started YmGenApplication in 18.312 seconds (process running for 19.358)
2025-07-22 14:02:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gen.yml, group=DEFAULT_GROUP
2025-07-22 14:02:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 14:02:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 14:02:17 [RMI TCP Connection(2)-192.168.124.10] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 14:02:33 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753164153573-91607913][thread:307][ds:default-master][action:select][cmd:
SELECT * FROM INFORMATION_SCHEMA.COLUMNS
WHERE (TABLE_SCHEMA = ? AND TABLE_NAME LIKE ?)
ORDER BY TABLE_NAME ASC, ORDINAL_POSITION ASC
]
[param:
param0=yumeng-salary(java.lang.String)
param1=auth_code(java.lang.String)
];
2025-07-22 14:02:33 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753164153573-91607913][thread:307][ds:default-master][action:select][执行耗时:00:00:00.044]
2025-07-22 14:02:33 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753164153573-91607913][thread:307][ds:default-master][action:select][封装耗时:00:00:00.007][封装行数:16]
2025-07-22 14:02:33 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753164153573-91607913][thread:307][ds:default-master][columns][catalog:null][schema:SCHEMA:yumeng-salary][table:TABLE:auth_code][total:16][根据metadata解析:0][根据系统表查询:16][根据驱动内置接口补充:0][执行耗时:00:00:00.099]
2025-07-22 14:02:33 [XNIO-1 task-2] INFO  o.anyline.data.adapter.DriverAdapter - [cmd:1753164153573-91607913][thread:307][ds:default-master][columns][catalog:null][schema:SCHEMA:yumeng-salary][table:TABLE:auth_code][total:16][根据metadata解析:0][根据系统表查询:16][根据根据驱动内置接口补充:0][执行耗时:00:00:00.099]
2025-07-22 14:02:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:02:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[65ms]
2025-07-22 14:03:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:03:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[33ms]
2025-07-22 14:03:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:03:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 14:24:22 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
