2025-07-30 08:45:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 08:45:30 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 25768 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 08:45:30 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-30 08:45:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-30 08:45:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-30 08:45:37 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-30 08:45:37 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-30 08:45:37 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-30 08:45:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-30 08:45:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-30 08:45:38 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-30 08:45:38 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 08:45:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753836338379
timestamp=1753836338379
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 08:45:38 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xbbe30e1c, L:/*************:5905 - R:/*************:8091]
2025-07-30 08:45:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 152 ms, version:1.7.1,role:TMROLE,channel:[id: 0xbbe30e1c, L:/*************:5905 - R:/*************:8091]
2025-07-30 08:45:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-30 08:45:38 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-30 08:45:38 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-30 08:45:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-30 08:45:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-30 08:45:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-30 08:45:40 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-30 08:45:42 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-30 08:45:43 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-30 08:45:43 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-30 08:45:44 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-30 08:45:44 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-30 08:45:46 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-30 08:45:46 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-30 08:45:46 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-30 08:45:46 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-30 08:45:46 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-30 08:45:49 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 23.05 seconds (process running for 25.198)
2025-07-30 08:45:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-30 08:45:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-30 08:45:50 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 08:46:38 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 08:46:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 08:46:38 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xab889dca, L:/*************:6135 - R:/*************:8091]
2025-07-30 08:46:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 123 ms, version:1.7.1,role:RMROLE,channel:[id: 0xab889dca, L:/*************:6135 - R:/*************:8091]
2025-07-30 08:58:30 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiIzVGxiMU5EZ3JDbFdja3QxempCSHF0VW1saExqRUVPQSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.uYELFLP_pzJjLW3fR3m77L06qBq_Hdh41TIMHVAcmbs
2025-07-30 08:58:31 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 08:58:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 08:58:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[767ms]
2025-07-30 08:58:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 08:58:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[506ms]
2025-07-30 08:58:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 08:58:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-07-30 08:58:37 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-30 08:58:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 08:58:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[44ms]
2025-07-30 08:58:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 08:58:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[120ms]
2025-07-30 08:58:37 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJCTkFpTm5IeU1WZGVGcjdqN3k2bkQ5VFBlcHhxYnl1YyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.26KKX-l76CvryYFDx4VIQX1Ul-U_2-E_l3n-yQZ2o-k
2025-07-30 08:59:26 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiJPWFhPWUtDRkhJMGd2QTVOTDZvbUdROEkzbnNVN2haQSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.CJ4Hj2VYRJx5FBCZtXqkR3McwEPLknCCm5XSHMMcc7c
2025-07-30 08:59:26 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 08:59:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 08:59:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[49ms]
2025-07-30 08:59:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 08:59:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[172ms]
2025-07-30 08:59:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 08:59:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-30 08:59:31 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[txwz][Success][登录成功]
2025-07-30 08:59:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 08:59:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-30 08:59:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 08:59:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[163ms]
2025-07-30 08:59:31 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiJyUHVJbDJkWjZFVE1MeXBkdk9OclN4UUVCSzJUVlBRWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.i9NJoU5-KH7GIf9bn0wqboESLojfidagoJz4E0EUP44
2025-07-30 09:39:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 09:39:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[192ms]
2025-07-30 09:39:20 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Logout][退出成功]
2025-07-30 09:39:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 09:39:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[9ms]
2025-07-30 09:39:20 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJCTkFpTm5IeU1WZGVGcjdqN3k2bkQ5VFBlcHhxYnl1YyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.26KKX-l76CvryYFDx4VIQX1Ul-U_2-E_l3n-yQZ2o-k
2025-07-30 09:39:21 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 09:39:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[90ms]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[349ms]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[101ms]
2025-07-30 09:39:28 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJPeDhZMzZTM3lYZFJSWDU5STk0eFB2SmNNZmF4SmpteiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.85uamrl-HPGINerfmXqeRyUiXGL_x3mgcVjkRTO_8s8
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xbbe30e1c, L:/*************:5905 - R:/*************:8091] read idle.
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xbbe30e1c, L:/*************:5905 - R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbe30e1c, L:/*************:5905 - R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbe30e1c, L:/*************:5905 ! R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xab889dca, L:/*************:6135 - R:/*************:8091] read idle.
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xab889dca, L:/*************:6135 - R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xab889dca, L:/*************:6135 - R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]) will closed
2025-07-30 09:50:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xab889dca, L:/*************:6135 ! R:/*************:8091]) will closed
2025-07-30 09:51:05 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 09:51:05 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753840265048
timestamp=1753840265048
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 09:51:05 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xddf75bb3, L:/*************:2668 - R:/*************:8091]
2025-07-30 09:51:05 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0xddf75bb3, L:/*************:2668 - R:/*************:8091]
2025-07-30 09:51:05 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 09:51:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 09:51:05 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x582ef6df, L:/*************:5753 - R:/*************:8091]
2025-07-30 09:51:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x582ef6df, L:/*************:5753 - R:/*************:8091]
2025-07-30 09:56:19 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[98ms]
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[194ms]
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[txwz][Success][登录成功]
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-30 09:56:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 09:56:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[68ms]
2025-07-30 09:56:23 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiJFQ0J1dHR1YlRLcDk1MDMxUExGa2dxZ3pNU2xkSDZWaiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.nE4wlmr-CQA1RKNmGePtudlNVsnaFaIJaGeeD_dBQUY
2025-07-30 10:00:27 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[57ms]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[129ms]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[txwz][Success][登录成功]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[143ms]
2025-07-30 10:00:30 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiJ5ek92em1IeGZMZEdBMVhJR2RONENJYmxtSFVUVTNoVyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.2Cut7ZTg7I2ld9f_jiJYh0BoEimVzI_jcMdiGd-inkM
2025-07-30 10:00:49 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 10:00:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:00:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[29ms]
2025-07-30 10:00:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 10:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[149ms]
2025-07-30 10:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-30 10:00:52 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-30 10:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 10:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-30 10:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 10:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[72ms]
2025-07-30 10:00:52 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJNT0JRWko4TXhDNW9JbE9rYk94bGdaenNNOHhDTEdTcyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.n73Syw-yqPFCpDA6PomYdu_DNm7Me2tO6o6alSkdhu0
2025-07-30 10:01:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:01:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[55ms]
2025-07-30 10:01:13 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Logout][退出成功]
2025-07-30 10:01:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 10:01:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-30 10:01:14 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJNT0JRWko4TXhDNW9JbE9rYk94bGdaenNNOHhDTEdTcyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.n73Syw-yqPFCpDA6PomYdu_DNm7Me2tO6o6alSkdhu0
2025-07-30 10:01:15 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 10:01:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:01:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[22ms]
2025-07-30 10:01:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 10:01:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[199ms]
2025-07-30 10:01:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:01:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-30 10:01:22 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-30 10:01:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 10:01:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-07-30 10:01:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 10:01:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[93ms]
2025-07-30 10:01:22 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJjWW9jaHBuVkhJcDlPRldpNVB3WFNkMVc4ZnhqZm9XZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.DgOADCT7O7ESmXJnf7Vn1uqhkGSfRWjgsQGGJnt2lrs
2025-07-30 10:28:17 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-30 10:28:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x582ef6df, L:/*************:5753 - R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x582ef6df, L:/*************:5753 - R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xddf75bb3, L:/*************:2668 - R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xddf75bb3, L:/*************:2668 - R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xddf75bb3, L:/*************:2668 ! R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x582ef6df, L:/*************:5753 ! R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xddf75bb3, L:/*************:2668 ! R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x582ef6df, L:/*************:5753 ! R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xddf75bb3, L:/*************:2668 ! R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x582ef6df, L:/*************:5753 ! R:/*************:8091]
2025-07-30 10:28:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x582ef6df, L:/*************:5753 ! R:/*************:8091]) will closed
2025-07-30 10:28:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xddf75bb3, L:/*************:2668 ! R:/*************:8091]) will closed
2025-07-30 10:28:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x582ef6df, L:/*************:5753 ! R:/*************:8091]) will closed
2025-07-30 10:28:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xddf75bb3, L:/*************:2668 ! R:/*************:8091]) will closed
2025-07-30 10:28:25 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 10:28:25 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753842505050
timestamp=1753842505050
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 10:28:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 10:28:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x582ef6df, L:/*************:5753 ! R:/*************:8091]
2025-07-30 10:28:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x582ef6df, L:/*************:5753 ! R:/*************:8091]
2025-07-30 10:28:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 10:28:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7a61ad5a, L:null ! R:/*************:8091]) will closed
2025-07-30 10:28:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x45cf44c7, L:null ! R:/*************:8091]) will closed
2025-07-30 10:28:35 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 10:28:35 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753842515049
timestamp=1753842515049
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 10:28:35 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x382ddcec, L:/*************:10146 - R:/*************:8091]
2025-07-30 10:28:35 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:TMROLE,channel:[id: 0x382ddcec, L:/*************:10146 - R:/*************:8091]
2025-07-30 10:28:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 10:28:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 10:28:35 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xece530f2, L:/*************:10149 - R:/*************:8091]
2025-07-30 10:28:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:RMROLE,channel:[id: 0xece530f2, L:/*************:10149 - R:/*************:8091]
2025-07-30 10:42:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:42:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[63ms]
2025-07-30 10:42:32 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[txwz][Logout][退出成功]
2025-07-30 10:42:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 10:42:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-30 10:42:33 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiJ5ek92em1IeGZMZEdBMVhJR2RONENJYmxtSFVUVTNoVyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.2Cut7ZTg7I2ld9f_jiJYh0BoEimVzI_jcMdiGd-inkM
2025-07-30 10:42:35 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[27ms]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[140ms]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[txwz][Success][登录成功]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[77ms]
2025-07-30 10:43:41 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiJWcXhlMDRCS1ZpQVBKN0ZTTWduUzVnRUtyMXRkcWRYNSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.3UK__1lBtVWXexvELjsAn4ou8-ynluVB1mpJ_9sS4PQ
2025-07-30 10:43:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:43:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-30 10:43:58 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-30 10:43:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 10:43:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-30 10:43:58 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJjWW9jaHBuVkhJcDlPRldpNVB3WFNkMVc4ZnhqZm9XZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.DgOADCT7O7ESmXJnf7Vn1uqhkGSfRWjgsQGGJnt2lrs
2025-07-30 10:43:59 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[21ms]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[142ms]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[46ms]
2025-07-30 10:44:02 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJUQnQ0YUxpUEc4cFJlMkU4czk2MFhXYUptVnR1bzBFUSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.o52m0X8msr7i2o2c8sgI-LM5JPVsEJX3aggp3nkOfgc
2025-07-30 11:46:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x382ddcec, L:/*************:10146 - R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xece530f2, L:/*************:10149 - R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xece530f2, L:/*************:10149 - R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x382ddcec, L:/*************:10146 - R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x382ddcec, L:/*************:10146 ! R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xece530f2, L:/*************:10149 ! R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x382ddcec, L:/*************:10146 ! R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xece530f2, L:/*************:10149 ! R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x382ddcec, L:/*************:10146 ! R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xece530f2, L:/*************:10149 ! R:/*************:8091]
2025-07-30 11:46:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x382ddcec, L:/*************:10146 ! R:/*************:8091]) will closed
2025-07-30 11:46:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xece530f2, L:/*************:10149 ! R:/*************:8091]) will closed
2025-07-30 11:46:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x382ddcec, L:/*************:10146 ! R:/*************:8091]) will closed
2025-07-30 11:46:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xece530f2, L:/*************:10149 ! R:/*************:8091]) will closed
2025-07-30 11:46:05 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:05 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753847165049
timestamp=1753847165049
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 11:46:05 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xece530f2, L:/*************:10149 ! R:/*************:8091]
2025-07-30 11:46:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xece530f2, L:/*************:10149 ! R:/*************:8091]
2025-07-30 11:46:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 11:46:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfc32f700, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x21d09262, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:15 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:15 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753847175062
timestamp=1753847175062
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 11:46:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 11:46:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5f236dd3, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3e95e6c5, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:25 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:25 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753847185061
timestamp=1753847185061
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 11:46:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 11:46:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7f1f99e4, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48f7159f, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:35 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:35 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753847195048
timestamp=1753847195048
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 11:46:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 11:46:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7fc9c61c, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4d89d6bc, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:45 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:45 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753847205049
timestamp=1753847205049
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 11:46:45 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 11:46:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x42278455, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf917e2f9, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:55 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:55 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753847215053
timestamp=1753847215053
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 11:46:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:46:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 11:46:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb9b49714, L:null ! R:/*************:8091]) will closed
2025-07-30 11:46:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67dadf38, L:null ! R:/*************:8091]) will closed
2025-07-30 11:47:05 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:47:05 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753847225048
timestamp=1753847225048
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 11:47:05 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:47:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 11:47:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2803c4b4, L:null ! R:/*************:8091]) will closed
2025-07-30 11:47:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd4d9dea7, L:null ! R:/*************:8091]) will closed
2025-07-30 11:47:15 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:47:15 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753847235063
timestamp=1753847235063
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 11:47:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:47:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 11:47:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbe3e7ca2, L:null ! R:/*************:8091]) will closed
2025-07-30 11:47:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf055f2bf, L:null ! R:/*************:8091]) will closed
2025-07-30 11:47:25 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:47:25 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753847245049
timestamp=1753847245049
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 11:47:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 11:47:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 11:47:27 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-30 11:47:27 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-30 11:47:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc270dfd4, L:null ! R:/*************:8091]) will closed
2025-07-30 11:47:27 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-30 11:47:27 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-30 11:47:27 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-30 11:47:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x85ba56a3, L:null ! R:/*************:8091]) will closed
2025-07-30 14:15:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 14:15:33 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 32880 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 14:15:33 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-30 14:15:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-30 14:15:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-30 14:15:38 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-30 14:15:38 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-30 14:15:38 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-30 14:15:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-30 14:15:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-30 14:15:38 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-30 14:15:38 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 14:15:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753856138748
timestamp=1753856138748
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 14:15:38 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x71f2301b, L:/*************:10743 - R:/*************:8091]
2025-07-30 14:15:39 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 103 ms, version:1.7.1,role:TMROLE,channel:[id: 0x71f2301b, L:/*************:10743 - R:/*************:8091]
2025-07-30 14:15:39 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-30 14:15:39 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-30 14:15:39 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-30 14:15:39 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-30 14:15:39 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-30 14:15:39 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-30 14:15:39 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-30 14:15:41 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-30 14:15:42 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-30 14:15:42 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-30 14:15:42 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-30 14:15:42 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-30 14:15:44 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-30 14:15:44 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-30 14:15:44 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-30 14:15:44 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-30 14:15:44 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-30 14:15:46 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 16.534 seconds (process running for 17.669)
2025-07-30 14:15:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-30 14:15:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-30 14:15:47 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 14:16:39 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 14:16:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 14:16:39 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xcfb4797d, L:/*************:10875 - R:/*************:8091]
2025-07-30 14:16:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xcfb4797d, L:/*************:10875 - R:/*************:8091]
2025-07-30 14:21:10 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJUQnQ0YUxpUEc4cFJlMkU4czk2MFhXYUptVnR1bzBFUSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.o52m0X8msr7i2o2c8sgI-LM5JPVsEJX3aggp3nkOfgc
2025-07-30 14:21:11 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 14:21:15 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 14:21:21 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 14:22:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 14:22:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[255ms]
2025-07-30 14:22:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 14:22:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[314ms]
2025-07-30 14:22:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 14:22:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-07-30 14:22:52 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-30 14:22:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 14:22:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[25ms]
2025-07-30 14:22:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 14:22:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[80ms]
2025-07-30 14:22:53 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJLUnBVaHRZQVc3R1B5SHoxOUpadmRzNE0zd0x6c29mZCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.LTAsmfbQnA2xBJ3zwK5LWa2hRun1sv1kbLX2RLyXDxE
2025-07-30 15:08:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 15:08:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[68ms]
2025-07-30 15:08:47 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Logout][退出成功]
2025-07-30 15:08:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 15:08:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[16ms]
2025-07-30 15:08:47 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJLUnBVaHRZQVc3R1B5SHoxOUpadmRzNE0zd0x6c29mZCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.LTAsmfbQnA2xBJ3zwK5LWa2hRun1sv1kbLX2RLyXDxE
2025-07-30 15:08:49 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 15:08:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 15:08:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[22ms]
2025-07-30 15:08:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 15:08:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[136ms]
2025-07-30 15:08:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 15:08:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-30 15:08:59 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[txwz][Success][登录成功]
2025-07-30 15:08:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 15:08:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-30 15:08:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 15:08:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[87ms]
2025-07-30 15:08:59 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiJGaFc3ZURZTlEyVXBvdEtOTXRpdG8wTUxBNkFMZFBsdyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.p6F6deI2Aaoiqar1U_FDOWdT8tENsIr5YxZVP1KhH4w
2025-07-30 15:56:46 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiJWcXhlMDRCS1ZpQVBKN0ZTTWduUzVnRUtyMXRkcWRYNSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.3UK__1lBtVWXexvELjsAn4ou8-ynluVB1mpJ_9sS4PQ
2025-07-30 15:56:47 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 15:56:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 15:56:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[51ms]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[145ms]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[txwz][Success][登录成功]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[76ms]
2025-07-30 15:56:55 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiI0c3hTMnpLNHJHYXZ1cU9tOHRNdmdYSENxWk0zRHNEcSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.wq5ncnEZYqiEtJ-56GwysalkznTELTirsyBwH5ww09Q
2025-07-30 15:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 15:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[20ms]
2025-07-30 15:57:13 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[txwz][Logout][退出成功]
2025-07-30 15:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 15:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-30 15:57:13 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1950009240501964802, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTUwMDA5MjQwNTAxOTY0ODAyIiwicm5TdHIiOiJGaFc3ZURZTlEyVXBvdEtOTXRpdG8wTUxBNkFMZFBsdyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTk1MDAwOTI0MDUwMTk2NDgwMiwidXNlck5hbWUiOiJ0eHd6IiwiZGVwdElkIjoxOTUwMDY4NDU3OTYzODE5MDA5LCJkZXB0TmFtZSI6IuiIqui_kOe7j-mUgOWVhuWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.p6F6deI2Aaoiqar1U_FDOWdT8tENsIr5YxZVP1KhH4w
2025-07-30 15:57:14 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[39ms]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[134ms]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[47ms]
2025-07-30 15:57:52 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJHQUhnejc0cmhKbWdEM29ub2dFMFV4Q3p6dTNRbVQ1TCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.Wz0bxB8BkX9CIUWoYIj_wgNbd66ckFNdKt7muHcVRi8
2025-07-30 15:59:45 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-30 15:59:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x71f2301b, L:/*************:10743 ! R:/*************:8091]
2025-07-30 15:59:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xcfb4797d, L:/*************:10875 ! R:/*************:8091]
2025-07-30 15:59:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xcfb4797d, L:/*************:10875 ! R:/*************:8091]
2025-07-30 15:59:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x71f2301b, L:/*************:10743 ! R:/*************:8091]
2025-07-30 15:59:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x71f2301b, L:/*************:10743 ! R:/*************:8091]
2025-07-30 15:59:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xcfb4797d, L:/*************:10875 ! R:/*************:8091]
2025-07-30 15:59:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x71f2301b, L:/*************:10743 ! R:/*************:8091]
2025-07-30 15:59:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xcfb4797d, L:/*************:10875 ! R:/*************:8091]
2025-07-30 15:59:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x71f2301b, L:/*************:10743 ! R:/*************:8091]) will closed
2025-07-30 15:59:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcfb4797d, L:/*************:10875 ! R:/*************:8091]) will closed
2025-07-30 15:59:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x71f2301b, L:/*************:10743 ! R:/*************:8091]) will closed
2025-07-30 15:59:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcfb4797d, L:/*************:10875 ! R:/*************:8091]) will closed
2025-07-30 15:59:48 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 15:59:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753862388537
timestamp=1753862388537
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 15:59:49 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 15:59:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 15:59:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa4ebd931, L:null ! R:/*************:8091]) will closed
2025-07-30 15:59:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7dce8b4, L:null ! R:/*************:8091]) will closed
2025-07-30 15:59:58 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 15:59:58 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753862398537
timestamp=1753862398537
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 15:59:58 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x1b5f7784, L:/*************:13903 - R:/*************:8091]
2025-07-30 15:59:58 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:TMROLE,channel:[id: 0x1b5f7784, L:/*************:13903 - R:/*************:8091]
2025-07-30 15:59:59 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 15:59:59 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 15:59:59 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x859129b1, L:/*************:13904 - R:/*************:8091]
2025-07-30 15:59:59 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x859129b1, L:/*************:13904 - R:/*************:8091]
2025-07-30 16:01:09 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-30 16:01:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x1b5f7784, L:/*************:13903 ! R:/*************:8091]
2025-07-30 16:01:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x859129b1, L:/*************:13904 ! R:/*************:8091]
2025-07-30 16:01:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x1b5f7784, L:/*************:13903 ! R:/*************:8091]
2025-07-30 16:01:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x859129b1, L:/*************:13904 ! R:/*************:8091]
2025-07-30 16:01:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x1b5f7784, L:/*************:13903 ! R:/*************:8091]
2025-07-30 16:01:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x859129b1, L:/*************:13904 ! R:/*************:8091]
2025-07-30 16:01:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x1b5f7784, L:/*************:13903 ! R:/*************:8091]
2025-07-30 16:01:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x859129b1, L:/*************:13904 ! R:/*************:8091]
2025-07-30 16:01:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x859129b1, L:/*************:13904 ! R:/*************:8091]) will closed
2025-07-30 16:01:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1b5f7784, L:/*************:13903 ! R:/*************:8091]) will closed
2025-07-30 16:01:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x859129b1, L:/*************:13904 ! R:/*************:8091]) will closed
2025-07-30 16:01:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1b5f7784, L:/*************:13903 ! R:/*************:8091]) will closed
2025-07-30 16:01:18 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:01:18 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753862478551
timestamp=1753862478551
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 16:01:19 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:01:19 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 16:01:19 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x50269050, L:/*************:14123 - R:/*************:8091]
2025-07-30 16:01:19 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x50269050, L:/*************:14123 - R:/*************:8091]
2025-07-30 16:01:19 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe5e5e310, L:/*************:14119 - R:/*************:8091]
2025-07-30 16:01:19 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0xe5e5e310, L:/*************:14119 - R:/*************:8091]
2025-07-30 16:11:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 16:11:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[170ms]
2025-07-30 16:11:14 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-30 16:11:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 16:11:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-30 16:11:14 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJHQUhnejc0cmhKbWdEM29ub2dFMFV4Q3p6dTNRbVQ1TCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.Wz0bxB8BkX9CIUWoYIj_wgNbd66ckFNdKt7muHcVRi8
2025-07-30 16:11:15 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[40ms]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[115ms]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15900419286][Success][登录成功]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[52ms]
2025-07-30 16:11:20 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiJ2UjVia2FoeWlSSGVlZTdaVU9wSnhWZFN0SVBjVmZyQSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.Y48Pc7ohI5ZSe0glVs9jrB9ebSeH1rcJR1Kf7CTPmms
2025-07-30 16:14:19 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-30 16:14:21 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe5e5e310, L:/*************:14119 ! R:/*************:8091]
2025-07-30 16:14:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x50269050, L:/*************:14123 ! R:/*************:8091]
2025-07-30 16:14:21 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe5e5e310, L:/*************:14119 ! R:/*************:8091]
2025-07-30 16:14:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x50269050, L:/*************:14123 ! R:/*************:8091]
2025-07-30 16:14:21 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe5e5e310, L:/*************:14119 ! R:/*************:8091]
2025-07-30 16:14:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x50269050, L:/*************:14123 ! R:/*************:8091]
2025-07-30 16:14:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x50269050, L:/*************:14123 ! R:/*************:8091]
2025-07-30 16:14:21 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe5e5e310, L:/*************:14119 ! R:/*************:8091]
2025-07-30 16:14:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x50269050, L:/*************:14123 ! R:/*************:8091]) will closed
2025-07-30 16:14:21 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe5e5e310, L:/*************:14119 ! R:/*************:8091]) will closed
2025-07-30 16:14:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x50269050, L:/*************:14123 ! R:/*************:8091]) will closed
2025-07-30 16:14:21 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe5e5e310, L:/*************:14119 ! R:/*************:8091]) will closed
2025-07-30 16:14:28 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:14:28 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753863268546
timestamp=1753863268546
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 16:14:29 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:14:29 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 16:14:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb849de7b, L:null ! R:/*************:8091]) will closed
2025-07-30 16:14:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaa3776a6, L:null ! R:/*************:8091]) will closed
2025-07-30 16:14:38 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:14:38 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753863278539
timestamp=1753863278539
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 16:14:38 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7dc39000, L:/*************:3200 - R:/*************:8091]
2025-07-30 16:14:38 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x7dc39000, L:/*************:3200 - R:/*************:8091]
2025-07-30 16:14:39 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:14:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 16:14:39 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xebcded81, L:/*************:3201 - R:/*************:8091]
2025-07-30 16:14:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:RMROLE,channel:[id: 0xebcded81, L:/*************:3201 - R:/*************:8091]
2025-07-30 16:35:28 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-30 16:35:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xebcded81, L:/*************:3201 ! R:/*************:8091]
2025-07-30 16:35:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7dc39000, L:/*************:3200 ! R:/*************:8091]
2025-07-30 16:35:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7dc39000, L:/*************:3200 ! R:/*************:8091]
2025-07-30 16:35:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xebcded81, L:/*************:3201 ! R:/*************:8091]
2025-07-30 16:35:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7dc39000, L:/*************:3200 ! R:/*************:8091]
2025-07-30 16:35:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xebcded81, L:/*************:3201 ! R:/*************:8091]
2025-07-30 16:35:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xebcded81, L:/*************:3201 ! R:/*************:8091]
2025-07-30 16:35:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7dc39000, L:/*************:3200 ! R:/*************:8091]
2025-07-30 16:35:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xebcded81, L:/*************:3201 ! R:/*************:8091]) will closed
2025-07-30 16:35:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7dc39000, L:/*************:3200 ! R:/*************:8091]) will closed
2025-07-30 16:35:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7dc39000, L:/*************:3200 ! R:/*************:8091]) will closed
2025-07-30 16:35:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xebcded81, L:/*************:3201 ! R:/*************:8091]) will closed
2025-07-30 16:35:38 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:35:38 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753864538537
timestamp=1753864538537
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 16:35:39 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:35:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 16:35:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7eb4195c, L:null ! R:/*************:8091]) will closed
2025-07-30 16:35:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x40330b43, L:null ! R:/*************:8091]) will closed
2025-07-30 16:35:48 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:35:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753864548537
timestamp=1753864548537
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 16:35:48 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd9830b5a, L:/*************:6509 - R:/*************:8091]
2025-07-30 16:35:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd9830b5a, L:/*************:6509 - R:/*************:8091]
2025-07-30 16:35:49 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 16:35:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 16:35:49 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7a1312a4, L:/*************:6510 - R:/*************:8091]
2025-07-30 16:35:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x7a1312a4, L:/*************:6510 - R:/*************:8091]
2025-07-30 18:12:09 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiJ2UjVia2FoeWlSSGVlZTdaVU9wSnhWZFN0SVBjVmZyQSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.Y48Pc7ohI5ZSe0glVs9jrB9ebSeH1rcJR1Kf7CTPmms
2025-07-30 18:12:09 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-30 19:16:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7a1312a4, L:/*************:6510 - R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x7a1312a4, L:/*************:6510 - R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd9830b5a, L:/*************:6509 - R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7a1312a4, L:/*************:6510 ! R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xd9830b5a, L:/*************:6509 - R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7a1312a4, L:/*************:6510 ! R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7a1312a4, L:/*************:6510 ! R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7a1312a4, L:/*************:6510 ! R:/*************:8091]) will closed
2025-07-30 19:16:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd9830b5a, L:/*************:6509 ! R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7a1312a4, L:/*************:6510 ! R:/*************:8091]) will closed
2025-07-30 19:16:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd9830b5a, L:/*************:6509 ! R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd9830b5a, L:/*************:6509 ! R:/*************:8091]
2025-07-30 19:16:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd9830b5a, L:/*************:6509 ! R:/*************:8091]) will closed
2025-07-30 19:16:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd9830b5a, L:/*************:6509 ! R:/*************:8091]) will closed
2025-07-30 19:16:39 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:16:39 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753874199034
timestamp=1753874199034
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 19:16:39 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:16:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7a1312a4, L:/*************:6510 ! R:/*************:8091]
2025-07-30 19:16:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7a1312a4, L:/*************:6510 ! R:/*************:8091]
2025-07-30 19:16:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 19:16:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x588f7c90, L:null ! R:/*************:8091]) will closed
2025-07-30 19:16:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x57a79fa6, L:null ! R:/*************:8091]) will closed
2025-07-30 19:16:49 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:16:49 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753874209035
timestamp=1753874209035
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 19:16:49 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:16:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 19:16:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf3267d08, L:null ! R:/*************:8091]) will closed
2025-07-30 19:16:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfc46d0c3, L:null ! R:/*************:8091]) will closed
2025-07-30 19:16:59 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:16:59 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753874219040
timestamp=1753874219040
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 19:16:59 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:16:59 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 19:17:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8f71a5e8, L:null ! R:/*************:8091]) will closed
2025-07-30 19:17:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcc869d82, L:null ! R:/*************:8091]) will closed
2025-07-30 19:17:09 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:17:09 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753874229035
timestamp=1753874229035
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 19:17:09 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:17:09 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 19:17:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe4f32e1, L:null ! R:/*************:8091]) will closed
2025-07-30 19:17:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x02e5a298, L:null ! R:/*************:8091]) will closed
2025-07-30 19:17:19 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:17:19 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753874239035
timestamp=1753874239035
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 19:17:19 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:17:19 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 19:17:21 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x516eeaba, L:null ! R:/*************:8091]) will closed
2025-07-30 19:17:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbf185fbc, L:null ! R:/*************:8091]) will closed
2025-07-30 19:17:29 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:17:29 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753874249036
timestamp=1753874249036
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 19:17:29 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:17:29 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-30 19:17:31 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x608efb46, L:null ! R:/*************:8091]) will closed
2025-07-30 19:17:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8d8f4166, L:null ! R:/*************:8091]) will closed
2025-07-30 19:17:38 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-30 19:17:38 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-30 19:17:38 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-30 19:17:39 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-30 19:17:39 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753874259036
timestamp=1753874259036
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-30 19:17:39 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-30 19:17:39 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-30 19:17:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfd8dfbff, L:null ! R:/*************:8091]) will closed
