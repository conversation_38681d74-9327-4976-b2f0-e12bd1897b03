2025-07-25 08:33:57 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 08:33:57 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 24336 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 08:33:57 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-25 08:33:57 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-25 08:33:57 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:01 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 08:34:02 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 08:34:02 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 08:34:02 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 08:34:02 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 08:34:02 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 08:34:02 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 08:34:02 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753403642577
timestamp=1753403642577
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 08:34:02 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd2947f5e, L:/*************:1988 - R:/*************:8091]
2025-07-25 08:34:02 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 139 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd2947f5e, L:/*************:1988 - R:/*************:8091]
2025-07-25 08:34:02 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-25 08:34:02 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 08:34:02 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 08:34:02 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 08:34:02 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-25 08:34:02 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 08:34:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 08:34:06 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 08:34:08 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 08:34:08 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 08:34:08 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-25 08:34:08 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-25 08:34:10 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 08:34:10 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 08:34:10 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 08:34:10 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 08:34:10 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-25 08:34:13 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 18.213 seconds (process running for 19.228)
2025-07-25 08:34:13 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 08:34:13 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-25 08:34:13 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 08:35:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 08:35:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 08:35:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x33fe4db9, L:/*************:2286 - R:/*************:8091]
2025-07-25 08:35:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x33fe4db9, L:/*************:2286 - R:/*************:8091]
2025-07-25 08:35:33 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJRV1B2SVF0OU9tV1hUR21sbjNJNnYyZURLcFJJRGlQTCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.FSn90wjPpTZ4rpR224l-83XN_6OSYpakfsmVaN8NGHY
2025-07-25 08:35:34 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 08:35:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 08:35:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[227ms]
2025-07-25 08:35:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 08:35:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[350ms]
2025-07-25 08:35:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 08:35:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-25 08:35:39 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-25 08:35:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 08:35:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[24ms]
2025-07-25 08:35:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 08:35:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[96ms]
2025-07-25 08:35:39 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJrUzIxVHl6VzRTbVN3RGUxZEhKWm9ldWNEa0wxV1NwVSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9._0Xj1Ggnq5W0i4dKV2DzPKtKX14jZg-Dtp_5df6Rakk
2025-07-25 08:50:14 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1943143574256308226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTQzMTQzNTc0MjU2MzA4MjI2Iiwicm5TdHIiOiJkR3YzUTg3MHRscklvRjhvaWRQN3FjcnBUQU9hb3BZSSIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTk0MzE0MzU3NDI1NjMwODIyNiwiZGVwdElkIjoxOTM0MjYyMTYxMTg2MzI4NTc4fQ.xq7nmbBmLJE_ppsfB1dWMOwYsFbxxsOMsZ1vAMxgLDE
2025-07-25 10:00:24 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJrUzIxVHl6VzRTbVN3RGUxZEhKWm9ldWNEa0wxV1NwVSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9._0Xj1Ggnq5W0i4dKV2DzPKtKX14jZg-Dtp_5df6Rakk
2025-07-25 10:00:25 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[65ms]
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[123ms]
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-07-25 10:00:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 10:00:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[78ms]
2025-07-25 10:00:30 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTd3ZyVXZTUHY3NDV5eXd6aE1MUURtVHFkWVpJbjRGdiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.G7M2j5tOAK7bUxSA27InOBxlIm6ob-95hkO_QwPI_f8
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd2947f5e, L:/*************:1988 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x33fe4db9, L:/*************:2286 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x33fe4db9, L:/*************:2286 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xd2947f5e, L:/*************:1988 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd2947f5e, L:/*************:1988 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x33fe4db9, L:/*************:2286 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x33fe4db9, L:/*************:2286 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd2947f5e, L:/*************:1988 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x33fe4db9, L:/*************:2286 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd2947f5e, L:/*************:1988 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x33fe4db9, L:/*************:2286 ! R:/*************:8091]) will closed
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd2947f5e, L:/*************:1988 ! R:/*************:8091]) will closed
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x33fe4db9, L:/*************:2286 ! R:/*************:8091]) will closed
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd2947f5e, L:/*************:1988 ! R:/*************:8091]) will closed
2025-07-25 10:44:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753411472761
timestamp=1753411472761
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 10:44:33 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:33 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x33fe4db9, L:/*************:2286 ! R:/*************:8091]
2025-07-25 10:44:33 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x33fe4db9, L:/*************:2286 ! R:/*************:8091]
2025-07-25 10:44:33 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 10:44:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x066c0fe1, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x31b4736d, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753411482762
timestamp=1753411482762
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 10:44:43 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 10:44:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8955f591, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0d5878ab, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753411492767
timestamp=1753411492767
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 10:44:53 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:53 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 10:44:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd42203d8, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf2096b41, L:null ! R:/*************:8091]) will closed
2025-07-25 11:23:54 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 11:23:54 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 4600 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 11:23:54 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-25 11:23:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-25 11:23:54 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 11:23:59 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 11:23:59 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 11:23:59 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 11:23:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 11:23:59 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 11:23:59 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 11:23:59 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 11:24:00 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753413840019
timestamp=1753413840019
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 11:24:00 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x409f8d27, L:/*************:9946 - R:/*************:8091]
2025-07-25 11:24:00 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 69 ms, version:1.7.1,role:TMROLE,channel:[id: 0x409f8d27, L:/*************:9946 - R:/*************:8091]
2025-07-25 11:24:00 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-25 11:24:00 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 11:24:00 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 11:24:00 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 11:24:00 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-25 11:24:00 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 11:24:01 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 11:24:03 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 11:24:04 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 11:24:04 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 11:24:04 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-25 11:24:04 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-25 11:24:06 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 11:24:06 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 11:24:06 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 11:24:06 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 11:24:06 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-25 11:24:09 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 17.514 seconds (process running for 18.574)
2025-07-25 11:24:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 11:24:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-25 11:24:09 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 11:25:00 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 11:25:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 11:25:00 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x797ec042, L:/*************:10117 - R:/*************:8091]
2025-07-25 11:25:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x797ec042, L:/*************:10117 - R:/*************:8091]
2025-07-25 11:27:01 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJTd3ZyVXZTUHY3NDV5eXd6aE1MUURtVHFkWVpJbjRGdiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.G7M2j5tOAK7bUxSA27InOBxlIm6ob-95hkO_QwPI_f8
2025-07-25 11:27:01 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 11:27:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[389ms]
2025-07-25 11:27:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 11:27:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[438ms]
2025-07-25 11:27:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-07-25 11:27:07 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-25 11:27:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 11:27:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[31ms]
2025-07-25 11:27:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 11:27:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[203ms]
2025-07-25 11:27:07 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJTYWd4bGd1YlZOVzFmVnVaQ3Y5QllaY2NTclc4cEpLOCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.d__mDhiaeW09dy3KN5q1NxztKsolmfTphPT2Qgz-_Ww
2025-07-25 11:27:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[38ms]
2025-07-25 11:27:17 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Logout][退出成功]
2025-07-25 11:27:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 11:27:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-25 11:27:17 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJTYWd4bGd1YlZOVzFmVnVaQ3Y5QllaY2NTclc4cEpLOCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.d__mDhiaeW09dy3KN5q1NxztKsolmfTphPT2Qgz-_Ww
2025-07-25 11:27:18 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 11:27:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[28ms]
2025-07-25 11:27:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 11:27:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[88ms]
2025-07-25 11:27:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-25 11:27:26 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-07-25 11:27:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 11:27:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-25 11:27:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 11:27:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[129ms]
2025-07-25 11:27:26 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJxYjR4UFlZREhIS2J4azRjWnd4WllFMVhZMk1qaFNzUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.Ne7GFR-T2rTzWJPLQg5mMAwWT5MgQ_Ws7r1nCE-z4pM
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x409f8d27, L:/*************:9946 - R:/*************:8091] read idle.
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x409f8d27, L:/*************:9946 - R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x409f8d27, L:/*************:9946 - R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x409f8d27, L:/*************:9946 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x797ec042, L:/*************:10117 - R:/*************:8091] read idle.
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x797ec042, L:/*************:10117 - R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x797ec042, L:/*************:10117 - R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x797ec042, L:/*************:10117 ! R:/*************:8091]) will closed
2025-07-25 13:27:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:27:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753421256677
timestamp=1753421256677
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 13:27:36 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4bebe999, L:/*************:11699 - R:/*************:8091]
2025-07-25 13:27:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x4bebe999, L:/*************:11699 - R:/*************:8091]
2025-07-25 13:27:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:27:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 13:27:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x6aa96e9f, L:/*************:11701 - R:/*************:8091]
2025-07-25 13:27:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:RMROLE,channel:[id: 0x6aa96e9f, L:/*************:11701 - R:/*************:8091]
2025-07-25 13:28:17 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJxYjR4UFlZREhIS2J4azRjWnd4WllFMVhZMk1qaFNzUyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.Ne7GFR-T2rTzWJPLQg5mMAwWT5MgQ_Ws7r1nCE-z4pM
2025-07-25 13:28:17 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 13:29:17 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4bebe999, L:/*************:11699 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x6aa96e9f, L:/*************:11701 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x6aa96e9f, L:/*************:11701 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x6aa96e9f, L:/*************:11701 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x6aa96e9f, L:/*************:11701 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6aa96e9f, L:/*************:11701 ! R:/*************:8091]) will closed
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6aa96e9f, L:/*************:11701 ! R:/*************:8091]) will closed
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4bebe999, L:/*************:11699 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4bebe999, L:/*************:11699 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4bebe999, L:/*************:11699 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4bebe999, L:/*************:11699 ! R:/*************:8091]) will closed
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4bebe999, L:/*************:11699 ! R:/*************:8091]) will closed
2025-07-25 13:29:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753421366676
timestamp=1753421366676
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 13:29:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 13:29:28 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5a84b31a, L:/*************:12020 - R:/*************:8091]
2025-07-25 13:29:28 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xcd78a8b5, L:/*************:12024 - R:/*************:8091]
2025-07-25 13:29:28 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 35 ms, version:1.7.1,role:TMROLE,channel:[id: 0x5a84b31a, L:/*************:12020 - R:/*************:8091]
2025-07-25 13:29:28 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 65 ms, version:1.7.1,role:RMROLE,channel:[id: 0xcd78a8b5, L:/*************:12024 - R:/*************:8091]
2025-07-25 13:30:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:30:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[58ms]
2025-07-25 13:30:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:30:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-25 13:30:51 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Error][验证码已失效]
2025-07-25 13:30:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 13:30:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-25 13:30:51 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 13:30:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:30:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[25ms]
2025-07-25 13:30:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 13:30:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[133ms]
2025-07-25 13:30:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:30:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-25 13:30:57 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-25 13:30:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 13:30:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-25 13:30:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 13:30:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[67ms]
2025-07-25 13:30:57 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJ2enNVelRkcVJoMzhreXZnSVRhVlE0VXQzWkRocmJNOSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.TtKcVGBbkr5DrRoJPxTkQ9H0MUx41FF9n796xotU6pg
2025-07-25 13:31:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:31:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[26ms]
2025-07-25 13:31:04 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Logout][退出成功]
2025-07-25 13:31:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 13:31:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-25 13:31:04 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJ2enNVelRkcVJoMzhreXZnSVRhVlE0VXQzWkRocmJNOSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.TtKcVGBbkr5DrRoJPxTkQ9H0MUx41FF9n796xotU6pg
2025-07-25 13:31:05 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 13:31:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:31:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[25ms]
2025-07-25 13:31:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 13:31:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[86ms]
2025-07-25 13:31:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:31:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-25 13:31:19 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-07-25 13:31:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 13:31:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-25 13:31:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 13:31:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[68ms]
2025-07-25 13:31:19 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJWNFlkd0JBQURFQTBQYlhXeUo4NjBtYTZIcW1rWkpVNSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.y2r3zlt2S94EuIZPv0jAqpocirHqEn35wnharv8yZe8
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xcd78a8b5, L:/*************:12024 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5a84b31a, L:/*************:12020 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5a84b31a, L:/*************:12020 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xcd78a8b5, L:/*************:12024 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xcd78a8b5, L:/*************:12024 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5a84b31a, L:/*************:12020 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5a84b31a, L:/*************:12020 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xcd78a8b5, L:/*************:12024 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xcd78a8b5, L:/*************:12024 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5a84b31a, L:/*************:12020 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a84b31a, L:/*************:12020 ! R:/*************:8091]) will closed
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcd78a8b5, L:/*************:12024 ! R:/*************:8091]) will closed
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a84b31a, L:/*************:12020 ! R:/*************:8091]) will closed
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcd78a8b5, L:/*************:12024 ! R:/*************:8091]) will closed
2025-07-25 14:55:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753426526678
timestamp=1753426526678
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 14:55:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xcd78a8b5, L:/*************:12024 ! R:/*************:8091]
2025-07-25 14:55:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xcd78a8b5, L:/*************:12024 ! R:/*************:8091]
2025-07-25 14:55:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 14:55:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xda417923, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x155d84ef, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753426536676
timestamp=1753426536676
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 14:55:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 14:55:38 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x895e19b0, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa53cd7c7, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753426546688
timestamp=1753426546688
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 14:55:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 14:55:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x100ce318, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:49 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7162a148, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753426556676
timestamp=1753426556676
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 14:55:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 14:55:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdee80f3b, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:59 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x61cfe3f8, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753426566682
timestamp=1753426566682
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 14:56:07 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 14:56:08 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x21dc7ca1, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3df046a4, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753426576676
timestamp=1753426576676
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 14:56:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 14:56:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6cadc096, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9b7c75da, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753426586676
timestamp=1753426586676
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 14:56:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 14:56:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2e9fab98, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x38b096c3, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753426596678
timestamp=1753426596678
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 14:56:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 14:56:38 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5274ae08, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc03d09a5, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 15:12:53 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 15:12:53 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 30692 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 15:12:53 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-25 15:12:53 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-25 15:12:53 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 15:12:58 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 15:12:58 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 15:12:58 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 15:12:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 15:12:59 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 15:12:59 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 15:12:59 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 15:12:59 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753427579430
timestamp=1753427579430
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 15:12:59 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x55446a93, L:/*************:12175 - R:/*************:8091]
2025-07-25 15:12:59 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 62 ms, version:1.7.1,role:TMROLE,channel:[id: 0x55446a93, L:/*************:12175 - R:/*************:8091]
2025-07-25 15:12:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-25 15:12:59 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 15:12:59 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 15:12:59 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 15:12:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-25 15:12:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 15:13:01 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 15:13:03 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 15:13:05 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 15:13:05 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 15:13:05 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-25 15:13:06 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-25 15:13:07 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 15:13:07 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 15:13:07 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 15:13:07 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 15:13:07 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-25 15:13:10 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 19.342 seconds (process running for 20.435)
2025-07-25 15:13:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 15:13:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-25 15:13:10 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 15:13:59 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 15:13:59 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 15:13:59 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x3a79cc70, L:/*************:12328 - R:/*************:8091]
2025-07-25 15:13:59 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x3a79cc70, L:/*************:12328 - R:/*************:8091]
2025-07-25 15:14:16 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJWNFlkd0JBQURFQTBQYlhXeUo4NjBtYTZIcW1rWkpVNSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.y2r3zlt2S94EuIZPv0jAqpocirHqEn35wnharv8yZe8
2025-07-25 15:14:17 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 15:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 15:14:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[302ms]
2025-07-25 15:14:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 15:14:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[335ms]
2025-07-25 15:14:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 15:14:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-07-25 15:14:24 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-07-25 15:14:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 15:14:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[27ms]
2025-07-25 15:14:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 15:14:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[103ms]
2025-07-25 15:14:25 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJCVkVYTzlDOFI1TExqMWFxTEVHWWJtbmJwVDd0aUZqbCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.keJWH64amuwf4vmjoYVWR3e781b5kVvIYmRO_wA9U-A
2025-07-25 17:47:35 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 17:47:35 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 17:47:35 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 17:47:35 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 17:47:35 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 17:47:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x55446a93, L:/*************:12175 ! R:/*************:8091]
2025-07-25 17:47:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x55446a93, L:/*************:12175 ! R:/*************:8091]
2025-07-25 17:47:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x55446a93, L:/*************:12175 ! R:/*************:8091]
2025-07-25 17:47:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x55446a93, L:/*************:12175 ! R:/*************:8091]
2025-07-25 17:47:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x55446a93, L:/*************:12175 ! R:/*************:8091]) will closed
2025-07-25 17:47:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x55446a93, L:/*************:12175 ! R:/*************:8091]) will closed
2025-07-25 17:47:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x3a79cc70, L:/*************:12328 ! R:/*************:8091]
2025-07-25 17:47:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x3a79cc70, L:/*************:12328 ! R:/*************:8091]
2025-07-25 17:47:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3a79cc70, L:/*************:12328 ! R:/*************:8091]
2025-07-25 17:47:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3a79cc70, L:/*************:12328 ! R:/*************:8091]
2025-07-25 17:47:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a79cc70, L:/*************:12328 ! R:/*************:8091]) will closed
2025-07-25 17:47:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a79cc70, L:/*************:12328 ! R:/*************:8091]) will closed
2025-07-25 17:47:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 17:47:40 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 40576 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 17:47:40 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-25 17:47:40 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-25 17:47:40 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 17:47:43 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 17:47:43 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 17:47:43 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 17:47:43 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 17:47:43 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 17:47:43 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 17:47:43 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 17:47:43 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753436863675
timestamp=1753436863675
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 17:47:43 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x19f6f944, L:/*************:7276 - R:/*************:8091]
2025-07-25 17:47:43 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 72 ms, version:1.7.1,role:TMROLE,channel:[id: 0x19f6f944, L:/*************:7276 - R:/*************:8091]
2025-07-25 17:47:43 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-25 17:47:43 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 17:47:43 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 17:47:43 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 17:47:43 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-25 17:47:43 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 17:47:44 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 17:47:46 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 17:47:46 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 17:47:46 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 17:47:47 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-25 17:47:47 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-25 17:47:48 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 17:47:48 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 17:47:48 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 17:47:48 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 17:47:48 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-25 17:47:51 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 12.995 seconds (process running for 14.152)
2025-07-25 17:47:51 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 17:47:51 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-25 17:47:51 [RMI TCP Connection(6)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 17:48:43 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 17:48:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 17:48:43 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd7895e62, L:/*************:7465 - R:/*************:8091]
2025-07-25 17:48:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0xd7895e62, L:/*************:7465 - R:/*************:8091]
2025-07-25 17:49:36 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJCVkVYTzlDOFI1TExqMWFxTEVHWWJtbmJwVDd0aUZqbCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.keJWH64amuwf4vmjoYVWR3e781b5kVvIYmRO_wA9U-A
2025-07-25 17:49:38 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 17:49:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:49:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[328ms]
2025-07-25 17:49:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:49:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[50ms]
2025-07-25 17:49:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:52:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:52:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[181ms]
2025-07-25 17:52:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:52:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[25ms]
2025-07-25 17:52:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:52:53 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-25 17:53:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:53:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[163ms]
2025-07-25 17:53:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:53:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[26ms]
2025-07-25 17:53:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:54:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:54:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[168ms]
2025-07-25 17:54:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:54:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[26ms]
2025-07-25 17:54:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:54:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:54:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[186ms]
2025-07-25 17:54:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:54:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[32ms]
2025-07-25 17:54:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:56:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:56:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[165ms]
2025-07-25 17:56:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:56:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[25ms]
2025-07-25 17:56:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:56:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:56:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[162ms]
2025-07-25 17:56:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:56:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[24ms]
2025-07-25 17:56:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:57:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:57:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[163ms]
2025-07-25 17:57:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:57:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[25ms]
2025-07-25 17:57:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:57:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteMailService],MethodName=[send],SpendTime=[1394ms]
2025-07-25 17:57:18 [XNIO-1 task-2] INFO  c.y.a.c.ForgotPasswordController - 发送重置密码验证码
2025-07-25 19:13:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x19f6f944, L:/*************:7276 - R:/*************:8091] read idle.
2025-07-25 19:13:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xd7895e62, L:/*************:7465 - R:/*************:8091] read idle.
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd7895e62, L:/*************:7465 - R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x19f6f944, L:/*************:7276 - R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7895e62, L:/*************:7465 - R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7895e62, L:/*************:7465 ! R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x19f6f944, L:/*************:7276 - R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]) will closed
2025-07-25 19:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x19f6f944, L:/*************:7276 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 19:13:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753441992368
timestamp=1753441992368
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 19:13:12 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xbc1ff819, L:/*************:11181 - R:/*************:8091]
2025-07-25 19:13:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 18 ms, version:1.7.1,role:TMROLE,channel:[id: 0xbc1ff819, L:/*************:11181 - R:/*************:8091]
2025-07-25 19:13:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 19:13:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 19:13:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa48de72f, L:/*************:11184 - R:/*************:8091]
2025-07-25 19:13:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0xa48de72f, L:/*************:11184 - R:/*************:8091]
2025-07-25 20:19:46 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 20:19:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:19:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[126ms]
2025-07-25 20:19:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 20:19:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[194ms]
2025-07-25 20:19:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:19:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-25 20:19:51 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-07-25 20:19:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 20:19:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[22ms]
2025-07-25 20:19:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 20:19:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[88ms]
2025-07-25 20:19:51 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJ5OFBvNURTdnZ2M3BHd2JMcXM1UUxudmdxV3dpNUE2QyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.0SgSTZxOpeOUF6yVlVdhq-YlSqGEnDsxu_7k929R3Eg
2025-07-25 20:32:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:32:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[40ms]
2025-07-25 20:32:40 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-07-25 20:32:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 20:32:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-25 20:32:40 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJ5OFBvNURTdnZ2M3BHd2JMcXM1UUxudmdxV3dpNUE2QyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.0SgSTZxOpeOUF6yVlVdhq-YlSqGEnDsxu_7k929R3Eg
2025-07-25 20:32:41 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[163ms]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[70ms]
2025-07-25 20:32:44 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiI2OXFkREFPWmJLR2dBSGFoamdHZjU3em9wQUZGVnRVTCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.NzHYNuV3HniciLycgBiDizuOx49BCJsInhcbJMZU6Kg
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa48de72f, L:/*************:11184 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xbc1ff819, L:/*************:11181 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xbc1ff819, L:/*************:11181 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xa48de72f, L:/*************:11184 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xbc1ff819, L:/*************:11181 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa48de72f, L:/*************:11184 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xbc1ff819, L:/*************:11181 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa48de72f, L:/*************:11184 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xbc1ff819, L:/*************:11181 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa48de72f, L:/*************:11184 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa48de72f, L:/*************:11184 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbc1ff819, L:/*************:11181 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbc1ff819, L:/*************:11181 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa48de72f, L:/*************:11184 ! R:/*************:8091]) will closed
2025-07-25 20:41:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753447300668
timestamp=1753447300668
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 20:41:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa48de72f, L:/*************:11184 ! R:/*************:8091]
2025-07-25 20:41:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa48de72f, L:/*************:11184 ! R:/*************:8091]
2025-07-25 20:41:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 20:41:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6a6de7fd, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:43 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x53960d50, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753447310682
timestamp=1753447310682
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 20:41:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 20:41:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb97cc7fb, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1c096c86, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753447320669
timestamp=1753447320669
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 20:42:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 20:42:02 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2c27f55d, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:03 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc9cf7ead, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753447330668
timestamp=1753447330668
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 20:42:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 20:42:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc8fe1c0b, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe4e48b10, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753447340668
timestamp=1753447340668
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 20:42:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 20:42:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2e9b58fd, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x30b4c83e, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753447350668
timestamp=1753447350668
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 20:42:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 20:42:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x28faf857, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x794d3fbe, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753447360668
timestamp=1753447360668
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 20:42:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 20:42:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3f4cb761, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:43 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb59f0860, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753447370668
timestamp=1753447370668
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 20:42:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 20:42:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1c7e41dd, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2bf19048, L:null ! R:/*************:8091]) will closed
2025-07-25 20:43:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:43:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753447380668
timestamp=1753447380668
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-25 20:43:00 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 20:43:00 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 20:43:00 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 20:43:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:43:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 20:43:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc49295ca, L:null ! R:/*************:8091]) will closed
2025-07-25 20:43:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5ca6c1c8, L:null ! R:/*************:8091]) will closed
