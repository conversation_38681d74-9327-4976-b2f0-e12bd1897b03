2025-07-19 08:55:27 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-19 08:55:28 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 28284 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 08:55:28 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-19 08:55:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-19 08:55:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-19 08:55:31 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-19 08:55:31 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 08:55:31 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 08:55:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-19 08:55:31 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-19 08:55:31 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-19 08:55:31 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 08:55:32 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752886531998
timestamp=1752886531998
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 08:55:32 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x733dfa2d, L:/*************:4766 - R:/*************:8091]
2025-07-19 08:55:32 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 145 ms, version:1.7.1,role:TMROLE,channel:[id: 0x733dfa2d, L:/*************:4766 - R:/*************:8091]
2025-07-19 08:55:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-19 08:55:32 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-19 08:55:32 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-19 08:55:32 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-19 08:55:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-19 08:55:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-19 08:55:33 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-19 08:55:35 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-19 08:55:37 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-19 08:55:37 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-19 08:55:37 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-19 08:55:38 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-19 08:55:40 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-19 08:55:40 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-19 08:55:40 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-19 08:55:40 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-19 08:55:40 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-19 08:55:43 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 16.942 seconds (process running for 18.072)
2025-07-19 08:55:43 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-19 08:55:43 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-19 08:55:43 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 08:56:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 08:56:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 08:56:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7acfe262, L:/*************:4976 - R:/*************:8091]
2025-07-19 08:56:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x7acfe262, L:/*************:4976 - R:/*************:8091]
2025-07-19 08:57:47 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJjNXdVbXJSbnZLRjFrU1FpNkxyQ1E2SjZ2ME4ybkhTNSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.jCa7oz4y2Lq2Wd4ylEuqtqFWgFq2d2VK-5bcP08gk-E
2025-07-19 08:57:48 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 08:57:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 08:57:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[218ms]
2025-07-19 08:57:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 08:57:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[239ms]
2025-07-19 08:57:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 08:57:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-19 08:57:53 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Success][登录成功]
2025-07-19 08:57:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 08:57:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[25ms]
2025-07-19 08:57:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 08:57:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[82ms]
2025-07-19 08:57:53 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSM1FYR2RNYk91ZE9qbkl2RExQRnVRZHFQa3ZLWmJwaSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.urv9h2Zt6P1hsEYpE6JQhrrX-h7JzMPdjHqr5ggKlU4
2025-07-19 09:06:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 09:06:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[59ms]
2025-07-19 09:06:42 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[admin][Logout][退出成功]
2025-07-19 09:06:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 09:06:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-19 09:06:42 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJSM1FYR2RNYk91ZE9qbkl2RExQRnVRZHFQa3ZLWmJwaSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.urv9h2Zt6P1hsEYpE6JQhrrX-h7JzMPdjHqr5ggKlU4
2025-07-19 09:06:45 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 09:06:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 09:06:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[20ms]
2025-07-19 09:06:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 09:06:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[124ms]
2025-07-19 09:06:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 09:06:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-19 09:06:56 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-19 09:06:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 09:06:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-19 09:06:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 09:06:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[73ms]
2025-07-19 09:06:56 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiIyUlhyNXFRN0RYSGVsRmJCQXdEN0hsMklVYjdhQ2VBRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.ckxj4C7eu2sAngyYMDmrcR9fqlSx7pO4ythpAeV-wKs
2025-07-19 10:47:27 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiIyUlhyNXFRN0RYSGVsRmJCQXdEN0hsMklVYjdhQ2VBRCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.ckxj4C7eu2sAngyYMDmrcR9fqlSx7pO4ythpAeV-wKs
2025-07-19 10:47:28 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 10:47:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 10:47:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[95ms]
2025-07-19 10:47:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 10:47:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[200ms]
2025-07-19 10:47:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 10:47:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-19 10:47:49 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-19 10:47:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 10:47:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-19 10:47:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 10:47:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[98ms]
2025-07-19 10:47:49 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJPa0FGN0JXRHdGaTRjcXFCYXRkemRRTjZwckZKTHdpNSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.IEA8IXePBA0Whzea_a1cD6WRxI6MdjTBVr4Ma6kS2Q8
2025-07-19 11:00:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7acfe262, L:/*************:4976 - R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x733dfa2d, L:/*************:4766 - R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x733dfa2d, L:/*************:4766 - R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x7acfe262, L:/*************:4976 - R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7acfe262, L:/*************:4976 ! R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x733dfa2d, L:/*************:4766 ! R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7acfe262, L:/*************:4976 ! R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x733dfa2d, L:/*************:4766 ! R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7acfe262, L:/*************:4976 ! R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x733dfa2d, L:/*************:4766 ! R:/*************:8091]
2025-07-19 11:00:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7acfe262, L:/*************:4976 ! R:/*************:8091]) will closed
2025-07-19 11:00:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x733dfa2d, L:/*************:4766 ! R:/*************:8091]) will closed
2025-07-19 11:00:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x733dfa2d, L:/*************:4766 ! R:/*************:8091]) will closed
2025-07-19 11:00:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7acfe262, L:/*************:4976 ! R:/*************:8091]) will closed
2025-07-19 11:08:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-19 11:08:38 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 2424 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 11:08:38 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-19 11:08:38 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-19 11:08:38 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-19 11:08:41 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-19 11:08:41 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 11:08:41 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 11:08:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-19 11:08:41 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-19 11:08:41 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-19 11:08:41 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 11:08:41 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752894521520
timestamp=1752894521520
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 11:08:41 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7600c014, L:/*************:2766 - R:/*************:8091]
2025-07-19 11:08:41 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 77 ms, version:1.7.1,role:TMROLE,channel:[id: 0x7600c014, L:/*************:2766 - R:/*************:8091]
2025-07-19 11:08:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-19 11:08:41 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-19 11:08:41 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-19 11:08:41 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-19 11:08:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-19 11:08:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-19 11:08:42 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-19 11:08:43 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-19 11:08:44 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-19 11:08:44 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-19 11:08:44 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-19 11:08:44 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-19 11:08:45 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-19 11:08:45 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-19 11:08:45 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-19 11:08:46 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-19 11:08:46 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-19 11:08:48 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 11.995 seconds (process running for 13.026)
2025-07-19 11:08:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-19 11:08:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-19 11:08:48 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 11:09:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 11:09:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 11:09:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x604f0e33, L:/*************:2973 - R:/*************:8091]
2025-07-19 11:09:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x604f0e33, L:/*************:2973 - R:/*************:8091]
2025-07-19 11:15:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:15:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[108ms]
2025-07-19 11:15:25 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-19 11:15:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:15:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[15ms]
2025-07-19 11:15:26 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJPa0FGN0JXRHdGaTRjcXFCYXRkemRRTjZwckZKTHdpNSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.IEA8IXePBA0Whzea_a1cD6WRxI6MdjTBVr4Ma6kS2Q8
2025-07-19 11:15:27 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[21ms]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[144ms]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Success][登录成功]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[75ms]
2025-07-19 11:15:32 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJueVZUYThnajRvN2dzdWFab29RcUJ1aVlBMU9RZzJxZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.njSAQLMI4AQlrFyU5UbQuQf78US5BieNW-zRlO1__7M
2025-07-19 11:15:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:15:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[25ms]
2025-07-19 11:15:52 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Logout][退出成功]
2025-07-19 11:15:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:15:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-19 11:15:52 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJueVZUYThnajRvN2dzdWFab29RcUJ1aVlBMU9RZzJxZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.njSAQLMI4AQlrFyU5UbQuQf78US5BieNW-zRlO1__7M
2025-07-19 11:15:53 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 11:15:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:15:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[21ms]
2025-07-19 11:15:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 11:15:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[130ms]
2025-07-19 11:15:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:15:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[21ms]
2025-07-19 11:15:57 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-19 11:15:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:15:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 11:15:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 11:15:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[66ms]
2025-07-19 11:15:57 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJzc1JlbEZ3cWtrd1VEQjRnbWg3T1daczJSZTBlR2RGViIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.eGHPlIDbM4vMIaLbq27ajJRl9VMSaG5FIXL3cBLRsbE
2025-07-19 11:16:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:16:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[47ms]
2025-07-19 11:16:33 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-19 11:16:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:16:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-19 11:16:33 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJzc1JlbEZ3cWtrd1VEQjRnbWg3T1daczJSZTBlR2RGViIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.eGHPlIDbM4vMIaLbq27ajJRl9VMSaG5FIXL3cBLRsbE
2025-07-19 11:16:34 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 11:16:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:16:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[21ms]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[134ms]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15900419286][Success][登录成功]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[67ms]
2025-07-19 11:16:39 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiJ5Vk9IQUlxWHZYTW1rU0xzTlJFTUhVSlNSNlNabHNOWiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.UZwfUZulgTKkWqV5U64XWnd2KRYkKCXNOH99bAQNAFk
2025-07-19 11:17:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:17:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[40ms]
2025-07-19 11:17:47 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15900419286][Logout][退出成功]
2025-07-19 11:17:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:17:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 11:17:47 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiJ5Vk9IQUlxWHZYTW1rU0xzTlJFTUhVSlNSNlNabHNOWiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.UZwfUZulgTKkWqV5U64XWnd2KRYkKCXNOH99bAQNAFk
2025-07-19 11:17:49 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[26ms]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[131ms]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[66ms]
2025-07-19 11:17:56 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJMdTBqbDZoRWxCS3ZvZk5mT0toYUZoaFNTQ3NQY1FadiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.pAGgsCEqz8jRTQ34RB1QHIbR5kB7ao_2oSSFP-Bgtg0
2025-07-19 11:32:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:32:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[48ms]
2025-07-19 11:32:24 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Logout][退出成功]
2025-07-19 11:32:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:32:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 11:32:24 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJMdTBqbDZoRWxCS3ZvZk5mT0toYUZoaFNTQ3NQY1FadiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.pAGgsCEqz8jRTQ34RB1QHIbR5kB7ao_2oSSFP-Bgtg0
2025-07-19 11:32:25 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[131ms]
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15020035670][Success][登录成功]
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 11:32:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 11:32:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[65ms]
2025-07-19 11:32:29 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934278130524852226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0Mjc4MTMwNTI0ODUyMjI2Iiwicm5TdHIiOiJ3MDVRcFJTYXhRRURBeHRSYVFxdWZ3S0N2NGJQekRhZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI3ODEzMDUyNDg1MjIyNiwidXNlck5hbWUiOiIxNTAyMDAzNTY3MCIsImRlcHRJZCI6MTkzNDI2Mjg3NTY4MTgyMDY3MywiZGVwdE5hbWUiOiLpnZLlspvmlrDmtbfnkZ7mtIvmtbfkuovmnI3liqHmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.oaQCk-iBCPxEcclJzENX0dQD6Cm96PNILGa0hrmFySg
2025-07-19 11:37:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:37:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[48ms]
2025-07-19 11:37:39 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15020035670][Logout][退出成功]
2025-07-19 11:37:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:37:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 11:37:39 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934278130524852226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0Mjc4MTMwNTI0ODUyMjI2Iiwicm5TdHIiOiJ3MDVRcFJTYXhRRURBeHRSYVFxdWZ3S0N2NGJQekRhZyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI3ODEzMDUyNDg1MjIyNiwidXNlck5hbWUiOiIxNTAyMDAzNTY3MCIsImRlcHRJZCI6MTkzNDI2Mjg3NTY4MTgyMDY3MywiZGVwdE5hbWUiOiLpnZLlspvmlrDmtbfnkZ7mtIvmtbfkuovmnI3liqHmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.oaQCk-iBCPxEcclJzENX0dQD6Cm96PNILGa0hrmFySg
2025-07-19 11:37:40 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 11:48:51 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-19 11:48:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x604f0e33, L:/*************:2973 ! R:/*************:8091]
2025-07-19 11:48:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7600c014, L:/*************:2766 ! R:/*************:8091]
2025-07-19 11:48:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7600c014, L:/*************:2766 ! R:/*************:8091]
2025-07-19 11:48:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x604f0e33, L:/*************:2973 ! R:/*************:8091]
2025-07-19 11:48:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7600c014, L:/*************:2766 ! R:/*************:8091]
2025-07-19 11:48:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x604f0e33, L:/*************:2973 ! R:/*************:8091]
2025-07-19 11:48:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7600c014, L:/*************:2766 ! R:/*************:8091]
2025-07-19 11:48:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x604f0e33, L:/*************:2973 ! R:/*************:8091]
2025-07-19 11:48:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7600c014, L:/*************:2766 ! R:/*************:8091]) will closed
2025-07-19 11:48:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x604f0e33, L:/*************:2973 ! R:/*************:8091]) will closed
2025-07-19 11:48:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x604f0e33, L:/*************:2973 ! R:/*************:8091]) will closed
2025-07-19 11:48:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7600c014, L:/*************:2766 ! R:/*************:8091]) will closed
2025-07-19 11:49:01 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 11:49:01 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752896941343
timestamp=1752896941343
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 11:49:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 11:49:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 11:49:03 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2bf599c0, L:/*************:5149 - R:/*************:8091]
2025-07-19 11:49:03 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 56 ms, version:1.7.1,role:TMROLE,channel:[id: 0x2bf599c0, L:/*************:5149 - R:/*************:8091]
2025-07-19 11:49:03 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5a643710, L:/*************:5151 - R:/*************:8091]
2025-07-19 11:49:03 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x5a643710, L:/*************:5151 - R:/*************:8091]
2025-07-19 11:53:03 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 11:53:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:53:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[65ms]
2025-07-19 11:53:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 11:53:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[141ms]
2025-07-19 11:53:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 11:53:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 11:53:06 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15020035670][Success][登录成功]
2025-07-19 11:53:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 11:53:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-19 11:53:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 11:53:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[62ms]
2025-07-19 11:53:06 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934278130524852226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0Mjc4MTMwNTI0ODUyMjI2Iiwicm5TdHIiOiJoVFYwMDNxWjBTNVI1NmcxMG84eDJQaXJKdm5DaldMViIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI3ODEzMDUyNDg1MjIyNiwidXNlck5hbWUiOiIxNTAyMDAzNTY3MCIsImRlcHRJZCI6MTkzNDI2Mjg3NTY4MTgyMDY3MywiZGVwdE5hbWUiOiLpnZLlspvmlrDmtbfnkZ7mtIvmtbfkuovmnI3liqHmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.I8LukW3xihKUuQRCCXzSPcYGHRc0gLijuRCBZhPvRz0
2025-07-19 12:59:22 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934278130524852226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0Mjc4MTMwNTI0ODUyMjI2Iiwicm5TdHIiOiJoVFYwMDNxWjBTNVI1NmcxMG84eDJQaXJKdm5DaldMViIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI3ODEzMDUyNDg1MjIyNiwidXNlck5hbWUiOiIxNTAyMDAzNTY3MCIsImRlcHRJZCI6MTkzNDI2Mjg3NTY4MTgyMDY3MywiZGVwdE5hbWUiOiLpnZLlspvmlrDmtbfnkZ7mtIvmtbfkuovmnI3liqHmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.I8LukW3xihKUuQRCCXzSPcYGHRc0gLijuRCBZhPvRz0
2025-07-19 12:59:23 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 13:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:32:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[47ms]
2025-07-19 13:32:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:32:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 13:32:45 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15020035670][Error][验证码已失效]
2025-07-19 13:32:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:32:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 13:32:45 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 13:32:48 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:32:48 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 13:32:48 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 13:32:48 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[162ms]
2025-07-19 13:32:49 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:32:49 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 13:32:49 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15020035670][Success][登录成功]
2025-07-19 13:32:49 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:32:49 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 13:32:49 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 13:32:49 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[94ms]
2025-07-19 13:32:49 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934278130524852226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0Mjc4MTMwNTI0ODUyMjI2Iiwicm5TdHIiOiJNaTRvdnk2dTd6a04zUUVRUjVUZ3h4MmlyY2ZqS3ZBMCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI3ODEzMDUyNDg1MjIyNiwidXNlck5hbWUiOiIxNTAyMDAzNTY3MCIsImRlcHRJZCI6MTkzNDI2Mjg3NTY4MTgyMDY3MywiZGVwdE5hbWUiOiLpnZLlspvmlrDmtbfnkZ7mtIvmtbfkuovmnI3liqHmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.Rytgz1G-3H9ZM4139Crhurz-laYf4Wfxxda4HiAcdmw
2025-07-19 13:33:17 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[39ms]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[124ms]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[44ms]
2025-07-19 13:33:28 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJ4aTRlQ1QxQXpCTms5TmRHUm9uSk1pMGNodXk3c1RIdSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.TGVCoqTLnbRtme5W9WnEas7SHb8RpvUzI6LuMqPwiT0
2025-07-19 13:40:27 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:40:27 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[38ms]
2025-07-19 13:40:27 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-19 13:40:27 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:40:27 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 13:40:27 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJ4aTRlQ1QxQXpCTms5TmRHUm9uSk1pMGNodXk3c1RIdSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.TGVCoqTLnbRtme5W9WnEas7SHb8RpvUzI6LuMqPwiT0
2025-07-19 13:40:28 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[25ms]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[131ms]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15900419286][Success][登录成功]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[60ms]
2025-07-19 13:40:33 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiI0RHpuOUtqVW5YYUhZbmdOUkpwamFWSmlQTDMxRnp1SCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.d-PY3P-eaCNW-xKJiIgMgr8nU4mBOPaHG0C9qa9upMQ
2025-07-19 13:53:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:53:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[50ms]
2025-07-19 13:53:39 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15900419286][Logout][退出成功]
2025-07-19 13:53:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:53:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 13:53:39 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiI0RHpuOUtqVW5YYUhZbmdOUkpwamFWSmlQTDMxRnp1SCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.d-PY3P-eaCNW-xKJiIgMgr8nU4mBOPaHG0C9qa9upMQ
2025-07-19 13:53:40 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 13:53:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:53:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[24ms]
2025-07-19 13:53:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 13:53:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[118ms]
2025-07-19 13:53:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:53:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-19 13:53:47 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-19 13:53:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:53:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 13:53:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 13:53:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[41ms]
2025-07-19 13:53:47 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJVWUVWU09tVjBPUXpnVEJTc3ZhTjZGcHNmRURLZjBucyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.xFhcAS3ZLwZjT381pjQg5AmPdgX2GXZb-IZwLyQ18Eg
2025-07-19 13:54:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:54:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[43ms]
2025-07-19 13:54:42 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-19 13:54:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:54:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 13:54:42 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJVWUVWU09tVjBPUXpnVEJTc3ZhTjZGcHNmRURLZjBucyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.xFhcAS3ZLwZjT381pjQg5AmPdgX2GXZb-IZwLyQ18Eg
2025-07-19 13:54:43 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 13:54:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:54:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 13:54:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 13:54:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[98ms]
2025-07-19 13:54:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:54:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 13:54:46 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-19 13:54:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:54:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 13:54:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 13:54:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[57ms]
2025-07-19 13:54:46 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJzWTJaRkREbVZKdThBUThDZ1dubzlpbHBzekFJa2FKZSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.SlIFFn5_P7GbeyWE6BEbSYun3QmqDwVLYSrKrjI8m-A
2025-07-19 13:55:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:55:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[43ms]
2025-07-19 13:55:25 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-19 13:55:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:55:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 13:55:25 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJzWTJaRkREbVZKdThBUThDZ1dubzlpbHBzekFJa2FKZSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.SlIFFn5_P7GbeyWE6BEbSYun3QmqDwVLYSrKrjI8m-A
2025-07-19 13:55:26 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 13:55:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[27ms]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[95ms]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15900419286][Success][登录成功]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[40ms]
2025-07-19 13:55:31 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiJsVm1KOVhIWndlQWpndzEzN0x1TnVtVGtZamN3eWNDRyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.EP6NwQYZQLFv1qiksRuutipEFaxUnZkfboCBLkKkFS0
2025-07-19 14:06:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 14:06:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[39ms]
2025-07-19 14:06:00 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15900419286][Logout][退出成功]
2025-07-19 14:06:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 14:06:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 14:06:00 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiJsVm1KOVhIWndlQWpndzEzN0x1TnVtVGtZamN3eWNDRyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.EP6NwQYZQLFv1qiksRuutipEFaxUnZkfboCBLkKkFS0
2025-07-19 14:06:01 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 14:06:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 14:06:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[25ms]
2025-07-19 14:06:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 14:06:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[106ms]
2025-07-19 14:06:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 14:06:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-19 14:06:07 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-19 14:06:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 14:06:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 14:06:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 14:06:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[44ms]
2025-07-19 14:06:07 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJId05zb1ZTN1NzTFN1WWkyNFp3b0ZXbTBDSElxY3IxeCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.pL62kR2whs_jYRCFlRpRaHmmo6nSvYVMs-HI20oY5XU
2025-07-19 14:24:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 14:24:48 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[23ms]
2025-07-19 14:24:48 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-19 14:24:48 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 14:24:48 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-19 14:24:48 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJId05zb1ZTN1NzTFN1WWkyNFp3b0ZXbTBDSElxY3IxeCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.pL62kR2whs_jYRCFlRpRaHmmo6nSvYVMs-HI20oY5XU
2025-07-19 14:24:49 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 14:25:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 14:25:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[21ms]
2025-07-19 14:25:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 14:25:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[173ms]
2025-07-19 14:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 14:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-19 14:25:05 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15900419286][Success][登录成功]
2025-07-19 14:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 14:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 14:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 14:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[79ms]
2025-07-19 14:25:05 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiJUbGs0SFRJaW9mV1RoWmU4dnV5enNJcm5pb05aa2p5MyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.gvPBta46uy9m1GGOQqt9yVxqaw7NsHZxpSFol5mPpaA
2025-07-19 14:28:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 14:28:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[30ms]
2025-07-19 14:28:23 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15900419286][Logout][退出成功]
2025-07-19 14:28:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 14:28:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-19 14:28:23 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934266697045086210, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiJUbGs0SFRJaW9mV1RoWmU4dnV5enNJcm5pb05aa2p5MyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.gvPBta46uy9m1GGOQqt9yVxqaw7NsHZxpSFol5mPpaA
2025-07-19 14:28:24 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 14:28:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 14:28:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[22ms]
2025-07-19 14:28:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 14:28:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[133ms]
2025-07-19 14:28:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 14:28:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-19 14:28:31 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-19 14:28:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 14:28:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-19 14:28:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 14:28:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[54ms]
2025-07-19 14:28:31 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJoclBkd09OeTZwYjZqUENiYXp6ZE50VjhONjZVOGJ6SyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.0l5OzbFp0W1oeQTyeZ-8VQf0xrSqkb77MNHjYLJleF8
2025-07-19 15:20:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 15:20:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[91ms]
2025-07-19 15:20:46 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-19 15:20:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 15:20:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[25ms]
2025-07-19 15:20:46 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJoclBkd09OeTZwYjZqUENiYXp6ZE50VjhONjZVOGJ6SyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.0l5OzbFp0W1oeQTyeZ-8VQf0xrSqkb77MNHjYLJleF8
2025-07-19 15:20:47 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 15:20:56 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 15:27:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 15:27:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[48ms]
2025-07-19 15:27:33 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15020035670][Logout][退出成功]
2025-07-19 15:27:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 15:27:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-07-19 15:27:33 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934278130524852226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0Mjc4MTMwNTI0ODUyMjI2Iiwicm5TdHIiOiJNaTRvdnk2dTd6a04zUUVRUjVUZ3h4MmlyY2ZqS3ZBMCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI3ODEzMDUyNDg1MjIyNiwidXNlck5hbWUiOiIxNTAyMDAzNTY3MCIsImRlcHRJZCI6MTkzNDI2Mjg3NTY4MTgyMDY3MywiZGVwdE5hbWUiOiLpnZLlspvmlrDmtbfnkZ7mtIvmtbfkuovmnI3liqHmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.Rytgz1G-3H9ZM4139Crhurz-laYf4Wfxxda4HiAcdmw
2025-07-19 15:27:33 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 15:27:38 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 16:02:04 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 16:02:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 16:02:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[109ms]
2025-07-19 16:02:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 16:02:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[1192ms]
2025-07-19 16:02:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 16:02:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[6ms]
2025-07-19 16:02:08 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15020035670][Success][登录成功]
2025-07-19 16:02:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 16:02:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[10ms]
2025-07-19 16:02:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 16:02:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[137ms]
2025-07-19 16:02:09 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934278130524852226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0Mjc4MTMwNTI0ODUyMjI2Iiwicm5TdHIiOiJOdG9aSFdTT2NXMEExUlJYamJLVjdnbmlUaU4ybGVqYSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI3ODEzMDUyNDg1MjIyNiwidXNlck5hbWUiOiIxNTAyMDAzNTY3MCIsImRlcHRJZCI6MTkzNDI2Mjg3NTY4MTgyMDY3MywiZGVwdE5hbWUiOiLpnZLlspvmlrDmtbfnkZ7mtIvmtbfkuovmnI3liqHmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.DpnFWwS1qpwH2jLIasTCe4hcEcMG4euTwKK6EYkxKHc
2025-07-19 16:40:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5a643710, L:/*************:5151 - R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2bf599c0, L:/*************:5149 - R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5a643710, L:/*************:5151 - R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x2bf599c0, L:/*************:5149 - R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5a643710, L:/*************:5151 ! R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2bf599c0, L:/*************:5149 ! R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2bf599c0, L:/*************:5149 ! R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5a643710, L:/*************:5151 ! R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2bf599c0, L:/*************:5149 ! R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5a643710, L:/*************:5151 ! R:/*************:8091]
2025-07-19 16:40:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a643710, L:/*************:5151 ! R:/*************:8091]) will closed
2025-07-19 16:40:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2bf599c0, L:/*************:5149 ! R:/*************:8091]) will closed
2025-07-19 16:40:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a643710, L:/*************:5151 ! R:/*************:8091]) will closed
2025-07-19 16:40:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2bf599c0, L:/*************:5149 ! R:/*************:8091]) will closed
2025-07-19 16:45:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-19 16:45:02 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 23720 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 16:45:02 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-19 16:45:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-19 16:45:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-19 16:45:07 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-19 16:45:07 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 16:45:07 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 16:45:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-19 16:45:07 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-19 16:45:07 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-19 16:45:07 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 16:45:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752914708009
timestamp=1752914708009
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 16:45:08 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x29742ebe, L:/*************:11609 - R:/*************:8091]
2025-07-19 16:45:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 146 ms, version:1.7.1,role:TMROLE,channel:[id: 0x29742ebe, L:/*************:11609 - R:/*************:8091]
2025-07-19 16:45:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-19 16:45:08 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-19 16:45:08 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-19 16:45:08 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-19 16:45:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-19 16:45:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-19 16:45:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-19 16:45:11 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-19 16:45:13 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-19 16:45:13 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-19 16:45:13 [redisson-netty-2-7] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-19 16:45:13 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-19 16:45:16 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-19 16:45:16 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-19 16:45:16 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-19 16:45:16 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-19 16:45:16 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-19 16:45:19 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 19.36 seconds (process running for 20.431)
2025-07-19 16:45:19 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-19 16:45:19 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-19 16:45:19 [RMI TCP Connection(5)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 16:46:08 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 16:46:08 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 16:46:08 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x93a6c18f, L:/*************:11871 - R:/*************:8091]
2025-07-19 16:46:08 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x93a6c18f, L:/*************:11871 - R:/*************:8091]
2025-07-19 17:55:09 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934278130524852226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0Mjc4MTMwNTI0ODUyMjI2Iiwicm5TdHIiOiJOdG9aSFdTT2NXMEExUlJYamJLVjdnbmlUaU4ybGVqYSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI3ODEzMDUyNDg1MjIyNiwidXNlck5hbWUiOiIxNTAyMDAzNTY3MCIsImRlcHRJZCI6MTkzNDI2Mjg3NTY4MTgyMDY3MywiZGVwdE5hbWUiOiLpnZLlspvmlrDmtbfnkZ7mtIvmtbfkuovmnI3liqHmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.DpnFWwS1qpwH2jLIasTCe4hcEcMG4euTwKK6EYkxKHc
2025-07-19 17:55:10 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-19 17:55:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[181ms]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[230ms]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15020035670][Success][登录成功]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[38ms]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[82ms]
2025-07-19 17:55:13 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934278130524852226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0Mjc4MTMwNTI0ODUyMjI2Iiwicm5TdHIiOiJnRm1YVGRwWXZEM2VrMGpNN25haU1QMzcwRG5sV1NZVyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI3ODEzMDUyNDg1MjIyNiwidXNlck5hbWUiOiIxNTAyMDAzNTY3MCIsImRlcHRJZCI6MTkzNDI2Mjg3NTY4MTgyMDY3MywiZGVwdE5hbWUiOiLpnZLlspvmlrDmtbfnkZ7mtIvmtbfkuovmnI3liqHmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.gyOvUJzDM3Mrc27uVjg_fSSwHqzUd0Rgt1Z6a55xg6Y
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x29742ebe, L:/*************:11609 - R:/*************:8091] read idle.
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x29742ebe, L:/*************:11609 - R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x29742ebe, L:/*************:11609 - R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x29742ebe, L:/*************:11609 ! R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x93a6c18f, L:/*************:11871 - R:/*************:8091] read idle.
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x93a6c18f, L:/*************:11871 - R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93a6c18f, L:/*************:11871 - R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]) will closed
2025-07-19 18:45:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93a6c18f, L:/*************:11871 ! R:/*************:8091]) will closed
2025-07-19 18:45:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:45:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752921954585
timestamp=1752921954585
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 18:45:54 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2007cc5e, L:/*************:3315 - R:/*************:8091]
2025-07-19 18:45:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:TMROLE,channel:[id: 0x2007cc5e, L:/*************:3315 - R:/*************:8091]
2025-07-19 18:45:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:45:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 18:45:55 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x6399859a, L:/*************:3318 - R:/*************:8091]
2025-07-19 18:45:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0x6399859a, L:/*************:3318 - R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2007cc5e, L:/*************:3315 - R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x6399859a, L:/*************:3318 - R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x2007cc5e, L:/*************:3315 - R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x6399859a, L:/*************:3318 - R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2007cc5e, L:/*************:3315 ! R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x6399859a, L:/*************:3318 ! R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2007cc5e, L:/*************:3315 ! R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x6399859a, L:/*************:3318 ! R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2007cc5e, L:/*************:3315 ! R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x6399859a, L:/*************:3318 ! R:/*************:8091]
2025-07-19 18:46:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2007cc5e, L:/*************:3315 ! R:/*************:8091]) will closed
2025-07-19 18:46:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6399859a, L:/*************:3318 ! R:/*************:8091]) will closed
2025-07-19 18:46:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2007cc5e, L:/*************:3315 ! R:/*************:8091]) will closed
2025-07-19 18:46:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6399859a, L:/*************:3318 ! R:/*************:8091]) will closed
2025-07-19 18:46:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:46:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752921984571
timestamp=1752921984571
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 18:46:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:46:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x6399859a, L:/*************:3318 ! R:/*************:8091]
2025-07-19 18:46:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x6399859a, L:/*************:3318 ! R:/*************:8091]
2025-07-19 18:46:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 18:46:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb6edae2c, L:null ! R:/*************:8091]) will closed
2025-07-19 18:46:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x13821d73, L:null ! R:/*************:8091]) will closed
2025-07-19 18:46:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:46:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752921994579
timestamp=1752921994579
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 18:46:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:46:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 18:46:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x054e3a45, L:null ! R:/*************:8091]) will closed
2025-07-19 18:46:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc4fba7a4, L:null ! R:/*************:8091]) will closed
2025-07-19 18:46:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:46:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752922004571
timestamp=1752922004571
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 18:46:45 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:46:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 18:46:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe30cbe5d, L:null ! R:/*************:8091]) will closed
2025-07-19 18:46:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb1a6807e, L:null ! R:/*************:8091]) will closed
2025-07-19 18:46:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:46:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752922014580
timestamp=1752922014580
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 18:46:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:46:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 18:46:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0f31f309, L:null ! R:/*************:8091]) will closed
2025-07-19 18:46:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x26e79bcf, L:null ! R:/*************:8091]) will closed
2025-07-19 18:47:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:47:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752922024571
timestamp=1752922024571
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 18:47:05 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:47:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 18:47:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd8e61de3, L:null ! R:/*************:8091]) will closed
2025-07-19 18:47:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x006df75b, L:null ! R:/*************:8091]) will closed
2025-07-19 18:47:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:47:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752922034571
timestamp=1752922034571
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 18:47:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:47:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-19 18:47:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x570b1f9d, L:null ! R:/*************:8091]) will closed
2025-07-19 18:47:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbb8a1687, L:null ! R:/*************:8091]) will closed
2025-07-19 18:47:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:47:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752922044570
timestamp=1752922044570
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-19 18:47:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-19 18:47:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
