2025-07-16 08:33:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 08:33:29 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 23864 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 08:33:29 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-16 08:33:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-16 08:33:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 08:33:33 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 08:33:34 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 08:33:34 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 08:33:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 08:33:34 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 08:33:34 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 08:33:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 08:33:34 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 08:33:34 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 08:33:34 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 08:33:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 08:33:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 08:33:36 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 08:33:38 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 08:33:40 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 08:33:40 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 08:33:40 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 08:33:40 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 08:33:42 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 08:33:42 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 08:33:42 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 08:33:43 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 08:33:43 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth ************:9210 register finished
2025-07-16 08:33:45 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 18.781 seconds (process running for 19.823)
2025-07-16 08:33:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 08:33:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-16 08:33:46 [RMI TCP Connection(4)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 08:34:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 08:34:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752626074524
timestamp=1752626074524
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 08:34:34 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x1ce8f3a0, L:/************:2295 - R:/************:8091]
2025-07-16 08:34:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 24 ms, version:1.7.1,role:TMROLE,channel:[id: 0x1ce8f3a0, L:/************:2295 - R:/************:8091]
2025-07-16 08:34:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 08:34:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 08:34:34 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xec765eb3, L:/************:2296 - R:/************:8091]
2025-07-16 08:34:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0xec765eb3, L:/************:2296 - R:/************:8091]
2025-07-16 08:43:25 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 08:43:54 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 08:50:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 08:50:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[659ms]
2025-07-16 08:50:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 08:50:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[15ms]
2025-07-16 08:50:51 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Error][验证码已失效]
2025-07-16 08:50:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 08:50:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[53ms]
2025-07-16 08:50:51 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 09:08:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 09:08:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[108ms]
2025-07-16 09:08:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 09:08:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-16 09:08:51 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Error][验证码已失效]
2025-07-16 09:08:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 09:08:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-16 09:08:51 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 09:08:54 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 09:08:54 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-16 09:08:54 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 09:08:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[572ms]
2025-07-16 09:08:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 09:08:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-16 09:08:55 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Success][登录成功]
2025-07-16 09:08:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 09:08:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-16 09:08:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 09:08:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[101ms]
2025-07-16 09:08:55 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJPQlJKZFdxM2xGMklOOWJRN1RieTFxTU1SUGZjSUtIeiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.L0fdwGPvZOJXOj6p2NpF7PrF1jq-e3DP_u9RaSIUhII
2025-07-16 09:09:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 09:09:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[73ms]
2025-07-16 09:09:26 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Logout][退出成功]
2025-07-16 09:09:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 09:09:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-16 09:09:26 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJPQlJKZFdxM2xGMklOOWJRN1RieTFxTU1SUGZjSUtIeiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.L0fdwGPvZOJXOj6p2NpF7PrF1jq-e3DP_u9RaSIUhII
2025-07-16 09:09:27 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[23ms]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[173ms]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[70ms]
2025-07-16 09:09:34 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJjVlNnMmVqQVBNWW8xamZlTHdqSFJWblRnTkRzREhkcSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Yc76xMMTAkIJNv5wzCP_99Kq2C7YcH1kyRTjYI5GU3U
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xec765eb3, L:/************:2296 - R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x1ce8f3a0, L:/************:2295 - R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x1ce8f3a0, L:/************:2295 - R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xec765eb3, L:/************:2296 - R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xec765eb3, L:/************:2296 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x1ce8f3a0, L:/************:2295 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xec765eb3, L:/************:2296 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x1ce8f3a0, L:/************:2295 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x1ce8f3a0, L:/************:2295 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xec765eb3, L:/************:2296 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1ce8f3a0, L:/************:2295 ! R:/************:8091]) will closed
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xec765eb3, L:/************:2296 ! R:/************:8091]) will closed
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1ce8f3a0, L:/************:2295 ! R:/************:8091]) will closed
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xec765eb3, L:/************:2296 ! R:/************:8091]) will closed
2025-07-16 09:14:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:14:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628494658
timestamp=1752628494658
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:14:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:14:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xec765eb3, L:/************:2296 ! R:/************:8091]
2025-07-16 09:14:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xec765eb3, L:/************:2296 ! R:/************:8091]
2025-07-16 09:14:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:15:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x82d41711, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628504661
timestamp=1752628504661
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:15:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd0f5acfe, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:15:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc96561fa, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628514663
timestamp=1752628514663
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:15:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:15:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd95520ee, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628524665
timestamp=1752628524665
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:15:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x90202938, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe5865ad5, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:15:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628534667
timestamp=1752628534667
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:15:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7f2b8fc3, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0a06e701, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:15:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaff14832, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628544669
timestamp=1752628544669
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:15:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe8f3c3e6, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:44 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:15:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628554671
timestamp=1752628554671
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:15:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88d635b1, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:15:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x824f2dca, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x703f4832, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628564673
timestamp=1752628564673
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:16:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:16:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8a0b855d, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x80a425d4, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628574675
timestamp=1752628574675
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:16:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:16:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x61142f6d, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628584677
timestamp=1752628584677
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:16:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9dfa55ed, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xceafdd51, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:16:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4a6925ee, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628594679
timestamp=1752628594679
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:16:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbb9670fa, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:16:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7405136e, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628604687
timestamp=1752628604687
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:16:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf61810e9, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:44 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:16:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628614689
timestamp=1752628614689
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:16:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6f6a4327, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x670a0601, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:17:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7b130d65, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628624691
timestamp=1752628624691
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:17:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc5b2264b, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:17:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628634693
timestamp=1752628634693
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:17:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x01060757, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6bb9359e, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:17:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbf3ac5dc, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628644696
timestamp=1752628644696
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:17:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbc87827a, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:17:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x60c26370, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628654698
timestamp=1752628654698
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:17:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:17:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x464e6b38, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7a5a810b, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628664700
timestamp=1752628664700
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:17:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7994f13, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:44 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:17:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x153fbf44, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628674703
timestamp=1752628674703
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:17:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfafdd43a, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:18:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x16982838, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628684705
timestamp=1752628684705
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:18:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:18:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2e354952, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628694706
timestamp=1752628694706
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:18:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa5f0649f, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xafd0c7c3, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:18:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb96ced11, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628704708
timestamp=1752628704708
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:18:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x07a065c5, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:18:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x952d2db9, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628714711
timestamp=1752628714711
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:18:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:18:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe8f8d6f, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628724712
timestamp=1752628724712
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:18:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb10151a9, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe66ee303, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:44 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:18:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628734714
timestamp=1752628734714
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:18:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb9e43961, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc2b1ad00, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:19:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628744716
timestamp=1752628744716
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:19:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8b770f06, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x83c4994f, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:19:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628754718
timestamp=1752628754718
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:19:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcd5147a8, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:19:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e9843cb, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628764719
timestamp=1752628764719
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:19:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x137c2feb, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:19:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48ca9482, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628774721
timestamp=1752628774721
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:19:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb31f926f, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:19:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbc2da8c2, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628784722
timestamp=1752628784722
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:19:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa8d63bc9, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:44 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:19:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdeb82139, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628794724
timestamp=1752628794724
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:19:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd407f9e6, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x59e7b943, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:20:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628804726
timestamp=1752628804726
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:20:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x014b3f9b, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf12eaa61, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:20:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8ef0f8dc, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628814729
timestamp=1752628814729
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:20:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:20:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeb82ce9f, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628824730
timestamp=1752628824730
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:20:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb5578a9a, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8c338f62, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:20:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628834732
timestamp=1752628834732
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:20:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf9c062cf, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1a81f7c8, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:20:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628844733
timestamp=1752628844733
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:20:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf52a1052, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9b1bb41c, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:45 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:20:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628854735
timestamp=1752628854735
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:20:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xba333634, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbc5e629, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:21:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfbbd909f, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628864737
timestamp=1752628864737
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:21:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x242cb60c, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:05 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:21:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa1045379, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628874740
timestamp=1752628874740
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:21:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x447ecc65, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:21:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfedda7f2, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628884742
timestamp=1752628884742
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:21:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xddc6a3a9, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:21:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628894744
timestamp=1752628894744
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:21:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x95a61377, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x66d51751, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:21:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628904745
timestamp=1752628904745
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:21:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbf40b98, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:45 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:21:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc72b9f28, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x14ecfff7, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628914747
timestamp=1752628914747
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:21:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:21:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd614d63d, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2341f594, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628924749
timestamp=1752628924749
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:22:05 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:22:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x974cbdc0, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628934751
timestamp=1752628934751
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:22:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x87c0d437, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2bf14efa, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:22:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628944753
timestamp=1752628944753
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:22:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x75d0c460, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb4c4c023, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:22:33 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-16 09:22:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3eaaaa43, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628954755
timestamp=1752628954755
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:22:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:22:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf302b712, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x37a8ae24, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628964757
timestamp=1752628964757
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:22:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1903bf22, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:45 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:22:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcddd23dd, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:22:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752628974759
timestamp=1752628974759
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:22:54 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4457bd5d, L:/***************:7215 - R:/***************:8091]
2025-07-16 09:22:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:TMROLE,channel:[id: 0x4457bd5d, L:/***************:7215 - R:/***************:8091]
2025-07-16 09:22:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x99d05f07, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:22:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:22:55 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe82102af, L:/***************:7219 - R:/***************:8091]
2025-07-16 09:22:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 30 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe82102af, L:/***************:7219 - R:/***************:8091]
2025-07-16 09:25:08 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4457bd5d, L:/***************:7215 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe82102af, L:/***************:7219 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4457bd5d, L:/***************:7215 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe82102af, L:/***************:7219 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4457bd5d, L:/***************:7215 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe82102af, L:/***************:7219 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4457bd5d, L:/***************:7215 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe82102af, L:/***************:7219 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4457bd5d, L:/***************:7215 ! R:/***************:8091]) will closed
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe82102af, L:/***************:7219 ! R:/***************:8091]) will closed
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4457bd5d, L:/***************:7215 ! R:/***************:8091]) will closed
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe82102af, L:/***************:7219 ! R:/***************:8091]) will closed
2025-07-16 09:25:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752629114695
timestamp=1752629114695
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:25:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:25:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7c8735b1, L:null ! R:/***************:8091]) will closed
2025-07-16 09:25:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x63596ec9, L:null ! R:/***************:8091]) will closed
2025-07-16 09:25:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752629124696
timestamp=1752629124696
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-16 09:25:24 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x05f7b6fa, L:/***************:7431 - R:/***************:8091]
2025-07-16 09:25:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 62 ms, version:1.7.1,role:TMROLE,channel:[id: 0x05f7b6fa, L:/***************:7431 - R:/***************:8091]
2025-07-16 09:25:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 09:25:24 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x89c75c8e, L:/***************:7435 - R:/***************:8091]
2025-07-16 09:25:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 13 ms, version:1.7.1,role:RMROLE,channel:[id: 0x89c75c8e, L:/***************:7435 - R:/***************:8091]
2025-07-16 10:01:03 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-16 10:01:03 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-16 10:01:03 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-16 10:01:03 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-16 10:01:03 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-16 10:01:03 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x05f7b6fa, L:/***************:7431 ! R:/***************:8091]
2025-07-16 10:01:03 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x05f7b6fa, L:/***************:7431 ! R:/***************:8091]
2025-07-16 10:01:03 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x05f7b6fa, L:/***************:7431 ! R:/***************:8091]
2025-07-16 10:01:03 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x05f7b6fa, L:/***************:7431 ! R:/***************:8091]
2025-07-16 10:01:03 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x05f7b6fa, L:/***************:7431 ! R:/***************:8091]) will closed
2025-07-16 10:01:03 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x05f7b6fa, L:/***************:7431 ! R:/***************:8091]) will closed
2025-07-16 10:01:03 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x89c75c8e, L:/***************:7435 ! R:/***************:8091]
2025-07-16 10:01:03 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x89c75c8e, L:/***************:7435 ! R:/***************:8091]
2025-07-16 10:01:03 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x89c75c8e, L:/***************:7435 ! R:/***************:8091]
2025-07-16 10:01:03 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x89c75c8e, L:/***************:7435 ! R:/***************:8091]
2025-07-16 10:01:03 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x89c75c8e, L:/***************:7435 ! R:/***************:8091]) will closed
2025-07-16 10:01:03 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x89c75c8e, L:/***************:7435 ! R:/***************:8091]) will closed
2025-07-16 10:01:12 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 10:01:13 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 21452 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 10:01:13 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-16 10:01:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-16 10:01:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 10:01:16 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 10:01:16 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 10:01:16 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 10:01:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 10:01:16 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 10:01:16 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 10:01:16 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:01:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1752631276963
timestamp=1752631276963
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'} >
2025-07-16 10:01:17 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf33ab839, L:/***************:13982 - R:/***************:8091]
2025-07-16 10:01:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 74 ms, version:1.7.1,role:TMROLE,channel:[id: 0xf33ab839, L:/***************:13982 - R:/***************:8091]
2025-07-16 10:01:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 10:01:17 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 10:01:17 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 10:01:17 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 10:01:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 10:01:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 10:01:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 10:01:19 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 10:01:20 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 10:01:20 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 10:01:21 [redisson-netty-2-7] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 10:01:21 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 10:01:23 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 10:01:23 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 10:01:23 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 10:01:23 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 10:01:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth ***************:9210 register finished
2025-07-16 10:01:25 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 15.625 seconds (process running for 17.705)
2025-07-16 10:01:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 10:01:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-16 10:01:26 [RMI TCP Connection(2)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 10:02:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:02:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 10:02:17 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5f0600f2, L:/***************:14109 - R:/***************:8091]
2025-07-16 10:02:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x5f0600f2, L:/***************:14109 - R:/***************:8091]
2025-07-16 10:02:24 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 10:02:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 10:02:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[370ms]
2025-07-16 10:02:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 10:02:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[469ms]
2025-07-16 10:02:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 10:02:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-16 10:02:30 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-16 10:02:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 10:02:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[62ms]
2025-07-16 10:02:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 10:02:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[235ms]
2025-07-16 10:02:30 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJIMnFyUGtyaXNNc0dFdXJzSmVia21nZldzaTRjcTBlbiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.hHCB2qLQ0Ctlsuj1h93UeRgq3ckS5Dp6AbNWNS-jNY8
2025-07-16 10:07:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 10:07:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[82ms]
2025-07-16 10:07:33 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Logout][退出成功]
2025-07-16 10:07:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 10:07:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-16 10:07:34 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJIMnFyUGtyaXNNc0dFdXJzSmVia21nZldzaTRjcTBlbiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.hHCB2qLQ0Ctlsuj1h93UeRgq3ckS5Dp6AbNWNS-jNY8
2025-07-16 10:07:35 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 10:07:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 10:07:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[44ms]
2025-07-16 10:07:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 10:07:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[277ms]
2025-07-16 10:07:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 10:07:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-16 10:07:45 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-16 10:07:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 10:07:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-16 10:07:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 10:07:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[121ms]
2025-07-16 10:07:45 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJmT2FldHZ5ZHROSldMQld4VmZ3SWlxQTlQbWdGWnJKZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.aP_3HBEwntFYlWoTVLZdh2ThxxbOQIJxnhkN-8oRzms
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf33ab839, L:/***************:13982 - R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5f0600f2, L:/***************:14109 - R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xf33ab839, L:/***************:13982 - R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5f0600f2, L:/***************:14109 - R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5f0600f2, L:/***************:14109 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf33ab839, L:/***************:13982 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5f0600f2, L:/***************:14109 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf33ab839, L:/***************:13982 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5f0600f2, L:/***************:14109 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf33ab839, L:/***************:13982 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf33ab839, L:/***************:13982 ! R:/***************:8091]) will closed
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5f0600f2, L:/***************:14109 ! R:/***************:8091]) will closed
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf33ab839, L:/***************:13982 ! R:/***************:8091]) will closed
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5f0600f2, L:/***************:14109 ! R:/***************:8091]) will closed
2025-07-16 10:09:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:09:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1752631786795
timestamp=1752631786795
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'} >
2025-07-16 10:09:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:09:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5f0600f2, L:/***************:14109 ! R:/***************:8091]
2025-07-16 10:09:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5f0600f2, L:/***************:14109 ! R:/***************:8091]
2025-07-16 10:09:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 10:09:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5f9a3a1f, L:null ! R:/***************:8091]) will closed
2025-07-16 10:09:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:09:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1752631796797
timestamp=1752631796797
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'} >
2025-07-16 10:09:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0fcd532d, L:null ! R:/***************:8091]) will closed
2025-07-16 10:09:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:09:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 10:10:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1752631806798
timestamp=1752631806798
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'} >
2025-07-16 10:10:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcab2d612, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa4033a49, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:07 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 10:10:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1752631816800
timestamp=1752631816800
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'} >
2025-07-16 10:10:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3d372ed6, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x49e864de, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 10:10:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9125266b, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1752631826803
timestamp=1752631826803
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'} >
2025-07-16 10:10:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 10:10:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaed665ea, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xef754724, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1752631836805
timestamp=1752631836805
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'} >
2025-07-16 10:10:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5e039ac6, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 10:10:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf8bb8f65, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1752631846807
timestamp=1752631846807
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'} >
2025-07-16 10:10:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xecba140c, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 10:10:47 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-16 10:10:47 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-16 10:10:47 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-16 10:10:47 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-16 10:10:47 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-16 10:10:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeb5f6374, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x438a936c, L:null ! R:/***************:8091]) will closed
2025-07-16 10:49:25 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 10:49:25 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 10300 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 10:49:25 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-16 10:49:25 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-16 10:49:25 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 10:49:30 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 10:49:30 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 10:49:30 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 10:49:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 10:49:30 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 10:49:30 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 10:49:30 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 10:49:30 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752634170817
timestamp=1752634170817
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 10:49:31 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2cc73461, L:/*************:10303 - R:/*************:8091]
2025-07-16 10:49:31 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 71 ms, version:1.7.1,role:TMROLE,channel:[id: 0x2cc73461, L:/*************:10303 - R:/*************:8091]
2025-07-16 10:49:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 10:49:31 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 10:49:31 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 10:49:31 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 10:49:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 10:49:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 10:49:32 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 10:49:34 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 10:49:35 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 10:49:35 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 10:49:35 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 10:49:35 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 10:49:37 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 10:49:37 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 10:49:37 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 10:49:37 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 10:49:37 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-16 10:49:39 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 15.95 seconds (process running for 16.879)
2025-07-16 10:49:39 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 10:49:39 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-16 10:49:40 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 10:50:17 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJjVlNnMmVqQVBNWW8xamZlTHdqSFJWblRnTkRzREhkcSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Yc76xMMTAkIJNv5wzCP_99Kq2C7YcH1kyRTjYI5GU3U
2025-07-16 10:50:18 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 10:50:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 10:50:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 10:50:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5a71dbab, L:/*************:10456 - R:/*************:8091]
2025-07-16 10:50:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0x5a71dbab, L:/*************:10456 - R:/*************:8091]
2025-07-16 10:50:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 10:50:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[426ms]
2025-07-16 10:50:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 10:50:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[508ms]
2025-07-16 10:50:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 10:50:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-07-16 10:50:55 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-16 10:50:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 10:50:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[39ms]
2025-07-16 10:50:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 10:50:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[102ms]
2025-07-16 10:50:55 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJYbTJvVkNlMHk2azlxekVpTGZqTzVxQmhsaFo2cmxGQyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.XNfCh_SEzPliEvRFH2rNZLXpXv7Eel9AYLf2rZ2Q3-o
2025-07-16 11:01:12 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 11:01:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:01:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[61ms]
2025-07-16 11:01:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 11:01:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[181ms]
2025-07-16 11:01:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:01:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-16 11:01:17 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-16 11:01:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 11:01:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[16ms]
2025-07-16 11:01:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 11:01:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[72ms]
2025-07-16 11:01:17 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJaQzQ3SXhqNlcwSEdobjJSSDV4UVhDTnpjUk0zMzFRSSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.BOgbcUmz2qR6Zka04l1LznjO5DkgVZbEN8_8_blIQSM
2025-07-16 11:03:30 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJmT2FldHZ5ZHROSldMQld4VmZ3SWlxQTlQbWdGWnJKZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.aP_3HBEwntFYlWoTVLZdh2ThxxbOQIJxnhkN-8oRzms
2025-07-16 11:03:31 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 11:03:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:03:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[50ms]
2025-07-16 11:03:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 11:03:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[138ms]
2025-07-16 11:03:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:03:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-16 11:03:35 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-16 11:03:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 11:03:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-16 11:03:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 11:03:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[74ms]
2025-07-16 11:03:35 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJrcjY2RXZQRzRMdnpPVXkxUFZ2c2VwZVkwdEFrV2JGSSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.7Z3YUsawqV6GLwLxUhx3y3k-sP4DrkrdUCAHpAVEqr0
2025-07-16 11:09:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:09:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[59ms]
2025-07-16 11:09:12 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-16 11:09:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 11:09:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-16 11:09:13 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJrcjY2RXZQRzRMdnpPVXkxUFZ2c2VwZVkwdEFrV2JGSSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.7Z3YUsawqV6GLwLxUhx3y3k-sP4DrkrdUCAHpAVEqr0
2025-07-16 11:09:14 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 11:09:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:09:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-16 11:09:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 11:09:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[214ms]
2025-07-16 11:09:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:09:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-16 11:09:17 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-16 11:09:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 11:09:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-16 11:09:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 11:09:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[101ms]
2025-07-16 11:09:17 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJhZEM3VlhHcUhSRlRtY1JKSVpRT0VtSm5PWlhaUFhFbCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.2gg-qw3uC-29Stjsx_uuHRVArOF4gRrDcQnsUsdSNvs
2025-07-16 11:18:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:18:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[48ms]
2025-07-16 11:18:09 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-16 11:18:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 11:18:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-16 11:18:09 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJhZEM3VlhHcUhSRlRtY1JKSVpRT0VtSm5PWlhaUFhFbCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.2gg-qw3uC-29Stjsx_uuHRVArOF4gRrDcQnsUsdSNvs
2025-07-16 11:18:10 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[104ms]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Success][登录成功]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[49ms]
2025-07-16 11:18:13 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJTeXRodWtLVDdPeW1CclVGMkdzUUNjRWRGWGJ2cVV3VSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.QzpgTB3D_PMOyChPGyy_03p8F3PE8JiNW80xZ5L4NII
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x2cc73461, L:/*************:10303 - R:/*************:8091] read idle.
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2cc73461, L:/*************:10303 - R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2cc73461, L:/*************:10303 - R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2cc73461, L:/*************:10303 ! R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x5a71dbab, L:/*************:10456 - R:/*************:8091] read idle.
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5a71dbab, L:/*************:10456 - R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a71dbab, L:/*************:10456 - R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]) will closed
2025-07-16 12:26:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a71dbab, L:/*************:10456 ! R:/*************:8091]) will closed
2025-07-16 12:26:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 12:26:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752639977431
timestamp=1752639977431
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 12:26:17 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x1f761feb, L:/*************:5654 - R:/*************:8091]
2025-07-16 12:26:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:TMROLE,channel:[id: 0x1f761feb, L:/*************:5654 - R:/*************:8091]
2025-07-16 12:26:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 12:26:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 12:26:17 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe2d1215a, L:/*************:5658 - R:/*************:8091]
2025-07-16 12:26:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe2d1215a, L:/*************:5658 - R:/*************:8091]
2025-07-16 12:26:35 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJTeXRodWtLVDdPeW1CclVGMkdzUUNjRWRGWGJ2cVV3VSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.QzpgTB3D_PMOyChPGyy_03p8F3PE8JiNW80xZ5L4NII
2025-07-16 12:26:35 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 12:26:55 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJaQzQ3SXhqNlcwSEdobjJSSDV4UVhDTnpjUk0zMzFRSSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.BOgbcUmz2qR6Zka04l1LznjO5DkgVZbEN8_8_blIQSM
2025-07-16 12:26:55 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x1f761feb, L:/*************:5654 - R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe2d1215a, L:/*************:5658 - R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xe2d1215a, L:/*************:5658 - R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x1f761feb, L:/*************:5654 - R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x1f761feb, L:/*************:5654 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe2d1215a, L:/*************:5658 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x1f761feb, L:/*************:5654 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x1f761feb, L:/*************:5654 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1f761feb, L:/*************:5654 ! R:/*************:8091]) will closed
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1f761feb, L:/*************:5654 ! R:/*************:8091]) will closed
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe2d1215a, L:/*************:5658 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe2d1215a, L:/*************:5658 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2d1215a, L:/*************:5658 ! R:/*************:8091]) will closed
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2d1215a, L:/*************:5658 ! R:/*************:8091]) will closed
2025-07-16 12:27:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 12:27:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe2d1215a, L:/*************:5658 ! R:/*************:8091]
2025-07-16 12:27:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe2d1215a, L:/*************:5658 ! R:/*************:8091]
2025-07-16 12:27:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 12:27:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x933b93b4, L:null ! R:/*************:8091]) will closed
2025-07-16 13:26:34 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 13:26:34 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 14864 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 13:26:34 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-16 13:26:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-16 13:26:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 13:26:39 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 13:26:39 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 13:26:39 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 13:26:39 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 13:26:39 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 13:26:39 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 13:26:39 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 13:26:39 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752643599619
timestamp=1752643599619
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 13:26:39 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x470b5f91, L:/*************:9043 - R:/*************:8091]
2025-07-16 13:26:39 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 99 ms, version:1.7.1,role:TMROLE,channel:[id: 0x470b5f91, L:/*************:9043 - R:/*************:8091]
2025-07-16 13:26:39 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 13:26:39 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 13:26:39 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 13:26:39 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 13:26:39 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 13:26:39 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 13:26:40 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 13:26:43 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 13:26:44 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 13:26:44 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 13:26:44 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 13:26:44 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 13:26:46 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 13:26:46 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 13:26:46 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 13:26:46 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 13:26:46 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-16 13:26:48 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 16.895 seconds (process running for 18.279)
2025-07-16 13:26:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 13:26:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-16 13:26:49 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 13:27:12 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 13:27:39 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 13:27:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 13:27:39 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x34815f4f, L:/*************:9249 - R:/*************:8091]
2025-07-16 13:27:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x34815f4f, L:/*************:9249 - R:/*************:8091]
2025-07-16 13:36:48 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 13:38:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 13:38:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1669ms]
2025-07-16 13:38:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 13:38:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[1140ms]
2025-07-16 13:38:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 13:38:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[7ms]
2025-07-16 13:38:09 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-16 13:38:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 13:38:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[72ms]
2025-07-16 13:38:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 13:38:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[144ms]
2025-07-16 13:38:10 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJEN1BaanpUdU10NGtab1ZEdnJhSWZveHpIc0c5NGI2NyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.eqE1UyU8SxM_pAnyZgDOoDkqjgCu4zCkvwXumqLucDQ
2025-07-16 13:38:41 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[90ms]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[151ms]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Success][登录成功]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[90ms]
2025-07-16 13:38:45 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJVN0pDeTdIZE9JQ3loc0pLeldFUUYzZFpMVG56Tmd1SCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.GYNwxz3XGTQb4J5eg1eDewl5fgOOyZWMl95QRx2SF9c
2025-07-16 14:32:45 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 14:36:32 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x34815f4f, L:/*************:9249 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x470b5f91, L:/*************:9043 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x34815f4f, L:/*************:9249 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x470b5f91, L:/*************:9043 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x470b5f91, L:/*************:9043 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x34815f4f, L:/*************:9249 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x34815f4f, L:/*************:9249 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x470b5f91, L:/*************:9043 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x34815f4f, L:/*************:9249 ! R:/*************:8091]) will closed
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x470b5f91, L:/*************:9043 ! R:/*************:8091]) will closed
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x34815f4f, L:/*************:9249 ! R:/*************:8091]) will closed
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x470b5f91, L:/*************:9043 ! R:/*************:8091]) will closed
2025-07-16 14:36:39 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:36:39 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752647799433
timestamp=1752647799433
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 14:36:39 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:36:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 14:36:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0196b2f2, L:null ! R:/*************:8091]) will closed
2025-07-16 14:36:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x04bf7b2a, L:null ! R:/*************:8091]) will closed
2025-07-16 14:36:49 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:36:49 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752647809429
timestamp=1752647809429
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 14:36:49 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x3bad9ad6, L:/*************:3054 - R:/*************:8091]
2025-07-16 14:36:49 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:TMROLE,channel:[id: 0x3bad9ad6, L:/*************:3054 - R:/*************:8091]
2025-07-16 14:36:49 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:36:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 14:36:49 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xc7d48ac8, L:/*************:3057 - R:/*************:8091]
2025-07-16 14:36:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0xc7d48ac8, L:/*************:3057 - R:/*************:8091]
2025-07-16 14:38:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 14:38:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[46ms]
2025-07-16 14:38:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 14:38:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-16 14:38:18 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Error][验证码已失效]
2025-07-16 14:38:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 14:38:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[5ms]
2025-07-16 14:38:18 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[132ms]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[84ms]
2025-07-16 14:38:22 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiIwM3FpY2t4WHowbll6bEFIcTA1UElTa0o1MVRMb0dGWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.WfojBPIbOMqoRU8GUrsinmugcs9xLe5SRBZcFZsblEU
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xc7d48ac8, L:/*************:3057 - R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xc7d48ac8, L:/*************:3057 - R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xc7d48ac8, L:/*************:3057 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc7d48ac8, L:/*************:3057 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc7d48ac8, L:/*************:3057 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc7d48ac8, L:/*************:3057 ! R:/*************:8091]) will closed
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc7d48ac8, L:/*************:3057 ! R:/*************:8091]) will closed
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x3bad9ad6, L:/*************:3054 - R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x3bad9ad6, L:/*************:3054 - R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x3bad9ad6, L:/*************:3054 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3bad9ad6, L:/*************:3054 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3bad9ad6, L:/*************:3054 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3bad9ad6, L:/*************:3054 ! R:/*************:8091]) will closed
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3bad9ad6, L:/*************:3054 ! R:/*************:8091]) will closed
2025-07-16 14:50:49 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:50:49 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752648649429
timestamp=1752648649429
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 14:50:49 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:50:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc7d48ac8, L:/*************:3057 ! R:/*************:8091]
2025-07-16 14:50:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc7d48ac8, L:/*************:3057 ! R:/*************:8091]
2025-07-16 14:50:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 14:50:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1759267e, L:null ! R:/*************:8091]) will closed
2025-07-16 14:50:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x86c2aefc, L:null ! R:/*************:8091]) will closed
2025-07-16 14:57:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 14:57:25 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 23980 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 14:57:25 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-16 14:57:25 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-16 14:57:25 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 14:57:29 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 14:57:29 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 14:57:29 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 14:57:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 14:57:30 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 14:57:30 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 14:57:30 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:57:30 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752649050513
timestamp=1752649050513
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 14:57:30 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd4eaf9cf, L:/*************:6320 - R:/*************:8091]
2025-07-16 14:57:30 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 149 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd4eaf9cf, L:/*************:6320 - R:/*************:8091]
2025-07-16 14:57:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 14:57:30 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 14:57:30 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 14:57:30 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 14:57:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-16 14:57:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 14:57:32 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 14:57:34 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 14:57:35 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 14:57:35 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 14:57:35 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 14:57:36 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-16 14:57:37 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 14:57:37 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 14:57:37 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 14:57:38 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 14:57:38 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth *************:9210 register finished
2025-07-16 14:57:40 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 18.144 seconds (process running for 19.13)
2025-07-16 14:57:40 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 14:57:40 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-16 14:57:41 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 14:57:45 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiIwM3FpY2t4WHowbll6bEFIcTA1UElTa0o1MVRMb0dGWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.WfojBPIbOMqoRU8GUrsinmugcs9xLe5SRBZcFZsblEU
2025-07-16 14:57:47 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiIwM3FpY2t4WHowbll6bEFIcTA1UElTa0o1MVRMb0dGWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.WfojBPIbOMqoRU8GUrsinmugcs9xLe5SRBZcFZsblEU
2025-07-16 14:57:47 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 14:58:09 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 14:58:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 14:58:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[239ms]
2025-07-16 14:58:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 14:58:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[332ms]
2025-07-16 14:58:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 14:58:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-07-16 14:58:13 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-16 14:58:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 14:58:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[28ms]
2025-07-16 14:58:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 14:58:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[96ms]
2025-07-16 14:58:13 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJZcFBaSjdaTm5qVnhpMUxpMHNXaEpyb1FjVGdMeHJCVSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.wyV6Fs6PTTNQZPVfTQzQvVCcgRAjpPbl4Ds-jvRhOiM
2025-07-16 14:58:30 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:58:30 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 14:58:30 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2617d5ad, L:/*************:6571 - R:/*************:8091]
2025-07-16 14:58:30 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x2617d5ad, L:/*************:6571 - R:/*************:8091]
2025-07-16 16:05:26 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJZcFBaSjdaTm5qVnhpMUxpMHNXaEpyb1FjVGdMeHJCVSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.wyV6Fs6PTTNQZPVfTQzQvVCcgRAjpPbl4Ds-jvRhOiM
2025-07-16 16:05:26 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 16:26:25 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJVN0pDeTdIZE9JQ3loc0pLeldFUUYzZFpMVG56Tmd1SCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.GYNwxz3XGTQb4J5eg1eDewl5fgOOyZWMl95QRx2SF9c
2025-07-16 16:26:26 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 16:37:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 16:37:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[374ms]
2025-07-16 16:37:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 16:37:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[11ms]
2025-07-16 16:37:54 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Error][验证码已失效]
2025-07-16 16:37:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 16:37:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[30ms]
2025-07-16 16:37:54 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 16:38:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 16:38:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[27ms]
2025-07-16 16:38:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 16:38:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[337ms]
2025-07-16 16:38:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 16:38:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-16 16:38:02 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-16 16:38:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 16:38:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-07-16 16:38:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 16:38:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[86ms]
2025-07-16 16:38:02 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiI3Q25CRld3QUtoRUhLRGc4VGNBMmt6Qjg1VEZRU1hociIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.WTAWpoZrofGRtBilmswYSOljSXCgImbfZZCyIkaXLWA
2025-07-16 16:38:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 16:38:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[71ms]
2025-07-16 16:38:31 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Logout][退出成功]
2025-07-16 16:38:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 16:38:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-16 16:38:31 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiI3Q25CRld3QUtoRUhLRGc4VGNBMmt6Qjg1VEZRU1hociIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.WTAWpoZrofGRtBilmswYSOljSXCgImbfZZCyIkaXLWA
2025-07-16 16:38:32 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 16:38:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 16:38:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[32ms]
2025-07-16 16:38:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-16 16:38:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[130ms]
2025-07-16 16:38:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 16:38:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-16 16:38:39 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-16 16:38:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 16:38:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-16 16:38:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-16 16:38:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[59ms]
2025-07-16 16:38:39 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJSTG9XZkhraGx2dGhnRHZvSXc1MmhKT2RaZWFrajVhdyIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.q7nP9vw1Xb0M6XFclVfjTDwWn6Lq_WlcnNxohBdXrHM
2025-07-16 16:43:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 16:43:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[45ms]
2025-07-16 16:43:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-16 16:43:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[3ms]
2025-07-16 16:43:26 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Error][验证码已失效]
2025-07-16 16:43:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-16 16:43:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-16 16:43:26 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xd4eaf9cf, L:/*************:6320 - R:/*************:8091] read idle.
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x2617d5ad, L:/*************:6571 - R:/*************:8091] read idle.
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd4eaf9cf, L:/*************:6320 - R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2617d5ad, L:/*************:6571 - R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd4eaf9cf, L:/*************:6320 - R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2617d5ad, L:/*************:6571 - R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd4eaf9cf, L:/*************:6320 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2617d5ad, L:/*************:6571 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:11:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 19:11:34 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xc5bb4548, L:/*************:12369 - R:/*************:8091]
2025-07-16 19:11:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 13 ms, version:1.7.1,role:RMROLE,channel:[id: 0xc5bb4548, L:/*************:12369 - R:/*************:8091]
2025-07-16 19:11:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:11:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752664297423
timestamp=1752664297423
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 19:11:37 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x1da4db62, L:/*************:6060 - R:/*************:8091]
2025-07-16 19:11:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:TMROLE,channel:[id: 0x1da4db62, L:/*************:6060 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x1da4db62, L:/*************:6060 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xc5bb4548, L:/*************:12369 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x1da4db62, L:/*************:6060 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xc5bb4548, L:/*************:12369 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xc5bb4548, L:/*************:12369 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x1da4db62, L:/*************:6060 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc5bb4548, L:/*************:12369 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x1da4db62, L:/*************:6060 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc5bb4548, L:/*************:12369 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x1da4db62, L:/*************:6060 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc5bb4548, L:/*************:12369 ! R:/*************:8091]) will closed
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1da4db62, L:/*************:6060 ! R:/*************:8091]) will closed
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc5bb4548, L:/*************:12369 ! R:/*************:8091]) will closed
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1da4db62, L:/*************:6060 ! R:/*************:8091]) will closed
2025-07-16 19:14:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:14:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752664487427
timestamp=1752664487427
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 19:14:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:14:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc5bb4548, L:/*************:12369 ! R:/*************:8091]
2025-07-16 19:14:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc5bb4548, L:/*************:12369 ! R:/*************:8091]
2025-07-16 19:14:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 19:14:49 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd002ea1d, L:null ! R:/*************:8091]) will closed
2025-07-16 19:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x241770cd, L:null ! R:/*************:8091]) will closed
2025-07-16 19:14:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:14:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752664497423
timestamp=1752664497423
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 19:14:58 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:14:58 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 19:14:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3d6d182c, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:00 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x107f083d, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:07 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:07 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752664507423
timestamp=1752664507423
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 19:15:08 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:08 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 19:15:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xda4c2e51, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xad1c4651, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752664517424
timestamp=1752664517424
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 19:15:18 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:18 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 19:15:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6437ab0a, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4eb94c59, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752664527434
timestamp=1752664527434
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 19:15:28 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:28 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 19:15:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa4246047, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc97f7aba, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752664537423
timestamp=1752664537423
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 19:15:38 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 19:15:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x33ce2bd4, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0fc0f1db, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752664547422
timestamp=1752664547422
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 19:15:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 19:15:49 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf3fe45d9, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf812e028, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752664557430
timestamp=1752664557430
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'} >
2025-07-16 19:15:58 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:58 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-16 19:15:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf0ba8b7a, L:null ! R:/*************:8091]) will closed
2025-07-16 19:16:00 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xae14bd72, L:null ! R:/*************:8091]) will closed
2025-07-16 19:16:03 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-16 19:16:03 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-16 19:16:03 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-16 19:16:03 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-16 19:16:03 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
