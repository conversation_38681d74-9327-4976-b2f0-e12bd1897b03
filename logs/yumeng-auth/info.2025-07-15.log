2025-07-15 10:06:50 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-15 10:06:51 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 25264 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-15 10:06:51 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-15 10:06:51 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-15 10:06:51 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-15 10:06:55 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-15 10:06:55 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-15 10:06:55 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-15 10:06:55 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-15 10:06:55 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-15 10:06:55 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-15 10:06:55 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-15 10:06:55 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-15 10:06:55 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-15 10:06:55 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-15 10:06:55 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-15 10:06:55 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-15 10:06:56 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-15 10:06:58 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-15 10:06:59 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-15 10:06:59 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-15 10:07:00 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-15 10:07:00 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-15 10:07:02 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-15 10:07:02 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-15 10:07:02 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-15 10:07:02 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-15 10:07:02 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth ************:9210 register finished
2025-07-15 10:07:05 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 16.571 seconds (process running for 17.721)
2025-07-15 10:07:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-15 10:07:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-15 10:07:05 [RMI TCP Connection(2)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 10:07:42 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJrSHNjRjBPekw5dkd4blp0RHYyb25rNmJRbjJKSWlyZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.JUZWGyeTBBvso0tDGMI89r8RDSXmF6w32fMBs5ij8RY
2025-07-15 10:07:42 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-15 10:07:55 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 10:07:55 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752545275482
timestamp=1752545275482
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 10:07:55 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x3a1ed00c, L:/************:7589 - R:/************:8091]
2025-07-15 10:07:55 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 22 ms, version:1.7.1,role:TMROLE,channel:[id: 0x3a1ed00c, L:/************:7589 - R:/************:8091]
2025-07-15 10:07:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 10:07:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 10:07:55 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x508008be, L:/************:7590 - R:/************:8091]
2025-07-15 10:07:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x508008be, L:/************:7590 - R:/************:8091]
2025-07-15 10:09:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 10:09:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[645ms]
2025-07-15 10:09:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-15 10:09:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[483ms]
2025-07-15 10:09:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 10:09:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-15 10:09:08 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-15 10:09:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-15 10:09:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[33ms]
2025-07-15 10:09:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-15 10:09:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[122ms]
2025-07-15 10:09:08 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJQYzdlVVF4d2h0dUtFQ3NTbnBVamJHMG5rVVZrSU1wWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.vNAaCpU94N_5lNuygUPvYij_S1WG_YXTZIVgirzVW98
2025-07-15 11:02:51 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJQYzdlVVF4d2h0dUtFQ3NTbnBVamJHMG5rVVZrSU1wWSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.vNAaCpU94N_5lNuygUPvYij_S1WG_YXTZIVgirzVW98
2025-07-15 11:02:52 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-15 11:02:54 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 11:02:54 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[89ms]
2025-07-15 11:02:54 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-15 11:02:54 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[179ms]
2025-07-15 11:02:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 11:02:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-15 11:02:55 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-15 11:02:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-15 11:02:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[8ms]
2025-07-15 11:02:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-15 11:02:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[82ms]
2025-07-15 11:02:55 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJCQlM5NXcxY3lMZTh0NndaYzVRRjF4S0tGd1RHY1d6OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.sVTqLmEMLXbFVQRk6AS-RHaj1C7Wf-m1q7YGo4eY_FI
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x3a1ed00c, L:/************:7589 - R:/************:8091] read idle.
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3a1ed00c, L:/************:7589 - R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a1ed00c, L:/************:7589 - R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a1ed00c, L:/************:7589 ! R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x508008be, L:/************:7590 - R:/************:8091] read idle.
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x508008be, L:/************:7590 - R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x508008be, L:/************:7590 - R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x508008be, L:/************:7590 ! R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x508008be, L:/************:7590 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x508008be, L:/************:7590 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x508008be, L:/************:7590 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x508008be, L:/************:7590 ! R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x508008be, L:/************:7590 ! R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x508008be, L:/************:7590 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x508008be, L:/************:7590 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x508008be, L:/************:7590 ! R:/************:8091]
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x508008be, L:/************:7590 ! R:/************:8091]) will closed
2025-07-15 12:04:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x508008be, L:/************:7590 ! R:/************:8091]) will closed
2025-07-15 12:04:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:04:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552242508
timestamp=1752552242508
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 12:04:02 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5bfaadc6, L:/************:5634 - R:/************:8091]
2025-07-15 12:04:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x5bfaadc6, L:/************:5634 - R:/************:8091]
2025-07-15 12:04:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:04:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 12:04:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x11d85878, L:/************:5641 - R:/************:8091]
2025-07-15 12:04:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x11d85878, L:/************:5641 - R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5bfaadc6, L:/************:5634 - R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x11d85878, L:/************:5641 - R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x11d85878, L:/************:5641 - R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5bfaadc6, L:/************:5634 - R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5bfaadc6, L:/************:5634 ! R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x11d85878, L:/************:5641 ! R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5bfaadc6, L:/************:5634 ! R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x11d85878, L:/************:5641 ! R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5bfaadc6, L:/************:5634 ! R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x11d85878, L:/************:5641 ! R:/************:8091]
2025-07-15 12:05:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5bfaadc6, L:/************:5634 ! R:/************:8091]) will closed
2025-07-15 12:05:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x11d85878, L:/************:5641 ! R:/************:8091]) will closed
2025-07-15 12:05:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5bfaadc6, L:/************:5634 ! R:/************:8091]) will closed
2025-07-15 12:05:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x11d85878, L:/************:5641 ! R:/************:8091]) will closed
2025-07-15 12:06:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552362512
timestamp=1752552362512
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 12:06:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x11d85878, L:/************:5641 ! R:/************:8091]
2025-07-15 12:06:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x11d85878, L:/************:5641 ! R:/************:8091]
2025-07-15 12:06:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 12:06:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4f1d2386, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2f6d93df, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552372508
timestamp=1752552372508
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 12:06:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 12:06:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcb5a8e80, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x34100695, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:22 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:22 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552382508
timestamp=1752552382508
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 12:06:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 12:06:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeb0bd59e, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6817e033, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552392514
timestamp=1752552392514
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 12:06:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 12:06:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x76530943, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xad1be0cd, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552402508
timestamp=1752552402508
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 12:06:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 12:06:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x84da8b3f, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe5d1c922, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552412518
timestamp=1752552412518
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 12:06:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:06:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 12:06:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcacd8733, L:null ! R:/************:8091]) will closed
2025-07-15 12:06:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3986378c, L:null ! R:/************:8091]) will closed
2025-07-15 12:07:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:07:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552422507
timestamp=1752552422507
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 12:07:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:07:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 12:07:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x47eb8a51, L:null ! R:/************:8091]) will closed
2025-07-15 12:07:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3bf4a0dd, L:null ! R:/************:8091]) will closed
2025-07-15 12:07:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:07:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552432509
timestamp=1752552432509
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 12:07:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 12:07:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 12:07:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0f5a3b26, L:null ! R:/************:8091]) will closed
2025-07-15 12:07:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3d6c4f74, L:null ! R:/************:8091]) will closed
2025-07-15 12:07:18 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-15 12:07:18 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-15 12:07:18 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-15 12:07:19 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-15 12:07:19 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-15 13:33:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-15 13:33:02 [main] INFO  com.ym.auth.YmAuthApplication - Starting YmAuthApplication using Java 17.0.10 with PID 36172 (D:\project\yumeng\ship-Integrated-management-api\yumeng-auth\target\classes started by qingyi in D:\project\yumeng)
2025-07-15 13:33:02 [main] INFO  com.ym.auth.YmAuthApplication - The following 1 profile is active: "dev"
2025-07-15 13:33:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-auth.yml, group=DEFAULT_GROUP] success
2025-07-15 13:33:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-15 13:33:05 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-15 13:33:06 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-15 13:33:06 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-15 13:33:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-15 13:33:06 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-15 13:33:06 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-15 13:33:06 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 13:33:06 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752557586398
timestamp=1752557586398
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 13:33:06 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x240746fc, L:/************:4954 - R:/************:8091]
2025-07-15 13:33:06 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 54 ms, version:1.7.1,role:TMROLE,channel:[id: 0x240746fc, L:/************:4954 - R:/************:8091]
2025-07-15 13:33:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-15 13:33:06 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-15 13:33:06 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-15 13:33:06 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-15 13:33:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-auth] txServiceGroup[yumeng-auth-group]
2025-07-15 13:33:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-15 13:33:07 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-15 13:33:08 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-15 13:33:09 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-15 13:33:09 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-15 13:33:09 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-15 13:33:10 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 8.142.34.95/8.142.34.95:6379
2025-07-15 13:33:11 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-15 13:33:11 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-15 13:33:11 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-15 13:33:11 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-15 13:33:11 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-auth ************:9210 register finished
2025-07-15 13:33:13 [main] INFO  com.ym.auth.YmAuthApplication - Started YmAuthApplication in 13.887 seconds (process running for 14.946)
2025-07-15 13:33:13 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-15 13:33:13 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-auth.yml, group=DEFAULT_GROUP
2025-07-15 13:33:14 [RMI TCP Connection(3)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 13:33:33 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJCQlM5NXcxY3lMZTh0NndaYzVRRjF4S0tGd1RHY1d6OCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.sVTqLmEMLXbFVQRk6AS-RHaj1C7Wf-m1q7YGo4eY_FI
2025-07-15 13:33:34 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-15 13:33:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 13:33:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[269ms]
2025-07-15 13:33:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-15 13:33:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[389ms]
2025-07-15 13:33:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 13:33:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-07-15 13:33:39 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Success][登录成功]
2025-07-15 13:33:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-15 13:33:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[25ms]
2025-07-15 13:33:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-15 13:33:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[95ms]
2025-07-15 13:33:39 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJKSmdwNFBkSjJUd0hXd1BFN0NaaGxNaXZidWdDbDY5dCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.b9vSuNZfTj9_4KMBTIO_j6Zr2zwI0cZYbRuCSbhTfqY
2025-07-15 13:34:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 13:34:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 13:34:06 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x012eb935, L:/************:5105 - R:/************:8091]
2025-07-15 13:34:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x012eb935, L:/************:5105 - R:/************:8091]
2025-07-15 14:10:05 [XNIO-1 task-2] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-15 14:10:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 14:10:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[95ms]
2025-07-15 14:10:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-15 14:10:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[311ms]
2025-07-15 14:10:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 14:10:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[2ms]
2025-07-15 14:10:15 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[xhry][Success][登录成功]
2025-07-15 14:10:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-15 14:10:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-15 14:10:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-15 14:10:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[77ms]
2025-07-15 14:10:15 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJEZTFYSXgxMFI0clJkcTh0UkdPMGhDbXl0alRib1lQMCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Xah_sqfp9DLJQEZX5DC25iMzhpX8wT2E83uQP6iwfsI
2025-07-15 14:27:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 14:27:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[27ms]
2025-07-15 14:27:57 [XNIO-1 task-2] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[frd][Logout][退出成功]
2025-07-15 14:27:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-15 14:27:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-15 14:27:57 [XNIO-1 task-2] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259258107203585, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJKSmdwNFBkSjJUd0hXd1BFN0NaaGxNaXZidWdDbDY5dCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.b9vSuNZfTj9_4KMBTIO_j6Zr2zwI0cZYbRuCSbhTfqY
2025-07-15 14:27:58 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[21ms]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[152ms]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.log.event.LogEventListener - [0:0:0:0:0:0:0:1]内网IP[15253590286][Success][登录成功]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[67ms]
2025-07-15 14:28:03 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogin, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJMcFhOMXpOTXNuaHkyZWl6NmNudHJhNDNjREYyUG03ayIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.RO3xqwhKV9jWA8YX_2eyX-4_M2QJNu7xRnQEKmKXUzw
2025-07-15 14:37:45 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-15 14:37:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x012eb935, L:/************:5105 ! R:/************:8091]
2025-07-15 14:37:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x240746fc, L:/************:4954 ! R:/************:8091]
2025-07-15 14:37:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x012eb935, L:/************:5105 ! R:/************:8091]
2025-07-15 14:37:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x240746fc, L:/************:4954 ! R:/************:8091]
2025-07-15 14:37:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x240746fc, L:/************:4954 ! R:/************:8091]
2025-07-15 14:37:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x012eb935, L:/************:5105 ! R:/************:8091]
2025-07-15 14:37:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x012eb935, L:/************:5105 ! R:/************:8091]
2025-07-15 14:37:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x240746fc, L:/************:4954 ! R:/************:8091]
2025-07-15 14:37:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x012eb935, L:/************:5105 ! R:/************:8091]) will closed
2025-07-15 14:37:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x240746fc, L:/************:4954 ! R:/************:8091]) will closed
2025-07-15 14:37:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x240746fc, L:/************:4954 ! R:/************:8091]) will closed
2025-07-15 14:37:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x012eb935, L:/************:5105 ! R:/************:8091]) will closed
2025-07-15 14:37:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 14:37:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752561476214
timestamp=1752561476214
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 14:37:56 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x0b2325e4, L:/************:12778 - R:/************:8091]
2025-07-15 14:37:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 63 ms, version:1.7.1,role:TMROLE,channel:[id: 0x0b2325e4, L:/************:12778 - R:/************:8091]
2025-07-15 14:37:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 14:37:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 14:37:56 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xfcfd4fdc, L:/************:12789 - R:/************:8091]
2025-07-15 14:37:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 13 ms, version:1.7.1,role:RMROLE,channel:[id: 0xfcfd4fdc, L:/************:12789 - R:/************:8091]
2025-07-15 15:30:52 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:sys_user:1934259119388987393, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MTE5Mzg4OTg3MzkzIiwicm5TdHIiOiJEZTFYSXgxMFI0clJkcTh0UkdPMGhDbXl0alRib1lQMCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTExOTM4ODk4NzM5MywidXNlck5hbWUiOiJ4aHJ5IiwiZGVwdElkIjoxOTM0MjYyODc1NjgxODIwNjczLCJkZXB0TmFtZSI6IumdkuWym-aWsOa1t-eRnua0i-a1t-S6i-acjeWKoeaciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.Xah_sqfp9DLJQEZX5DC25iMzhpX8wT2E83uQP6iwfsI
2025-07-15 15:34:30 [XNIO-1 task-3] INFO  c.y.auth.listener.UserActionListener - user doLogout, useId:app_user:1934265922029981698, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY1OTIyMDI5OTgxNjk4Iiwicm5TdHIiOiJMcFhOMXpOTXNuaHkyZWl6NmNudHJhNDNjREYyUG03ayIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NTkyMjAyOTk4MTY5OCwidXNlck5hbWUiOiIxNTI1MzU5MDI4NiIsImRlcHRJZCI6MTkzNzAyNzc3MTM3Njk2MzU4NSwiZGVwdE5hbWUiOiLlt6XotYTnu5PnrpfkuJrliqHpg6giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.RO3xqwhKV9jWA8YX_2eyX-4_M2QJNu7xRnQEKmKXUzw
2025-07-15 15:34:31 [XNIO-1 task-3] INFO  c.y.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:0:0:0:0:0:0:0:1:'
2025-07-15 15:41:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xfcfd4fdc, L:/************:12789 - R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xfcfd4fdc, L:/************:12789 - R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xfcfd4fdc, L:/************:12789 ! R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfcfd4fdc, L:/************:12789 ! R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfcfd4fdc, L:/************:12789 ! R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfcfd4fdc, L:/************:12789 ! R:/************:8091]) will closed
2025-07-15 15:41:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfcfd4fdc, L:/************:12789 ! R:/************:8091]) will closed
2025-07-15 15:41:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x0b2325e4, L:/************:12778 - R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x0b2325e4, L:/************:12778 - R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x0b2325e4, L:/************:12778 ! R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x0b2325e4, L:/************:12778 ! R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x0b2325e4, L:/************:12778 ! R:/************:8091]
2025-07-15 15:41:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0b2325e4, L:/************:12778 ! R:/************:8091]) will closed
2025-07-15 15:41:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0b2325e4, L:/************:12778 ! R:/************:8091]) will closed
2025-07-15 15:41:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:41:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565296229
timestamp=1752565296229
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:41:36 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xdf448786, L:/************:8111 - R:/************:8091]
2025-07-15 15:41:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 30 ms, version:1.7.1,role:TMROLE,channel:[id: 0xdf448786, L:/************:8111 - R:/************:8091]
2025-07-15 15:41:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:41:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfcfd4fdc, L:/************:12789 ! R:/************:8091]
2025-07-15 15:41:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfcfd4fdc, L:/************:12789 ! R:/************:8091]
2025-07-15 15:41:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:41:36 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xdc375298, L:/************:8115 - R:/************:8091]
2025-07-15 15:41:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0xdc375298, L:/************:8115 - R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xdc375298, L:/************:8115 - R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xdc375298, L:/************:8115 - R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xdc375298, L:/************:8115 ! R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xdc375298, L:/************:8115 ! R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xdc375298, L:/************:8115 ! R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xdf448786, L:/************:8111 - R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdc375298, L:/************:8115 ! R:/************:8091]) will closed
2025-07-15 15:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xdf448786, L:/************:8111 - R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdc375298, L:/************:8115 ! R:/************:8091]) will closed
2025-07-15 15:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xdf448786, L:/************:8111 ! R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xdf448786, L:/************:8111 ! R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xdf448786, L:/************:8111 ! R:/************:8091]
2025-07-15 15:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdf448786, L:/************:8111 ! R:/************:8091]) will closed
2025-07-15 15:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdf448786, L:/************:8111 ! R:/************:8091]) will closed
2025-07-15 15:41:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:41:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565306224
timestamp=1752565306224
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:41:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:41:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xdc375298, L:/************:8115 ! R:/************:8091]
2025-07-15 15:41:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xdc375298, L:/************:8115 ! R:/************:8091]
2025-07-15 15:41:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:41:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xba3ca74b, L:null ! R:/************:8091]) will closed
2025-07-15 15:41:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:41:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565316225
timestamp=1752565316225
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:41:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x453c8f41, L:null ! R:/************:8091]) will closed
2025-07-15 15:41:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:41:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:42:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8d302faf, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565326227
timestamp=1752565326227
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:42:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xea37ce38, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:42:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565336229
timestamp=1752565336229
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:42:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2ed34677, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:42:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdcd63bfb, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4ca6521c, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565346231
timestamp=1752565346231
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:42:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x828f7267, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:42:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8404bdc2, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565356232
timestamp=1752565356232
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:42:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:42:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x26876ff8, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565366233
timestamp=1752565366233
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:42:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcc10b45e, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:42:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x45d72656, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdbe158cc, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565376234
timestamp=1752565376234
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:42:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:42:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65311502, L:null ! R:/************:8091]) will closed
2025-07-15 15:42:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:43:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd03bb0a0, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565386236
timestamp=1752565386236
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:43:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:43:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5988e898, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565396238
timestamp=1752565396238
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:43:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7654b4aa, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:43:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x04734e39, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565406239
timestamp=1752565406239
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:43:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4cc5ee54, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcf63bc51, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:43:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4b68f59a, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565416241
timestamp=1752565416241
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:43:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:43:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x222c332c, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3ef4ac88, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565426242
timestamp=1752565426242
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:43:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x16aebf95, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:43:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565436243
timestamp=1752565436243
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:43:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa95c6dbc, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1b084a73, L:null ! R:/************:8091]) will closed
2025-07-15 15:43:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:43:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:44:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565446245
timestamp=1752565446245
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:44:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa975ba7a, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:44:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb8145dce, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565456246
timestamp=1752565456246
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:44:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48bd165b, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:44:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9e43713a, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565466247
timestamp=1752565466247
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x21d9b148, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x18a47147, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:44:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x373f3723, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565476248
timestamp=1752565476248
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:44:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6e7292dc, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:44:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565486250
timestamp=1752565486250
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:44:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb7ac475b, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65efc0bc, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:44:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565496251
timestamp=1752565496251
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:44:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbec6018, L:null ! R:/************:8091]) will closed
2025-07-15 15:44:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:44:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:44:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x552f6970, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565506252
timestamp=1752565506252
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:45:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa309e78d, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1d5a91b4, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:45:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5d4bbb71, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565516255
timestamp=1752565516255
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:45:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:45:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb68a750f, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xee284043, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565526256
timestamp=1752565526256
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:45:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb1d27d36, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:45:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa02e2999, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565536257
timestamp=1752565536257
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:45:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:45:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x75d0f50d, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd3570ffd, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565546259
timestamp=1752565546259
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:45:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe82064a1, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:45:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc05ba852, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565556260
timestamp=1752565556260
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:45:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1cb35d45, L:null ! R:/************:8091]) will closed
2025-07-15 15:45:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:45:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:46:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3218222b, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565566263
timestamp=1752565566263
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:46:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:46:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf92228a7, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565576264
timestamp=1752565576264
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:46:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x809d0111, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd60f9a22, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:46:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565586265
timestamp=1752565586265
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:46:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x37f70876, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00509447, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:46:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1ff42bd4, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565596267
timestamp=1752565596267
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:46:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:46:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xff5652fa, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb93240b3, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565606276
timestamp=1752565606276
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:46:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:46:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3db9fc60, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9b29a480, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565616277
timestamp=1752565616277
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:46:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x585a8503, L:null ! R:/************:8091]) will closed
2025-07-15 15:46:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:46:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:47:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7889792, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565626278
timestamp=1752565626278
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:47:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x566c018c, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:47:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfefe3af7, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565636280
timestamp=1752565636280
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:47:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1d2f3490, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:47:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd66dec68, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565646282
timestamp=1752565646282
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:47:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2eaf1d4a, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:47:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565656284
timestamp=1752565656284
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:47:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x428660d6, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0c5adf20, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:47:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565666285
timestamp=1752565666285
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:47:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x02ef6c91, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb62f3a50, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:47:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7574c467, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565676287
timestamp=1752565676287
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:47:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4ceceb98, L:null ! R:/************:8091]) will closed
2025-07-15 15:47:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:47:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:48:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3416576e, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565686288
timestamp=1752565686288
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:48:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:48:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x334628f1, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2992e316, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565696290
timestamp=1752565696290
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:48:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe71d3540, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:48:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb3a11936, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565706291
timestamp=1752565706291
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:48:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:48:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x45ef493a, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565716292
timestamp=1752565716292
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:48:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd745676e, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4cbefc76, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:48:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565726293
timestamp=1752565726293
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:48:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6ffea394, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x38111faf, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:48:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5c3319b3, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565736295
timestamp=1752565736295
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:48:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2b23b26d, L:null ! R:/************:8091]) will closed
2025-07-15 15:48:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:48:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:49:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe3693a5c, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565746297
timestamp=1752565746297
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:49:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:49:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb2b2372e, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x209b167b, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565756298
timestamp=1752565756298
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:49:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6b446879, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:49:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfc88e5f8, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565766300
timestamp=1752565766300
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:49:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:49:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcc26743a, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x503f7647, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565776303
timestamp=1752565776303
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:49:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2c7f22e9, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:49:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5751288a, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565786304
timestamp=1752565786304
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:49:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd5f35e35, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:49:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x932066e1, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565796306
timestamp=1752565796306
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:49:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf73fdaaa, L:null ! R:/************:8091]) will closed
2025-07-15 15:49:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:49:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:50:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6ce41def, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565806307
timestamp=1752565806307
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:50:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe4aa6e13, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:50:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xee061d4f, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565816309
timestamp=1752565816309
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:50:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd30c3af6, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:50:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88631252, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565826311
timestamp=1752565826311
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:50:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x84bb10d9, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:50:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5f051d31, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565836312
timestamp=1752565836312
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:50:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:50:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd32944db, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565846313
timestamp=1752565846313
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:50:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb14edeb0, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:50:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd13ee8de, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565856314
timestamp=1752565856314
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:50:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe385c008, L:null ! R:/************:8091]) will closed
2025-07-15 15:50:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:50:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:50:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7f2c7586, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565866316
timestamp=1752565866316
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:51:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbfcbce63, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4e943cb6, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:51:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565876317
timestamp=1752565876317
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:51:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2941af4e, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:51:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2ea6ac37, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1fa5ec87, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565886325
timestamp=1752565886325
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:51:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x08f18db8, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:51:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6d29672a, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565896326
timestamp=1752565896326
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:51:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x08c32745, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:51:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2951ebe8, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565906327
timestamp=1752565906327
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:51:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x95ae1852, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:51:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x943ae854, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565916330
timestamp=1752565916330
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:51:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4cb4a106, L:null ! R:/************:8091]) will closed
2025-07-15 15:51:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:51:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:52:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfcee8718, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565926331
timestamp=1752565926331
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:52:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:52:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2c283bc4, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3f5bf4bc, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565936332
timestamp=1752565936332
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:52:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:52:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x581b7dad, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565946334
timestamp=1752565946334
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:52:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xedaae50d, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x79ef4bf9, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:52:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565956335
timestamp=1752565956335
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:52:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1fb8c2f0, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x61e629eb, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:52:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565966336
timestamp=1752565966336
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:52:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x50c87885, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb0d4f0ed, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:52:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565976338
timestamp=1752565976338
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:52:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e574129, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4ff10f87, L:null ! R:/************:8091]) will closed
2025-07-15 15:52:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:52:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:53:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565986339
timestamp=1752565986339
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x94edbeee, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x83166edc, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:53:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565996341
timestamp=1752565996341
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:53:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1669c6fb, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7f0be48f, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:53:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566006342
timestamp=1752566006342
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:53:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9fde7c7e, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x901df1bd, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:53:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566016343
timestamp=1752566016343
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:53:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x741ef59a, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb3be61d1, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:53:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7ad42ce9, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566026346
timestamp=1752566026346
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:53:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:53:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbd417fc8, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x11577c44, L:null ! R:/************:8091]) will closed
2025-07-15 15:53:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566036346
timestamp=1752566036346
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:53:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:53:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:53:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x49d2d506, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566046347
timestamp=1752566046347
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:54:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdcf24d3a, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:54:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe354c9a3, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566056348
timestamp=1752566056348
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:54:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe78d7396, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:54:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd85d861b, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x134ede91, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566066350
timestamp=1752566066350
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:54:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb85083a7, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:54:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc92663ff, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566076351
timestamp=1752566076351
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:54:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaf7fd182, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:54:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566086352
timestamp=1752566086352
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:54:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x524e9e13, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x882ebab1, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:54:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xba11abc5, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566096353
timestamp=1752566096353
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:54:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9504eaac, L:null ! R:/************:8091]) will closed
2025-07-15 15:54:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:54:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:55:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x041812ce, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566106355
timestamp=1752566106355
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:55:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:55:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4ba2343f, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9ee91661, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566116356
timestamp=1752566116356
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:55:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x35c2ce8e, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:55:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566126357
timestamp=1752566126357
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:55:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe224553e, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xacec2725, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:55:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566136359
timestamp=1752566136359
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:55:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2a7c8fda, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x15e2fa43, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:55:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566146360
timestamp=1752566146360
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:55:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x290ce9e6, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:55:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2a627b6a, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb56cfb6e, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566156361
timestamp=1752566156361
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:55:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd8c4f0e6, L:null ! R:/************:8091]) will closed
2025-07-15 15:55:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:55:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:56:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566166362
timestamp=1752566166362
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:56:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9c8af2f7, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1197e0f7, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:56:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9224a25e, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566176363
timestamp=1752566176363
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:56:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:56:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x15631281, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfea582bb, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566186365
timestamp=1752566186365
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:56:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:56:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdb7fc48a, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c204f94, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566196366
timestamp=1752566196366
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:56:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:56:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x58ad4668, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x864b7c0d, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566206368
timestamp=1752566206368
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:56:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x995b9fe5, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:56:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa79e1585, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566216369
timestamp=1752566216369
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:56:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb6d6ade3, L:null ! R:/************:8091]) will closed
2025-07-15 15:56:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:56:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:57:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x20f5ead2, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566226371
timestamp=1752566226371
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:57:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfa4bf959, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:57:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566236371
timestamp=1752566236371
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:57:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf148eb26, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4e1b35a6, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:57:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566246372
timestamp=1752566246372
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:57:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x34acd93d, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x91ad885d, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:57:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67b1f8a2, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566256374
timestamp=1752566256374
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:57:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:57:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5181c9ec, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566266375
timestamp=1752566266375
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:57:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e0ad196, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2fb62106, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:57:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5d32470c, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566276376
timestamp=1752566276376
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:57:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9255c6d9, L:null ! R:/************:8091]) will closed
2025-07-15 15:57:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:57:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:58:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566286377
timestamp=1752566286377
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:58:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x98d65133, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8e2f14ef, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:58:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9b17ae0b, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566296378
timestamp=1752566296378
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:58:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2fc50a33, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:58:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566306379
timestamp=1752566306379
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:58:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8e44a72a, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc07f6c4b, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:58:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566316380
timestamp=1752566316380
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:58:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5510d0e6, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8a990cfe, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:58:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566326381
timestamp=1752566326381
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:58:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe5f0221e, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdbd70824, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:58:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb8c77118, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566336383
timestamp=1752566336383
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:58:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe52ead3d, L:null ! R:/************:8091]) will closed
2025-07-15 15:58:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:58:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:59:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566346383
timestamp=1752566346383
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:59:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4ccf7ff5, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:59:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7729072d, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x56d68885, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566356385
timestamp=1752566356385
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:59:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0c58e8c8, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:59:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2716ab47, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566366386
timestamp=1752566366386
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:59:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe1588cd7, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:59:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566376387
timestamp=1752566376387
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:59:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6abc3321, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7f62d53c, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:59:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1874d928, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566386389
timestamp=1752566386389
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:59:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x05f70499, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:59:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566396390
timestamp=1752566396390
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 15:59:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0a42ad37, L:null ! R:/************:8091]) will closed
2025-07-15 15:59:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 15:59:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 15:59:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4eb125fd, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x50530ded, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566406391
timestamp=1752566406391
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:00:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:00:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf083883c, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac007713, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566416392
timestamp=1752566416392
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:00:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaa3a34bc, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:00:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf739c11b, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566426393
timestamp=1752566426393
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:00:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8d171937, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:00:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfb0fc2ec, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566436395
timestamp=1752566436395
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:00:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x19b0a323, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:00:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1868f436, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566446397
timestamp=1752566446397
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:00:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c1810a0, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:00:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb3b99edb, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566456398
timestamp=1752566456398
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:00:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x165e4611, L:null ! R:/************:8091]) will closed
2025-07-15 16:00:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:00:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:01:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa540da81, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566466410
timestamp=1752566466410
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:01:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe383ab18, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:01:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeec9e33c, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566476411
timestamp=1752566476411
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:01:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:01:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0712edf8, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x814017a7, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566486412
timestamp=1752566486412
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:01:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:01:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1cb12446, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566496413
timestamp=1752566496413
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:01:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6764da39, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x998c715e, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:01:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566506414
timestamp=1752566506414
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:01:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x903dbd19, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:01:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3fd45f5f, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566516415
timestamp=1752566516415
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:01:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x656ec60a, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdcc19f64, L:null ! R:/************:8091]) will closed
2025-07-15 16:01:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:01:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:02:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566526416
timestamp=1752566526416
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:02:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x52e39117, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x23f90264, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:02:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa2fd55a7, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566536418
timestamp=1752566536418
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:02:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa663650f, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:02:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566546419
timestamp=1752566546419
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:02:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7762895b, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaacf22c5, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:02:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566556420
timestamp=1752566556420
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:02:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4d77c752, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:02:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x42f805bc, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566566421
timestamp=1752566566421
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:02:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x51c83ebc, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:02:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x834065d4, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566576422
timestamp=1752566576422
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:02:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe9a95e1d, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3c735613, L:null ! R:/************:8091]) will closed
2025-07-15 16:02:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:02:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:03:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566586423
timestamp=1752566586423
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:03:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5e67d081, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb712a578, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:03:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe10ca882, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566596424
timestamp=1752566596424
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:03:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:03:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfce4114f, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf0e1e18f, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566606425
timestamp=1752566606425
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:03:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe1f909c, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:03:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5c7cafe9, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566616426
timestamp=1752566616426
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:03:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe0c149c4, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:03:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566626427
timestamp=1752566626427
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:03:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x02bb4254, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:03:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd95317da, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x64ee638e, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566636429
timestamp=1752566636429
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:03:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2d0b4037, L:null ! R:/************:8091]) will closed
2025-07-15 16:03:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:03:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:04:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6ca44045, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566646432
timestamp=1752566646432
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:04:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x57518108, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:04:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566656432
timestamp=1752566656432
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:04:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x18ee1687, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4e0feb0a, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:04:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x47574679, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566666434
timestamp=1752566666434
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:04:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6a760e95, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:04:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566676436
timestamp=1752566676436
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:04:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf6523af2, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe5b3b110, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:04:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaa3f7ddf, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566686437
timestamp=1752566686437
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:04:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:04:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x01d4afd6, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566696438
timestamp=1752566696438
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:04:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8046c71b, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xef16e0e7, L:null ! R:/************:8091]) will closed
2025-07-15 16:04:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:04:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:05:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2e29b1cd, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566706439
timestamp=1752566706439
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:05:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x39bbdb81, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:05:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x83ed8fcc, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566716440
timestamp=1752566716440
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:05:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3cee2fb2, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:05:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566726441
timestamp=1752566726441
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:05:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x85e73c24, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:05:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x76cbae92, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb2a4f98f, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566736442
timestamp=1752566736442
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:05:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:05:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd6c14e28, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9886c7ff, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566746444
timestamp=1752566746444
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:05:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:05:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x957aaf38, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9e02f558, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566756446
timestamp=1752566756446
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:05:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x767af4e3, L:null ! R:/************:8091]) will closed
2025-07-15 16:05:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:05:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:06:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9c090580, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566766447
timestamp=1752566766447
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:06:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdc2d09b5, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:06:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x34e4b34c, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566776448
timestamp=1752566776448
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:06:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeea30ed7, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:06:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566786449
timestamp=1752566786449
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:06:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x804fc033, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc3a4b1e4, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:06:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566796450
timestamp=1752566796450
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:06:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x69234aac, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67f39406, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:06:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2bc96340, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566806451
timestamp=1752566806451
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:06:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x76b7c181, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:06:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x180525eb, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566816453
timestamp=1752566816453
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:06:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x39266ac6, L:null ! R:/************:8091]) will closed
2025-07-15 16:06:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:06:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:07:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5d14c273, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566826454
timestamp=1752566826454
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:07:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9c089b94, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:07:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x71abefad, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566836455
timestamp=1752566836455
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:07:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x29461eb6, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:07:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa2da0f49, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566846457
timestamp=1752566846457
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:07:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xccb3316e, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9ce84273, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566856458
timestamp=1752566856458
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:07:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x319e2067, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566866459
timestamp=1752566866459
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:07:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1792996a, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:07:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1b9e4e90, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566876460
timestamp=1752566876460
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:07:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xee767e51, L:null ! R:/************:8091]) will closed
2025-07-15 16:07:56 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:07:56 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:07:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x076f00e5, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566886461
timestamp=1752566886461
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:08:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x81c4cca4, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:08:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x95d457f4, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x24fe23e6, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566896462
timestamp=1752566896462
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:08:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x641e5305, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:08:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf8ba0c88, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566906464
timestamp=1752566906464
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:08:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x75d72c7e, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:08:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1f0762b7, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566916465
timestamp=1752566916465
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:08:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa196c346, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
2025-07-15 16:08:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe689d02, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752566926466
timestamp=1752566926466
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'} >
2025-07-15 16:08:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8c74f348, L:null ! R:/************:8091]) will closed
2025-07-15 16:08:46 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-15 16:08:46 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'} >
