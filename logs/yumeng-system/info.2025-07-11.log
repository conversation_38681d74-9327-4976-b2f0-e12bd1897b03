2025-07-11 21:16:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-11 21:16:55 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 26392 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-11 21:16:55 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-11 21:16:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-11 21:16:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-11 21:16:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-11 21:17:06 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-11 21:17:06 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-11 21:17:06 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-11 21:17:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-11 21:17:06 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-11 21:17:06 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-11 21:17:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-11 21:17:07 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-11 21:17:07 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-11 21:17:07 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-11 21:17:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-11 21:17:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-11 21:17:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-11 21:17:12 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-11 21:17:12 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-11 21:17:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-11 21:17:13 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@e68c09d
2025-07-11 21:17:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-11 21:17:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-11 21:17:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-11 21:17:18 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-11 21:17:19 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-11 21:17:19 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-11 21:17:19 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-11 21:17:20 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-11 21:17:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-11 21:17:23 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-11 21:17:30 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-11 21:17:30 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-11 21:17:30 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-11 21:17:30 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-11 21:17:31 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system 192.168.0.76:9201 register finished
2025-07-11 21:17:36 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 46.691 seconds (process running for 48.999)
2025-07-11 21:17:36 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-11 21:17:36 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-11 21:17:36 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-11 21:17:36 [RMI TCP Connection(2)-192.168.0.76] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 21:26:46 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-11 21:26:46 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
