2025-07-18 10:07:09 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 10:07:09 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 7984 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 10:07:09 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 10:07:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 10:07:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 10:07:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 10:07:16 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 10:07:16 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 10:07:16 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 10:07:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 10:07:16 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:07:16 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 10:07:16 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:07:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752804437021
timestamp=1752804437021
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 10:07:17 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x91eab4d8, L:/*************:11470 - R:/*************:8091]
2025-07-18 10:07:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 191 ms, version:1.7.1,role:TMROLE,channel:[id: 0x91eab4d8, L:/*************:11470 - R:/*************:8091]
2025-07-18 10:07:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 10:07:17 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 10:07:17 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 10:07:17 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:07:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 10:07:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 10:07:19 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 10:07:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 10:07:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 10:07:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 10:10:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 10:10:13 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 35004 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 10:10:13 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 10:10:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 10:10:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 10:10:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 10:10:19 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 10:10:19 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 10:10:19 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 10:10:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 10:10:19 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:10:19 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 10:10:19 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:10:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752804619507
timestamp=1752804619507
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 10:10:19 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa01f991a, L:/*************:13427 - R:/*************:8091]
2025-07-18 10:10:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 47 ms, version:1.7.1,role:TMROLE,channel:[id: 0xa01f991a, L:/*************:13427 - R:/*************:8091]
2025-07-18 10:10:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 10:10:19 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 10:10:19 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 10:10:19 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:10:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 10:10:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 10:10:20 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 10:10:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 10:10:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 10:10:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 10:10:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa01f991a, L:/*************:13427 ! R:/*************:8091]
2025-07-18 10:10:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa01f991a, L:/*************:13427 ! R:/*************:8091]
2025-07-18 10:10:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa01f991a, L:/*************:13427 ! R:/*************:8091]
2025-07-18 10:10:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa01f991a, L:/*************:13427 ! R:/*************:8091]
2025-07-18 10:10:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa01f991a, L:/*************:13427 ! R:/*************:8091]) will closed
2025-07-18 10:10:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa01f991a, L:/*************:13427 ! R:/*************:8091]) will closed
2025-07-18 10:11:35 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 10:11:35 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 20456 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 10:11:35 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 10:11:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 10:11:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 10:11:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 10:11:41 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 10:11:41 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 10:11:41 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 10:11:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 10:11:41 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:11:41 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 10:11:42 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:11:42 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752804702211
timestamp=1752804702211
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 10:11:42 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4425dd5a, L:/*************:13912 - R:/*************:8091]
2025-07-18 10:11:42 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 95 ms, version:1.7.1,role:TMROLE,channel:[id: 0x4425dd5a, L:/*************:13912 - R:/*************:8091]
2025-07-18 10:11:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 10:11:42 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 10:11:42 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 10:11:42 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:11:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 10:11:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 10:11:43 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 10:11:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 10:11:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 10:11:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 10:11:46 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@31f5829e
2025-07-18 10:11:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 10:11:46 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:11:46 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 10:11:46 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 10:11:46 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2ec41a79, L:/*************:13939 - R:/*************:8091]
2025-07-18 10:11:46 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x2ec41a79, L:/*************:13939 - R:/*************:8091]
2025-07-18 10:11:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 10:11:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 10:11:48 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 10:11:49 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 10:11:49 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 10:11:49 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 10:11:49 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 10:11:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 10:11:51 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 10:11:54 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 10:11:54 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 10:11:54 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 10:11:54 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 10:11:54 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-18 10:11:58 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 26.146 seconds (process running for 27.273)
2025-07-18 10:11:59 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-18 10:11:59 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 10:11:59 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 10:11:59 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 10:18:57 [DubboServerHandler-*************:20882-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:18:57 [DubboServerHandler-*************:20882-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[151ms]
2025-07-18 10:18:57 [DubboServerHandler-*************:20882-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-18 10:18:58 [DubboServerHandler-*************:20882-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[496ms]
2025-07-18 10:18:58 [DubboServerHandler-*************:20882-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:18:58 [DubboServerHandler-*************:20882-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-18 10:18:58 [DubboServerHandler-*************:20882-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-18 10:18:58 [DubboServerHandler-*************:20882-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[8ms]
2025-07-18 10:18:58 [DubboServerHandler-*************:20882-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-18 10:18:58 [DubboServerHandler-*************:20882-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[100ms]
2025-07-18 10:19:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-18 10:19:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[5ms]
2025-07-18 10:19:12 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1276ms]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[28ms]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-18 10:19:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-18 10:19:15 [DubboServerHandler-*************:20882-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:19:15 [DubboServerHandler-*************:20882-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-18 10:19:15 [DubboServerHandler-*************:20882-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-18 10:19:15 [DubboServerHandler-*************:20882-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-18 10:19:22 [DubboServerHandler-*************:20882-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:19:22 [DubboServerHandler-*************:20882-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-18 10:19:22 [DubboServerHandler-*************:20882-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-18 10:19:22 [DubboServerHandler-*************:20882-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[99ms]
2025-07-18 10:19:23 [DubboServerHandler-*************:20882-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:19:23 [DubboServerHandler-*************:20882-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-18 10:19:23 [DubboServerHandler-*************:20882-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-18 10:19:23 [DubboServerHandler-*************:20882-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-18 10:19:23 [DubboServerHandler-*************:20882-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-18 10:19:23 [DubboServerHandler-*************:20882-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[64ms]
2025-07-18 10:19:24 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:24 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:24 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:24 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:38 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:19:45 [DubboServerHandler-*************:20882-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:19:45 [DubboServerHandler-*************:20882-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[19ms]
2025-07-18 10:19:45 [DubboServerHandler-*************:20882-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-18 10:19:45 [DubboServerHandler-*************:20882-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[152ms]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-18 10:19:51 [DubboServerHandler-*************:20882-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[64ms]
2025-07-18 10:19:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:19:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 10:19:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:26:40 [DubboServerHandler-*************:20882-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[80ms]
2025-07-18 10:26:43 [DubboServerHandler-*************:20882-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[78ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[41ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[107ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[83ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-49] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-49] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [DubboServerHandler-*************:20882-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-18 10:26:46 [DubboServerHandler-*************:20882-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:46 [DubboServerHandler-*************:20882-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:26:46 [DubboServerHandler-*************:20882-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:46 [DubboServerHandler-*************:20882-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[50ms]
2025-07-18 10:26:46 [DubboServerHandler-*************:20882-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:46 [DubboServerHandler-*************:20882-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:26:47 [DubboServerHandler-*************:20882-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:26:47 [DubboServerHandler-*************:20882-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[127ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[54ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-70] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-70] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:27:01 [DubboServerHandler-*************:20882-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:02 [DubboServerHandler-*************:20882-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:27:06 [DubboServerHandler-*************:20882-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:06 [DubboServerHandler-*************:20882-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:27:06 [DubboServerHandler-*************:20882-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:06 [DubboServerHandler-*************:20882-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[51ms]
2025-07-18 10:27:06 [DubboServerHandler-*************:20882-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:06 [DubboServerHandler-*************:20882-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:27:07 [DubboServerHandler-*************:20882-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:27:07 [DubboServerHandler-*************:20882-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[134ms]
2025-07-18 10:27:22 [DubboServerHandler-*************:20882-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:22 [DubboServerHandler-*************:20882-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[18ms]
2025-07-18 10:27:22 [DubboServerHandler-*************:20882-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:22 [DubboServerHandler-*************:20882-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[51ms]
2025-07-18 10:27:22 [DubboServerHandler-*************:20882-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:22 [DubboServerHandler-*************:20882-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:27:23 [DubboServerHandler-*************:20882-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:27:23 [DubboServerHandler-*************:20882-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[128ms]
2025-07-18 10:28:33 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:28:33 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:28:33 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-18 10:28:33 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-18 10:28:48 [DubboServerHandler-*************:20882-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:28:48 [DubboServerHandler-*************:20882-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:48 [DubboServerHandler-*************:20882-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-18 10:28:48 [DubboServerHandler-*************:20882-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:48 [DubboServerHandler-*************:20882-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[87ms]
2025-07-18 10:28:48 [DubboServerHandler-*************:20882-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-18 10:28:48 [DubboServerHandler-*************:20882-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [DubboServerHandler-*************:20882-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:31:46 [DubboServerHandler-*************:20882-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:46 [DubboServerHandler-*************:20882-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-18 10:31:46 [DubboServerHandler-*************:20882-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:46 [DubboServerHandler-*************:20882-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-18 10:31:46 [DubboServerHandler-*************:20882-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:46 [DubboServerHandler-*************:20882-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:31:46 [DubboServerHandler-*************:20882-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:46 [DubboServerHandler-*************:20882-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:31:50 [DubboServerHandler-*************:20882-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:50 [DubboServerHandler-*************:20882-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[50ms]
2025-07-18 10:31:50 [DubboServerHandler-*************:20882-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:50 [DubboServerHandler-*************:20882-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:31:50 [DubboServerHandler-*************:20882-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:50 [DubboServerHandler-*************:20882-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:31:50 [DubboServerHandler-*************:20882-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:50 [DubboServerHandler-*************:20882-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[81ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[101ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[83ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-107] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-107] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-108] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-108] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-111] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-111] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-112] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-112] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[24ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [DubboServerHandler-*************:20882-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:31:54 [DubboServerHandler-*************:20882-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:54 [DubboServerHandler-*************:20882-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 10:31:54 [DubboServerHandler-*************:20882-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:54 [DubboServerHandler-*************:20882-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[50ms]
2025-07-18 10:31:54 [DubboServerHandler-*************:20882-thread-124] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:54 [DubboServerHandler-*************:20882-thread-124] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:31:55 [DubboServerHandler-*************:20882-thread-125] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:31:55 [DubboServerHandler-*************:20882-thread-125] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[130ms]
2025-07-18 10:32:20 [DubboServerHandler-*************:20882-thread-126] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:32:20 [DubboServerHandler-*************:20882-thread-126] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[22ms]
2025-07-18 10:32:20 [DubboServerHandler-*************:20882-thread-127] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-18 10:32:20 [DubboServerHandler-*************:20882-thread-127] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-18 10:32:28 [DubboServerHandler-*************:20882-thread-128] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:32:28 [DubboServerHandler-*************:20882-thread-128] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-18 10:32:29 [DubboServerHandler-*************:20882-thread-129] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-18 10:32:29 [DubboServerHandler-*************:20882-thread-129] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[95ms]
2025-07-18 10:32:29 [DubboServerHandler-*************:20882-thread-130] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 10:32:29 [DubboServerHandler-*************:20882-thread-130] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-18 10:32:29 [DubboServerHandler-*************:20882-thread-131] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-18 10:32:29 [DubboServerHandler-*************:20882-thread-131] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-18 10:32:29 [DubboServerHandler-*************:20882-thread-132] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-18 10:32:29 [DubboServerHandler-*************:20882-thread-132] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[63ms]
2025-07-18 10:32:30 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:32:30 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:32:30 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:32:30 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 10:36:04 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:36:04 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:36:04 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-18 10:36:04 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[28ms]
2025-07-18 10:36:45 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:36:45 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:36:45 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-18 10:36:45 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-18 10:38:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:38:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:38:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-18 10:38:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-18 10:42:26 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-18 10:43:14 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:43:14 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:43:14 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-18 10:43:14 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-18 10:44:26 [DubboServerHandler-*************:20882-thread-136] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 10:44:26 [DubboServerHandler-*************:20882-thread-136] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 10:47:38 [DubboServerHandler-*************:20882-thread-137] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 10:47:38 [DubboServerHandler-*************:20882-thread-137] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 10:47:43 [DubboServerHandler-*************:20882-thread-138] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 10:47:43 [DubboServerHandler-*************:20882-thread-138] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 10:49:39 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:49:39 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:49:39 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-18 10:49:39 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-18 10:50:25 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-18 10:50:25 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 10:50:28 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 10:50:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4425dd5a, L:/*************:13912 ! R:/*************:8091]
2025-07-18 10:50:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4425dd5a, L:/*************:13912 ! R:/*************:8091]
2025-07-18 10:50:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4425dd5a, L:/*************:13912 ! R:/*************:8091]
2025-07-18 10:50:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4425dd5a, L:/*************:13912 ! R:/*************:8091]
2025-07-18 10:50:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4425dd5a, L:/*************:13912 ! R:/*************:8091]) will closed
2025-07-18 10:50:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4425dd5a, L:/*************:13912 ! R:/*************:8091]) will closed
2025-07-18 10:50:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2ec41a79, L:/*************:13939 ! R:/*************:8091]
2025-07-18 10:50:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2ec41a79, L:/*************:13939 ! R:/*************:8091]
2025-07-18 10:50:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2ec41a79, L:/*************:13939 ! R:/*************:8091]
2025-07-18 10:50:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2ec41a79, L:/*************:13939 ! R:/*************:8091]
2025-07-18 10:50:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2ec41a79, L:/*************:13939 ! R:/*************:8091]) will closed
2025-07-18 10:50:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2ec41a79, L:/*************:13939 ! R:/*************:8091]) will closed
2025-07-18 10:50:40 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 10:50:40 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 14048 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 10:50:40 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 10:50:41 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 10:50:41 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 10:50:41 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 10:50:45 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 10:50:45 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 10:50:45 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 10:50:45 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 10:50:46 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:50:46 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 10:50:46 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:50:46 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752807046212
timestamp=1752807046212
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 10:50:46 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x00dc0228, L:/*************:7517 - R:/*************:8091]
2025-07-18 10:50:46 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 83 ms, version:1.7.1,role:TMROLE,channel:[id: 0x00dc0228, L:/*************:7517 - R:/*************:8091]
2025-07-18 10:50:46 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 10:50:46 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 10:50:46 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 10:50:46 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:50:46 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 10:50:46 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 10:50:47 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 10:50:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 10:50:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 10:50:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 10:50:49 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@579c24c2
2025-07-18 10:50:49 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 10:50:49 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:50:49 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 10:50:49 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 10:50:49 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd47a7630, L:/*************:7522 - R:/*************:8091]
2025-07-18 10:50:49 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xd47a7630, L:/*************:7522 - R:/*************:8091]
2025-07-18 10:50:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 10:50:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 10:50:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 10:50:52 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 10:50:52 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 10:50:52 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 10:50:53 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 10:50:53 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 10:50:54 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 10:50:57 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 10:50:57 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 10:50:57 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 10:50:58 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 10:50:58 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-18 10:51:03 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 24.741 seconds (process running for 26.771)
2025-07-18 10:51:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-18 10:51:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 10:51:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 10:51:03 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 10:51:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:51:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:51:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[156ms]
2025-07-18 10:51:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[231ms]
2025-07-18 10:54:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:54:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 10:54:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-18 10:54:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd47a7630, L:/*************:7522 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x00dc0228, L:/*************:7517 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x00dc0228, L:/*************:7517 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xd47a7630, L:/*************:7522 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x00dc0228, L:/*************:7517 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x00dc0228, L:/*************:7517 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x00dc0228, L:/*************:7517 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00dc0228, L:/*************:7517 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd47a7630, L:/*************:7522 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd47a7630, L:/*************:7522 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00dc0228, L:/*************:7517 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd47a7630, L:/*************:7522 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd47a7630, L:/*************:7522 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd47a7630, L:/*************:7522 ! R:/*************:8091]) will closed
2025-07-18 11:13:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:13:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752808416034
timestamp=1752808416034
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 11:13:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x06d5b48e, L:/**********:7358 ! R:/*************:8091]
2025-07-18 11:13:36 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:13:36 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 11:13:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd47a7630, L:/*************:7522 ! R:/*************:8091]
2025-07-18 11:13:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd47a7630, L:/*************:7522 ! R:/*************:8091]
2025-07-18 11:13:36 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 11:13:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x17a56ddc, L:/**********:7375 ! R:/*************:8091]
2025-07-18 11:13:57 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-18 11:13:57 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-18 11:14:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x06d5b48e, L:/**********:7358 ! R:/*************:8091]
2025-07-18 11:14:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x06d5b48e, L:/**********:7358 ! R:/*************:8091]
2025-07-18 11:14:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x06d5b48e, L:/**********:7358 ! R:/*************:8091]) will closed
2025-07-18 11:14:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x06d5b48e, L:/**********:7358 ! R:/*************:8091]) will closed
2025-07-18 11:14:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:14:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752808446047
timestamp=1752808446047
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 11:14:06 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x60b7c591, L:/*************:7327 - R:/*************:8091]
2025-07-18 11:14:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:TMROLE,channel:[id: 0x60b7c591, L:/*************:7327 - R:/*************:8091]
2025-07-18 11:14:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x17a56ddc, L:/**********:7375 ! R:/*************:8091]
2025-07-18 11:14:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x17a56ddc, L:/**********:7375 ! R:/*************:8091]
2025-07-18 11:14:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x17a56ddc, L:/**********:7375 ! R:/*************:8091]) will closed
2025-07-18 11:14:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x17a56ddc, L:/**********:7375 ! R:/*************:8091]) will closed
2025-07-18 11:14:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:14:06 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 11:14:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 11:14:06 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xfd5ecc4a, L:/*************:7407 - R:/*************:8091]
2025-07-18 11:14:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0xfd5ecc4a, L:/*************:7407 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xfd5ecc4a, L:/*************:7407 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xfd5ecc4a, L:/*************:7407 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xfd5ecc4a, L:/*************:7407 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfd5ecc4a, L:/*************:7407 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfd5ecc4a, L:/*************:7407 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfd5ecc4a, L:/*************:7407 ! R:/*************:8091]) will closed
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfd5ecc4a, L:/*************:7407 ! R:/*************:8091]) will closed
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x60b7c591, L:/*************:7327 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x60b7c591, L:/*************:7327 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x60b7c591, L:/*************:7327 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x60b7c591, L:/*************:7327 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x60b7c591, L:/*************:7327 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x60b7c591, L:/*************:7327 ! R:/*************:8091]) will closed
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x60b7c591, L:/*************:7327 ! R:/*************:8091]) will closed
2025-07-18 11:39:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 11:39:55 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 12748 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 11:39:55 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 11:39:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 11:39:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 11:39:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 11:40:05 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 11:40:05 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 11:40:05 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 11:40:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 11:40:06 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 11:40:06 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 11:40:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 11:40:07 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 11:40:07 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 11:40:07 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 11:40:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 11:40:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 11:40:08 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 13:42:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 13:42:21 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 18544 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 13:42:21 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 13:42:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 13:42:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 13:42:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 13:42:25 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 13:42:25 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 13:42:25 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 13:42:25 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 13:42:25 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 13:42:25 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 13:42:26 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 13:42:26 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752817346154
timestamp=1752817346154
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 13:42:26 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x1cead6fe, L:/*************:8527 - R:/*************:8091]
2025-07-18 13:42:26 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 60 ms, version:1.7.1,role:TMROLE,channel:[id: 0x1cead6fe, L:/*************:8527 - R:/*************:8091]
2025-07-18 13:42:26 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 13:42:26 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 13:42:26 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 13:42:26 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 13:42:26 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 13:42:26 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 13:42:27 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 13:42:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 13:42:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 13:42:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 13:42:28 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3eacf08e
2025-07-18 13:42:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 13:42:28 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 13:42:28 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 13:42:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 13:42:28 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7ee731f1, L:/*************:8533 - R:/*************:8091]
2025-07-18 13:42:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x7ee731f1, L:/*************:8533 - R:/*************:8091]
2025-07-18 13:42:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 13:42:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 13:42:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 13:42:30 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 13:42:30 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 13:42:31 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 13:42:31 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 13:42:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 13:42:32 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 13:42:35 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 13:42:35 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 13:42:35 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 13:42:35 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 13:42:35 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-18 13:42:38 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 19.48 seconds (process running for 20.655)
2025-07-18 13:42:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-18 13:42:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 13:42:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 13:42:39 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 13:43:27 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-18 13:43:27 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 13:43:30 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 13:43:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x1cead6fe, L:/*************:8527 ! R:/*************:8091]
2025-07-18 13:43:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x1cead6fe, L:/*************:8527 ! R:/*************:8091]
2025-07-18 13:43:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x1cead6fe, L:/*************:8527 ! R:/*************:8091]
2025-07-18 13:43:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x1cead6fe, L:/*************:8527 ! R:/*************:8091]
2025-07-18 13:43:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1cead6fe, L:/*************:8527 ! R:/*************:8091]) will closed
2025-07-18 13:43:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1cead6fe, L:/*************:8527 ! R:/*************:8091]) will closed
2025-07-18 13:43:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7ee731f1, L:/*************:8533 ! R:/*************:8091]
2025-07-18 13:43:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7ee731f1, L:/*************:8533 ! R:/*************:8091]
2025-07-18 13:43:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7ee731f1, L:/*************:8533 ! R:/*************:8091]
2025-07-18 13:43:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7ee731f1, L:/*************:8533 ! R:/*************:8091]
2025-07-18 13:43:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7ee731f1, L:/*************:8533 ! R:/*************:8091]) will closed
2025-07-18 13:43:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7ee731f1, L:/*************:8533 ! R:/*************:8091]) will closed
2025-07-18 14:20:59 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 14:21:00 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 10756 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 14:21:00 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 14:21:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 14:21:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 14:21:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 14:21:07 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 14:21:07 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 14:21:07 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 14:21:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 14:21:08 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:21:08 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 14:21:08 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 14:21:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752819668456
timestamp=1752819668456
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 14:21:08 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x673561b0, L:/*************:4787 - R:/*************:8091]
2025-07-18 14:21:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 105 ms, version:1.7.1,role:TMROLE,channel:[id: 0x673561b0, L:/*************:4787 - R:/*************:8091]
2025-07-18 14:21:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 14:21:08 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 14:21:08 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 14:21:08 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:21:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 14:21:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 14:21:10 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 14:21:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 14:21:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 14:21:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 14:21:12 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1b48c142
2025-07-18 14:21:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 14:21:13 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 14:21:13 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 14:21:13 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 14:21:13 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x62b1c2e0, L:/*************:4795 - R:/*************:8091]
2025-07-18 14:21:13 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x62b1c2e0, L:/*************:4795 - R:/*************:8091]
2025-07-18 14:21:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 14:21:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 14:21:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 14:21:15 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 14:21:15 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 14:21:16 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 14:21:16 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 14:21:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 14:21:17 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 14:21:20 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 14:21:21 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 14:21:21 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 14:21:21 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 14:21:21 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-18 14:21:25 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 28.424 seconds (process running for 29.737)
2025-07-18 14:21:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-18 14:21:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 14:21:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 14:21:26 [RMI TCP Connection(6)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 14:23:13 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-18 15:02:23 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-18 15:02:23 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 15:02:26 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 15:02:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x673561b0, L:/*************:4787 ! R:/*************:8091]
2025-07-18 15:02:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x673561b0, L:/*************:4787 ! R:/*************:8091]
2025-07-18 15:02:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x673561b0, L:/*************:4787 ! R:/*************:8091]
2025-07-18 15:02:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x673561b0, L:/*************:4787 ! R:/*************:8091]
2025-07-18 15:02:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x673561b0, L:/*************:4787 ! R:/*************:8091]) will closed
2025-07-18 15:02:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x673561b0, L:/*************:4787 ! R:/*************:8091]) will closed
2025-07-18 15:02:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x62b1c2e0, L:/*************:4795 ! R:/*************:8091]
2025-07-18 15:02:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x62b1c2e0, L:/*************:4795 ! R:/*************:8091]
2025-07-18 15:02:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x62b1c2e0, L:/*************:4795 ! R:/*************:8091]
2025-07-18 15:02:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x62b1c2e0, L:/*************:4795 ! R:/*************:8091]
2025-07-18 15:02:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x62b1c2e0, L:/*************:4795 ! R:/*************:8091]) will closed
2025-07-18 15:02:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x62b1c2e0, L:/*************:4795 ! R:/*************:8091]) will closed
2025-07-18 15:02:31 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 15:02:32 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 14040 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 15:02:32 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 15:02:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 15:02:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 15:02:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 15:02:37 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 15:02:37 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 15:02:37 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 15:02:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 15:02:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 15:02:38 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 15:02:38 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:02:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752822158255
timestamp=1752822158255
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 15:02:38 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf4ad0f92, L:/*************:3911 - R:/*************:8091]
2025-07-18 15:02:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 108 ms, version:1.7.1,role:TMROLE,channel:[id: 0xf4ad0f92, L:/*************:3911 - R:/*************:8091]
2025-07-18 15:02:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 15:02:38 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 15:02:38 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 15:02:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 15:02:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 15:02:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 15:02:40 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 15:02:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 15:02:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 15:02:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 15:02:42 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7b4db485
2025-07-18 15:02:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 15:02:42 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:02:42 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:02:42 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 15:02:42 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x831530a6, L:/*************:3965 - R:/*************:8091]
2025-07-18 15:02:42 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x831530a6, L:/*************:3965 - R:/*************:8091]
2025-07-18 15:02:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 15:02:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 15:02:45 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 15:02:45 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 15:02:45 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 15:02:45 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 15:02:46 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 15:02:46 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 15:02:47 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 15:02:51 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 15:02:51 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 15:02:51 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 15:02:51 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 15:02:51 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-18 15:02:55 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 25.481 seconds (process running for 26.741)
2025-07-18 15:02:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-18 15:02:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 15:02:55 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 15:02:55 [RMI TCP Connection(6)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:57:21 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-18 15:57:21 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf4ad0f92, L:/*************:3911 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x831530a6, L:/*************:3965 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x831530a6, L:/*************:3965 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xf4ad0f92, L:/*************:3911 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf4ad0f92, L:/*************:3911 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x831530a6, L:/*************:3965 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf4ad0f92, L:/*************:3911 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x831530a6, L:/*************:3965 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf4ad0f92, L:/*************:3911 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x831530a6, L:/*************:3965 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf4ad0f92, L:/*************:3911 ! R:/*************:8091]) will closed
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x831530a6, L:/*************:3965 ! R:/*************:8091]) will closed
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf4ad0f92, L:/*************:3911 ! R:/*************:8091]) will closed
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x831530a6, L:/*************:3965 ! R:/*************:8091]) will closed
2025-07-18 15:57:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825447998
timestamp=1752825447998
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 15:57:28 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:28 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:57:28 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x831530a6, L:/*************:3965 ! R:/*************:8091]
2025-07-18 15:57:28 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x831530a6, L:/*************:3965 ! R:/*************:8091]
2025-07-18 15:57:28 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 15:57:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2ec6c5c, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1f8f9204, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825457999
timestamp=1752825457999
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 15:57:38 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:38 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:57:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 15:57:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xded61e18, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x39465da3, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825467999
timestamp=1752825467999
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 15:57:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:57:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 15:57:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x28ffa918, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x74a96205, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:58 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:58 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825478006
timestamp=1752825478006
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 15:57:58 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:58 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:57:58 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 15:58:00 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65074d19, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:00 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd52e9075, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:08 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:08 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825488009
timestamp=1752825488009
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 15:58:08 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:08 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:58:08 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 15:58:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc0e17fa3, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x84642646, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825497998
timestamp=1752825497998
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 15:58:18 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:18 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:58:18 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 15:58:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa1cca167, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x15199ba5, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825507999
timestamp=1752825507999
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 15:58:28 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:28 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:58:28 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 15:58:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0da72a1b, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9b233800, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825517999
timestamp=1752825517999
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 15:58:38 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:38 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:58:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 15:58:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x61b7852c, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbac976e7, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:44 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 15:58:44 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 15:58:44 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 15:58:44 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 15:58:44 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 15:58:44 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-18 15:58:44 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 15:58:44 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 15:58:45 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 15:58:45 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 15:58:45 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 15:59:50 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 15:59:50 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 1332 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 15:59:50 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 15:59:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 15:59:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 15:59:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 16:00:00 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 16:00:00 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 16:00:00 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 16:00:00 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 16:00:00 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 16:00:00 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 16:00:01 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 16:00:01 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825601163
timestamp=1752825601163
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 16:00:01 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd3ec8da8, L:/*************:11401 - R:/*************:8091]
2025-07-18 16:00:01 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 147 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd3ec8da8, L:/*************:11401 - R:/*************:8091]
2025-07-18 16:00:01 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 16:00:01 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 16:00:01 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 16:00:01 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 16:00:01 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 16:00:01 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 16:00:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 16:00:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 16:00:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 16:00:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 16:00:06 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6183dd2
2025-07-18 16:00:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 16:00:06 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 16:00:06 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 16:00:06 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 16:00:06 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x744677b2, L:/*************:11434 - R:/*************:8091]
2025-07-18 16:00:06 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x744677b2, L:/*************:11434 - R:/*************:8091]
2025-07-18 16:00:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 16:00:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 16:00:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 16:00:09 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 16:00:09 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 16:00:09 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 16:00:09 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 16:00:10 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 16:00:11 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 16:00:14 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 16:00:14 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 16:00:14 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 16:00:14 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 16:00:14 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-18 16:00:21 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 33.22 seconds (process running for 34.448)
2025-07-18 16:00:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-18 16:00:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 16:00:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 16:00:21 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 16:02:38 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-18 17:02:48 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-18 17:02:48 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 17:02:51 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 17:02:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd3ec8da8, L:/*************:11401 ! R:/*************:8091]
2025-07-18 17:02:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd3ec8da8, L:/*************:11401 ! R:/*************:8091]
2025-07-18 17:02:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd3ec8da8, L:/*************:11401 ! R:/*************:8091]
2025-07-18 17:02:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd3ec8da8, L:/*************:11401 ! R:/*************:8091]
2025-07-18 17:02:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x744677b2, L:/*************:11434 ! R:/*************:8091]
2025-07-18 17:02:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x744677b2, L:/*************:11434 ! R:/*************:8091]
2025-07-18 17:02:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x744677b2, L:/*************:11434 ! R:/*************:8091]
2025-07-18 17:02:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x744677b2, L:/*************:11434 ! R:/*************:8091]
2025-07-18 17:02:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd3ec8da8, L:/*************:11401 ! R:/*************:8091]) will closed
2025-07-18 17:02:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x744677b2, L:/*************:11434 ! R:/*************:8091]) will closed
2025-07-18 17:02:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x744677b2, L:/*************:11434 ! R:/*************:8091]) will closed
2025-07-18 17:02:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd3ec8da8, L:/*************:11401 ! R:/*************:8091]) will closed
2025-07-18 17:03:05 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 17:03:05 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 17920 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 17:03:05 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 17:03:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 17:03:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 17:03:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 17:03:10 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 17:03:11 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 17:03:11 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 17:03:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 17:03:11 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 17:03:11 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 17:03:11 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 17:03:11 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752829391548
timestamp=1752829391548
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 17:03:11 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd1c354bb, L:/*************:4920 - R:/*************:8091]
2025-07-18 17:03:11 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 92 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd1c354bb, L:/*************:4920 - R:/*************:8091]
2025-07-18 17:03:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 17:03:11 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 17:03:11 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 17:03:11 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 17:03:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 17:03:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 17:03:13 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 17:03:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 17:03:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 17:03:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 17:03:15 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@470bef04
2025-07-18 17:03:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 17:03:15 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 17:03:15 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 17:03:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 17:03:15 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd112bfb1, L:/*************:5552 - R:/*************:8091]
2025-07-18 17:03:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xd112bfb1, L:/*************:5552 - R:/*************:8091]
2025-07-18 17:03:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 17:03:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 17:03:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 17:03:18 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 17:03:18 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 17:03:18 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 17:03:18 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 17:03:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 17:03:20 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 17:03:23 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 17:03:23 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 17:03:23 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 17:03:23 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 17:03:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-18 17:03:27 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 24.274 seconds (process running for 26.166)
2025-07-18 17:03:27 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-18 17:03:27 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 17:03:27 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 17:03:27 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 17:08:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 17:08:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[2176ms]
2025-07-18 17:08:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:08:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:08:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[13ms]
2025-07-18 17:08:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[80ms]
2025-07-18 17:21:16 [XNIO-1 task-2] INFO  c.y.s.c.system.SysNoticeController - 获取当前用户通知公告列表PageQuery(pageSize=2, pageNum=1, orderByColumn=null, isAsc=null)
2025-07-18 17:21:16 [XNIO-1 task-2] INFO  c.y.s.s.impl.SysNoticeServiceImpl - 部门ID: 1934262161186328578
2025-07-18 17:21:16 [XNIO-1 task-2] INFO  c.y.s.s.impl.SysNoticeServiceImpl - 用户部门: SysDept(deptId=1934262161186328578, parentId=100, deptName=青岛孚润德船舶管理有限公司, deptCategory=null, orderNum=3, leader=1934259258107203585, phone=null, email=null, status=0, delFlag=0, ancestors=0,100)
2025-07-18 17:21:16 [XNIO-1 task-2] INFO  c.y.s.s.impl.SysNoticeServiceImpl - 用户部门祖先列表3: [0, 100, 1934262161186328578]
2025-07-18 17:21:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:21:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[345ms]
2025-07-18 17:21:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:21:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:21:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-07-18 17:21:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[7ms]
2025-07-18 17:21:27 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 17:21:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[676ms]
2025-07-18 17:21:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:21:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:21:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 17:21:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 17:22:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 17:22:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[448ms]
2025-07-18 17:22:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:22:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:22:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 17:22:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 17:22:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:22:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:22:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 17:22:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 17:22:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:22:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:22:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 17:22:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-18 17:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 17:25:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 17:28:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 17:28:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[439ms]
2025-07-18 17:28:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:28:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:28:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 17:28:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 17:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 17:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-18 17:30:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:30:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:30:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 17:30:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-18 17:32:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:32:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 17:32:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 17:32:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[6ms]
2025-07-18 17:36:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:36:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[85ms]
2025-07-18 17:36:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:36:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:36:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 17:36:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-18 17:37:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:37:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[33ms]
2025-07-18 17:37:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:37:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:37:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 17:37:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 17:37:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:37:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[4ms]
2025-07-18 17:37:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:37:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:37:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 17:37:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-18 17:37:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:37:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[62ms]
2025-07-18 17:37:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:37:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 17:37:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 17:37:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xd112bfb1, L:/*************:5552 - R:/*************:8091] read idle.
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd112bfb1, L:/*************:5552 - R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd112bfb1, L:/*************:5552 - R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd112bfb1, L:/*************:5552 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xd1c354bb, L:/*************:4920 - R:/*************:8091] read idle.
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd1c354bb, L:/*************:4920 - R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd1c354bb, L:/*************:4920 - R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd1c354bb, L:/*************:4920 ! R:/*************:8091]) will closed
2025-07-18 18:25:48 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 18:25:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752834348260
timestamp=1752834348260
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 18:25:48 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2173493f, L:/*************:8311 - R:/*************:8091]
2025-07-18 18:25:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:TMROLE,channel:[id: 0x2173493f, L:/*************:8311 - R:/*************:8091]
2025-07-18 18:25:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 18:25:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 18:25:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 18:25:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xac5872f8, L:/*************:8322 - R:/*************:8091]
2025-07-18 18:25:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0xac5872f8, L:/*************:8322 - R:/*************:8091]
2025-07-18 18:32:13 [DubboServerHandler-*************:20882-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 18:32:13 [DubboServerHandler-*************:20882-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[213ms]
2025-07-18 18:32:14 [DubboServerHandler-*************:20882-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-18 18:32:14 [DubboServerHandler-*************:20882-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[189ms]
2025-07-18 18:32:14 [DubboServerHandler-*************:20882-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 18:32:14 [DubboServerHandler-*************:20882-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-18 18:32:14 [DubboServerHandler-*************:20882-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-18 18:32:14 [DubboServerHandler-*************:20882-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[6ms]
2025-07-18 18:32:14 [DubboServerHandler-*************:20882-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-18 18:32:14 [DubboServerHandler-*************:20882-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[107ms]
2025-07-18 18:32:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:32:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:32:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-18 18:32:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-18 18:49:49 [DubboServerHandler-*************:20882-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserByUserName]
2025-07-18 18:49:49 [DubboServerHandler-*************:20882-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserByUserName],SpendTime=[398ms]
2025-07-18 18:49:49 [DubboServerHandler-*************:20882-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 18:49:50 [DubboServerHandler-*************:20882-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[25ms]
2025-07-18 18:49:50 [DubboServerHandler-*************:20882-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfoByOpenid]
2025-07-18 18:49:50 [DubboServerHandler-*************:20882-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfoByOpenid],SpendTime=[187ms]
2025-07-18 18:49:50 [DubboServerHandler-*************:20882-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-18 18:49:50 [DubboServerHandler-*************:20882-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-18 18:49:50 [DubboServerHandler-*************:20882-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-18 18:49:50 [DubboServerHandler-*************:20882-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-18 18:49:50 [DubboServerHandler-*************:20882-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-18 18:49:50 [DubboServerHandler-*************:20882-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[82ms]
2025-07-18 18:49:51 [XNIO-1 task-2] INFO  c.y.s.c.UserplusInfoController - 获取实名认证状态
2025-07-18 18:49:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 18:49:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[218ms]
2025-07-18 18:49:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:49:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:49:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 18:49:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-07-18 18:49:52 [XNIO-1 task-4] INFO  c.y.s.c.system.SysNoticeController - 获取当前用户通知公告列表PageQuery(pageSize=2, pageNum=1, orderByColumn=null, isAsc=null)
2025-07-18 18:49:52 [XNIO-1 task-4] INFO  c.y.s.s.impl.SysNoticeServiceImpl - 部门ID: 1934262161186328578
2025-07-18 18:49:52 [XNIO-1 task-4] INFO  c.y.s.s.impl.SysNoticeServiceImpl - 用户部门: SysDept(deptId=1934262161186328578, parentId=100, deptName=青岛孚润德船舶管理有限公司, deptCategory=null, orderNum=3, leader=1934259258107203585, phone=null, email=null, status=0, delFlag=0, ancestors=0,100)
2025-07-18 18:49:52 [XNIO-1 task-4] INFO  c.y.s.s.impl.SysNoticeServiceImpl - 用户部门祖先列表3: [0, 100, 1934262161186328578]
2025-07-18 18:49:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 18:49:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[4ms]
2025-07-18 18:49:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:49:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:49:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-18 18:49:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-18 18:50:07 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 18:50:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[542ms]
2025-07-18 18:50:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 18:50:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 18:50:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 18:50:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-18 18:50:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 18:50:11 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[368ms]
2025-07-18 18:50:11 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 18:50:11 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 18:50:11 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 18:50:11 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-18 18:50:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 18:50:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 18:50:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 18:50:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 18:50:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:50:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:50:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-18 18:50:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-18 18:56:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:56:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 18:56:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-18 18:56:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[32ms]
2025-07-18 19:03:39 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 19:03:39 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 19:03:39 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-18 19:03:39 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[27ms]
2025-07-18 19:04:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:04:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:04:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 19:04:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-07-18 19:05:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 19:05:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 19:05:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-18 19:05:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-18 19:06:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 19:06:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 19:06:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-18 19:06:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[29ms]
2025-07-18 19:07:34 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xac5872f8, L:/*************:8322 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2173493f, L:/*************:8311 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2173493f, L:/*************:8311 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xac5872f8, L:/*************:8322 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xac5872f8, L:/*************:8322 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2173493f, L:/*************:8311 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xac5872f8, L:/*************:8322 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2173493f, L:/*************:8311 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac5872f8, L:/*************:8322 ! R:/*************:8091]) will closed
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2173493f, L:/*************:8311 ! R:/*************:8091]) will closed
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2173493f, L:/*************:8311 ! R:/*************:8091]) will closed
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac5872f8, L:/*************:8322 ! R:/*************:8091]) will closed
2025-07-18 19:07:38 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:38 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752836858221
timestamp=1752836858221
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:07:38 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:38 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:07:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:07:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6108546f, L:null ! R:/*************:8091]) will closed
2025-07-18 19:07:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x59ab8146, L:null ! R:/*************:8091]) will closed
2025-07-18 19:07:48 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752836868219
timestamp=1752836868219
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:07:48 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x8a3cbaf8, L:/*************:2715 - R:/*************:8091]
2025-07-18 19:07:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x8a3cbaf8, L:/*************:2715 - R:/*************:8091]
2025-07-18 19:07:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:07:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:07:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x54cf64aa, L:/*************:2716 - R:/*************:8091]
2025-07-18 19:07:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0x54cf64aa, L:/*************:2716 - R:/*************:8091]
2025-07-18 19:10:45 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-18 19:12:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 19:12:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[495ms]
2025-07-18 19:12:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:12:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:12:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-18 19:12:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-07-18 19:12:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 19:12:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[432ms]
2025-07-18 19:12:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:12:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:12:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 19:12:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 19:12:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:12:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:12:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 19:12:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 19:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[239ms]
2025-07-18 19:20:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:20:41 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[47ms]
2025-07-18 19:22:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:22:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[70ms]
2025-07-18 19:23:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:23:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[64ms]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[49ms]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[25ms]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[19ms]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [DubboServerHandler-*************:20882-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[18ms]
2025-07-18 19:24:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 19:24:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 19:24:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-18 19:24:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [DubboServerHandler-*************:20882-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-18 19:25:01 [DubboServerHandler-*************:20882-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:25:01 [DubboServerHandler-*************:20882-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[112ms]
2025-07-18 19:25:01 [DubboServerHandler-*************:20882-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:25:01 [DubboServerHandler-*************:20882-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[21ms]
2025-07-18 19:25:01 [DubboServerHandler-*************:20882-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:25:01 [DubboServerHandler-*************:20882-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[24ms]
2025-07-18 19:25:01 [DubboServerHandler-*************:20882-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:25:01 [DubboServerHandler-*************:20882-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-18 19:25:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:25:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[66ms]
2025-07-18 19:25:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:25:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-18 19:25:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:25:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[26ms]
2025-07-18 19:26:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:26:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[65ms]
2025-07-18 19:26:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:26:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[30ms]
2025-07-18 19:30:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[77ms]
2025-07-18 19:30:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[3ms]
2025-07-18 19:30:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[4ms]
2025-07-18 19:30:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[4ms]
2025-07-18 19:30:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[28ms]
2025-07-18 19:30:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[5ms]
2025-07-18 19:35:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:35:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[66ms]
2025-07-18 19:35:33 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-18 19:35:33 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-18 19:35:35 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 19:35:36 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 19:35:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x8a3cbaf8, L:/*************:2715 ! R:/*************:8091]
2025-07-18 19:35:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x8a3cbaf8, L:/*************:2715 ! R:/*************:8091]
2025-07-18 19:35:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x8a3cbaf8, L:/*************:2715 ! R:/*************:8091]
2025-07-18 19:35:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x8a3cbaf8, L:/*************:2715 ! R:/*************:8091]
2025-07-18 19:35:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8a3cbaf8, L:/*************:2715 ! R:/*************:8091]) will closed
2025-07-18 19:35:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8a3cbaf8, L:/*************:2715 ! R:/*************:8091]) will closed
2025-07-18 19:35:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x54cf64aa, L:/*************:2716 ! R:/*************:8091]
2025-07-18 19:35:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x54cf64aa, L:/*************:2716 ! R:/*************:8091]
2025-07-18 19:35:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x54cf64aa, L:/*************:2716 ! R:/*************:8091]
2025-07-18 19:35:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x54cf64aa, L:/*************:2716 ! R:/*************:8091]
2025-07-18 19:35:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x54cf64aa, L:/*************:2716 ! R:/*************:8091]) will closed
2025-07-18 19:35:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x54cf64aa, L:/*************:2716 ! R:/*************:8091]) will closed
2025-07-18 19:35:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 19:35:46 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 24032 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 19:35:46 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 19:35:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 19:35:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 19:35:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 19:35:51 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 19:35:51 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 19:35:51 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 19:35:51 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 19:35:51 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 19:35:51 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 19:35:51 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:35:51 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752838551826
timestamp=1752838551826
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:35:52 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xecd96208, L:/*************:7460 - R:/*************:8091]
2025-07-18 19:35:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 97 ms, version:1.7.1,role:TMROLE,channel:[id: 0xecd96208, L:/*************:7460 - R:/*************:8091]
2025-07-18 19:35:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 19:35:52 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 19:35:52 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 19:35:52 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 19:35:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 19:35:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 19:35:53 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 19:35:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 19:35:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 19:35:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 19:35:55 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@302e3484
2025-07-18 19:35:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 19:35:55 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:35:55 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:35:55 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:35:55 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xb7ddb353, L:/*************:7474 - R:/*************:8091]
2025-07-18 19:35:55 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0xb7ddb353, L:/*************:7474 - R:/*************:8091]
2025-07-18 19:35:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 19:35:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 19:35:57 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 19:35:58 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 19:35:58 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 19:35:58 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 19:35:58 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 19:35:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 19:36:00 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 19:36:03 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 19:36:03 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 19:36:03 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 19:36:03 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 19:36:03 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-18 19:36:07 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 23.492 seconds (process running for 24.902)
2025-07-18 19:36:07 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-18 19:36:07 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 19:36:07 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 19:36:07 [RMI TCP Connection(5)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 19:36:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:36:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:36:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[7ms]
2025-07-18 19:36:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[79ms]
2025-07-18 19:38:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:38:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:38:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 19:38:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-18 19:38:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:38:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[107ms]
2025-07-18 19:38:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:38:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 19:38:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 19:38:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-18 19:49:12 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-18 19:49:12 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xecd96208, L:/*************:7460 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xb7ddb353, L:/*************:7474 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xb7ddb353, L:/*************:7474 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xecd96208, L:/*************:7460 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xecd96208, L:/*************:7460 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xb7ddb353, L:/*************:7474 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xecd96208, L:/*************:7460 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb7ddb353, L:/*************:7474 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xecd96208, L:/*************:7460 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb7ddb353, L:/*************:7474 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb7ddb353, L:/*************:7474 ! R:/*************:8091]) will closed
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xecd96208, L:/*************:7460 ! R:/*************:8091]) will closed
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb7ddb353, L:/*************:7474 ! R:/*************:8091]) will closed
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xecd96208, L:/*************:7460 ! R:/*************:8091]) will closed
2025-07-18 19:49:21 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:21 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839361560
timestamp=1752839361560
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:49:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:49:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb7ddb353, L:/*************:7474 ! R:/*************:8091]
2025-07-18 19:49:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb7ddb353, L:/*************:7474 ! R:/*************:8091]
2025-07-18 19:49:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:49:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x847795ce, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88f1e37b, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:31 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:31 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839371559
timestamp=1752839371559
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:49:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:49:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:49:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdb7f79ff, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7d5ecc20, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:41 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:41 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839381559
timestamp=1752839381559
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:49:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:49:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:49:43 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x552d0fb7, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7f51881a, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:51 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:51 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839391571
timestamp=1752839391571
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:49:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:49:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:49:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbf349a19, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x77c37e61, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:01 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:01 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839401564
timestamp=1752839401564
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:50:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:50:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:50:03 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeb593291, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x64e67e31, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:11 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:11 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839411556
timestamp=1752839411556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:50:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:50:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:50:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x36ceac2f, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd621429c, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:21 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:21 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839421556
timestamp=1752839421556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:50:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:50:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:50:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3b12b5c5, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa31d8c63, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:31 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:31 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839431555
timestamp=1752839431555
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:50:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:50:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:50:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb0b6b6df, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x11a8c122, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:41 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:41 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839441554
timestamp=1752839441554
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:50:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:50:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:50:43 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9a23a515, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 19:50:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcad967bc, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 19:50:44 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 19:57:16 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 19:57:16 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 24788 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 19:57:16 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-18 19:57:16 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 19:57:16 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-18 19:57:16 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 19:57:23 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 19:57:23 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 19:57:23 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 19:57:23 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 19:57:23 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 19:57:23 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 19:57:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 19:57:24 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 19:57:24 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 19:57:24 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 19:57:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-18 19:57:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 19:57:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 19:57:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 19:57:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 19:57:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 19:57:28 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5111de33
2025-07-18 19:57:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 19:57:28 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:57:28 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:57:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 19:57:28 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x22d8d633, L:/*************:11681 - R:/*************:8091]
2025-07-18 19:57:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 57 ms, version:1.7.1,role:RMROLE,channel:[id: 0x22d8d633, L:/*************:11681 - R:/*************:8091]
2025-07-18 19:57:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 19:57:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 19:57:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 19:57:31 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 19:57:31 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 19:57:31 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 19:57:31 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 19:57:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 19:57:33 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 19:57:36 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 19:57:36 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 19:57:36 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 19:57:36 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 19:57:36 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-18 19:57:40 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 27.141 seconds (process running for 28.302)
2025-07-18 19:57:40 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-18 19:57:40 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 19:57:40 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 19:57:41 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 19:58:23 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:58:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839904007
timestamp=1752839904007
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 19:58:24 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xb572c4b5, L:/*************:11844 - R:/*************:8091]
2025-07-18 19:58:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:TMROLE,channel:[id: 0xb572c4b5, L:/*************:11844 - R:/*************:8091]
2025-07-18 20:00:27 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-18 20:02:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 20:02:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-18 20:02:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[104ms]
2025-07-18 20:02:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[139ms]
2025-07-18 20:06:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 20:06:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[2387ms]
2025-07-18 20:06:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 20:06:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 20:06:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[8ms]
2025-07-18 20:06:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[27ms]
2025-07-18 20:06:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 20:06:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[274ms]
2025-07-18 20:06:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 20:06:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 20:06:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-18 20:06:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-18 20:16:41 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-18 20:16:41 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xb572c4b5, L:/*************:11844 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x22d8d633, L:/*************:11681 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xb572c4b5, L:/*************:11844 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x22d8d633, L:/*************:11681 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x22d8d633, L:/*************:11681 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xb572c4b5, L:/*************:11844 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x22d8d633, L:/*************:11681 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb572c4b5, L:/*************:11844 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x22d8d633, L:/*************:11681 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb572c4b5, L:/*************:11844 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x22d8d633, L:/*************:11681 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb572c4b5, L:/*************:11844 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x22d8d633, L:/*************:11681 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb572c4b5, L:/*************:11844 ! R:/*************:8091]) will closed
2025-07-18 20:16:43 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:43 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752841003933
timestamp=1752841003933
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 20:16:44 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:44 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:16:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x22d8d633, L:/*************:11681 ! R:/*************:8091]
2025-07-18 20:16:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x22d8d633, L:/*************:11681 ! R:/*************:8091]
2025-07-18 20:16:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 20:16:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9054db67, L:null ! R:/*************:8091]) will closed
2025-07-18 20:16:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x948ab3e2, L:null ! R:/*************:8091]) will closed
2025-07-18 20:16:53 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:53 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752841013941
timestamp=1752841013941
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 20:16:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:54 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:16:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 20:16:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x736bb7f9, L:null ! R:/*************:8091]) will closed
2025-07-18 20:16:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeb3e8042, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:03 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:03 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752841023927
timestamp=1752841023927
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 20:17:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:04 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 20:17:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9dc793ea, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0189b672, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:13 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:13 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752841033929
timestamp=1752841033929
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 20:17:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:14 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 20:17:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcf9b4f68, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8cd54884, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:23 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:23 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752841043927
timestamp=1752841043927
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 20:17:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:24 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 20:17:25 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x129fef71, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x749345dc, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:33 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:33 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752841053927
timestamp=1752841053927
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 20:17:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:34 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 20:17:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaedb7369, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8addb755, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:43 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:43 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752841063933
timestamp=1752841063933
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 20:17:44 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:44 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 20:17:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8335eb77, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x63212d16, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:53 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:53 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752841073926
timestamp=1752841073926
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 20:17:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:54 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 20:17:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x030814b1, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x60b04fb4, L:null ! R:/*************:8091]) will closed
2025-07-18 20:18:03 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:18:03 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752841083925
timestamp=1752841083925
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-18 20:18:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:18:04 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:18:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-18 20:18:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2057abca, L:null ! R:/*************:8091]) will closed
2025-07-18 20:18:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x22c6564f, L:null ! R:/*************:8091]) will closed
2025-07-18 20:18:12 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 20:18:12 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 20:18:12 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 20:18:12 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 20:18:13 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 20:18:13 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-18 20:18:13 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 20:18:13 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 20:18:13 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 20:18:13 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 20:18:13 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
