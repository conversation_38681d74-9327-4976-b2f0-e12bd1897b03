2025-07-26 08:50:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 08:50:17 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 28328 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 08:50:17 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 08:50:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 08:50:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-26 08:50:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 08:50:23 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-26 08:50:24 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-26 08:50:24 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-26 08:50:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-26 08:50:24 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-26 08:50:24 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-26 08:50:24 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 08:50:24 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753491024509
timestamp=1753491024509
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 08:50:24 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x98850599, L:/*************:3288 - R:/*************:8091]
2025-07-26 08:50:24 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 67 ms, version:1.7.1,role:TMROLE,channel:[id: 0x98850599, L:/*************:3288 - R:/*************:8091]
2025-07-26 08:50:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-26 08:50:24 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-26 08:50:24 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-26 08:50:24 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-26 08:50:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-26 08:50:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-26 08:50:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 08:50:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 08:50:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-26 08:50:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 08:50:27 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3676e04a
2025-07-26 08:50:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 08:50:27 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 08:50:27 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 08:50:27 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 08:50:27 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa6e21624, L:/*************:3316 - R:/*************:8091]
2025-07-26 08:50:27 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0xa6e21624, L:/*************:3316 - R:/*************:8091]
2025-07-26 08:50:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 08:50:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-26 08:50:29 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-26 08:50:29 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 08:50:29 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 08:50:30 [redisson-netty-2-2] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 08:50:30 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 08:50:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-26 08:50:31 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 08:50:34 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 08:50:34 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 08:50:34 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 08:50:34 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 08:50:34 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-26 08:50:38 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 23.383 seconds (process running for 24.639)
2025-07-26 08:50:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-26 08:50:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 08:50:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 08:50:38 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 08:51:52 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-26 08:59:08 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 08:59:08 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[88ms]
2025-07-26 08:59:08 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-26 08:59:08 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[231ms]
2025-07-26 08:59:09 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 08:59:09 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-26 08:59:09 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-26 08:59:09 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-26 08:59:09 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-26 08:59:09 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[79ms]
2025-07-26 08:59:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 08:59:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 08:59:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 08:59:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-07-26 08:59:55 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 08:59:55 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 09:02:15 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:02:15 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[43ms]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[161ms]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-26 09:39:17 [DubboServerHandler-*************:20881-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[63ms]
2025-07-26 09:39:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 09:39:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 09:39:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 09:39:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 09:39:36 [DubboServerHandler-*************:20881-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:39:36 [DubboServerHandler-*************:20881-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 09:39:57 [DubboServerHandler-*************:20881-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:39:57 [DubboServerHandler-*************:20881-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 09:40:08 [DubboServerHandler-*************:20881-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:40:08 [DubboServerHandler-*************:20881-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 09:40:48 [DubboServerHandler-*************:20881-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:40:48 [DubboServerHandler-*************:20881-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:02:04 [DubboServerHandler-*************:20881-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:02:04 [DubboServerHandler-*************:20881-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 10:11:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 10:11:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 10:11:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-26 10:11:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[27ms]
2025-07-26 10:26:21 [DubboServerHandler-*************:20881-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:26:21 [DubboServerHandler-*************:20881-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-26 10:26:31 [DubboServerHandler-*************:20881-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:26:31 [DubboServerHandler-*************:20881-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:26:57 [DubboServerHandler-*************:20881-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:26:57 [DubboServerHandler-*************:20881-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 10:48:34 [DubboServerHandler-*************:20881-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:48:34 [DubboServerHandler-*************:20881-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 10:49:16 [DubboServerHandler-*************:20881-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:49:16 [DubboServerHandler-*************:20881-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:51:15 [DubboServerHandler-*************:20881-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:51:15 [DubboServerHandler-*************:20881-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:52:17 [DubboServerHandler-*************:20881-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:52:17 [DubboServerHandler-*************:20881-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:52:56 [DubboServerHandler-*************:20881-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:52:56 [DubboServerHandler-*************:20881-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:54:26 [DubboServerHandler-*************:20881-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:54:26 [DubboServerHandler-*************:20881-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:54:31 [DubboServerHandler-*************:20881-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:54:31 [DubboServerHandler-*************:20881-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:54:34 [DubboServerHandler-*************:20881-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:54:34 [DubboServerHandler-*************:20881-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:54:53 [DubboServerHandler-*************:20881-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:54:53 [DubboServerHandler-*************:20881-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:55:07 [DubboServerHandler-*************:20881-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:55:07 [DubboServerHandler-*************:20881-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:58:19 [DubboServerHandler-*************:20881-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:58:19 [DubboServerHandler-*************:20881-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:59:05 [DubboServerHandler-*************:20881-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:59:05 [DubboServerHandler-*************:20881-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 10:59:31 [DubboServerHandler-*************:20881-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:59:31 [DubboServerHandler-*************:20881-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 11:00:00 [DubboServerHandler-*************:20881-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:00:00 [DubboServerHandler-*************:20881-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 11:00:39 [DubboServerHandler-*************:20881-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:00:39 [DubboServerHandler-*************:20881-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 11:00:55 [DubboServerHandler-*************:20881-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:00:55 [DubboServerHandler-*************:20881-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 11:34:13 [DubboServerHandler-*************:20881-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:34:13 [DubboServerHandler-*************:20881-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-26 11:34:25 [DubboServerHandler-*************:20881-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:34:25 [DubboServerHandler-*************:20881-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 11:44:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:44:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:44:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[28ms]
2025-07-26 11:44:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[31ms]
2025-07-26 11:49:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:49:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:49:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[31ms]
2025-07-26 11:49:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[31ms]
2025-07-26 11:49:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:49:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:49:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 11:49:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 11:49:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:49:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:49:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 11:49:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 11:49:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:49:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:49:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 11:49:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 11:50:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:50:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 11:50:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-26 11:50:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[20ms]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[152ms]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-26 13:33:20 [DubboServerHandler-*************:20881-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[62ms]
2025-07-26 13:33:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:33:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:33:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 13:33:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 13:36:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:36:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:36:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:36:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[57ms]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:36:32 [DubboServerHandler-*************:20881-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:38:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-70] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-70] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[61ms]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:24 [DubboServerHandler-*************:20881-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-26 13:38:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:38:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:38:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 13:38:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[52ms]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:38:52 [DubboServerHandler-*************:20881-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[50ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:39:07 [DubboServerHandler-*************:20881-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:39:08 [DubboServerHandler-*************:20881-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-26 13:39:08 [DubboServerHandler-*************:20881-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[102ms]
2025-07-26 13:39:12 [DubboServerHandler-*************:20881-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 13:39:12 [DubboServerHandler-*************:20881-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-26 13:41:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:41:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:41:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-07-26 13:41:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[12ms]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[36ms]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-107] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:41:53 [DubboServerHandler-*************:20881-thread-107] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:42:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:42:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:42:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:42:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-108] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-108] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-111] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-111] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-112] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-112] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:18 [DubboServerHandler-*************:20881-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:42:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:42:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:42:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 13:42:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-07-26 13:42:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:42:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:42:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:42:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-07-26 13:42:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:42:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:42:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:42:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[49ms]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:42:58 [DubboServerHandler-*************:20881-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[36ms]
2025-07-26 13:44:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:44:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:44:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 13:44:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-124] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-124] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[49ms]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-125] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-125] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-126] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-126] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-127] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-127] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-128] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-128] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-129] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-129] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-130] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-130] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-131] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:44:26 [DubboServerHandler-*************:20881-thread-131] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:48:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:48:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:48:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:48:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-07-26 13:48:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:48:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:48:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 13:48:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-132] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-132] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-133] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-133] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-134] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-134] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-135] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-135] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-136] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-136] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-137] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-137] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-138] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-138] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-139] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:27 [DubboServerHandler-*************:20881-thread-139] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-140] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-140] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-141] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-141] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-142] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-142] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-143] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-143] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-144] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-144] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-145] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-145] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-146] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-146] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-147] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:32 [DubboServerHandler-*************:20881-thread-147] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-148] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-148] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-149] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-149] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-150] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-150] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-151] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-151] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-152] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-152] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-153] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-153] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-154] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-154] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-155] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:48:45 [DubboServerHandler-*************:20881-thread-155] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:51:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:51:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:51:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 13:51:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-156] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-156] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-157] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-157] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-158] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-158] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-159] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-159] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-160] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-160] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-161] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-161] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-162] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-162] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-163] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:51:55 [DubboServerHandler-*************:20881-thread-163] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-164] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-164] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-165] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-165] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-166] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-166] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-167] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-167] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-168] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-168] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-169] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-169] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-170] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-170] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-171] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:15 [DubboServerHandler-*************:20881-thread-171] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-26 13:52:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:52:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 13:52:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:52:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-172] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-172] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-173] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-173] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-174] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-174] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-175] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-175] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-176] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-176] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-177] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-177] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-178] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-178] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-179] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:52:22 [DubboServerHandler-*************:20881-thread-179] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-180] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-180] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-181] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-181] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-182] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-182] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-183] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-183] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-184] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-184] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-185] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-185] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-186] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-186] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-187] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:56:52 [DubboServerHandler-*************:20881-thread-187] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:57:04 [DubboServerHandler-*************:20881-thread-188] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:57:04 [DubboServerHandler-*************:20881-thread-188] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-26 13:57:04 [DubboServerHandler-*************:20881-thread-189] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:57:04 [DubboServerHandler-*************:20881-thread-189] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:57:04 [DubboServerHandler-*************:20881-thread-190] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:57:04 [DubboServerHandler-*************:20881-thread-190] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 13:57:04 [DubboServerHandler-*************:20881-thread-191] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:57:05 [DubboServerHandler-*************:20881-thread-191] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:57:05 [DubboServerHandler-*************:20881-thread-192] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:57:05 [DubboServerHandler-*************:20881-thread-192] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 13:57:05 [DubboServerHandler-*************:20881-thread-193] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:57:05 [DubboServerHandler-*************:20881-thread-193] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:57:05 [DubboServerHandler-*************:20881-thread-194] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:57:05 [DubboServerHandler-*************:20881-thread-194] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:57:05 [DubboServerHandler-*************:20881-thread-195] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:57:05 [DubboServerHandler-*************:20881-thread-195] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-196] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-196] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-197] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-197] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-198] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-198] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:58:24 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 13:59:45 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:00:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:00:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:00:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 14:00:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[6ms]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:00:16 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:02:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 14:02:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 14:03:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:03:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:03:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:03:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[36ms]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 14:03:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:03:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:03:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:03:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:03:56 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:04:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:04:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:04:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:04:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:04:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:04:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:04:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:04:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:04:31 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:33 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:34 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:05:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:05:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:05:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:05:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:05:58 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:58 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 14:05:58 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:58 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:05:58 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:58 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:05:58 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:05:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:05:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:05:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:05:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:05:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:06:05 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:06:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:06:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:06:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:06:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:06:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-26 14:06:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:06:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:06:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:06:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:06:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:08:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:08:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:08:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:08:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:08:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:08:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:08:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:08:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:08:48 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:10:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:10:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:10:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:10:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:10:27 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 14:11:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:11:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:11:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:11:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:37 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:11:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:11:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:11:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:11:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:11:46 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:11:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:11:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:11:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:11:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:12:00 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:14:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:14:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:14:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:14:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:14:59 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:15:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:15:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:15:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:15:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:15:39 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 14:16:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:16:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:16:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:16:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 14:16:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 14:16:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:16:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:16:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:16:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:16:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:16:57 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:17:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:17:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:17:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:17:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:06 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-26 14:17:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[54ms]
2025-07-26 14:17:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:36 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 14:17:38 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:38 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[54ms]
2025-07-26 14:17:38 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:17:38 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 14:18:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:18:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:18:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:18:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:18:02 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:22:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:22:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:22:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:22:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[6ms]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[56ms]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:22:17 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:30:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:30:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:30:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:30:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:29 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:30:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:30:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:30:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:30:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:30:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[294ms]
2025-07-26 14:30:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:30:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[243ms]
2025-07-26 14:30:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:30:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:30:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:30:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:30:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:30:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:36:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:36:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:36:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:36:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 14:36:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:36:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:36:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:36:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:36:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:36:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:36:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:36:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:36:47 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 14:38:12 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:38:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[52ms]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:40:41 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 14:40:42 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:40:42 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 14:40:42 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:40:42 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:42:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:42:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 14:42:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 14:42:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 14:42:19 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:36:49 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 15:36:49 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[23ms]
2025-07-26 15:36:49 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-26 15:36:50 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[135ms]
2025-07-26 15:36:50 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 15:36:50 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-26 15:36:50 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-26 15:36:50 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-26 15:36:50 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-26 15:36:50 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[64ms]
2025-07-26 15:36:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:36:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:36:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 15:36:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:36:53 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:39:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:39:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:39:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 15:39:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 15:39:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:39:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:39:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 15:39:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:39:40 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:41:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:41:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:41:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 15:41:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:41:26 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:43:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:43:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:43:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 15:43:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:43:25 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:44:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:44:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 15:44:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 15:44:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 15:44:13 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 16:17:03 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 16:17:03 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[30ms]
2025-07-26 16:17:03 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-26 16:17:03 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-26 16:17:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 16:17:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-26 16:17:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-26 16:17:11 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[84ms]
2025-07-26 16:17:12 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 16:17:12 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-26 16:17:12 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-26 16:17:12 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-26 16:17:12 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-26 16:17:12 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[77ms]
2025-07-26 16:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 16:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 16:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 16:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[23ms]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[122ms]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-26 16:57:32 [DubboServerHandler-*************:20881-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[66ms]
2025-07-26 16:57:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 16:57:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 16:57:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 16:57:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-26 17:18:50 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-26 17:18:50 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-26 17:18:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa6e21624, L:/*************:3316 - R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x98850599, L:/*************:3288 - R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xa6e21624, L:/*************:3316 - R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x98850599, L:/*************:3288 - R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x98850599, L:/*************:3288 ! R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x98850599, L:/*************:3288 ! R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x98850599, L:/*************:3288 ! R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa6e21624, L:/*************:3316 ! R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa6e21624, L:/*************:3316 ! R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa6e21624, L:/*************:3316 ! R:/*************:8091]
2025-07-26 17:18:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa6e21624, L:/*************:3316 ! R:/*************:8091]) will closed
2025-07-26 17:18:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x98850599, L:/*************:3288 ! R:/*************:8091]) will closed
2025-07-26 17:18:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa6e21624, L:/*************:3316 ! R:/*************:8091]) will closed
2025-07-26 17:18:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x98850599, L:/*************:3288 ! R:/*************:8091]) will closed
2025-07-26 17:18:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 17:18:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753521534401
timestamp=1753521534401
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 17:18:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 17:18:54 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 17:18:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa6e21624, L:/*************:3316 ! R:/*************:8091]
2025-07-26 17:18:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa6e21624, L:/*************:3316 ! R:/*************:8091]
2025-07-26 17:18:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 17:18:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8acebb6e, L:null ! R:/*************:8091]) will closed
2025-07-26 17:18:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0cb829de, L:null ! R:/*************:8091]) will closed
2025-07-26 17:19:00 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 17:19:00 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 17:19:00 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 17:19:00 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 17:19:00 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 17:19:00 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-26 17:19:00 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 17:19:00 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 17:19:01 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 17:19:01 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 17:19:01 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-26 20:08:10 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 20:08:10 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 14672 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 20:08:10 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-26 20:08:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 20:08:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-26 20:08:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 20:08:19 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-26 20:08:19 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-26 20:08:19 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-26 20:08:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-26 20:08:19 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-26 20:08:19 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-26 20:08:19 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 20:08:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753531700030
timestamp=1753531700030
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 20:08:20 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x01db9b63, L:/*************:13141 - R:/*************:8091]
2025-07-26 20:08:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 82 ms, version:1.7.1,role:TMROLE,channel:[id: 0x01db9b63, L:/*************:13141 - R:/*************:8091]
2025-07-26 20:08:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-26 20:08:20 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-26 20:08:20 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-26 20:08:20 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-26 20:08:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-26 20:08:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-26 20:08:22 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 20:08:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 20:08:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-26 20:08:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 20:08:24 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@167080e
2025-07-26 20:08:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 20:08:25 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 20:08:25 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 20:08:25 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 20:08:25 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xfa1f8cbd, L:/*************:13158 - R:/*************:8091]
2025-07-26 20:08:25 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 13 ms, version:1.7.1,role:RMROLE,channel:[id: 0xfa1f8cbd, L:/*************:13158 - R:/*************:8091]
2025-07-26 20:08:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 20:08:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-26 20:08:28 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-26 20:08:29 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 20:08:29 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 20:08:29 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 20:08:30 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 20:08:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-26 20:08:31 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 20:08:35 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 20:08:35 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 20:08:35 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 20:08:35 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 20:08:36 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-26 20:08:40 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 32.419 seconds (process running for 33.805)
2025-07-26 20:08:40 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-26 20:08:40 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 20:08:40 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 20:08:40 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 20:09:04 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 20:09:04 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[120ms]
2025-07-26 20:09:04 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-26 20:09:04 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[299ms]
2025-07-26 20:09:05 [DubboServerHandler-*************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-26 20:09:05 [DubboServerHandler-*************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-26 20:09:05 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-26 20:09:05 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-26 20:09:05 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-26 20:09:05 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[70ms]
2025-07-26 20:09:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 20:09:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 20:09:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 20:09:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[6ms]
2025-07-26 20:10:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 20:10:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 20:10:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 20:10:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:10:08 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 20:11:11 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-26 20:13:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 20:13:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 20:13:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-26 20:13:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:13:45 [DubboServerHandler-*************:20880-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:01 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-49] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:10 [DubboServerHandler-*************:20880-thread-49] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:11 [DubboServerHandler-*************:20880-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:14:22 [DubboServerHandler-*************:20880-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:22 [DubboServerHandler-*************:20880-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-26 20:14:22 [DubboServerHandler-*************:20880-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:14:22 [DubboServerHandler-*************:20880-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:19 [DubboServerHandler-*************:20880-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[101ms]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[46ms]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-70] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-70] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:20 [DubboServerHandler-*************:20880-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportCompanyType]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportCompanyType],SpendTime=[23ms]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[167ms]
2025-07-26 20:18:26 [DubboServerHandler-*************:20880-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-26 20:18:27 [DubboServerHandler-*************:20880-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[98ms]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:18:35 [DubboServerHandler-*************:20880-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[59ms]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:06 [DubboServerHandler-*************:20880-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:09 [DubboServerHandler-*************:20880-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-107] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-108] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[89ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-108] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-107] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[80ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-111] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-112] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-111] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[24ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-112] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[73ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[26ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:14 [DubboServerHandler-*************:20880-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-124] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-124] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-125] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-125] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-126] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-126] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-127] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-127] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-128] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-128] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-129] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:15 [DubboServerHandler-*************:20880-thread-129] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-130] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-130] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-131] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-131] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-132] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-132] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-133] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-133] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-134] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-134] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-135] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:18 [DubboServerHandler-*************:20880-thread-135] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:21 [DubboServerHandler-*************:20880-thread-136] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:21 [DubboServerHandler-*************:20880-thread-136] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-26 20:19:21 [DubboServerHandler-*************:20880-thread-137] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:21 [DubboServerHandler-*************:20880-thread-137] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-26 20:19:21 [DubboServerHandler-*************:20880-thread-138] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:21 [DubboServerHandler-*************:20880-thread-138] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-26 20:19:21 [DubboServerHandler-*************:20880-thread-139] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:22 [DubboServerHandler-*************:20880-thread-139] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 20:19:22 [DubboServerHandler-*************:20880-thread-140] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:22 [DubboServerHandler-*************:20880-thread-140] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:22 [DubboServerHandler-*************:20880-thread-141] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:22 [DubboServerHandler-*************:20880-thread-141] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-142] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-142] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-143] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-143] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-144] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-144] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-145] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-145] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-146] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-146] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-147] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:19:29 [DubboServerHandler-*************:20880-thread-147] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-26 20:19:33 [DubboServerHandler-*************:20880-thread-148] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-26 20:19:33 [DubboServerHandler-*************:20880-thread-148] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[97ms]
2025-07-26 20:53:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 20:53:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-26 20:53:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-07-26 20:53:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[9ms]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-150] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-150] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[56ms]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-151] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-151] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-152] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-152] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-153] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-153] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-154] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-154] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-155] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-155] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-156] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-156] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-157] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:53:33 [DubboServerHandler-*************:20880-thread-157] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-158] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-158] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-159] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-159] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-160] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-160] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-161] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-161] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-162] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-162] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-163] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-163] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-164] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-164] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-165] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-165] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-166] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-166] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-167] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-167] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-168] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-168] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-169] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-169] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-170] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-170] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-171] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-171] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 20:56:41 [DubboServerHandler-*************:20880-thread-172] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-26 20:56:42 [DubboServerHandler-*************:20880-thread-172] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[116ms]
2025-07-26 20:56:47 [DubboServerHandler-*************:20880-thread-173] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 20:56:47 [DubboServerHandler-*************:20880-thread-173] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-174] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-174] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[52ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-175] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-175] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-176] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-176] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-177] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-177] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-178] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-178] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-179] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-179] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-180] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-180] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-181] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-181] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-182] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-182] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-183] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-183] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-184] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-184] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-185] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-185] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-186] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-186] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-187] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-187] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-188] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-26 21:01:14 [DubboServerHandler-*************:20880-thread-188] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[94ms]
2025-07-26 21:01:15 [DubboServerHandler-*************:20880-thread-189] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 21:01:15 [DubboServerHandler-*************:20880-thread-189] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-26 21:01:27 [DubboServerHandler-*************:20880-thread-190] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:27 [DubboServerHandler-*************:20880-thread-190] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-26 21:01:27 [DubboServerHandler-*************:20880-thread-191] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-191] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-192] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-192] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-193] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-193] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-194] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-194] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-195] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-195] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-196] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-196] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-197] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-197] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-198] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-198] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[49ms]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 21:01:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-26 21:13:06 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-26 21:13:06 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-26 21:13:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xfa1f8cbd, L:/*************:13158 - R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x01db9b63, L:/*************:13141 - R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xfa1f8cbd, L:/*************:13158 - R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x01db9b63, L:/*************:13141 - R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x01db9b63, L:/*************:13141 ! R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xfa1f8cbd, L:/*************:13158 ! R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x01db9b63, L:/*************:13141 ! R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfa1f8cbd, L:/*************:13158 ! R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfa1f8cbd, L:/*************:13158 ! R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x01db9b63, L:/*************:13141 ! R:/*************:8091]
2025-07-26 21:13:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x01db9b63, L:/*************:13141 ! R:/*************:8091]) will closed
2025-07-26 21:13:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfa1f8cbd, L:/*************:13158 ! R:/*************:8091]) will closed
2025-07-26 21:13:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfa1f8cbd, L:/*************:13158 ! R:/*************:8091]) will closed
2025-07-26 21:13:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x01db9b63, L:/*************:13141 ! R:/*************:8091]) will closed
2025-07-26 21:13:09 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:09 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753535589729
timestamp=1753535589729
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 21:13:10 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:10 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 21:13:10 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfa1f8cbd, L:/*************:13158 ! R:/*************:8091]
2025-07-26 21:13:10 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfa1f8cbd, L:/*************:13158 ! R:/*************:8091]
2025-07-26 21:13:10 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 21:13:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xba0948ad, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x33f8b0c9, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:19 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:19 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753535599728
timestamp=1753535599728
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 21:13:20 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:20 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 21:13:20 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 21:13:21 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x96b9c941, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x524899ba, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:29 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:29 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753535609729
timestamp=1753535609729
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 21:13:30 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:30 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 21:13:30 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 21:13:31 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2c8b859e, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7a59384, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:39 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:39 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753535619739
timestamp=1753535619739
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 21:13:40 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:40 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 21:13:40 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 21:13:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x944ccc17, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5142178d, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:49 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:49 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753535629741
timestamp=1753535629741
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 21:13:50 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:50 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 21:13:50 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 21:13:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x08ade641, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa6bbb2a1, L:null ! R:/*************:8091]) will closed
2025-07-26 21:13:59 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:13:59 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753535639729
timestamp=1753535639729
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 21:14:00 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:14:00 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 21:14:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 21:14:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x06efe347, L:null ! R:/*************:8091]) will closed
2025-07-26 21:14:02 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x15cd443d, L:null ! R:/*************:8091]) will closed
2025-07-26 21:14:09 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:14:09 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753535649743
timestamp=1753535649743
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 21:14:10 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:14:10 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 21:14:10 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 21:14:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x28fbd817, L:null ! R:/*************:8091]) will closed
2025-07-26 21:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x72ea4064, L:null ! R:/*************:8091]) will closed
2025-07-26 21:14:19 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:14:19 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753535659733
timestamp=1753535659733
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-26 21:14:20 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-26 21:14:20 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-26 21:14:20 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-26 21:14:21 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb7467128, L:null ! R:/*************:8091]) will closed
2025-07-26 21:14:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb8a6eecb, L:null ! R:/*************:8091]) will closed
2025-07-26 21:14:28 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 21:14:28 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 21:14:28 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 21:14:28 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 21:14:29 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 21:14:29 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-26 21:14:29 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 21:14:29 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 21:14:29 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 21:14:29 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 21:14:29 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
