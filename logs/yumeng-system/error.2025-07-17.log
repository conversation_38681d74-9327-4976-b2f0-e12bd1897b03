2025-07-17 10:46:35 [Thread-30] ERROR o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] Failed to subscribe DUBBO_GROUP:mapping:queues, cause: java.net.SocketException: Connection reset, dubbo version: 3.2.14, current host: *************, error code: 6-14. This may be caused by , go to https://dubbo.apache.org/faq/6/14 to find instructions. 
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:251)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:47)
	at redis.clients.jedis.Protocol.process(Protocol.java:135)
	at redis.clients.jedis.Protocol.read(Protocol.java:221)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:350)
	at redis.clients.jedis.Connection.getUnflushedObject(Connection.java:316)
	at redis.clients.jedis.JedisPubSubBase.process(JedisPubSubBase.java:115)
	at redis.clients.jedis.JedisPubSubBase.proceed(JedisPubSubBase.java:92)
	at redis.clients.jedis.Jedis.subscribe(Jedis.java:7941)
	at org.apache.dubbo.metadata.store.redis.RedisMetadataReport$MappingDataListener.run(RedisMetadataReport.java:499)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:328)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:966)
	at java.base/java.io.InputStream.read(InputStream.java:218)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:245)
	... 9 common frames omitted
2025-07-17 12:24:54 [redisson-timer-4-1] ERROR o.r.eviction.MapCacheEvictionTask - Unable to evict elements for 'global:sys_client'
org.redisson.client.RedisResponseTimeoutException: Redis server response timeout (3000 ms) occured after 0 retry attempts, is non-idempotent command: false Check connection with Redis node: 8.142.34.95/8.142.34.95:6379 for TCP packet drops or bandwidth limits.  Try to increase nettyThreads and/or timeout settings. command: (EVALSHA), params: [6cd746146db61c9edecea0bde01715df6324b158, 6, global:sys_client, redisson__timeout__set:{global:sys_client}, redisson__idle__set:{global:sys_client}, redisson_map_cache_expired:{global:sys_client}, redisson__map_cache__last_access__set:{global:sys_client}, redisson__execute_task_once_latch:{global:sys_client}, 1752726291046, 100, ...], channel: [id: 0x21dc91c9, L:/*************:4331 - R:8.142.34.95/8.142.34.95:6379]
	at org.redisson.command.RedisExecutor.lambda$scheduleResponseTimeout$10(RedisExecutor.java:450)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 12:24:54 [redisson-timer-4-1] ERROR o.r.eviction.MapCacheEvictionTask - Unable to evict elements for 'sys_dept'
org.redisson.client.RedisResponseTimeoutException: Redis server response timeout (3000 ms) occured after 0 retry attempts, is non-idempotent command: false Check connection with Redis node: 8.142.34.95/8.142.34.95:6379 for TCP packet drops or bandwidth limits.  Try to increase nettyThreads and/or timeout settings. command: (EVALSHA), params: [6cd746146db61c9edecea0bde01715df6324b158, 6, sys_dept, redisson__timeout__set:{sys_dept}, redisson__idle__set:{sys_dept}, redisson_map_cache_expired:{sys_dept}, redisson__map_cache__last_access__set:{sys_dept}, redisson__execute_task_once_latch:{sys_dept}, 1752726291099, 100, ...], channel: [id: 0xf1e4c19f, L:/*************:4330 - R:8.142.34.95/8.142.34.95:6379]
	at org.redisson.command.RedisExecutor.lambda$scheduleResponseTimeout$10(RedisExecutor.java:450)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 12:24:54 [redisson-timer-4-1] ERROR o.r.eviction.MapCacheEvictionTask - Unable to evict elements for 'sys_user_name'
org.redisson.client.RedisResponseTimeoutException: Redis server response timeout (3000 ms) occured after 0 retry attempts, is non-idempotent command: false Check connection with Redis node: 8.142.34.95/8.142.34.95:6379 for TCP packet drops or bandwidth limits.  Try to increase nettyThreads and/or timeout settings. command: (EVALSHA), params: [6cd746146db61c9edecea0bde01715df6324b158, 6, sys_user_name, redisson__timeout__set:{sys_user_name}, redisson__idle__set:{sys_user_name}, redisson_map_cache_expired:{sys_user_name}, redisson__map_cache__last_access__set:{sys_user_name}, redisson__execute_task_once_latch:{sys_user_name}, 1752726291100, 100, ...], channel: [id: 0xd57d7a05, L:/*************:4333 - R:8.142.34.95/8.142.34.95:6379]
	at org.redisson.command.RedisExecutor.lambda$scheduleResponseTimeout$10(RedisExecutor.java:450)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 13:28:36 [redisson-timer-4-1] ERROR o.r.eviction.MapCacheEvictionTask - Unable to evict elements for 'global:sys_client'
org.redisson.client.RedisResponseTimeoutException: Redis server response timeout (3000 ms) occured after 0 retry attempts, is non-idempotent command: false Check connection with Redis node: 8.142.34.95/8.142.34.95:6379 for TCP packet drops or bandwidth limits.  Try to increase nettyThreads and/or timeout settings. command: (EVALSHA), params: [6cd746146db61c9edecea0bde01715df6324b158, 6, global:sys_client, redisson__timeout__set:{global:sys_client}, redisson__idle__set:{global:sys_client}, redisson_map_cache_expired:{global:sys_client}, redisson__map_cache__last_access__set:{global:sys_client}, redisson__execute_task_once_latch:{global:sys_client}, 1752730112981, 100, ...], channel: [id: 0x9c4fc5fa, L:/*************:6949 - R:8.142.34.95/8.142.34.95:6379]
	at org.redisson.command.RedisExecutor.lambda$scheduleResponseTimeout$10(RedisExecutor.java:450)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 13:28:36 [redisson-timer-4-1] ERROR o.r.eviction.MapCacheEvictionTask - Unable to evict elements for 'sys_user_name'
org.redisson.client.RedisResponseTimeoutException: Redis server response timeout (3000 ms) occured after 0 retry attempts, is non-idempotent command: false Check connection with Redis node: 8.142.34.95/8.142.34.95:6379 for TCP packet drops or bandwidth limits.  Try to increase nettyThreads and/or timeout settings. command: (EVALSHA), params: [6cd746146db61c9edecea0bde01715df6324b158, 6, sys_user_name, redisson__timeout__set:{sys_user_name}, redisson__idle__set:{sys_user_name}, redisson_map_cache_expired:{sys_user_name}, redisson__map_cache__last_access__set:{sys_user_name}, redisson__execute_task_once_latch:{sys_user_name}, 1752730112981, 100, ...], channel: [id: 0x1177b391, L:/*************:7013 - R:8.142.34.95/8.142.34.95:6379]
	at org.redisson.command.RedisExecutor.lambda$scheduleResponseTimeout$10(RedisExecutor.java:450)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 13:28:36 [redisson-timer-4-1] ERROR o.r.eviction.MapCacheEvictionTask - Unable to evict elements for 'sys_dept'
org.redisson.client.RedisResponseTimeoutException: Redis server response timeout (3000 ms) occured after 0 retry attempts, is non-idempotent command: false Check connection with Redis node: 8.142.34.95/8.142.34.95:6379 for TCP packet drops or bandwidth limits.  Try to increase nettyThreads and/or timeout settings. command: (EVALSHA), params: [6cd746146db61c9edecea0bde01715df6324b158, 6, sys_dept, redisson__timeout__set:{sys_dept}, redisson__idle__set:{sys_dept}, redisson_map_cache_expired:{sys_dept}, redisson__map_cache__last_access__set:{sys_dept}, redisson__execute_task_once_latch:{sys_dept}, 1752730112981, 100, ...], channel: [id: 0x3b6dc691, L:/*************:6950 - R:8.142.34.95/8.142.34.95:6379]
	at org.redisson.command.RedisExecutor.lambda$scheduleResponseTimeout$10(RedisExecutor.java:450)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 14:10:14 [NettyClientSelector_TMROLE_1_1] ERROR i.s.c.r.n.AbstractNettyRemotingClient - 0318
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 14:10:14 [NettyClientSelector_RMROLE_1_1] ERROR i.s.c.r.n.AbstractNettyRemotingClient - 0318
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 16:31:55 [Thread-30] ERROR o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] Failed to subscribe DUBBO_GROUP:mapping:queues, cause: java.net.SocketException: Connection reset, dubbo version: 3.2.14, current host: *************, error code: 6-14. This may be caused by , go to https://dubbo.apache.org/faq/6/14 to find instructions. 
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:251)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:47)
	at redis.clients.jedis.Protocol.process(Protocol.java:135)
	at redis.clients.jedis.Protocol.read(Protocol.java:221)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:350)
	at redis.clients.jedis.Connection.getUnflushedObject(Connection.java:316)
	at redis.clients.jedis.JedisPubSubBase.process(JedisPubSubBase.java:115)
	at redis.clients.jedis.JedisPubSubBase.proceed(JedisPubSubBase.java:92)
	at redis.clients.jedis.Jedis.subscribe(Jedis.java:7941)
	at org.apache.dubbo.metadata.store.redis.RedisMetadataReport$MappingDataListener.run(RedisMetadataReport.java:499)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:328)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:355)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:808)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:966)
	at java.base/java.io.InputStream.read(InputStream.java:218)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:245)
	... 9 common frames omitted
2025-07-17 18:28:47 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x064f0dcf, L:/*************:2553 - R:8.142.34.95/8.142.34.95:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://8.142.34.95:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:28:47 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x7e7d8709, L:/*************:2555 - R:8.142.34.95/8.142.34.95:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://8.142.34.95:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:28:47 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x0822a7a8, L:/*************:2554 - R:8.142.34.95/8.142.34.95:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://8.142.34.95:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:28:47 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x50fdc548, L:/*************:2556 - R:8.142.34.95/8.142.34.95:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://8.142.34.95:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:28:47 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x06aadaff, L:/*************:2558 - R:8.142.34.95/8.142.34.95:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://8.142.34.95:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:28:47 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xf9a6bf2a, L:/*************:2559 - R:8.142.34.95/8.142.34.95:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://8.142.34.95:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:28:47 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x56a421d0, L:/*************:2563 - R:8.142.34.95/8.142.34.95:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://8.142.34.95:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:28:47 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x1ca1e266, L:/*************:2564 - R:8.142.34.95/8.142.34.95:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://8.142.34.95:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:28:47 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x506a00f5, L:/*************:2565 - R:8.142.34.95/8.142.34.95:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://8.142.34.95:6379]
	at org.redisson.client.RedisConnection.lambda$async$2(RedisConnection.java:295)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:713)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:701)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:788)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:501)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:30:45 [NettyClientSelector_RMROLE_1_1] ERROR i.s.c.r.n.AbstractNettyRemotingClient - 0318
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-17 18:30:45 [NettyClientSelector_TMROLE_1_1] ERROR i.s.c.r.n.AbstractNettyRemotingClient - 0318
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:356)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
