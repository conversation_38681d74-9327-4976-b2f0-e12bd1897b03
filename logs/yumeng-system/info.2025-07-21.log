2025-07-21 08:37:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-21 08:37:34 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 28580 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-21 08:37:34 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-21 08:37:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-21 08:37:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-21 08:37:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-21 08:37:41 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-21 08:37:41 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-21 08:37:41 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-21 08:37:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-21 08:37:41 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-21 08:37:41 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-21 08:37:42 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 08:37:42 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753058262291
timestamp=1753058262291
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 08:37:42 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe47936d3, L:/*************:3612 - R:/*************:8091]
2025-07-21 08:37:42 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 131 ms, version:1.7.1,role:TMROLE,channel:[id: 0xe47936d3, L:/*************:3612 - R:/*************:8091]
2025-07-21 08:37:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-21 08:37:42 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-21 08:37:42 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-21 08:37:42 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-21 08:37:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-21 08:37:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-21 08:37:44 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-21 08:37:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-21 08:37:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-21 08:37:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-21 08:37:47 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@46b3c1c2
2025-07-21 08:37:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-21 08:37:47 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 08:37:47 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 08:37:47 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 08:37:47 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x00f53012, L:/*************:3651 - R:/*************:8091]
2025-07-21 08:37:47 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x00f53012, L:/*************:3651 - R:/*************:8091]
2025-07-21 08:37:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-21 08:37:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-21 08:37:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-21 08:37:50 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-21 08:37:50 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-21 08:37:50 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-21 08:37:50 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-21 08:37:51 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-21 08:37:52 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-21 08:37:55 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-21 08:37:55 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-21 08:37:55 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-21 08:37:55 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-21 08:37:55 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-21 08:38:00 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 29.23 seconds (process running for 30.66)
2025-07-21 08:38:00 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-21 08:38:00 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-21 08:38:00 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-21 08:38:00 [RMI TCP Connection(6)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 08:41:24 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 08:41:24 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[112ms]
2025-07-21 08:41:24 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 08:41:24 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 08:41:24 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 08:41:24 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-07-21 08:41:28 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 08:41:28 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 08:41:28 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 08:41:28 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[307ms]
2025-07-21 08:41:29 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 08:41:29 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 08:41:29 [DubboServerHandler-*************:20881-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 08:41:29 [DubboServerHandler-*************:20881-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 08:41:29 [DubboServerHandler-*************:20881-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 08:41:29 [DubboServerHandler-*************:20881-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[100ms]
2025-07-21 08:41:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 08:41:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1247ms]
2025-07-21 08:41:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 08:41:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 08:41:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 08:41:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[6ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[43ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[40ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:41:52 [DubboServerHandler-*************:20881-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 08:44:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 08:44:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[64ms]
2025-07-21 08:44:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 08:44:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 08:44:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 08:44:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 08:44:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 08:44:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[26ms]
2025-07-21 08:44:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 08:44:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 08:44:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 08:44:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 08:44:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 08:44:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-21 08:44:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 08:44:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 08:44:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 08:44:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[43ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[49ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[19ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:02 [DubboServerHandler-*************:20881-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:30 [DubboServerHandler-*************:20881-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-49] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-49] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:31 [DubboServerHandler-*************:20881-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 08:45:35 [DubboServerHandler-*************:20881-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 09:00:43 [DubboServerHandler-*************:20881-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 09:00:43 [DubboServerHandler-*************:20881-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[33ms]
2025-07-21 09:00:43 [DubboServerHandler-*************:20881-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 09:00:43 [DubboServerHandler-*************:20881-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[104ms]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 09:00:51 [DubboServerHandler-*************:20881-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[78ms]
2025-07-21 09:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:00:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 09:04:01 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-21 09:04:01 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-21 09:04:01 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-21 09:04:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:04:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:04:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-21 09:04:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-21 09:05:53 [DubboServerHandler-*************:20881-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 09:05:53 [DubboServerHandler-*************:20881-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-07-21 09:09:52 [DubboServerHandler-*************:20881-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 09:09:52 [DubboServerHandler-*************:20881-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-21 09:09:56 [DubboServerHandler-*************:20881-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 09:09:56 [DubboServerHandler-*************:20881-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-21 09:11:58 [DubboServerHandler-*************:20881-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 09:11:58 [DubboServerHandler-*************:20881-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-21 09:12:01 [DubboServerHandler-*************:20881-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 09:12:01 [DubboServerHandler-*************:20881-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-21 09:13:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:13:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:13:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[23ms]
2025-07-21 09:13:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-21 09:15:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:15:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:15:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-21 09:15:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-21 09:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-21 09:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-21 09:17:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:17:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:17:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:17:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-07-21 09:17:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:17:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:17:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:17:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 09:20:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:20:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:20:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[29ms]
2025-07-21 09:20:53 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[32ms]
2025-07-21 09:21:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:21:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:21:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-21 09:21:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-21 09:27:09 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-21 09:27:09 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-21 09:27:12 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-21 09:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe47936d3, L:/*************:3612 ! R:/*************:8091]
2025-07-21 09:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe47936d3, L:/*************:3612 ! R:/*************:8091]
2025-07-21 09:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe47936d3, L:/*************:3612 ! R:/*************:8091]
2025-07-21 09:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe47936d3, L:/*************:3612 ! R:/*************:8091]
2025-07-21 09:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe47936d3, L:/*************:3612 ! R:/*************:8091]) will closed
2025-07-21 09:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe47936d3, L:/*************:3612 ! R:/*************:8091]) will closed
2025-07-21 09:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x00f53012, L:/*************:3651 ! R:/*************:8091]
2025-07-21 09:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x00f53012, L:/*************:3651 ! R:/*************:8091]
2025-07-21 09:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x00f53012, L:/*************:3651 ! R:/*************:8091]
2025-07-21 09:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x00f53012, L:/*************:3651 ! R:/*************:8091]
2025-07-21 09:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00f53012, L:/*************:3651 ! R:/*************:8091]) will closed
2025-07-21 09:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00f53012, L:/*************:3651 ! R:/*************:8091]) will closed
2025-07-21 09:27:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-21 09:27:18 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 22256 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-21 09:27:18 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-21 09:27:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-21 09:27:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-21 09:27:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-21 09:27:23 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-21 09:27:23 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-21 09:27:23 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-21 09:27:23 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-21 09:27:23 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-21 09:27:23 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-21 09:27:23 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 09:27:23 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753061243655
timestamp=1753061243655
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 09:27:23 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x44f1f3df, L:/*************:13750 - R:/*************:8091]
2025-07-21 09:27:23 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 84 ms, version:1.7.1,role:TMROLE,channel:[id: 0x44f1f3df, L:/*************:13750 - R:/*************:8091]
2025-07-21 09:27:23 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-21 09:27:23 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-21 09:27:23 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-21 09:27:23 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-21 09:27:23 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-21 09:27:23 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-21 09:27:25 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-21 09:27:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-21 09:27:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-21 09:27:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-21 09:27:28 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@75fe135a
2025-07-21 09:27:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-21 09:27:28 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 09:27:28 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 09:27:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 09:27:28 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5cdce4f5, L:/*************:13773 - R:/*************:8091]
2025-07-21 09:27:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x5cdce4f5, L:/*************:13773 - R:/*************:8091]
2025-07-21 09:27:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-21 09:27:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-21 09:27:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-21 09:27:31 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-21 09:27:31 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-21 09:27:31 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-21 09:27:32 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-21 09:27:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-21 09:27:33 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-21 09:27:36 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-21 09:27:36 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-21 09:27:36 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-21 09:27:36 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-21 09:27:36 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-21 09:27:41 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 25.308 seconds (process running for 26.848)
2025-07-21 09:27:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-21 09:27:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-21 09:27:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-21 09:27:41 [RMI TCP Connection(7)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 09:28:31 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-21 09:28:31 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-21 09:28:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5cdce4f5, L:/*************:13773 - R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x44f1f3df, L:/*************:13750 - R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5cdce4f5, L:/*************:13773 - R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x44f1f3df, L:/*************:13750 - R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x44f1f3df, L:/*************:13750 ! R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5cdce4f5, L:/*************:13773 ! R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x44f1f3df, L:/*************:13750 ! R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5cdce4f5, L:/*************:13773 ! R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x44f1f3df, L:/*************:13750 ! R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5cdce4f5, L:/*************:13773 ! R:/*************:8091]
2025-07-21 09:28:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x44f1f3df, L:/*************:13750 ! R:/*************:8091]) will closed
2025-07-21 09:28:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5cdce4f5, L:/*************:13773 ! R:/*************:8091]) will closed
2025-07-21 09:28:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5cdce4f5, L:/*************:13773 ! R:/*************:8091]) will closed
2025-07-21 09:28:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x44f1f3df, L:/*************:13750 ! R:/*************:8091]) will closed
2025-07-21 09:28:33 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 09:28:33 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753061313474
timestamp=1753061313474
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 09:28:33 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 09:28:33 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 09:28:33 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5cdce4f5, L:/*************:13773 ! R:/*************:8091]
2025-07-21 09:28:33 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5cdce4f5, L:/*************:13773 ! R:/*************:8091]
2025-07-21 09:28:33 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 09:32:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-21 09:32:58 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 26548 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-21 09:32:58 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-21 09:32:58 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-21 09:32:58 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-21 09:32:58 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-21 09:33:02 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-21 09:33:02 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-21 09:33:02 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-21 09:33:02 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-21 09:33:03 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-21 09:33:03 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-21 09:33:03 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 09:33:03 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753061583186
timestamp=1753061583186
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 09:33:03 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa39f1a6a, L:/*************:1452 - R:/*************:8091]
2025-07-21 09:33:03 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 49 ms, version:1.7.1,role:TMROLE,channel:[id: 0xa39f1a6a, L:/*************:1452 - R:/*************:8091]
2025-07-21 09:33:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-21 09:33:03 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-21 09:33:03 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-21 09:33:03 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-21 09:33:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-21 09:33:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-21 09:33:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-21 09:33:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-21 09:33:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-21 09:33:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-21 09:33:05 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4907472e
2025-07-21 09:33:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-21 09:33:06 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 09:33:06 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 09:33:06 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 09:33:06 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf1f64b26, L:/*************:1460 - R:/*************:8091]
2025-07-21 09:33:06 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0xf1f64b26, L:/*************:1460 - R:/*************:8091]
2025-07-21 09:33:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-21 09:33:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-21 09:33:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-21 09:33:08 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-21 09:33:08 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-21 09:33:08 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-21 09:33:08 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-21 09:33:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-21 09:33:09 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-21 09:33:12 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-21 09:33:12 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-21 09:33:12 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-21 09:33:12 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-21 09:33:12 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-21 09:33:16 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 20.235 seconds (process running for 21.4)
2025-07-21 09:33:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-21 09:33:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-21 09:33:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-21 09:33:16 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 09:34:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:34:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:34:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[222ms]
2025-07-21 09:34:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[246ms]
2025-07-21 09:34:57 [DubboServerHandler-*************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 09:34:57 [DubboServerHandler-*************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-21 09:35:03 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 09:35:03 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-21 09:35:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-21 09:35:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-21 09:35:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-21 09:35:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-21 09:35:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-21 09:35:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-21 09:35:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-21 09:35:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[27ms]
2025-07-21 09:35:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:35:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1301ms]
2025-07-21 09:35:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[23ms]
2025-07-21 09:35:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType],SpendTime=[2ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType],SpendTime=[11ms]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[27ms]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType],SpendTime=[0ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType],SpendTime=[2ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType],SpendTime=[1ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType],SpendTime=[1ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType],SpendTime=[0ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDictService],MethodName=[selectDictDataByType],SpendTime=[1ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-21 09:35:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[5ms]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[30ms]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:35:21 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[29ms]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[27ms]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-21 09:35:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-21 09:35:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:35:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[29ms]
2025-07-21 09:35:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:35:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:35:23 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:35:54 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-21 09:36:12 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:12 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:12 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:36:12 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:36:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-21 09:36:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-21 09:36:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-21 09:36:13 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-21 09:36:14 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:14 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:14 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-21 09:36:14 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-21 09:36:14 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[43ms]
2025-07-21 09:36:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-21 09:36:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[26ms]
2025-07-21 09:36:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-21 09:36:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[28ms]
2025-07-21 09:36:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[28ms]
2025-07-21 09:36:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-21 09:36:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[27ms]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-21 09:36:18 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[47ms]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:36:19 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:44:15 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 09:44:15 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[87ms]
2025-07-21 09:44:15 [DubboServerHandler-*************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 09:44:15 [DubboServerHandler-*************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-21 09:44:20 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 09:44:20 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-21 09:44:20 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 09:44:20 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[134ms]
2025-07-21 09:44:21 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 09:44:21 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 09:44:21 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 09:44:21 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 09:44:21 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 09:44:21 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[148ms]
2025-07-21 09:44:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:44:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:44:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:44:22 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 09:44:53 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 09:44:53 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[26ms]
2025-07-21 09:44:53 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 09:44:53 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 09:44:59 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 09:44:59 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 09:44:59 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 09:44:59 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[154ms]
2025-07-21 09:45:00 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 09:45:00 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 09:45:00 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 09:45:00 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-21 09:45:00 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 09:45:00 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[58ms]
2025-07-21 09:45:01 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:45:01 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:45:01 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 09:45:01 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[63ms]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-21 09:45:06 [DubboServerHandler-*************:20880-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[42ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:07 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-21 09:45:09 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[57ms]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:45:10 [DubboServerHandler-*************:20880-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-21 09:55:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:55:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 09:55:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-07-21 09:55:17 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[14ms]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[64ms]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-49] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-49] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:55:24 [DubboServerHandler-*************:20880-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-21 09:55:25 [DubboServerHandler-*************:20880-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:55:25 [DubboServerHandler-*************:20880-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-21 09:55:25 [DubboServerHandler-*************:20880-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:55:25 [DubboServerHandler-*************:20880-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-21 09:55:25 [DubboServerHandler-*************:20880-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 09:55:25 [DubboServerHandler-*************:20880-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-21 10:02:04 [DubboServerHandler-*************:20880-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 10:02:04 [DubboServerHandler-*************:20880-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[31ms]
2025-07-21 10:02:04 [DubboServerHandler-*************:20880-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 10:02:04 [DubboServerHandler-*************:20880-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xf1f64b26, L:/*************:1460 - R:/*************:8091] read idle.
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf1f64b26, L:/*************:1460 - R:/*************:8091]
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1f64b26, L:/*************:1460 - R:/*************:8091]) will closed
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]) will closed
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]) will closed
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]) will closed
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]) will closed
2025-07-21 13:27:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1f64b26, L:/*************:1460 ! R:/*************:8091]) will closed
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xa39f1a6a, L:/*************:1452 - R:/*************:8091] read idle.
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa39f1a6a, L:/*************:1452 - R:/*************:8091]
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa39f1a6a, L:/*************:1452 - R:/*************:8091]) will closed
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]) will closed
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]) will closed
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]) will closed
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]) will closed
2025-07-21 13:27:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa39f1a6a, L:/*************:1452 ! R:/*************:8091]) will closed
2025-07-21 13:27:59 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 13:27:59 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753075679858
timestamp=1753075679858
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 13:27:59 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x67301622, L:/*************:1842 - R:/*************:8091]
2025-07-21 13:27:59 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:TMROLE,channel:[id: 0x67301622, L:/*************:1842 - R:/*************:8091]
2025-07-21 13:28:00 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 13:28:00 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 13:28:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 13:28:00 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x00c54466, L:/*************:2521 - R:/*************:8091]
2025-07-21 13:28:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x00c54466, L:/*************:2521 - R:/*************:8091]
2025-07-21 13:32:30 [DubboServerHandler-*************:20880-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:32:30 [DubboServerHandler-*************:20880-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[25ms]
2025-07-21 13:32:30 [DubboServerHandler-*************:20880-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:32:30 [DubboServerHandler-*************:20880-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 13:32:30 [DubboServerHandler-*************:20880-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 13:32:30 [DubboServerHandler-*************:20880-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[36ms]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[189ms]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 13:36:26 [DubboServerHandler-*************:20880-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[76ms]
2025-07-21 13:36:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 13:36:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 13:36:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 13:36:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 13:36:32 [DubboServerHandler-*************:20880-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:36:32 [DubboServerHandler-*************:20880-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-21 13:36:32 [DubboServerHandler-*************:20880-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 13:36:32 [DubboServerHandler-*************:20880-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 13:36:37 [DubboServerHandler-*************:20880-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:36:37 [DubboServerHandler-*************:20880-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 13:36:37 [DubboServerHandler-*************:20880-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:36:37 [DubboServerHandler-*************:20880-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 13:36:37 [DubboServerHandler-*************:20880-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 13:36:37 [DubboServerHandler-*************:20880-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 13:37:50 [DubboServerHandler-*************:20880-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:37:50 [DubboServerHandler-*************:20880-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[23ms]
2025-07-21 13:37:50 [DubboServerHandler-*************:20880-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:37:50 [DubboServerHandler-*************:20880-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-21 13:37:50 [DubboServerHandler-*************:20880-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 13:37:50 [DubboServerHandler-*************:20880-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-21 13:56:55 [DubboServerHandler-*************:20880-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:56:55 [DubboServerHandler-*************:20880-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[34ms]
2025-07-21 13:56:55 [DubboServerHandler-*************:20880-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 13:56:55 [DubboServerHandler-*************:20880-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 13:56:55 [DubboServerHandler-*************:20880-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 13:56:55 [DubboServerHandler-*************:20880-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-21 15:22:26 [DubboServerHandler-*************:20880-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 15:22:26 [DubboServerHandler-*************:20880-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[37ms]
2025-07-21 15:22:27 [DubboServerHandler-*************:20880-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 15:22:27 [DubboServerHandler-*************:20880-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[337ms]
2025-07-21 15:22:27 [DubboServerHandler-*************:20880-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 15:22:27 [DubboServerHandler-*************:20880-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-21 15:22:27 [DubboServerHandler-*************:20880-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 15:22:27 [DubboServerHandler-*************:20880-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-21 15:22:27 [DubboServerHandler-*************:20880-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 15:22:27 [DubboServerHandler-*************:20880-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[65ms]
2025-07-21 15:22:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:22:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:22:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 15:22:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 15:22:39 [DubboServerHandler-*************:20880-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 15:22:39 [DubboServerHandler-*************:20880-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-21 15:22:39 [DubboServerHandler-*************:20880-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 15:22:39 [DubboServerHandler-*************:20880-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 15:22:46 [DubboServerHandler-*************:20880-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 15:22:46 [DubboServerHandler-*************:20880-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 15:22:46 [DubboServerHandler-*************:20880-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 15:22:46 [DubboServerHandler-*************:20880-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[101ms]
2025-07-21 15:22:47 [DubboServerHandler-*************:20880-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 15:22:47 [DubboServerHandler-*************:20880-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 15:22:47 [DubboServerHandler-*************:20880-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 15:22:47 [DubboServerHandler-*************:20880-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-21 15:22:47 [DubboServerHandler-*************:20880-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 15:22:47 [DubboServerHandler-*************:20880-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[66ms]
2025-07-21 15:22:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:22:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:22:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 15:22:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 15:39:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:39:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:39:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-21 15:39:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-21 15:41:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:41:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:41:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-21 15:41:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-21 15:41:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:41:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:41:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 15:41:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 15:43:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:43:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 15:43:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[23ms]
2025-07-21 15:43:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[29ms]
2025-07-21 15:52:46 [DubboServerHandler-*************:20880-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-21 15:52:46 [DubboServerHandler-*************:20880-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[54ms]
2025-07-21 15:52:46 [DubboServerHandler-*************:20880-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 15:52:46 [DubboServerHandler-*************:20880-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-21 15:58:37 [DubboServerHandler-*************:20880-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 15:58:37 [DubboServerHandler-*************:20880-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[58ms]
2025-07-21 15:58:37 [DubboServerHandler-*************:20880-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 15:58:37 [DubboServerHandler-*************:20880-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-21 15:59:18 [DubboServerHandler-*************:20880-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 15:59:18 [DubboServerHandler-*************:20880-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-21 15:59:18 [DubboServerHandler-*************:20880-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 15:59:18 [DubboServerHandler-*************:20880-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-21 15:59:20 [DubboServerHandler-*************:20880-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 15:59:20 [DubboServerHandler-*************:20880-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-21 15:59:20 [DubboServerHandler-*************:20880-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 15:59:20 [DubboServerHandler-*************:20880-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 15:59:49 [DubboServerHandler-*************:20880-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 15:59:49 [DubboServerHandler-*************:20880-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-21 15:59:49 [DubboServerHandler-*************:20880-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 15:59:49 [DubboServerHandler-*************:20880-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 16:00:27 [DubboServerHandler-*************:20880-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:00:27 [DubboServerHandler-*************:20880-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-21 16:00:27 [DubboServerHandler-*************:20880-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:00:27 [DubboServerHandler-*************:20880-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-21 16:11:16 [DubboServerHandler-*************:20880-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:11:16 [DubboServerHandler-*************:20880-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-21 16:11:17 [DubboServerHandler-*************:20880-thread-124] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:11:17 [DubboServerHandler-*************:20880-thread-124] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-21 16:11:56 [DubboServerHandler-*************:20880-thread-125] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:11:56 [DubboServerHandler-*************:20880-thread-125] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-21 16:11:56 [DubboServerHandler-*************:20880-thread-126] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:11:56 [DubboServerHandler-*************:20880-thread-126] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-129] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-129] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-130] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-130] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-131] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-131] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-132] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-132] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-133] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-133] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-134] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:14:16 [DubboServerHandler-*************:20880-thread-134] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-135] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-135] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-136] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-136] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-137] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-137] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-138] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-138] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-139] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-139] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-140] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:15:45 [DubboServerHandler-*************:20880-thread-140] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 16:19:27 [DubboServerHandler-*************:20880-thread-141] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:19:27 [DubboServerHandler-*************:20880-thread-141] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-21 16:19:27 [DubboServerHandler-*************:20880-thread-142] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:19:27 [DubboServerHandler-*************:20880-thread-142] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 16:20:41 [DubboServerHandler-*************:20880-thread-143] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:20:41 [DubboServerHandler-*************:20880-thread-143] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-21 16:20:41 [DubboServerHandler-*************:20880-thread-144] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:20:41 [DubboServerHandler-*************:20880-thread-144] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 16:22:52 [DubboServerHandler-*************:20880-thread-145] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:22:52 [DubboServerHandler-*************:20880-thread-145] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-21 16:22:52 [DubboServerHandler-*************:20880-thread-146] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:22:52 [DubboServerHandler-*************:20880-thread-146] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 16:27:01 [DubboServerHandler-*************:20880-thread-147] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:27:01 [DubboServerHandler-*************:20880-thread-147] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-21 16:27:01 [DubboServerHandler-*************:20880-thread-148] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:27:01 [DubboServerHandler-*************:20880-thread-148] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-21 16:27:36 [DubboServerHandler-*************:20880-thread-149] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:27:36 [DubboServerHandler-*************:20880-thread-149] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[58ms]
2025-07-21 16:27:36 [DubboServerHandler-*************:20880-thread-150] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:27:36 [DubboServerHandler-*************:20880-thread-150] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 16:28:34 [DubboServerHandler-*************:20880-thread-151] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:28:34 [DubboServerHandler-*************:20880-thread-151] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-21 16:28:34 [DubboServerHandler-*************:20880-thread-152] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:28:34 [DubboServerHandler-*************:20880-thread-152] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 16:35:02 [DubboServerHandler-*************:20880-thread-155] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:02 [DubboServerHandler-*************:20880-thread-155] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-21 16:35:02 [DubboServerHandler-*************:20880-thread-156] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:02 [DubboServerHandler-*************:20880-thread-156] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-157] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-157] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-158] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-158] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-159] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-159] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-160] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-160] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-161] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-161] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-162] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:07 [DubboServerHandler-*************:20880-thread-162] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 16:35:36 [DubboServerHandler-*************:20880-thread-163] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:36 [DubboServerHandler-*************:20880-thread-163] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-21 16:35:36 [DubboServerHandler-*************:20880-thread-164] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:35:36 [DubboServerHandler-*************:20880-thread-164] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-21 16:36:58 [DubboServerHandler-*************:20880-thread-165] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:36:58 [DubboServerHandler-*************:20880-thread-165] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[65ms]
2025-07-21 16:36:58 [DubboServerHandler-*************:20880-thread-166] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:36:58 [DubboServerHandler-*************:20880-thread-166] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 16:37:09 [DubboServerHandler-*************:20880-thread-167] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:37:09 [DubboServerHandler-*************:20880-thread-167] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-21 16:37:09 [DubboServerHandler-*************:20880-thread-168] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:37:09 [DubboServerHandler-*************:20880-thread-168] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 16:37:19 [DubboServerHandler-*************:20880-thread-169] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:37:19 [DubboServerHandler-*************:20880-thread-169] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-21 16:37:19 [DubboServerHandler-*************:20880-thread-170] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:37:19 [DubboServerHandler-*************:20880-thread-170] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-21 16:37:38 [DubboServerHandler-*************:20880-thread-171] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:37:38 [DubboServerHandler-*************:20880-thread-171] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-21 16:37:38 [DubboServerHandler-*************:20880-thread-172] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:37:38 [DubboServerHandler-*************:20880-thread-172] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-21 16:39:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:39:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:39:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[38ms]
2025-07-21 16:39:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[44ms]
2025-07-21 16:39:28 [DubboServerHandler-*************:20880-thread-173] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:39:28 [DubboServerHandler-*************:20880-thread-173] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-21 16:39:28 [DubboServerHandler-*************:20880-thread-174] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:39:29 [DubboServerHandler-*************:20880-thread-174] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 16:39:43 [DubboServerHandler-*************:20880-thread-175] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:39:43 [DubboServerHandler-*************:20880-thread-175] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-21 16:39:43 [DubboServerHandler-*************:20880-thread-176] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:39:43 [DubboServerHandler-*************:20880-thread-176] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 16:40:37 [DubboServerHandler-*************:20880-thread-177] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:40:37 [DubboServerHandler-*************:20880-thread-177] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-21 16:40:37 [DubboServerHandler-*************:20880-thread-178] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:40:37 [DubboServerHandler-*************:20880-thread-178] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 16:40:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:40:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:40:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-21 16:40:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-21 16:40:48 [DubboServerHandler-*************:20880-thread-179] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:40:48 [DubboServerHandler-*************:20880-thread-179] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 16:40:48 [DubboServerHandler-*************:20880-thread-180] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:40:48 [DubboServerHandler-*************:20880-thread-180] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 16:40:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:40:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:40:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 16:40:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 16:40:58 [DubboServerHandler-*************:20880-thread-181] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:40:58 [DubboServerHandler-*************:20880-thread-181] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 16:40:58 [DubboServerHandler-*************:20880-thread-182] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:40:58 [DubboServerHandler-*************:20880-thread-182] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 16:41:03 [DubboServerHandler-*************:20880-thread-183] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:41:03 [DubboServerHandler-*************:20880-thread-183] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-21 16:41:03 [DubboServerHandler-*************:20880-thread-184] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 16:41:03 [DubboServerHandler-*************:20880-thread-184] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 16:41:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:41:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:41:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-21 16:41:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-21 16:42:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:42:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:42:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 16:42:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 16:42:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:42:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:42:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 16:42:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 16:49:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-21 16:49:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-21 16:49:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 16:49:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 16:49:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 16:49:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 16:49:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 16:49:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 16:49:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 16:49:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 16:49:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 16:49:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 16:49:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 17:00:07 [DubboServerHandler-*************:20880-thread-188] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 17:00:07 [DubboServerHandler-*************:20880-thread-188] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[32ms]
2025-07-21 17:00:07 [DubboServerHandler-*************:20880-thread-189] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 17:00:07 [DubboServerHandler-*************:20880-thread-189] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-07-21 17:00:14 [DubboServerHandler-*************:20880-thread-190] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 17:00:14 [DubboServerHandler-*************:20880-thread-190] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 17:00:14 [DubboServerHandler-*************:20880-thread-191] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 17:00:14 [DubboServerHandler-*************:20880-thread-191] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[167ms]
2025-07-21 17:00:14 [DubboServerHandler-*************:20880-thread-192] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 17:00:14 [DubboServerHandler-*************:20880-thread-192] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 17:00:15 [DubboServerHandler-*************:20880-thread-193] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 17:00:15 [DubboServerHandler-*************:20880-thread-193] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 17:00:15 [DubboServerHandler-*************:20880-thread-194] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 17:00:15 [DubboServerHandler-*************:20880-thread-194] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[59ms]
2025-07-21 17:00:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 17:00:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 17:00:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 17:00:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 17:09:02 [DubboServerHandler-*************:20880-thread-195] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 17:09:02 [DubboServerHandler-*************:20880-thread-195] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[23ms]
2025-07-21 17:09:02 [DubboServerHandler-*************:20880-thread-196] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 17:09:03 [DubboServerHandler-*************:20880-thread-196] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[176ms]
2025-07-21 17:09:03 [DubboServerHandler-*************:20880-thread-197] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 17:09:03 [DubboServerHandler-*************:20880-thread-197] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-21 17:09:03 [DubboServerHandler-*************:20880-thread-198] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 17:09:03 [DubboServerHandler-*************:20880-thread-198] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 17:09:03 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 17:09:03 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[61ms]
2025-07-21 17:09:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 17:09:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 17:09:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 17:09:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 17:09:08 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:09:08 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-21 17:09:08 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:09:08 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:09:09 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[50ms]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:49 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:50 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:12:52 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:13:17 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:13:17 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-21 17:13:17 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:13:17 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:13:17 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:13:17 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:13:17 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:13:17 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:13:17 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:13:18 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:13:18 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:13:18 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:13:18 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:13:18 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:13:18 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:13:18 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:13:53 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 17:13:53 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:06 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:36 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 17:14:46 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:46 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[49ms]
2025-07-21 17:14:46 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:46 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-21 17:14:46 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:46 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-21 17:14:46 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:14:46 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-21 17:17:04 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:17:04 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-21 17:17:04 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:17:04 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 17:17:04 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:17:04 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:17:04 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:17:04 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:19:29 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 17:23:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 17:23:28 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:31 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 17:23:34 [DubboServerHandler-*************:20880-thread-200] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x67301622, L:/*************:1842 - R:/*************:8091] read idle.
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x67301622, L:/*************:1842 - R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67301622, L:/*************:1842 - R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67301622, L:/*************:1842 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x67301622, L:/*************:1842 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x67301622, L:/*************:1842 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x67301622, L:/*************:1842 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67301622, L:/*************:1842 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67301622, L:/*************:1842 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x67301622, L:/*************:1842 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x67301622, L:/*************:1842 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x67301622, L:/*************:1842 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67301622, L:/*************:1842 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67301622, L:/*************:1842 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x00c54466, L:/*************:2521 - R:/*************:8091] read idle.
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x00c54466, L:/*************:2521 - R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00c54466, L:/*************:2521 - R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x00c54466, L:/*************:2521 ! R:/*************:8091]) will closed
2025-07-21 18:29:28 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 18:29:28 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753093768881
timestamp=1753093768881
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 18:29:28 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x3a635760, L:/*************:12448 - R:/*************:8091]
2025-07-21 18:29:28 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 12 ms, version:1.7.1,role:TMROLE,channel:[id: 0x3a635760, L:/*************:12448 - R:/*************:8091]
2025-07-21 18:29:29 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 18:29:29 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 18:29:29 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 18:29:29 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x92a8e976, L:/*************:12466 - R:/*************:8091]
2025-07-21 18:29:29 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 10 ms, version:1.7.1,role:RMROLE,channel:[id: 0x92a8e976, L:/*************:12466 - R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x3a635760, L:/*************:12448 - R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x92a8e976, L:/*************:12466 - R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x3a635760, L:/*************:12448 - R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x92a8e976, L:/*************:12466 - R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x3a635760, L:/*************:12448 ! R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x92a8e976, L:/*************:12466 ! R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3a635760, L:/*************:12448 ! R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x92a8e976, L:/*************:12466 ! R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3a635760, L:/*************:12448 ! R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x92a8e976, L:/*************:12466 ! R:/*************:8091]
2025-07-21 18:29:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a635760, L:/*************:12448 ! R:/*************:8091]) will closed
2025-07-21 18:29:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x92a8e976, L:/*************:12466 ! R:/*************:8091]) will closed
2025-07-21 18:29:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x92a8e976, L:/*************:12466 ! R:/*************:8091]) will closed
2025-07-21 18:29:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a635760, L:/*************:12448 ! R:/*************:8091]) will closed
2025-07-21 18:29:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 18:29:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753093796667
timestamp=1753093796667
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 18:29:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x008db5fd, L:null ! R:/*************:8091]) will closed
2025-07-21 18:29:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 18:29:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 18:29:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x92a8e976, L:/*************:12466 ! R:/*************:8091]
2025-07-21 18:29:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x92a8e976, L:/*************:12466 ! R:/*************:8091]
2025-07-21 18:29:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 18:29:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8e315046, L:null ! R:/*************:8091]) will closed
2025-07-21 18:30:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 18:30:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753093806659
timestamp=1753093806659
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 18:30:06 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x88dbb8e5, L:/*************:13288 - R:/*************:8091]
2025-07-21 18:30:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:TMROLE,channel:[id: 0x88dbb8e5, L:/*************:13288 - R:/*************:8091]
2025-07-21 18:30:07 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 18:30:07 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 18:30:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 18:30:07 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xc9d76c31, L:/*************:13291 - R:/*************:8091]
2025-07-21 18:30:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:RMROLE,channel:[id: 0xc9d76c31, L:/*************:13291 - R:/*************:8091]
2025-07-21 19:43:32 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 19:43:32 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[40ms]
2025-07-21 19:43:32 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 19:43:32 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[198ms]
2025-07-21 19:43:33 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 19:43:33 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-21 19:43:33 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 19:43:33 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-21 19:43:33 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 19:43:33 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[75ms]
2025-07-21 19:43:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:43:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:43:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:43:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-07-21 19:43:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:43:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:43:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:43:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:44:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:44:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:44:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:44:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 19:47:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:47:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:47:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:47:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-21 19:47:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:47:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:47:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:47:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 19:48:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:48:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:48:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 19:48:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 19:48:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:48:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:48:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:48:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:49:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:49:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:49:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:49:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 19:49:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:49:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 19:49:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 19:49:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[49ms]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:52 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:02:56 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:03:06 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[18ms]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-21 20:03:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[236ms]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[23ms]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-21 20:05:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-21 20:05:20 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 20:05:20 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[26ms]
2025-07-21 20:05:20 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 20:05:20 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 20:05:28 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 20:05:28 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 20:05:28 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-21 20:05:29 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[78ms]
2025-07-21 20:05:29 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 20:05:29 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-21 20:05:29 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 20:05:29 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 20:05:29 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-21 20:05:29 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[75ms]
2025-07-21 20:05:30 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 20:05:30 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 20:05:30 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-21 20:05:30 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-21 20:06:07 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 20:06:07 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-21 20:06:07 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-21 20:06:07 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-21 20:07:28 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 20:07:28 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-21 20:07:28 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-21 20:07:28 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[67ms]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[26ms]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[28ms]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-21 20:08:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-21 20:23:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-21 20:23:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[20ms]
2025-07-21 20:23:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-21 20:23:07 [DubboServerHandler-*************:20880-thread-199] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-21 20:31:19 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-21 20:31:19 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-21 20:31:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x88dbb8e5, L:/*************:13288 - R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xc9d76c31, L:/*************:13291 - R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xc9d76c31, L:/*************:13291 - R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x88dbb8e5, L:/*************:13288 - R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xc9d76c31, L:/*************:13291 ! R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x88dbb8e5, L:/*************:13288 ! R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x88dbb8e5, L:/*************:13288 ! R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc9d76c31, L:/*************:13291 ! R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x88dbb8e5, L:/*************:13288 ! R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc9d76c31, L:/*************:13291 ! R:/*************:8091]
2025-07-21 20:31:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc9d76c31, L:/*************:13291 ! R:/*************:8091]) will closed
2025-07-21 20:31:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88dbb8e5, L:/*************:13288 ! R:/*************:8091]) will closed
2025-07-21 20:31:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc9d76c31, L:/*************:13291 ! R:/*************:8091]) will closed
2025-07-21 20:31:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88dbb8e5, L:/*************:13288 ! R:/*************:8091]) will closed
2025-07-21 20:31:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:31:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753101086661
timestamp=1753101086661
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 20:31:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:31:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 20:31:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc9d76c31, L:/*************:13291 ! R:/*************:8091]
2025-07-21 20:31:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc9d76c31, L:/*************:13291 ! R:/*************:8091]
2025-07-21 20:31:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 20:31:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc0fee3ba, L:null ! R:/*************:8091]) will closed
2025-07-21 20:31:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0c231ff1, L:null ! R:/*************:8091]) will closed
2025-07-21 20:31:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:31:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753101096660
timestamp=1753101096660
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 20:31:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:31:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 20:31:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 20:31:38 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe511e5cb, L:null ! R:/*************:8091]) will closed
2025-07-21 20:31:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x60e16009, L:null ! R:/*************:8091]) will closed
2025-07-21 20:31:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:31:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753101106659
timestamp=1753101106659
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 20:31:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:31:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 20:31:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 20:31:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x762f017d, L:null ! R:/*************:8091]) will closed
2025-07-21 20:31:49 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x44045c17, L:null ! R:/*************:8091]) will closed
2025-07-21 20:31:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:31:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753101116669
timestamp=1753101116669
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 20:31:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:31:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 20:31:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 20:31:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xee45d8c6, L:null ! R:/*************:8091]) will closed
2025-07-21 20:31:59 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfcd43ba4, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753101126673
timestamp=1753101126673
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 20:32:07 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:07 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 20:32:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 20:32:08 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x318ad986, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1bb261af, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753101136659
timestamp=1753101136659
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 20:32:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:17 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 20:32:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 20:32:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x962302dd, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c7ed07a, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753101146670
timestamp=1753101146670
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 20:32:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 20:32:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 20:32:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x68204992, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfbbb5088, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753101156659
timestamp=1753101156659
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 20:32:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 20:32:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 20:32:38 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9bd3c0a8, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x423cf5b4, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753101166659
timestamp=1753101166659
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-21 20:32:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-21 20:32:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-21 20:32:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-21 20:32:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x04f32755, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:49 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbfc683e6, L:null ! R:/*************:8091]) will closed
2025-07-21 20:32:50 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-21 20:32:50 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-21 20:32:50 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-21 20:32:50 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-21 20:32:51 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-21 20:32:51 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-21 20:32:51 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-21 20:32:51 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-21 20:32:51 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-21 20:32:51 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-21 20:32:51 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
