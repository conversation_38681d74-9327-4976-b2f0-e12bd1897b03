2025-07-25 08:34:07 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 08:34:08 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 29528 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 08:34:08 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-25 08:34:08 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:08 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:08 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:14 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 08:34:14 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 08:34:14 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 08:34:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 08:34:14 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 08:34:14 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 08:34:14 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 08:34:14 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753403654970
timestamp=1753403654970
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 08:34:15 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd0d6cf70, L:/*************:2100 - R:/*************:8091]
2025-07-25 08:34:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 66 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd0d6cf70, L:/*************:2100 - R:/*************:8091]
2025-07-25 08:34:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-25 08:34:15 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 08:34:15 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 08:34:15 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 08:34:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-25 08:34:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 08:34:16 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 08:34:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 08:34:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 08:34:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 08:34:18 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@762078dc
2025-07-25 08:34:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 08:34:18 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 08:34:18 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 08:34:18 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 08:34:18 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xb5a2bb50, L:/*************:2136 - R:/*************:8091]
2025-07-25 08:34:18 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0xb5a2bb50, L:/*************:2136 - R:/*************:8091]
2025-07-25 08:34:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 08:34:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 08:34:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 08:34:20 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 08:34:20 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 08:34:21 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 08:34:21 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 08:34:21 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 08:34:22 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 08:34:25 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 08:34:25 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 08:34:25 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 08:34:25 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 08:34:25 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-25 08:34:29 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 24.415 seconds (process running for 25.738)
2025-07-25 08:34:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-25 08:34:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 08:34:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 08:34:29 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 08:35:38 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 08:35:38 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[96ms]
2025-07-25 08:35:38 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 08:35:38 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[328ms]
2025-07-25 08:35:39 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 08:35:39 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 08:35:39 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 08:35:39 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-25 08:35:39 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 08:35:39 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[90ms]
2025-07-25 08:35:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 08:35:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 08:35:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 08:35:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[4ms]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId],SpendTime=[181ms]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[90ms]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[35ms]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 08:35:50 [DubboServerHandler-*************:20881-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-25 08:36:53 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*********************************eng-cloud', saveDays=7, branchType=AT}
2025-07-25 10:00:29 [DubboServerHandler-*************:20881-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 10:00:29 [DubboServerHandler-*************:20881-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[23ms]
2025-07-25 10:00:29 [DubboServerHandler-*************:20881-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 10:00:29 [DubboServerHandler-*************:20881-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[120ms]
2025-07-25 10:00:29 [DubboServerHandler-*************:20881-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 10:00:29 [DubboServerHandler-*************:20881-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 10:00:29 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 10:00:29 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-25 10:00:29 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 10:00:30 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[73ms]
2025-07-25 10:00:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 10:00:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 10:00:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 10:00:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd0d6cf70, L:/*************:2100 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xb5a2bb50, L:/*************:2136 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xd0d6cf70, L:/*************:2100 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xb5a2bb50, L:/*************:2136 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd0d6cf70, L:/*************:2100 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xb5a2bb50, L:/*************:2136 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb5a2bb50, L:/*************:2136 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd0d6cf70, L:/*************:2100 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb5a2bb50, L:/*************:2136 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd0d6cf70, L:/*************:2100 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb5a2bb50, L:/*************:2136 ! R:/*************:8091]) will closed
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd0d6cf70, L:/*************:2100 ! R:/*************:8091]) will closed
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb5a2bb50, L:/*************:2136 ! R:/*************:8091]) will closed
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd0d6cf70, L:/*************:2100 ! R:/*************:8091]) will closed
2025-07-25 10:44:35 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:35 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753411475174
timestamp=1753411475174
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 10:44:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:35 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 10:44:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb5a2bb50, L:/*************:2136 ! R:/*************:8091]
2025-07-25 10:44:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb5a2bb50, L:/*************:2136 ! R:/*************:8091]
2025-07-25 10:44:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 10:44:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb401bb96, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbac109eb, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:45 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:45 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753411485171
timestamp=1753411485171
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 10:44:45 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:45 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 10:44:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 10:44:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x29ec80e3, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1c31e93b, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:55 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:55 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753411495171
timestamp=1753411495171
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 10:44:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:55 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 10:44:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 10:44:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x78bb1b1b, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x80661e7b, L:null ! R:/*************:8091]) will closed
2025-07-25 10:45:00 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-25 10:45:00 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-25 11:23:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 11:23:49 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 35284 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 11:23:49 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-25 11:23:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 11:23:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-25 11:23:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 11:23:57 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 11:23:57 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 11:23:57 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 11:23:57 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 11:23:57 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 11:23:57 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 11:23:57 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 11:23:57 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753413837971
timestamp=1753413837971
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 11:23:58 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4c258c5a, L:/*************:9933 - R:/*************:8091]
2025-07-25 11:23:58 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 169 ms, version:1.7.1,role:TMROLE,channel:[id: 0x4c258c5a, L:/*************:9933 - R:/*************:8091]
2025-07-25 11:23:58 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-25 11:23:58 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 11:23:58 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 11:23:58 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 11:23:58 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-25 11:23:58 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 11:23:59 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 11:24:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 11:24:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 11:24:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 11:24:01 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@50dfac19
2025-07-25 11:24:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 11:24:01 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 11:24:01 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 11:24:01 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 11:24:01 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x6bb0a58c, L:/*************:9959 - R:/*************:8091]
2025-07-25 11:24:01 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 9 ms, version:1.7.1,role:RMROLE,channel:[id: 0x6bb0a58c, L:/*************:9959 - R:/*************:8091]
2025-07-25 11:24:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 11:24:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 11:24:04 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 11:24:04 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 11:24:04 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 11:24:04 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 11:24:05 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 11:24:05 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 11:24:06 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 11:24:10 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 11:24:10 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 11:24:10 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 11:24:10 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 11:24:10 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-25 11:24:14 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 26.805 seconds (process running for 27.957)
2025-07-25 11:24:14 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-25 11:24:14 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 11:24:14 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 11:24:14 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 11:26:55 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*********************************eng-cloud', saveDays=7, branchType=AT}
2025-07-25 11:27:06 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:06 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[194ms]
2025-07-25 11:27:06 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 11:27:06 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[413ms]
2025-07-25 11:27:07 [DubboServerHandler-*************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:07 [DubboServerHandler-*************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-25 11:27:07 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 11:27:07 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-07-25 11:27:07 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 11:27:07 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[196ms]
2025-07-25 11:27:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 11:27:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 11:27:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 11:27:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[7ms]
2025-07-25 11:27:17 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:17 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 11:27:17 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 11:27:17 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-25 11:27:25 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:25 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-25 11:27:25 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 11:27:26 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[86ms]
2025-07-25 11:27:26 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 11:27:26 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 11:27:26 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 11:27:26 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-25 11:27:26 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 11:27:26 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[125ms]
2025-07-25 11:27:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 11:27:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 11:27:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 11:27:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 11:29:58 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 11:29:58 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-25 11:32:32 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 11:32:32 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 11:33:49 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 11:33:49 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-25 11:33:58 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 11:33:58 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-25 11:36:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 11:36:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 11:36:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[30ms]
2025-07-25 11:36:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[35ms]
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x4c258c5a, L:/*************:9933 - R:/*************:8091] read idle.
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4c258c5a, L:/*************:9933 - R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c258c5a, L:/*************:9933 - R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c258c5a, L:/*************:9933 ! R:/*************:8091]) will closed
2025-07-25 13:27:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:27:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753421246934
timestamp=1753421246934
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 13:27:26 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x3840ba48, L:/*************:11595 - R:/*************:8091]
2025-07-25 13:27:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 12 ms, version:1.7.1,role:TMROLE,channel:[id: 0x3840ba48, L:/*************:11595 - R:/*************:8091]
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x6bb0a58c, L:/*************:9959 - R:/*************:8091] read idle.
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x6bb0a58c, L:/*************:9959 - R:/*************:8091]
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6bb0a58c, L:/*************:9959 - R:/*************:8091]) will closed
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]) will closed
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]) will closed
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]) will closed
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]) will closed
2025-07-25 13:27:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6bb0a58c, L:/*************:9959 ! R:/*************:8091]) will closed
2025-07-25 13:27:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:27:35 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 13:27:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 13:27:35 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x235a1b9b, L:/*************:11686 - R:/*************:8091]
2025-07-25 13:27:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x235a1b9b, L:/*************:11686 - R:/*************:8091]
2025-07-25 13:29:17 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x235a1b9b, L:/*************:11686 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x235a1b9b, L:/*************:11686 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x235a1b9b, L:/*************:11686 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x235a1b9b, L:/*************:11686 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x235a1b9b, L:/*************:11686 ! R:/*************:8091]) will closed
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x235a1b9b, L:/*************:11686 ! R:/*************:8091]) will closed
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x3840ba48, L:/*************:11595 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x3840ba48, L:/*************:11595 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3840ba48, L:/*************:11595 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3840ba48, L:/*************:11595 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3840ba48, L:/*************:11595 ! R:/*************:8091]) will closed
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3840ba48, L:/*************:11595 ! R:/*************:8091]) will closed
2025-07-25 13:29:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753421364557
timestamp=1753421364557
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 13:29:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:25 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 13:29:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 13:29:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa5557ea4, L:null ! R:/*************:8091]) will closed
2025-07-25 13:29:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa0c84fb7, L:null ! R:/*************:8091]) will closed
2025-07-25 13:29:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753421374556
timestamp=1753421374556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 13:29:34 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5bcc6d9d, L:/*************:12044 - R:/*************:8091]
2025-07-25 13:29:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x5bcc6d9d, L:/*************:12044 - R:/*************:8091]
2025-07-25 13:29:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:35 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 13:29:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 13:29:35 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x13328212, L:/*************:12045 - R:/*************:8091]
2025-07-25 13:29:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0x13328212, L:/*************:12045 - R:/*************:8091]
2025-07-25 13:30:51 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:30:51 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[31ms]
2025-07-25 13:30:51 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:30:51 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 13:30:51 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 13:30:51 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-25 13:30:56 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:30:56 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 13:30:56 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 13:30:57 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[128ms]
2025-07-25 13:30:57 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:30:57 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 13:30:57 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 13:30:57 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-25 13:30:57 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 13:30:57 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[64ms]
2025-07-25 13:30:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 13:30:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 13:30:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 13:30:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-25 13:31:04 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:31:04 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 13:31:04 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 13:31:04 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-25 13:31:18 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:31:18 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-25 13:31:18 [DubboServerHandler-*************:20880-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 13:31:18 [DubboServerHandler-*************:20880-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[83ms]
2025-07-25 13:31:19 [DubboServerHandler-*************:20880-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 13:31:19 [DubboServerHandler-*************:20880-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[1ms]
2025-07-25 13:31:19 [DubboServerHandler-*************:20880-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 13:31:19 [DubboServerHandler-*************:20880-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-25 13:31:19 [DubboServerHandler-*************:20880-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 13:31:19 [DubboServerHandler-*************:20880-thread-48] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[65ms]
2025-07-25 13:31:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 13:31:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 13:31:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 13:31:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-25 13:32:27 [rpcDispatch_RMROLE_1_2_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*********************************eng-cloud', saveDays=7, branchType=AT}
2025-07-25 13:43:59 [DubboServerHandler-*************:20880-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 13:43:59 [DubboServerHandler-*************:20880-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 14:17:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 14:17:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 14:17:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-25 14:17:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[13ms]
2025-07-25 14:18:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 14:18:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 14:18:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 14:18:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-25 14:20:15 [DubboServerHandler-*************:20880-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId]
2025-07-25 14:20:15 [DubboServerHandler-*************:20880-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId],SpendTime=[89ms]
2025-07-25 14:20:16 [DubboServerHandler-*************:20880-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 14:20:16 [DubboServerHandler-*************:20880-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[48ms]
2025-07-25 14:20:16 [DubboServerHandler-*************:20880-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 14:20:16 [DubboServerHandler-*************:20880-thread-58] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[24ms]
2025-07-25 14:25:09 [DubboServerHandler-*************:20880-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 14:25:09 [DubboServerHandler-*************:20880-thread-59] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 14:27:19 [DubboServerHandler-*************:20880-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 14:27:19 [DubboServerHandler-*************:20880-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-25 14:55:19 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-25 14:55:19 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x13328212, L:/*************:12045 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x13328212, L:/*************:12045 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x13328212, L:/*************:12045 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x13328212, L:/*************:12045 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x13328212, L:/*************:12045 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x13328212, L:/*************:12045 ! R:/*************:8091]) will closed
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x13328212, L:/*************:12045 ! R:/*************:8091]) will closed
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5bcc6d9d, L:/*************:12044 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5bcc6d9d, L:/*************:12044 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5bcc6d9d, L:/*************:12044 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5bcc6d9d, L:/*************:12044 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5bcc6d9d, L:/*************:12044 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5bcc6d9d, L:/*************:12044 ! R:/*************:8091]) will closed
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5bcc6d9d, L:/*************:12044 ! R:/*************:8091]) will closed
2025-07-25 14:55:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753426524556
timestamp=1753426524556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 14:55:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:25 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 14:55:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x13328212, L:/*************:12045 ! R:/*************:8091]
2025-07-25 14:55:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x13328212, L:/*************:12045 ! R:/*************:8091]
2025-07-25 14:55:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 14:55:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x22df9aab, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf3ab0d8e, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753426534556
timestamp=1753426534556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 14:55:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:35 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 14:55:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 14:55:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf592bdb1, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x250e5e43, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753426544556
timestamp=1753426544556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 14:55:45 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:45 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 14:55:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 14:55:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7714318, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0eb32b24, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753426554556
timestamp=1753426554556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 14:55:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:55 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 14:55:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 14:55:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x461051b7, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8cb7cd8c, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753426564556
timestamp=1753426564556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 14:56:05 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:05 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 14:56:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 14:56:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xba7b0223, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7d788284, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753426574556
timestamp=1753426574556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 14:56:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:15 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 14:56:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 14:56:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3490448d, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3775427b, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753426584556
timestamp=1753426584556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 14:56:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:25 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 14:56:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 14:56:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbb8fe56c, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x77d4844d, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753426594567
timestamp=1753426594567
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 14:56:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:35 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 14:56:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 14:56:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x23ff44eb, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaebe2b25, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:41 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 14:56:41 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 14:56:41 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 14:56:41 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-25 14:56:42 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-25 15:13:07 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 15:13:08 [main] INFO  com.ym.system.YmSystemApplication - Starting YmSystemApplication using Java 17.0.10 with PID 37780 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-system\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 15:13:08 [main] INFO  com.ym.system.YmSystemApplication - The following 1 profile is active: "dev"
2025-07-25 15:13:08 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 15:13:08 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-system.yml, group=DEFAULT_GROUP] success
2025-07-25 15:13:08 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 15:13:13 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 15:13:13 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 15:13:13 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 15:13:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 15:13:13 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 15:13:13 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 15:13:13 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 15:13:13 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753427593834
timestamp=1753427593834
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 15:13:13 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x0f7b9ae9, L:/*************:12253 - R:/*************:8091]
2025-07-25 15:13:13 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 62 ms, version:1.7.1,role:TMROLE,channel:[id: 0x0f7b9ae9, L:/*************:12253 - R:/*************:8091]
2025-07-25 15:13:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-25 15:13:14 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 15:13:14 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 15:13:14 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 15:13:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-system] txServiceGroup[yumeng-system-group]
2025-07-25 15:13:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 15:13:15 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 15:13:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 15:13:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 15:13:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 15:13:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@c4e440b
2025-07-25 15:13:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 15:13:16 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 15:13:16 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 15:13:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 15:13:16 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5fd86454, L:/*************:12268 - R:/*************:8091]
2025-07-25 15:13:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x5fd86454, L:/*************:12268 - R:/*************:8091]
2025-07-25 15:13:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 15:13:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 15:13:18 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.service.impl.ApprovalRecordServiceImpl] with name [approvalRecordServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 15:13:19 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 15:13:19 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 15:13:19 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 15:13:19 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 15:13:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.system.controller.system.SysProfileController$$SpringCGLIB$$0] with name [sysProfileController] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 15:13:21 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 15:13:23 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 15:13:23 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 15:13:23 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 15:13:23 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 15:13:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-system *************:9201 register finished
2025-07-25 15:13:27 [main] INFO  com.ym.system.YmSystemApplication - Started YmSystemApplication in 22.578 seconds (process running for 24.126)
2025-07-25 15:13:27 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-system.yml, group=DEFAULT_GROUP
2025-07-25 15:13:27 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 15:13:27 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 15:13:27 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 15:14:24 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 15:14:24 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[113ms]
2025-07-25 15:14:24 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 15:14:24 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[298ms]
2025-07-25 15:14:24 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 15:14:24 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 15:14:24 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 15:14:24 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-25 15:14:24 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 15:14:25 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[95ms]
2025-07-25 15:14:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:14:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:14:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-25 15:14:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[6ms]
2025-07-25 15:14:33 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId]
2025-07-25 15:14:33 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId],SpendTime=[90ms]
2025-07-25 15:14:34 [DubboServerHandler-*************:20881-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 15:14:34 [DubboServerHandler-*************:20881-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[40ms]
2025-07-25 15:14:34 [DubboServerHandler-*************:20881-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 15:14:34 [DubboServerHandler-*************:20881-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[32ms]
2025-07-25 15:14:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:14:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:14:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-25 15:14:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[28ms]
2025-07-25 15:15:52 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*********************************eng-cloud', saveDays=7, branchType=AT}
2025-07-25 15:16:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:16:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:16:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-25 15:16:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-25 15:17:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:17:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:17:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 15:17:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-25 15:29:45 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId]
2025-07-25 15:29:45 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId],SpendTime=[89ms]
2025-07-25 15:29:45 [DubboServerHandler-*************:20881-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 15:29:45 [DubboServerHandler-*************:20881-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[28ms]
2025-07-25 15:29:45 [DubboServerHandler-*************:20881-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 15:29:45 [DubboServerHandler-*************:20881-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[19ms]
2025-07-25 15:30:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 15:30:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 15:30:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 15:30:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[15ms]
2025-07-25 15:38:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:38:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:38:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-25 15:38:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-25 15:44:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:44:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:44:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-25 15:44:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-25 15:50:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:50:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:50:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 15:50:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-25 15:52:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:52:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:52:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[22ms]
2025-07-25 15:52:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-25 15:52:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:52:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 15:52:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 15:52:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:03:00 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 16:03:00 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-07-25 16:17:54 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 16:17:54 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-25 16:25:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:25:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:25:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[40ms]
2025-07-25 16:25:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[43ms]
2025-07-25 16:27:58 [DubboServerHandler-*************:20881-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 16:27:58 [DubboServerHandler-*************:20881-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 16:28:47 [DubboServerHandler-*************:20881-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 16:28:47 [DubboServerHandler-*************:20881-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-25 16:29:26 [DubboServerHandler-*************:20881-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 16:29:26 [DubboServerHandler-*************:20881-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-25 16:30:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:30:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:30:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[23ms]
2025-07-25 16:30:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-25 16:40:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:40:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:40:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[31ms]
2025-07-25 16:40:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[34ms]
2025-07-25 16:41:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:41:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:41:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[17ms]
2025-07-25 16:41:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 16:42:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[27ms]
2025-07-25 16:42:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[29ms]
2025-07-25 16:42:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-25 16:42:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-25 16:42:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 16:42:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[21ms]
2025-07-25 16:42:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 16:42:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-25 16:42:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[2167ms]
2025-07-25 16:42:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[30ms]
2025-07-25 16:42:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[30ms]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[33ms]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[38ms]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 16:42:20 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 16:42:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 16:42:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[27ms]
2025-07-25 16:42:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:42:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 16:42:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:44:52 [DubboServerHandler-*************:20881-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 16:44:52 [DubboServerHandler-*************:20881-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-25 16:48:18 [DubboServerHandler-*************:20881-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 16:48:18 [DubboServerHandler-*************:20881-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 16:48:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:48:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:48:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[34ms]
2025-07-25 16:48:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[36ms]
2025-07-25 16:52:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[31ms]
2025-07-25 16:52:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[33ms]
2025-07-25 16:52:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-25 16:52:02 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[17ms]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 16:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[76ms]
2025-07-25 16:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 16:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[25ms]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[28ms]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[28ms]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[34ms]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 16:52:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:52:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[11ms]
2025-07-25 16:52:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:52:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[4ms]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[30ms]
2025-07-25 16:52:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[31ms]
2025-07-25 16:52:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[64ms]
2025-07-25 16:52:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 16:52:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:52:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 16:52:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:52:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[39ms]
2025-07-25 16:52:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:52:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 16:52:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[83ms]
2025-07-25 16:52:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[29ms]
2025-07-25 16:52:15 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[30ms]
2025-07-25 16:52:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:52:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[35ms]
2025-07-25 16:52:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[35ms]
2025-07-25 16:52:28 [DubboServerHandler-*************:20881-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId]
2025-07-25 16:52:29 [DubboServerHandler-*************:20881-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId],SpendTime=[333ms]
2025-07-25 16:52:30 [DubboServerHandler-*************:20881-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 16:52:30 [DubboServerHandler-*************:20881-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[27ms]
2025-07-25 16:52:30 [DubboServerHandler-*************:20881-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 16:52:30 [DubboServerHandler-*************:20881-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[41ms]
2025-07-25 16:53:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:53:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[72ms]
2025-07-25 16:53:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:53:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:53:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[28ms]
2025-07-25 16:53:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[29ms]
2025-07-25 16:58:29 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:58:29 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 16:58:29 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[30ms]
2025-07-25 16:58:29 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[32ms]
2025-07-25 17:00:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:00:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:00:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 17:00:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 17:03:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:03:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:03:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[24ms]
2025-07-25 17:03:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[26ms]
2025-07-25 17:03:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:03:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:03:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 17:03:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-25 17:03:53 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:03:53 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:03:53 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 17:03:53 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 17:07:31 [DubboServerHandler-*************:20881-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 17:07:31 [DubboServerHandler-*************:20881-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-25 17:10:16 [DubboServerHandler-*************:20881-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 17:10:16 [DubboServerHandler-*************:20881-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 17:10:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:10:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:10:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[23ms]
2025-07-25 17:10:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[25ms]
2025-07-25 17:11:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:11:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:11:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-25 17:11:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-25 17:11:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:11:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:11:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 17:11:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 17:12:20 [DubboServerHandler-*************:20881-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 17:12:20 [DubboServerHandler-*************:20881-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 17:13:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:13:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:13:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[31ms]
2025-07-25 17:13:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[32ms]
2025-07-25 17:13:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:13:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:13:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 17:13:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 17:14:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:14:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 17:14:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 17:14:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 17:49:46 [DubboServerHandler-*************:20881-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:49:47 [DubboServerHandler-*************:20881-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[243ms]
2025-07-25 17:49:47 [DubboServerHandler-*************:20881-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:49:47 [DubboServerHandler-*************:20881-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[36ms]
2025-07-25 17:52:33 [DubboServerHandler-*************:20881-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:52:33 [DubboServerHandler-*************:20881-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[155ms]
2025-07-25 17:52:33 [DubboServerHandler-*************:20881-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:52:33 [DubboServerHandler-*************:20881-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[22ms]
2025-07-25 17:52:55 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-25 17:53:01 [DubboServerHandler-*************:20881-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:53:01 [DubboServerHandler-*************:20881-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[142ms]
2025-07-25 17:53:01 [DubboServerHandler-*************:20881-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:53:01 [DubboServerHandler-*************:20881-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[24ms]
2025-07-25 17:54:02 [DubboServerHandler-*************:20881-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:54:02 [DubboServerHandler-*************:20881-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[149ms]
2025-07-25 17:54:02 [DubboServerHandler-*************:20881-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:54:02 [DubboServerHandler-*************:20881-thread-51] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[24ms]
2025-07-25 17:54:42 [DubboServerHandler-*************:20881-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:54:42 [DubboServerHandler-*************:20881-thread-52] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[155ms]
2025-07-25 17:54:42 [DubboServerHandler-*************:20881-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:54:42 [DubboServerHandler-*************:20881-thread-53] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[29ms]
2025-07-25 17:56:07 [DubboServerHandler-*************:20881-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:56:07 [DubboServerHandler-*************:20881-thread-54] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[142ms]
2025-07-25 17:56:07 [DubboServerHandler-*************:20881-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:56:07 [DubboServerHandler-*************:20881-thread-55] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[24ms]
2025-07-25 17:56:45 [DubboServerHandler-*************:20881-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:56:45 [DubboServerHandler-*************:20881-thread-56] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[136ms]
2025-07-25 17:56:45 [DubboServerHandler-*************:20881-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:56:45 [DubboServerHandler-*************:20881-thread-57] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[22ms]
2025-07-25 17:57:17 [DubboServerHandler-*************:20881-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 17:57:17 [DubboServerHandler-*************:20881-thread-60] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[143ms]
2025-07-25 17:57:17 [DubboServerHandler-*************:20881-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById]
2025-07-25 17:57:17 [DubboServerHandler-*************:20881-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectEmailById],SpendTime=[23ms]
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x5fd86454, L:/*************:12268 - R:/*************:8091] read idle.
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5fd86454, L:/*************:12268 - R:/*************:8091]
2025-07-25 19:13:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5fd86454, L:/*************:12268 - R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5fd86454, L:/*************:12268 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x0f7b9ae9, L:/*************:12253 - R:/*************:8091] read idle.
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x0f7b9ae9, L:/*************:12253 - R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0f7b9ae9, L:/*************:12253 - R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0f7b9ae9, L:/*************:12253 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 19:13:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 19:13:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 19:13:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x9ef1ad58, L:/*************:11185 - R:/*************:8091]
2025-07-25 19:13:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 9 ms, version:1.7.1,role:RMROLE,channel:[id: 0x9ef1ad58, L:/*************:11185 - R:/*************:8091]
2025-07-25 19:13:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 19:13:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753442000803
timestamp=1753442000803
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 19:13:20 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x3e4252c4, L:/*************:11787 - R:/*************:8091]
2025-07-25 19:13:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x3e4252c4, L:/*************:11787 - R:/*************:8091]
2025-07-25 20:19:50 [DubboServerHandler-*************:20881-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:19:50 [DubboServerHandler-*************:20881-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[44ms]
2025-07-25 20:19:50 [DubboServerHandler-*************:20881-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 20:19:51 [DubboServerHandler-*************:20881-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[190ms]
2025-07-25 20:19:51 [DubboServerHandler-*************:20881-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:19:51 [DubboServerHandler-*************:20881-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 20:19:51 [DubboServerHandler-*************:20881-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 20:19:51 [DubboServerHandler-*************:20881-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[3ms]
2025-07-25 20:19:51 [DubboServerHandler-*************:20881-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 20:19:51 [DubboServerHandler-*************:20881-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[86ms]
2025-07-25 20:19:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:19:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:19:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:19:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[3ms]
2025-07-25 20:19:58 [DubboServerHandler-*************:20881-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId]
2025-07-25 20:19:58 [DubboServerHandler-*************:20881-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId],SpendTime=[97ms]
2025-07-25 20:19:59 [DubboServerHandler-*************:20881-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 20:19:59 [DubboServerHandler-*************:20881-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[20ms]
2025-07-25 20:19:59 [DubboServerHandler-*************:20881-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 20:19:59 [DubboServerHandler-*************:20881-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[19ms]
2025-07-25 20:20:22 [DubboServerHandler-*************:20881-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId]
2025-07-25 20:20:23 [DubboServerHandler-*************:20881-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserPlusInfoService],MethodName=[selectDeptIdListByUserId],SpendTime=[95ms]
2025-07-25 20:20:23 [DubboServerHandler-*************:20881-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 20:20:23 [DubboServerHandler-*************:20881-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-25 20:20:23 [DubboServerHandler-*************:20881-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 20:20:23 [DubboServerHandler-*************:20881-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[0ms]
2025-07-25 20:20:27 [DubboServerHandler-*************:20881-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 20:20:27 [DubboServerHandler-*************:20881-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-25 20:20:30 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:30 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:30 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 20:20:30 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[20ms]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 20:20:31 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:20:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 20:20:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:20:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:20:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[18ms]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:20:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[2ms]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:20:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:20:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:20:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:20:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:20:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[1ms]
2025-07-25 20:21:26 [DubboServerHandler-*************:20881-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-25 20:21:26 [DubboServerHandler-*************:20881-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[0ms]
2025-07-25 20:23:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:23:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:23:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:23:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[19ms]
2025-07-25 20:30:15 [DubboServerHandler-*************:20881-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-25 20:30:15 [DubboServerHandler-*************:20881-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[19ms]
2025-07-25 20:32:40 [DubboServerHandler-*************:20881-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:32:40 [DubboServerHandler-*************:20881-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[19ms]
2025-07-25 20:32:40 [DubboServerHandler-*************:20881-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 20:32:40 [DubboServerHandler-*************:20881-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[1ms]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[160ms]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-07-25 20:32:44 [DubboServerHandler-*************:20881-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[68ms]
2025-07-25 20:32:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:32:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds]
2025-07-25 20:32:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:32:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteDeptService],MethodName=[selectDeptNameByIds],SpendTime=[0ms]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[50ms]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [DubboServerHandler-*************:20881-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-25 20:41:38 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 关闭微信消息处理线程池...
2025-07-25 20:41:38 [Thread-24] INFO  c.y.s.c.WechatTemplateController - 微信消息处理线程池已关闭
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x3e4252c4, L:/*************:11787 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x9ef1ad58, L:/*************:11185 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x3e4252c4, L:/*************:11787 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x9ef1ad58, L:/*************:11185 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x9ef1ad58, L:/*************:11185 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x3e4252c4, L:/*************:11787 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3e4252c4, L:/*************:11787 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x9ef1ad58, L:/*************:11185 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3e4252c4, L:/*************:11787 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x9ef1ad58, L:/*************:11185 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3e4252c4, L:/*************:11787 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9ef1ad58, L:/*************:11185 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3e4252c4, L:/*************:11787 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9ef1ad58, L:/*************:11185 ! R:/*************:8091]) will closed
2025-07-25 20:41:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753447300803
timestamp=1753447300803
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 20:41:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 20:41:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x9ef1ad58, L:/*************:11185 ! R:/*************:8091]
2025-07-25 20:41:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x9ef1ad58, L:/*************:11185 ! R:/*************:8091]
2025-07-25 20:41:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 20:41:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdd8a3f12, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:43 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8023693d, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753447310808
timestamp=1753447310808
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 20:41:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 20:41:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 20:41:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x79fdb04c, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x15257e8f, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753447320805
timestamp=1753447320805
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 20:42:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 20:42:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 20:42:02 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xed144df4, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:03 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x14d1df26, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753447330811
timestamp=1753447330811
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 20:42:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 20:42:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 20:42:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdde9cfe3, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8d05fb8e, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753447340806
timestamp=1753447340806
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 20:42:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 20:42:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 20:42:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7222defd, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6ab7c1ea, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753447350812
timestamp=1753447350812
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 20:42:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 20:42:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 20:42:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4cc78e0d, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd12f627c, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753447360803
timestamp=1753447360803
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 20:42:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 20:42:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 20:42:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2db60c45, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:43 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1b35bfc4, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753447370804
timestamp=1753447370804
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 20:42:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 20:42:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 20:42:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3668e970, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x137e3b02, L:null ! R:/*************:8091]) will closed
2025-07-25 20:43:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:43:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753447380812
timestamp=1753447380812
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'} >
2025-07-25 20:43:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:43:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*********************************eng-cloud
2025-07-25 20:43:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*********************************eng-cloud', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'} >
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  c.y.s.c.WechatTemplateController - 应用关闭，销毁微信消息处理线程池...
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-25 20:43:01 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-25 20:43:02 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-25 20:43:02 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-25 20:43:02 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-25 20:43:02 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfd7c31e6, L:null ! R:/*************:8091]) will closed
2025-07-25 20:43:02 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9c390bdc, L:null ! R:/*************:8091]) will closed
