2025-07-21 10:29:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-21 10:29:13 [main] INFO  com.ym.job.YmJobApplication - Starting YmJobApplication using Java 17.0.10 with PID 34208 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-job\target\classes started by qingyi in D:\project\yumeng)
2025-07-21 10:29:13 [main] INFO  com.ym.job.YmJobApplication - The following 1 profile is active: "dev"
2025-07-21 10:29:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-job.yml, group=DEFAULT_GROUP] success
2025-07-21 10:29:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-21 10:29:13 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-21 10:29:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-21 10:29:19 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-21 10:29:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-21 10:29:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-21 10:29:21 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5e774546
2025-07-21 10:29:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-21 10:29:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-21 10:29:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-21 10:29:21 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-21 10:29:21 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-21 10:29:21 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-21 10:29:22 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-21 10:29:23 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-21 10:29:23 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-21 10:29:23 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-21 10:29:24 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-21 10:29:24 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-job *************:9203 register finished
2025-07-21 10:29:26 [main] INFO  com.ym.job.YmJobApplication - Started YmJobApplication in 15.115 seconds (process running for 16.281)
2025-07-21 10:29:26 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.1.2
2025-07-21 10:29:26 [Thread-26] INFO  c.a.s.c.c.r.s.SnailNettyHttpServer - ------> snail-job client remoting server start success, nettype = com.aizuda.snailjob.client.common.rpc.server.SnailNettyHttpServer, port = 29203
2025-07-21 10:29:26 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.1.2
2025-07-21 10:29:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-21 10:29:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-job.yml, group=DEFAULT_GROUP
2025-07-21 10:29:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-21 10:29:27 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 10:46:50 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.1.2
2025-07-21 10:46:50 [Thread-26] INFO  c.a.s.c.c.r.s.SnailNettyHttpServer - --------> snail-job client remoting server stop.
2025-07-21 10:46:50 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-07-21 10:46:50 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-07-21 10:46:50 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.1.2
2025-07-21 10:46:50 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-21 10:46:50 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-21 10:46:50 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-21 10:46:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-21 10:46:50 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-21 10:46:51 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-21 10:46:51 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-21 10:46:51 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-21 10:46:51 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-21 10:46:51 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-21 10:57:47 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-21 10:57:47 [main] INFO  com.ym.job.YmJobApplication - Starting YmJobApplication using Java 17.0.10 with PID 38276 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-job\target\classes started by qingyi in D:\project\yumeng)
2025-07-21 10:57:47 [main] INFO  com.ym.job.YmJobApplication - The following 1 profile is active: "dev"
2025-07-21 10:57:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-job.yml, group=DEFAULT_GROUP] success
2025-07-21 10:57:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-21 10:57:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-21 10:57:51 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-21 10:57:52 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-21 10:57:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-21 10:57:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-21 10:57:53 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5ea4abef
2025-07-21 10:57:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-21 10:57:53 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-21 10:57:53 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-21 10:57:53 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-21 10:57:53 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-21 10:57:53 [redisson-netty-3-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-21 10:57:54 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-21 10:57:55 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-21 10:57:55 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-21 10:57:55 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-21 10:57:55 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-21 10:57:55 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-job *************:9203 register finished
2025-07-21 10:57:57 [main] INFO  com.ym.job.YmJobApplication - Started YmJobApplication in 12.021 seconds (process running for 12.972)
2025-07-21 10:57:57 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.1.2
2025-07-21 10:57:57 [Thread-26] INFO  c.a.s.c.c.r.s.SnailNettyHttpServer - ------> snail-job client remoting server start success, nettype = com.aizuda.snailjob.client.common.rpc.server.SnailNettyHttpServer, port = 29203
2025-07-21 10:57:57 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.1.2
2025-07-21 10:57:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-21 10:57:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-job.yml, group=DEFAULT_GROUP
2025-07-21 10:57:57 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-21 10:57:57 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.1.2
2025-07-21 11:00:53 [Thread-26] INFO  c.a.s.c.c.r.s.SnailNettyHttpServer - --------> snail-job client remoting server stop.
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.1.2
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-21 11:00:53 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-21 16:57:07 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-21 16:57:07 [main] INFO  com.ym.job.YmJobApplication - Starting YmJobApplication using Java 17.0.10 with PID 7184 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-job\target\classes started by qingyi in D:\project\yumeng)
2025-07-21 16:57:07 [main] INFO  com.ym.job.YmJobApplication - The following 1 profile is active: "dev"
2025-07-21 16:57:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-job.yml, group=DEFAULT_GROUP] success
2025-07-21 16:57:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-21 16:57:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-21 16:57:11 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-21 16:57:12 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-21 16:57:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-21 16:57:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-21 16:57:14 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@685f43ca
2025-07-21 16:57:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-21 16:57:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-21 16:57:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-21 16:57:14 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-21 16:57:14 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-21 16:57:14 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-21 16:57:14 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-21 16:57:16 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-21 16:57:16 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-21 16:57:16 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-21 16:57:16 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-21 16:57:16 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-job *************:9203 register finished
2025-07-21 16:57:18 [main] INFO  com.ym.job.YmJobApplication - Started YmJobApplication in 13.497 seconds (process running for 14.519)
2025-07-21 16:57:18 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client is preparing to start... v1.1.2
2025-07-21 16:57:18 [Thread-26] INFO  c.a.s.c.c.r.s.SnailNettyHttpServer - ------> snail-job client remoting server start success, nettype = com.aizuda.snailjob.client.common.rpc.server.SnailNettyHttpServer, port = 29203
2025-07-21 16:57:18 [main] INFO  c.a.s.c.c.r.c.NettyHttpConnectClient - netty client started /*************:7824 connect to server
2025-07-21 16:57:18 [main] INFO  c.a.s.c.c.init.SnailJobStartListener - snail-job client started successfully v1.1.2
2025-07-21 16:57:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-21 16:57:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-job.yml, group=DEFAULT_GROUP
2025-07-21 16:57:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-21 16:57:18 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-21 16:58:09 [snail-netty-server-1] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[254] 任务调度成功. 
2025-07-21 16:58:09 [snail-job-job-254-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":254,"wfContext":{},"changeWfContext":{}}
2025-07-21 16:58:09 [snail-job-job-254-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":254,"wfContext":{},"changeWfContext":{}}
2025-07-21 16:58:09 [snail-job-job-254-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[254] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 16:58:19 [snail-netty-server-2] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[255] 任务调度成功. 
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 开始执行美元对账单自动生成任务...
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 对账周期：Sun Jun 01 00:00:00 CST 2025 至 Mon Jun 30 23:59:59 CST 2025
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[255] [{"status":0,"result":"美元对账单自动生成任务异常：Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.","message":"任务执行失败"}]
2025-07-21 16:58:19 [snail-netty-server-3] INFO  c.a.s.c.job.core.client.JobEndPoint - 任务执行/调度失败执行重试. 重试次数:[1]
2025-07-21 16:58:19 [snail-netty-server-3] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[255] 任务调度成功. 
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 开始执行美元对账单自动生成任务...
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 对账周期：Sun Jun 01 00:00:00 CST 2025 至 Mon Jun 30 23:59:59 CST 2025
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[255] [{"status":0,"result":"美元对账单自动生成任务异常：Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.","message":"任务执行失败"}]
2025-07-21 16:58:19 [snail-netty-server-4] INFO  c.a.s.c.job.core.client.JobEndPoint - 任务执行/调度失败执行重试. 重试次数:[2]
2025-07-21 16:58:19 [snail-netty-server-4] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[255] 任务调度成功. 
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 开始执行美元对账单自动生成任务...
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 对账周期：Sun Jun 01 00:00:00 CST 2025 至 Mon Jun 30 23:59:59 CST 2025
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[255] [{"status":0,"result":"美元对账单自动生成任务异常：Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.","message":"任务执行失败"}]
2025-07-21 16:58:19 [snail-netty-server-5] INFO  c.a.s.c.job.core.client.JobEndPoint - 任务执行/调度失败执行重试. 重试次数:[3]
2025-07-21 16:58:19 [snail-netty-server-5] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[255] 任务调度成功. 
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 开始执行美元对账单自动生成任务...
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 对账周期：Sun Jun 01 00:00:00 CST 2025 至 Mon Jun 30 23:59:59 CST 2025
2025-07-21 16:58:19 [snail-job-job-255-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[255] [{"status":0,"result":"美元对账单自动生成任务异常：Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.","message":"任务执行失败"}]
2025-07-21 16:58:20 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[14]
2025-07-21 16:58:29 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[16]
2025-07-21 16:59:09 [snail-netty-server-6] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[256] 任务调度成功. 
2025-07-21 16:59:09 [snail-job-job-256-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":256,"wfContext":{},"changeWfContext":{}}
2025-07-21 16:59:09 [snail-job-job-256-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":256,"wfContext":{},"changeWfContext":{}}
2025-07-21 16:59:09 [snail-job-job-256-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[256] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 16:59:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[24]
2025-07-21 16:59:41 [snail-netty-server-7] INFO  c.a.s.c.job.core.client.JobEndPoint - 任务执行/调度失败执行重试. 重试次数:[4]
2025-07-21 16:59:41 [snail-netty-server-7] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[255] 任务调度成功. 
2025-07-21 16:59:41 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 开始执行美元对账单自动生成任务...
2025-07-21 16:59:41 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 对账周期：Sun Jun 01 00:00:00 CST 2025 至 Mon Jun 30 23:59:59 CST 2025
2025-07-21 16:59:41 [snail-job-job-255-1] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUsdReconciliationService],MethodName=[generateUsdReconciliation]
2025-07-21 16:59:41 [snail-job-job-255-1] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUsdReconciliationService],MethodName=[generateUsdReconciliation],SpendTime=[585ms]
2025-07-21 16:59:41 [snail-job-job-255-1] INFO  c.y.j.s.UsdReconciliationJobExecutor - 美元对账单自动生成任务完成，成功：3，失败：0
2025-07-21 16:59:41 [snail-job-job-255-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[255] [{"status":1,"result":"美元对账单自动生成任务完成，成功：3，失败：0","message":"任务执行成功"}]
2025-07-21 16:59:51 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[29]
2025-07-21 17:00:09 [snail-netty-server-8] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[257] 任务调度成功. 
2025-07-21 17:00:09 [snail-job-job-257-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":257,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:00:09 [snail-job-job-257-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":257,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:00:09 [snail-job-job-257-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[257] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:00:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[35]
2025-07-21 17:01:09 [snail-netty-server-9] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[258] 任务调度成功. 
2025-07-21 17:01:09 [snail-job-job-258-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":258,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:01:09 [snail-job-job-258-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":258,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:01:09 [snail-job-job-258-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[258] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:01:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[44]
2025-07-21 17:02:09 [snail-netty-server-10] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[259] 任务调度成功. 
2025-07-21 17:02:09 [snail-job-job-259-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":259,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:02:09 [snail-job-job-259-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":259,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:02:09 [snail-job-job-259-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[259] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:02:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[53]
2025-07-21 17:03:09 [snail-netty-server-11] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[260] 任务调度成功. 
2025-07-21 17:03:09 [snail-job-job-260-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":260,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:03:09 [snail-job-job-260-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":260,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:03:09 [snail-job-job-260-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[260] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:03:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[62]
2025-07-21 17:04:09 [snail-netty-server-12] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[261] 任务调度成功. 
2025-07-21 17:04:09 [snail-job-job-261-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":261,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:04:09 [snail-job-job-261-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":261,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:04:09 [snail-job-job-261-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[261] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:04:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[71]
2025-07-21 17:05:09 [snail-netty-server-13] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[262] 任务调度成功. 
2025-07-21 17:05:09 [snail-job-job-262-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":262,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:05:09 [snail-job-job-262-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":262,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:05:09 [snail-job-job-262-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[262] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:05:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[80]
2025-07-21 17:06:09 [snail-netty-server-14] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[263] 任务调度成功. 
2025-07-21 17:06:09 [snail-job-job-263-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":263,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:06:09 [snail-job-job-263-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":263,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:06:09 [snail-job-job-263-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[263] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:06:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[89]
2025-07-21 17:07:09 [snail-netty-server-15] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[264] 任务调度成功. 
2025-07-21 17:07:09 [snail-job-job-264-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":264,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:07:09 [snail-job-job-264-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":264,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:07:09 [snail-job-job-264-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[264] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:07:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[98]
2025-07-21 17:08:09 [snail-netty-server-16] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[265] 任务调度成功. 
2025-07-21 17:08:09 [snail-job-job-265-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":265,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:08:09 [snail-job-job-265-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":265,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:08:09 [snail-job-job-265-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[265] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:08:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[107]
2025-07-21 17:09:09 [snail-netty-server-1] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[266] 任务调度成功. 
2025-07-21 17:09:09 [snail-job-job-266-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":266,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:09:09 [snail-job-job-266-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":266,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:09:09 [snail-job-job-266-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[266] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:09:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[116]
2025-07-21 17:10:09 [snail-netty-server-2] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[267] 任务调度成功. 
2025-07-21 17:10:09 [snail-job-job-267-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":267,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:10:09 [snail-job-job-267-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":267,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:10:09 [snail-job-job-267-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[267] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:10:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[125]
2025-07-21 17:11:09 [snail-netty-server-3] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[268] 任务调度成功. 
2025-07-21 17:11:09 [snail-job-job-268-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":268,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:11:09 [snail-job-job-268-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":268,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:11:09 [snail-job-job-268-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[268] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:11:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[134]
2025-07-21 17:12:09 [snail-netty-server-4] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[269] 任务调度成功. 
2025-07-21 17:12:09 [snail-job-job-269-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":269,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:12:09 [snail-job-job-269-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":269,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:12:09 [snail-job-job-269-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[269] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:12:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[143]
2025-07-21 17:13:09 [snail-netty-server-5] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[270] 任务调度成功. 
2025-07-21 17:13:09 [snail-job-job-270-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":270,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:13:09 [snail-job-job-270-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":270,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:13:09 [snail-job-job-270-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[270] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:13:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[152]
2025-07-21 17:14:09 [snail-netty-server-6] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[271] 任务调度成功. 
2025-07-21 17:14:09 [snail-job-job-271-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":271,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:14:09 [snail-job-job-271-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":271,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:14:09 [snail-job-job-271-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[271] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:14:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[161]
2025-07-21 17:15:09 [snail-netty-server-7] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[272] 任务调度成功. 
2025-07-21 17:15:09 [snail-job-job-272-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":272,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:15:09 [snail-job-job-272-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":272,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:15:09 [snail-job-job-272-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[272] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:15:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[170]
2025-07-21 17:16:09 [snail-netty-server-8] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[273] 任务调度成功. 
2025-07-21 17:16:09 [snail-job-job-273-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":273,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:16:09 [snail-job-job-273-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":273,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:16:09 [snail-job-job-273-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[273] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:16:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[179]
2025-07-21 17:17:09 [snail-netty-server-9] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[274] 任务调度成功. 
2025-07-21 17:17:09 [snail-job-job-274-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":274,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:17:09 [snail-job-job-274-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":274,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:17:09 [snail-job-job-274-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[274] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:17:19 [nioEventLoopGroup-2-1] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[188]
2025-07-21 17:18:09 [snail-netty-server-10] INFO  c.a.s.c.job.core.client.JobEndPoint - 批次:[275] 任务调度成功. 
2025-07-21 17:18:09 [snail-job-job-275-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":275,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:18:09 [snail-job-job-275-1] INFO  c.y.job.snailjob.TestAnnoJobExecutor - testJobExecutor. jobArgs:{"argsStr":"null","executorInfo":"testJobExecutor","taskBatchId":275,"wfContext":{},"changeWfContext":{}}
2025-07-21 17:18:09 [snail-job-job-275-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - 任务执行成功 taskBatchId:[275] [{"status":1,"result":"测试成功","message":"任务执行成功"}]
2025-07-21 17:18:14 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.1.2
2025-07-21 17:18:14 [Thread-26] INFO  c.a.s.c.c.r.s.SnailNettyHttpServer - --------> snail-job client remoting server stop.
2025-07-21 17:18:14 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.1.2
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-21 17:18:15 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
