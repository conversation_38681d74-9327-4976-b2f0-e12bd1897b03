2025-07-21 10:29:26 [main] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:29:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":1}] 
2025-07-21 10:29:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":0,"args":["PING"]}] 
2025-07-21 10:29:36 [nioEventLoopGroup-2-2] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:29:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":2,"args":["PING"]}] 
2025-07-21 10:29:46 [nioEventLoopGroup-2-3] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:29:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":3,"args":["PING"]}] 
2025-07-21 10:29:56 [nioEventLoopGroup-2-4] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:29:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":4,"args":["PING"]}] 
2025-07-21 10:30:06 [nioEventLoopGroup-2-5] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:30:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":5,"args":["PING"]}] 
2025-07-21 10:30:16 [nioEventLoopGroup-2-6] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:30:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":6,"args":["PING"]}] 
2025-07-21 10:30:26 [nioEventLoopGroup-2-7] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:30:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":8}] 
2025-07-21 10:30:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":7,"args":["PING"]}] 
2025-07-21 10:30:27 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[0] message:[Request to remote interface timed out.]
2025-07-21 10:30:36 [nioEventLoopGroup-2-8] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:30:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":9,"args":["PING"]}] 
2025-07-21 10:30:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[2] message:[Request to remote interface timed out.]
2025-07-21 10:30:46 [nioEventLoopGroup-2-9] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:30:46 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[3] message:[Request to remote interface timed out.]
2025-07-21 10:30:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":10,"args":["PING"]}] 
2025-07-21 10:30:56 [nioEventLoopGroup-2-10] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:30:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":11,"args":["PING"]}] 
2025-07-21 10:30:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[4] message:[Request to remote interface timed out.]
2025-07-21 10:31:06 [nioEventLoopGroup-2-11] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:31:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":12,"args":["PING"]}] 
2025-07-21 10:31:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[5] message:[Request to remote interface timed out.]
2025-07-21 10:31:16 [nioEventLoopGroup-2-12] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:31:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":13,"args":["PING"]}] 
2025-07-21 10:31:17 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[6] message:[Request to remote interface timed out.]
2025-07-21 10:31:26 [nioEventLoopGroup-2-13] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:31:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[7] message:[Request to remote interface timed out.]
2025-07-21 10:31:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":14,"args":["PING"]}] 
2025-07-21 10:31:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":15}] 
2025-07-21 10:31:36 [nioEventLoopGroup-2-14] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:31:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":16,"args":["PING"]}] 
2025-07-21 10:31:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[9] message:[Request to remote interface timed out.]
2025-07-21 10:31:46 [nioEventLoopGroup-2-15] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:31:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":17,"args":["PING"]}] 
2025-07-21 10:31:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[10] message:[Request to remote interface timed out.]
2025-07-21 10:31:56 [nioEventLoopGroup-2-16] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:31:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":18,"args":["PING"]}] 
2025-07-21 10:31:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[11] message:[Request to remote interface timed out.]
2025-07-21 10:32:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":19,"args":["PING"]}] 
2025-07-21 10:32:06 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[12] message:[Request to remote interface timed out.]
2025-07-21 10:32:06 [nioEventLoopGroup-2-17] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:32:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":20,"args":["PING"]}] 
2025-07-21 10:32:16 [nioEventLoopGroup-2-18] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:32:17 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[13] message:[Request to remote interface timed out.]
2025-07-21 10:32:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":21}] 
2025-07-21 10:32:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":22,"args":["PING"]}] 
2025-07-21 10:32:26 [nioEventLoopGroup-2-19] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:32:27 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[14] message:[Request to remote interface timed out.]
2025-07-21 10:32:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":23,"args":["PING"]}] 
2025-07-21 10:32:36 [nioEventLoopGroup-2-20] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:32:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[16] message:[Request to remote interface timed out.]
2025-07-21 10:32:46 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[17] message:[Request to remote interface timed out.]
2025-07-21 10:32:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":24,"args":["PING"]}] 
2025-07-21 10:32:46 [nioEventLoopGroup-2-21] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:32:56 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[18] message:[Request to remote interface timed out.]
2025-07-21 10:32:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":25,"args":["PING"]}] 
2025-07-21 10:32:56 [nioEventLoopGroup-2-22] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:33:06 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[19] message:[Request to remote interface timed out.]
2025-07-21 10:33:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":26,"args":["PING"]}] 
2025-07-21 10:33:06 [nioEventLoopGroup-2-23] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:33:16 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[20] message:[Request to remote interface timed out.]
2025-07-21 10:33:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":27,"args":["PING"]}] 
2025-07-21 10:33:16 [nioEventLoopGroup-2-24] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:33:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":28}] 
2025-07-21 10:33:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":29,"args":["PING"]}] 
2025-07-21 10:33:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[22] message:[Request to remote interface timed out.]
2025-07-21 10:33:26 [nioEventLoopGroup-2-25] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:33:36 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[23] message:[Request to remote interface timed out.]
2025-07-21 10:33:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":30,"args":["PING"]}] 
2025-07-21 10:33:36 [nioEventLoopGroup-2-26] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:33:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":31,"args":["PING"]}] 
2025-07-21 10:33:46 [nioEventLoopGroup-2-27] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:33:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[24] message:[Request to remote interface timed out.]
2025-07-21 10:33:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":32,"args":["PING"]}] 
2025-07-21 10:33:56 [nioEventLoopGroup-2-28] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:33:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[25] message:[Request to remote interface timed out.]
2025-07-21 10:34:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":33,"args":["PING"]}] 
2025-07-21 10:34:06 [nioEventLoopGroup-2-29] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:34:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[26] message:[Request to remote interface timed out.]
2025-07-21 10:34:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":34,"args":["PING"]}] 
2025-07-21 10:34:16 [nioEventLoopGroup-2-30] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:34:17 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[27] message:[Request to remote interface timed out.]
2025-07-21 10:34:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":35,"args":["PING"]}] 
2025-07-21 10:34:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":36}] 
2025-07-21 10:34:26 [nioEventLoopGroup-2-31] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:34:27 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[29] message:[Request to remote interface timed out.]
2025-07-21 10:34:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":37,"args":["PING"]}] 
2025-07-21 10:34:36 [nioEventLoopGroup-2-32] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:34:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[30] message:[Request to remote interface timed out.]
2025-07-21 10:34:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":38,"args":["PING"]}] 
2025-07-21 10:34:46 [nioEventLoopGroup-2-1] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:34:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[31] message:[Request to remote interface timed out.]
2025-07-21 10:34:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":39,"args":["PING"]}] 
2025-07-21 10:34:56 [nioEventLoopGroup-2-2] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:34:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[32] message:[Request to remote interface timed out.]
2025-07-21 10:35:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":40,"args":["PING"]}] 
2025-07-21 10:35:06 [nioEventLoopGroup-2-3] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:35:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[33] message:[Request to remote interface timed out.]
2025-07-21 10:35:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":41,"args":["PING"]}] 
2025-07-21 10:35:16 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[34] message:[Request to remote interface timed out.]
2025-07-21 10:35:16 [nioEventLoopGroup-2-4] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:35:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":42,"args":["PING"]}] 
2025-07-21 10:35:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[35] message:[Request to remote interface timed out.]
2025-07-21 10:35:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":43}] 
2025-07-21 10:35:26 [nioEventLoopGroup-2-5] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:35:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":44,"args":["PING"]}] 
2025-07-21 10:35:36 [nioEventLoopGroup-2-6] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:35:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[37] message:[Request to remote interface timed out.]
2025-07-21 10:35:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":45,"args":["PING"]}] 
2025-07-21 10:35:46 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[38] message:[Request to remote interface timed out.]
2025-07-21 10:35:46 [nioEventLoopGroup-2-7] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:35:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":46,"args":["PING"]}] 
2025-07-21 10:35:56 [nioEventLoopGroup-2-8] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:35:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[39] message:[Request to remote interface timed out.]
2025-07-21 10:36:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":47,"args":["PING"]}] 
2025-07-21 10:36:06 [nioEventLoopGroup-2-9] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:36:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[40] message:[Request to remote interface timed out.]
2025-07-21 10:36:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":48,"args":["PING"]}] 
2025-07-21 10:36:16 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[41] message:[Request to remote interface timed out.]
2025-07-21 10:36:16 [nioEventLoopGroup-2-10] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:36:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":49}] 
2025-07-21 10:36:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":50,"args":["PING"]}] 
2025-07-21 10:36:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[42] message:[Request to remote interface timed out.]
2025-07-21 10:36:26 [nioEventLoopGroup-2-11] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:36:36 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[44] message:[Request to remote interface timed out.]
2025-07-21 10:36:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":51,"args":["PING"]}] 
2025-07-21 10:36:36 [nioEventLoopGroup-2-12] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:36:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":52,"args":["PING"]}] 
2025-07-21 10:36:46 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[45] message:[Request to remote interface timed out.]
2025-07-21 10:36:46 [nioEventLoopGroup-2-13] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:36:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":53,"args":["PING"]}] 
2025-07-21 10:36:56 [nioEventLoopGroup-2-14] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:36:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[46] message:[Request to remote interface timed out.]
2025-07-21 10:37:06 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[47] message:[Request to remote interface timed out.]
2025-07-21 10:37:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":54,"args":["PING"]}] 
2025-07-21 10:37:06 [nioEventLoopGroup-2-15] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:37:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":55,"args":["PING"]}] 
2025-07-21 10:37:16 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[48] message:[Request to remote interface timed out.]
2025-07-21 10:37:16 [nioEventLoopGroup-2-16] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:37:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":57}] 
2025-07-21 10:37:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":56,"args":["PING"]}] 
2025-07-21 10:37:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[50] message:[Request to remote interface timed out.]
2025-07-21 10:37:26 [nioEventLoopGroup-2-17] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:37:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":58,"args":["PING"]}] 
2025-07-21 10:37:36 [nioEventLoopGroup-2-18] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:37:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[51] message:[Request to remote interface timed out.]
2025-07-21 10:37:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":59,"args":["PING"]}] 
2025-07-21 10:37:46 [nioEventLoopGroup-2-19] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:37:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[52] message:[Request to remote interface timed out.]
2025-07-21 10:37:56 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[53] message:[Request to remote interface timed out.]
2025-07-21 10:37:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":60,"args":["PING"]}] 
2025-07-21 10:37:57 [nioEventLoopGroup-2-20] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:38:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":61,"args":["PING"]}] 
2025-07-21 10:38:07 [nioEventLoopGroup-2-21] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:38:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[54] message:[Request to remote interface timed out.]
2025-07-21 10:38:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":62,"args":["PING"]}] 
2025-07-21 10:38:17 [nioEventLoopGroup-2-22] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:38:17 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[55] message:[Request to remote interface timed out.]
2025-07-21 10:38:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":63,"args":["PING"]}] 
2025-07-21 10:38:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":64}] 
2025-07-21 10:38:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[56] message:[Request to remote interface timed out.]
2025-07-21 10:38:27 [nioEventLoopGroup-2-23] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:38:36 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[58] message:[Request to remote interface timed out.]
2025-07-21 10:38:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":65,"args":["PING"]}] 
2025-07-21 10:38:37 [nioEventLoopGroup-2-24] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:38:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":66,"args":["PING"]}] 
2025-07-21 10:38:47 [nioEventLoopGroup-2-25] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:38:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[59] message:[Request to remote interface timed out.]
2025-07-21 10:38:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":67,"args":["PING"]}] 
2025-07-21 10:38:57 [nioEventLoopGroup-2-26] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:38:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[60] message:[Request to remote interface timed out.]
2025-07-21 10:39:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":68,"args":["PING"]}] 
2025-07-21 10:39:07 [nioEventLoopGroup-2-27] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:39:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[61] message:[Request to remote interface timed out.]
2025-07-21 10:39:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":69,"args":["PING"]}] 
2025-07-21 10:39:17 [nioEventLoopGroup-2-28] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:39:17 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[62] message:[Request to remote interface timed out.]
2025-07-21 10:39:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":71}] 
2025-07-21 10:39:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":70,"args":["PING"]}] 
2025-07-21 10:39:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[63] message:[Request to remote interface timed out.]
2025-07-21 10:39:27 [nioEventLoopGroup-2-29] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:39:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":72,"args":["PING"]}] 
2025-07-21 10:39:37 [nioEventLoopGroup-2-30] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:39:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[65] message:[Request to remote interface timed out.]
2025-07-21 10:39:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":73,"args":["PING"]}] 
2025-07-21 10:39:47 [nioEventLoopGroup-2-31] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:39:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[66] message:[Request to remote interface timed out.]
2025-07-21 10:39:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":74,"args":["PING"]}] 
2025-07-21 10:39:57 [nioEventLoopGroup-2-32] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:39:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[67] message:[Request to remote interface timed out.]
2025-07-21 10:40:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":75,"args":["PING"]}] 
2025-07-21 10:40:07 [nioEventLoopGroup-2-1] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:40:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[68] message:[Request to remote interface timed out.]
2025-07-21 10:40:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":76,"args":["PING"]}] 
2025-07-21 10:40:17 [nioEventLoopGroup-2-2] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:40:17 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[69] message:[Request to remote interface timed out.]
2025-07-21 10:40:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":77,"args":["PING"]}] 
2025-07-21 10:40:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":78}] 
2025-07-21 10:40:27 [nioEventLoopGroup-2-3] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:40:27 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[70] message:[Request to remote interface timed out.]
2025-07-21 10:40:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":79,"args":["PING"]}] 
2025-07-21 10:40:36 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[72] message:[Request to remote interface timed out.]
2025-07-21 10:40:37 [nioEventLoopGroup-2-4] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:40:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":80,"args":["PING"]}] 
2025-07-21 10:40:47 [nioEventLoopGroup-2-5] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:40:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[73] message:[Request to remote interface timed out.]
2025-07-21 10:40:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":81,"args":["PING"]}] 
2025-07-21 10:40:57 [nioEventLoopGroup-2-6] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:40:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[74] message:[Request to remote interface timed out.]
2025-07-21 10:41:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":82,"args":["PING"]}] 
2025-07-21 10:41:06 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[75] message:[Request to remote interface timed out.]
2025-07-21 10:41:07 [nioEventLoopGroup-2-7] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:41:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":83,"args":["PING"]}] 
2025-07-21 10:41:16 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[76] message:[Request to remote interface timed out.]
2025-07-21 10:41:17 [nioEventLoopGroup-2-8] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:41:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[77] message:[Request to remote interface timed out.]
2025-07-21 10:41:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":85}] 
2025-07-21 10:41:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":84,"args":["PING"]}] 
2025-07-21 10:41:27 [nioEventLoopGroup-2-9] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:41:36 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[79] message:[Request to remote interface timed out.]
2025-07-21 10:41:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":86,"args":["PING"]}] 
2025-07-21 10:41:37 [nioEventLoopGroup-2-10] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:41:46 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[80] message:[Request to remote interface timed out.]
2025-07-21 10:41:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":87,"args":["PING"]}] 
2025-07-21 10:41:47 [nioEventLoopGroup-2-11] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:41:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":88,"args":["PING"]}] 
2025-07-21 10:41:57 [nioEventLoopGroup-2-12] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:41:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[81] message:[Request to remote interface timed out.]
2025-07-21 10:42:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":89,"args":["PING"]}] 
2025-07-21 10:42:06 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[82] message:[Request to remote interface timed out.]
2025-07-21 10:42:07 [nioEventLoopGroup-2-13] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:42:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":90,"args":["PING"]}] 
2025-07-21 10:42:16 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[83] message:[Request to remote interface timed out.]
2025-07-21 10:42:17 [nioEventLoopGroup-2-14] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:42:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":91,"args":["PING"]}] 
2025-07-21 10:42:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":92}] 
2025-07-21 10:42:27 [nioEventLoopGroup-2-15] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:42:27 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[84] message:[Request to remote interface timed out.]
2025-07-21 10:42:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":93,"args":["PING"]}] 
2025-07-21 10:42:37 [nioEventLoopGroup-2-16] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:42:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[86] message:[Request to remote interface timed out.]
2025-07-21 10:42:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":94,"args":["PING"]}] 
2025-07-21 10:42:47 [nioEventLoopGroup-2-17] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:42:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[87] message:[Request to remote interface timed out.]
2025-07-21 10:42:56 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[88] message:[Request to remote interface timed out.]
2025-07-21 10:42:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":95,"args":["PING"]}] 
2025-07-21 10:42:57 [nioEventLoopGroup-2-18] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:43:06 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[89] message:[Request to remote interface timed out.]
2025-07-21 10:43:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":96,"args":["PING"]}] 
2025-07-21 10:43:07 [nioEventLoopGroup-2-19] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:43:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":97,"args":["PING"]}] 
2025-07-21 10:43:17 [nioEventLoopGroup-2-20] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:43:17 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[90] message:[Request to remote interface timed out.]
2025-07-21 10:43:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":99}] 
2025-07-21 10:43:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":98,"args":["PING"]}] 
2025-07-21 10:43:27 [nioEventLoopGroup-2-21] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:43:27 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[91] message:[Request to remote interface timed out.]
2025-07-21 10:43:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":100,"args":["PING"]}] 
2025-07-21 10:43:37 [nioEventLoopGroup-2-22] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:43:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[93] message:[Request to remote interface timed out.]
2025-07-21 10:43:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":101,"args":["PING"]}] 
2025-07-21 10:43:47 [nioEventLoopGroup-2-23] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:43:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[94] message:[Request to remote interface timed out.]
2025-07-21 10:43:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":102,"args":["PING"]}] 
2025-07-21 10:43:57 [nioEventLoopGroup-2-24] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:43:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[95] message:[Request to remote interface timed out.]
2025-07-21 10:44:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":103,"args":["PING"]}] 
2025-07-21 10:44:07 [nioEventLoopGroup-2-25] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:44:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[96] message:[Request to remote interface timed out.]
2025-07-21 10:44:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":104,"args":["PING"]}] 
2025-07-21 10:44:16 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[97] message:[Request to remote interface timed out.]
2025-07-21 10:44:17 [nioEventLoopGroup-2-26] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:44:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[98] message:[Request to remote interface timed out.]
2025-07-21 10:44:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":106}] 
2025-07-21 10:44:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":105,"args":["PING"]}] 
2025-07-21 10:44:27 [nioEventLoopGroup-2-27] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:44:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":107,"args":["PING"]}] 
2025-07-21 10:44:37 [nioEventLoopGroup-2-28] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:44:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[100] message:[Request to remote interface timed out.]
2025-07-21 10:44:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":108,"args":["PING"]}] 
2025-07-21 10:44:46 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[101] message:[Request to remote interface timed out.]
2025-07-21 10:44:47 [nioEventLoopGroup-2-29] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:44:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":109,"args":["PING"]}] 
2025-07-21 10:44:57 [nioEventLoopGroup-2-30] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:44:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[102] message:[Request to remote interface timed out.]
2025-07-21 10:45:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":110,"args":["PING"]}] 
2025-07-21 10:45:07 [nioEventLoopGroup-2-31] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:45:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[103] message:[Request to remote interface timed out.]
2025-07-21 10:45:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":111,"args":["PING"]}] 
2025-07-21 10:45:16 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[104] message:[Request to remote interface timed out.]
2025-07-21 10:45:17 [nioEventLoopGroup-2-32] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:45:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":112,"args":["PING"]}] 
2025-07-21 10:45:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":113}] 
2025-07-21 10:45:27 [nioEventLoopGroup-2-1] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:45:27 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[105] message:[Request to remote interface timed out.]
2025-07-21 10:45:36 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[107] message:[Request to remote interface timed out.]
2025-07-21 10:45:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":114,"args":["PING"]}] 
2025-07-21 10:45:37 [nioEventLoopGroup-2-2] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:45:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":115,"args":["PING"]}] 
2025-07-21 10:45:47 [nioEventLoopGroup-2-3] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:45:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[108] message:[Request to remote interface timed out.]
2025-07-21 10:45:56 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":116,"args":["PING"]}] 
2025-07-21 10:45:57 [nioEventLoopGroup-2-4] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:45:57 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[109] message:[Request to remote interface timed out.]
2025-07-21 10:46:06 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":117,"args":["PING"]}] 
2025-07-21 10:46:07 [nioEventLoopGroup-2-5] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:46:07 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[110] message:[Request to remote interface timed out.]
2025-07-21 10:46:16 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":118,"args":["PING"]}] 
2025-07-21 10:46:17 [nioEventLoopGroup-2-6] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:46:17 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[111] message:[Request to remote interface timed out.]
2025-07-21 10:46:26 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":120}] 
2025-07-21 10:46:26 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[112] message:[Request to remote interface timed out.]
2025-07-21 10:46:26 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":119,"args":["PING"]}] 
2025-07-21 10:46:27 [nioEventLoopGroup-2-7] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:46:36 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":121,"args":["PING"]}] 
2025-07-21 10:46:37 [nioEventLoopGroup-2-8] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:46:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[114] message:[Request to remote interface timed out.]
2025-07-21 10:46:46 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":122,"args":["PING"]}] 
2025-07-21 10:46:47 [nioEventLoopGroup-2-9] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:46:47 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[115] message:[Request to remote interface timed out.]
2025-07-21 10:57:57 [main] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:57:57 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":1,"args":["PING"]}] 
2025-07-21 10:57:57 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":0}] 
2025-07-21 10:58:07 [nioEventLoopGroup-2-2] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:58:07 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":2,"args":["PING"]}] 
2025-07-21 10:58:17 [nioEventLoopGroup-2-3] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:58:17 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":3,"args":["PING"]}] 
2025-07-21 10:58:27 [nioEventLoopGroup-2-4] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:58:27 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":4,"args":["PING"]}] 
2025-07-21 10:58:37 [nioEventLoopGroup-2-5] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:58:37 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":5,"args":["PING"]}] 
2025-07-21 10:58:47 [nioEventLoopGroup-2-6] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:58:47 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":6,"args":["PING"]}] 
2025-07-21 10:58:57 [nioEventLoopGroup-2-7] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:58:57 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":7,"args":["PING"]}] 
2025-07-21 10:58:57 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":8}] 
2025-07-21 10:58:58 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[1] message:[Request to remote interface timed out.]
2025-07-21 10:59:07 [nioEventLoopGroup-2-8] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:59:07 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":9,"args":["PING"]}] 
2025-07-21 10:59:08 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[2] message:[Request to remote interface timed out.]
2025-07-21 10:59:17 [nioEventLoopGroup-2-9] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:59:17 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":10,"args":["PING"]}] 
2025-07-21 10:59:18 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[3] message:[Request to remote interface timed out.]
2025-07-21 10:59:27 [nioEventLoopGroup-2-10] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:59:27 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":11,"args":["PING"]}] 
2025-07-21 10:59:27 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[4] message:[Request to remote interface timed out.]
2025-07-21 10:59:37 [nioEventLoopGroup-2-11] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:59:37 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":12,"args":["PING"]}] 
2025-07-21 10:59:37 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[5] message:[Request to remote interface timed out.]
2025-07-21 10:59:47 [nioEventLoopGroup-2-12] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:59:47 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":13,"args":["PING"]}] 
2025-07-21 10:59:48 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[6] message:[Request to remote interface timed out.]
2025-07-21 10:59:57 [nioEventLoopGroup-2-13] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 10:59:57 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":14,"args":["PING"]}] 
2025-07-21 10:59:57 [sync-remote-config] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/sync/version] method:[POST] body:[{"reqId":15}] 
2025-07-21 10:59:58 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[7] message:[Request to remote interface timed out.]
2025-07-21 11:00:07 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":16,"args":["PING"]}] 
2025-07-21 11:00:07 [nioEventLoopGroup-2-14] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 11:00:08 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[9] message:[Request to remote interface timed out.]
2025-07-21 11:00:17 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[10] message:[Request to remote interface timed out.]
2025-07-21 11:00:17 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":17,"args":["PING"]}] 
2025-07-21 11:00:17 [nioEventLoopGroup-2-15] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 11:00:27 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":18,"args":["PING"]}] 
2025-07-21 11:00:27 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[11] message:[Request to remote interface timed out.]
2025-07-21 11:00:27 [nioEventLoopGroup-2-16] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 11:00:37 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":19,"args":["PING"]}] 
2025-07-21 11:00:37 [nioEventLoopGroup-2-17] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 11:00:38 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[12] message:[Request to remote interface timed out.]
2025-07-21 11:00:47 [sj-client-register] ERROR c.a.s.c.c.rpc.client.NettyChannel - send message but channel is null url:[/beat] method:[POST] body:[{"reqId":20,"args":["PING"]}] 
2025-07-21 11:00:47 [nioEventLoopGroup-2-18] ERROR c.a.s.c.c.r.c.NettyHttpConnectClient - connect error:Connection refused: no further information: /127.0.0.1:17888
2025-07-21 11:00:48 [snail-job-rpc-timeout-1] ERROR c.a.s.c.c.handler.ClientRegister - heartbeat check requestId:[13] message:[Request to remote interface timed out.]
2025-07-21 16:58:19 [snail-job-job-255-1] ERROR c.y.j.s.UsdReconciliationJobExecutor - 美元对账单自动生成任务异常：Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.
org.apache.dubbo.rpc.RpcException: Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.checkInvokers(AbstractClusterInvoker.java:397)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.invoke(AbstractClusterInvoker.java:358)
	at org.apache.dubbo.rpc.cluster.router.RouterSnapshotFilter.invoke(RouterSnapshotFilter.java:46)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.MetricsClusterFilter.invoke(MetricsClusterFilter.java:57)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.protocol.dubbo.filter.FutureFilter.invoke(FutureFilter.java:52)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ObservationSenderFilter.invoke(ObservationSenderFilter.java:62)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.spring.security.filter.ContextHolderParametersSelectedTransferFilter.invoke(ContextHolderParametersSelectedTransferFilter.java:40)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)
	at org.apache.dubbo.rpc.cluster.filter.support.MetricsConsumerFilter.invoke(MetricsConsumerFilter.java:38)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ConsumerClassLoaderFilter.invoke(ConsumerClassLoaderFilter.java:40)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ConsumerContextFilter.invoke(ConsumerContextFilter.java:119)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)
	at org.apache.dubbo.rpc.cluster.support.wrapper.AbstractCluster$ClusterFilterInvoker.invoke(AbstractCluster.java:101)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.invoke(MockClusterInvoker.java:106)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.invoke(ScopeClusterInvoker.java:171)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.invoke(MigrationInvoker.java:296)
	at org.apache.dubbo.rpc.proxy.InvocationUtil.invoke(InvocationUtil.java:64)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:81)
	at com.ym.salary.api.RemoteUsdReconciliationServiceDubboProxy0.generateUsdReconciliation(RemoteUsdReconciliationServiceDubboProxy0.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.dubbo.config.spring.util.LazyTargetInvocationHandler.invoke(LazyTargetInvocationHandler.java:54)
	at com.ym.salary.api.RemoteUsdReconciliationServiceDubboProxy0.generateUsdReconciliation(RemoteUsdReconciliationServiceDubboProxy0.java)
	at com.ym.job.snailjob.UsdReconciliationJobExecutor.jobExecute(UsdReconciliationJobExecutor.java:59)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at com.aizuda.snailjob.client.job.core.executor.AnnotationJobExecutor.doJobExecute(AnnotationJobExecutor.java:26)
	at com.aizuda.snailjob.client.job.core.executor.AbstractJobExecutor.lambda$jobExecute$0(AbstractJobExecutor.java:78)
	at com.google.common.util.concurrent.TrustedListenableFutureTask$TrustedFutureInterruptibleTask.runInterruptibly(TrustedListenableFutureTask.java:131)
	at com.google.common.util.concurrent.InterruptibleTask.run(InterruptibleTask.java:75)
	at com.google.common.util.concurrent.TrustedListenableFutureTask.run(TrustedListenableFutureTask.java:82)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-21 16:58:19 [snail-job-job-255-1] ERROR c.y.j.s.UsdReconciliationJobExecutor - 美元对账单自动生成任务异常：Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.
org.apache.dubbo.rpc.RpcException: Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.checkInvokers(AbstractClusterInvoker.java:397)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.invoke(AbstractClusterInvoker.java:358)
	at org.apache.dubbo.rpc.cluster.router.RouterSnapshotFilter.invoke(RouterSnapshotFilter.java:46)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.MetricsClusterFilter.invoke(MetricsClusterFilter.java:57)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.protocol.dubbo.filter.FutureFilter.invoke(FutureFilter.java:52)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ObservationSenderFilter.invoke(ObservationSenderFilter.java:62)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.spring.security.filter.ContextHolderParametersSelectedTransferFilter.invoke(ContextHolderParametersSelectedTransferFilter.java:40)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)
	at org.apache.dubbo.rpc.cluster.filter.support.MetricsConsumerFilter.invoke(MetricsConsumerFilter.java:38)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ConsumerClassLoaderFilter.invoke(ConsumerClassLoaderFilter.java:40)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ConsumerContextFilter.invoke(ConsumerContextFilter.java:119)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)
	at org.apache.dubbo.rpc.cluster.support.wrapper.AbstractCluster$ClusterFilterInvoker.invoke(AbstractCluster.java:101)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.invoke(MockClusterInvoker.java:106)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.invoke(ScopeClusterInvoker.java:171)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.invoke(MigrationInvoker.java:296)
	at org.apache.dubbo.rpc.proxy.InvocationUtil.invoke(InvocationUtil.java:64)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:81)
	at com.ym.salary.api.RemoteUsdReconciliationServiceDubboProxy0.generateUsdReconciliation(RemoteUsdReconciliationServiceDubboProxy0.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.dubbo.config.spring.util.LazyTargetInvocationHandler.invoke(LazyTargetInvocationHandler.java:54)
	at com.ym.salary.api.RemoteUsdReconciliationServiceDubboProxy0.generateUsdReconciliation(RemoteUsdReconciliationServiceDubboProxy0.java)
	at com.ym.job.snailjob.UsdReconciliationJobExecutor.jobExecute(UsdReconciliationJobExecutor.java:59)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at com.aizuda.snailjob.client.job.core.executor.AnnotationJobExecutor.doJobExecute(AnnotationJobExecutor.java:26)
	at com.aizuda.snailjob.client.job.core.executor.AbstractJobExecutor.lambda$jobExecute$0(AbstractJobExecutor.java:78)
	at com.google.common.util.concurrent.TrustedListenableFutureTask$TrustedFutureInterruptibleTask.runInterruptibly(TrustedListenableFutureTask.java:131)
	at com.google.common.util.concurrent.InterruptibleTask.run(InterruptibleTask.java:75)
	at com.google.common.util.concurrent.TrustedListenableFutureTask.run(TrustedListenableFutureTask.java:82)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-21 16:58:19 [snail-job-job-255-1] ERROR c.y.j.s.UsdReconciliationJobExecutor - 美元对账单自动生成任务异常：Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.
org.apache.dubbo.rpc.RpcException: Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.checkInvokers(AbstractClusterInvoker.java:397)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.invoke(AbstractClusterInvoker.java:358)
	at org.apache.dubbo.rpc.cluster.router.RouterSnapshotFilter.invoke(RouterSnapshotFilter.java:46)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.MetricsClusterFilter.invoke(MetricsClusterFilter.java:57)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.protocol.dubbo.filter.FutureFilter.invoke(FutureFilter.java:52)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ObservationSenderFilter.invoke(ObservationSenderFilter.java:62)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.spring.security.filter.ContextHolderParametersSelectedTransferFilter.invoke(ContextHolderParametersSelectedTransferFilter.java:40)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)
	at org.apache.dubbo.rpc.cluster.filter.support.MetricsConsumerFilter.invoke(MetricsConsumerFilter.java:38)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ConsumerClassLoaderFilter.invoke(ConsumerClassLoaderFilter.java:40)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ConsumerContextFilter.invoke(ConsumerContextFilter.java:119)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)
	at org.apache.dubbo.rpc.cluster.support.wrapper.AbstractCluster$ClusterFilterInvoker.invoke(AbstractCluster.java:101)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.invoke(MockClusterInvoker.java:106)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.invoke(ScopeClusterInvoker.java:171)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.invoke(MigrationInvoker.java:296)
	at org.apache.dubbo.rpc.proxy.InvocationUtil.invoke(InvocationUtil.java:64)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:81)
	at com.ym.salary.api.RemoteUsdReconciliationServiceDubboProxy0.generateUsdReconciliation(RemoteUsdReconciliationServiceDubboProxy0.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.dubbo.config.spring.util.LazyTargetInvocationHandler.invoke(LazyTargetInvocationHandler.java:54)
	at com.ym.salary.api.RemoteUsdReconciliationServiceDubboProxy0.generateUsdReconciliation(RemoteUsdReconciliationServiceDubboProxy0.java)
	at com.ym.job.snailjob.UsdReconciliationJobExecutor.jobExecute(UsdReconciliationJobExecutor.java:59)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at com.aizuda.snailjob.client.job.core.executor.AnnotationJobExecutor.doJobExecute(AnnotationJobExecutor.java:26)
	at com.aizuda.snailjob.client.job.core.executor.AbstractJobExecutor.lambda$jobExecute$0(AbstractJobExecutor.java:78)
	at com.google.common.util.concurrent.TrustedListenableFutureTask$TrustedFutureInterruptibleTask.runInterruptibly(TrustedListenableFutureTask.java:131)
	at com.google.common.util.concurrent.InterruptibleTask.run(InterruptibleTask.java:75)
	at com.google.common.util.concurrent.TrustedListenableFutureTask.run(TrustedListenableFutureTask.java:82)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-21 16:58:19 [snail-job-job-255-1] ERROR c.y.j.s.UsdReconciliationJobExecutor - 美元对账单自动生成任务异常：Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.
org.apache.dubbo.rpc.RpcException: Failed to invoke the method generateUsdReconciliation in the service com.ym.salary.api.RemoteUsdReconciliationService. No provider available for the service com.ym.salary.api.RemoteUsdReconciliationService from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: com.ym.salary.api.RemoteUsdReconciliationService)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) on the consumer ************* using the dubbo version 3.2.14. Please check if the providers have been started and registered.
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.checkInvokers(AbstractClusterInvoker.java:397)
	at org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.invoke(AbstractClusterInvoker.java:358)
	at org.apache.dubbo.rpc.cluster.router.RouterSnapshotFilter.invoke(RouterSnapshotFilter.java:46)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.MetricsClusterFilter.invoke(MetricsClusterFilter.java:57)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.protocol.dubbo.filter.FutureFilter.invoke(FutureFilter.java:52)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ObservationSenderFilter.invoke(ObservationSenderFilter.java:62)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.spring.security.filter.ContextHolderParametersSelectedTransferFilter.invoke(ContextHolderParametersSelectedTransferFilter.java:40)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)
	at org.apache.dubbo.rpc.cluster.filter.support.MetricsConsumerFilter.invoke(MetricsConsumerFilter.java:38)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ConsumerClassLoaderFilter.invoke(ConsumerClassLoaderFilter.java:40)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.support.ConsumerContextFilter.invoke(ConsumerContextFilter.java:119)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)
	at org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)
	at org.apache.dubbo.rpc.cluster.support.wrapper.AbstractCluster$ClusterFilterInvoker.invoke(AbstractCluster.java:101)
	at org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.invoke(MockClusterInvoker.java:106)
	at org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.invoke(ScopeClusterInvoker.java:171)
	at org.apache.dubbo.registry.client.migration.MigrationInvoker.invoke(MigrationInvoker.java:296)
	at org.apache.dubbo.rpc.proxy.InvocationUtil.invoke(InvocationUtil.java:64)
	at org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:81)
	at com.ym.salary.api.RemoteUsdReconciliationServiceDubboProxy0.generateUsdReconciliation(RemoteUsdReconciliationServiceDubboProxy0.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.dubbo.config.spring.util.LazyTargetInvocationHandler.invoke(LazyTargetInvocationHandler.java:54)
	at com.ym.salary.api.RemoteUsdReconciliationServiceDubboProxy0.generateUsdReconciliation(RemoteUsdReconciliationServiceDubboProxy0.java)
	at com.ym.job.snailjob.UsdReconciliationJobExecutor.jobExecute(UsdReconciliationJobExecutor.java:59)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:281)
	at com.aizuda.snailjob.client.job.core.executor.AnnotationJobExecutor.doJobExecute(AnnotationJobExecutor.java:26)
	at com.aizuda.snailjob.client.job.core.executor.AbstractJobExecutor.lambda$jobExecute$0(AbstractJobExecutor.java:78)
	at com.google.common.util.concurrent.TrustedListenableFutureTask$TrustedFutureInterruptibleTask.runInterruptibly(TrustedListenableFutureTask.java:131)
	at com.google.common.util.concurrent.InterruptibleTask.run(InterruptibleTask.java:75)
	at com.google.common.util.concurrent.TrustedListenableFutureTask.run(TrustedListenableFutureTask.java:82)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-21 17:18:15 [SpringApplicationShutdownHook] ERROR c.a.s.c.common.window.SlidingWindow - 到达总量窗口期通知异常
com.aizuda.snailjob.client.common.exception.SnailJobRemoteException: 网络异常
	at com.aizuda.snailjob.client.common.rpc.client.NettyChannel.send(NettyChannel.java:190)
	at com.aizuda.snailjob.client.common.rpc.client.RpcClientInvokeHandler.invoke(RpcClientInvokeHandler.java:60)
	at com.aizuda.snailjob.client.common.rpc.client.RpcClientInvokeHandler.invoke(RpcClientInvokeHandler.java:32)
	at jdk.proxy2/jdk.proxy2.$Proxy146.reportLogTask(Unknown Source)
	at com.aizuda.snailjob.client.common.log.report.ReportLogListener.handler(ReportLogListener.java:30)
	at com.aizuda.snailjob.client.common.window.SlidingWindow.doHandlerListener(SlidingWindow.java:196)
	at com.aizuda.snailjob.client.common.window.SlidingWindow.end(SlidingWindow.java:323)
	at com.aizuda.snailjob.client.common.log.report.AbstractLogReport.close(AbstractLogReport.java:62)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at com.aizuda.snailjob.client.common.init.SnailJobCloseListener.onApplicationEvent(SnailJobCloseListener.java:33)
	at com.aizuda.snailjob.client.common.init.SnailJobCloseListener.onApplicationEvent(SnailJobCloseListener.java:23)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:452)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:385)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1139)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1102)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
