2025-07-26 08:50:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 08:50:15 [main] INFO  c.y.w.WarehouseManagementApplication - Starting WarehouseManagementApplication using Java 17.0.10 with PID 26384 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-warehouse-management\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 08:50:15 [main] INFO  c.y.w.WarehouseManagementApplication - The following 1 profile is active: "dev"
2025-07-26 08:50:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 08:50:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-26 08:50:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 08:50:21 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 08:50:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 08:50:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 08:50:23 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@50ade311
2025-07-26 08:50:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 08:50:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sharding] success
2025-07-26 08:50:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 08:50:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-26 08:50:24 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 08:50:26 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 08:50:26 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 08:50:26 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 08:50:26 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 08:50:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 08:50:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 08:50:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 08:50:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 08:50:28 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-warehouse-management 192.168.124.4:9501 register finished
2025-07-26 08:50:31 [main] INFO  c.y.w.WarehouseManagementApplication - Started WarehouseManagementApplication in 18.628 seconds (process running for 19.778)
2025-07-26 08:50:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 08:50:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 08:50:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-26 08:50:31 [RMI TCP Connection(3)-192.168.124.4] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 08:59:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 08:59:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[57ms]
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [sharding] success,
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 09:00:48 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-26 09:00:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 09:00:56 [main] INFO  c.y.w.WarehouseManagementApplication - Starting WarehouseManagementApplication using Java 17.0.10 with PID 32340 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-warehouse-management\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 09:00:56 [main] INFO  c.y.w.WarehouseManagementApplication - The following 1 profile is active: "dev"
2025-07-26 09:00:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 09:00:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-26 09:00:56 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 09:01:00 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 09:01:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 09:01:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 09:01:01 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@57ae3c0e
2025-07-26 09:01:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 09:01:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sharding] success
2025-07-26 09:01:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 09:01:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-26 09:01:03 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 09:01:04 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 09:01:04 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 09:01:04 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 09:01:04 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 09:01:06 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 09:01:06 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 09:01:06 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 09:01:06 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 09:01:06 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-warehouse-management 192.168.124.4:9501 register finished
2025-07-26 09:01:09 [main] INFO  c.y.w.WarehouseManagementApplication - Started WarehouseManagementApplication in 15.096 seconds (process running for 16.556)
2025-07-26 09:01:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 09:01:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 09:01:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-26 09:01:09 [RMI TCP Connection(4)-192.168.124.4] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:02:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:02:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[54ms]
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [sharding] success,
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 09:35:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-26 09:36:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 09:36:04 [main] INFO  c.y.w.WarehouseManagementApplication - Starting WarehouseManagementApplication using Java 17.0.10 with PID 9412 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-warehouse-management\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 09:36:04 [main] INFO  c.y.w.WarehouseManagementApplication - The following 1 profile is active: "dev"
2025-07-26 09:36:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 09:36:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-26 09:36:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 09:36:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 09:36:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 09:36:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 09:36:10 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7bcd497
2025-07-26 09:36:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 09:36:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sharding] success
2025-07-26 09:36:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 09:36:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-26 09:36:12 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 09:36:13 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 09:36:13 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 09:36:13 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 09:36:13 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 09:36:15 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 09:36:15 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 09:36:15 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 09:36:15 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 09:36:15 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-warehouse-management 192.168.124.4:9501 register finished
2025-07-26 09:36:18 [main] INFO  c.y.w.WarehouseManagementApplication - Started WarehouseManagementApplication in 15.843 seconds (process running for 17.261)
2025-07-26 09:36:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 09:36:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 09:36:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-26 09:39:20 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 09:39:35 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=彩电, parentId=1948620221544210434, isParent=0, sort=1, isEnable=1, ancestors=null)
2025-07-26 09:39:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:39:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[63ms]
2025-07-26 09:39:57 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948268858713419778, name=家用电器, parentId=1948269616661901314, isParent=1, sort=1, isEnable=1, ancestors=0)
2025-07-26 09:39:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:39:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-26 09:40:08 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948268858713419778, name=家用电器, parentId=1948269616661901314, isParent=1, sort=1, isEnable=1, ancestors=0)
2025-07-26 09:40:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:40:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[22ms]
2025-07-26 09:40:47 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948268858713419778, name=家用电器, parentId=1948269616661901314, isParent=1, sort=1, isEnable=1, ancestors=0)
2025-07-26 09:40:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 09:40:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[24ms]
2025-07-26 10:00:27 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 10:00:27 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 10:00:27 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 10:00:27 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 10:00:27 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 10:00:27 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 10:00:27 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [sharding] success,
2025-07-26 10:00:27 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 10:00:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 10:00:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 10:00:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-26 10:00:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 10:00:34 [main] INFO  c.y.w.WarehouseManagementApplication - Starting WarehouseManagementApplication using Java 17.0.10 with PID 19328 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-warehouse-management\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 10:00:34 [main] INFO  c.y.w.WarehouseManagementApplication - The following 1 profile is active: "dev"
2025-07-26 10:00:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 10:00:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-26 10:00:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 10:00:38 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 10:00:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 10:00:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 10:00:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@57ae3c0e
2025-07-26 10:00:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 10:00:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sharding] success
2025-07-26 10:00:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 10:00:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-26 10:00:41 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 10:00:42 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 10:00:42 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 10:00:42 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 10:00:43 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 10:00:44 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 10:00:44 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 10:00:44 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 10:00:44 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 10:00:44 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-warehouse-management 192.168.124.4:9501 register finished
2025-07-26 10:00:47 [main] INFO  c.y.w.WarehouseManagementApplication - Started WarehouseManagementApplication in 15.696 seconds (process running for 16.977)
2025-07-26 10:00:47 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 10:00:47 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 10:00:47 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-26 10:00:47 [RMI TCP Connection(1)-192.168.124.4] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 10:02:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:02:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[56ms]
2025-07-26 10:26:21 [XNIO-1 task-3] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=test, parentId=0, isParent=1, sort=1, isEnable=1, ancestors=null)
2025-07-26 10:26:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:26:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[25ms]
2025-07-26 10:26:31 [XNIO-1 task-3] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948268858713419778, name=家用电器, parentId=1948932873900339202, isParent=1, sort=1, isEnable=1, ancestors=0)
2025-07-26 10:26:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:26:31 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-26 10:26:56 [XNIO-1 task-3] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948268858713419778, name=家用电器, parentId=0, isParent=1, sort=1, isEnable=1, ancestors=0,1948932873900339202)
2025-07-26 10:26:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:26:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-26 10:47:54 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 10:47:54 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 10:47:54 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 10:47:54 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 10:47:54 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 10:47:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 10:47:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [sharding] success,
2025-07-26 10:47:54 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 10:47:55 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 10:47:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 10:47:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-26 10:48:01 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 10:48:01 [main] INFO  c.y.w.WarehouseManagementApplication - Starting WarehouseManagementApplication using Java 17.0.10 with PID 6132 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-warehouse-management\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 10:48:01 [main] INFO  c.y.w.WarehouseManagementApplication - The following 1 profile is active: "dev"
2025-07-26 10:48:01 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 10:48:01 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-26 10:48:01 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 10:48:07 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 10:48:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 10:48:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 10:48:08 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@42ea4bac
2025-07-26 10:48:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 10:48:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sharding] success
2025-07-26 10:48:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 10:48:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-26 10:48:09 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 10:48:11 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 10:48:11 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 10:48:11 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 10:48:11 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 10:48:13 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 10:48:13 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 10:48:13 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 10:48:13 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 10:48:13 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-warehouse-management 192.168.124.4:9501 register finished
2025-07-26 10:48:16 [main] INFO  c.y.w.WarehouseManagementApplication - Started WarehouseManagementApplication in 17.257 seconds (process running for 18.745)
2025-07-26 10:48:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 10:48:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 10:48:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-26 10:48:16 [RMI TCP Connection(3)-192.168.124.4] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 10:48:33 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948268858713419778, name=家用电器, parentId=1948932873900339202, isParent=1, sort=1, isEnable=1, ancestors=0)
2025-07-26 10:48:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:48:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[67ms]
2025-07-26 10:49:15 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948269541239926785, name=电视, parentId=1948932873900339202, isParent=1, sort=1, isEnable=1, ancestors=0,1948268858713419778)
2025-07-26 10:49:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:49:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[25ms]
2025-07-26 10:49:52 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948269541239926785, name=电视, parentId=1948932873900339202, isParent=1, sort=1, isEnable=1, ancestors=0,1948268858713419778)
2025-07-26 10:51:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:51:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[21ms]
2025-07-26 10:52:15 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948269541239926785, name=电视, parentId=1948932873900339202, isParent=1, sort=1, isEnable=1, ancestors=0,1948268858713419778)
2025-07-26 10:52:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:52:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[20ms]
2025-07-26 10:52:56 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=242, parentId=1948273170097221634, isParent=1, sort=242, isEnable=1, ancestors=null)
2025-07-26 10:52:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:52:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-26 10:54:25 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=242, parentId=1948273170097221634, isParent=1, sort=242, isEnable=1, ancestors=null)
2025-07-26 10:54:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:54:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-26 10:54:30 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=242, parentId=1948273170097221634, isParent=1, sort=242, isEnable=1, ancestors=null)
2025-07-26 10:54:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:54:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-26 10:54:33 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=242, parentId=1948273170097221634, isParent=1, sort=242, isEnable=1, ancestors=null)
2025-07-26 10:54:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:54:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-26 10:54:53 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=242, parentId=1948273170097221634, isParent=1, sort=2, isEnable=1, ancestors=null)
2025-07-26 10:54:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:54:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[20ms]
2025-07-26 10:55:07 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948940056021008385, name=242242, parentId=1948273170097221634, isParent=1, sort=2, isEnable=1, ancestors=0,1948272566222303234,1948273170097221634)
2025-07-26 10:55:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:55:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[24ms]
2025-07-26 10:57:19 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948620221544210434, name=液晶电视, parentId=1948269541239926785, isParent=1, sort=1, isEnable=1, ancestors=0,1948932873900339202,1948269541239926785)
2025-07-26 10:58:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:58:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[25ms]
2025-07-26 10:59:05 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948620221544210434, name=液晶电视1, parentId=1948269541239926785, isParent=1, sort=1, isEnable=1, ancestors=0,1948932873900339202,1948269541239926785)
2025-07-26 10:59:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:59:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[21ms]
2025-07-26 10:59:17 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948620221544210434, name=液晶电视1, parentId=1948269541239926785, isParent=1, sort=1, isEnable=1, ancestors=0,1948932873900339202,1948269541239926785)
2025-07-26 10:59:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 10:59:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[26ms]
2025-07-26 10:59:59 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=1, parentId=1948273170097221634, isParent=1, sort=1, isEnable=1, ancestors=null)
2025-07-26 11:00:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:00:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[22ms]
2025-07-26 11:00:39 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=1, parentId=1948273170097221634, isParent=1, sort=1, isEnable=1, ancestors=null)
2025-07-26 11:00:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:00:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-26 11:00:54 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948941506470051841, name=165165, parentId=1948273170097221634, isParent=0, sort=1, isEnable=1, ancestors=0,1948272566222303234,1948273170097221634)
2025-07-26 11:00:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:00:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[24ms]
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [sharding] success,
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 11:31:36 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-26 11:31:41 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 11:31:41 [main] INFO  c.y.w.WarehouseManagementApplication - Starting WarehouseManagementApplication using Java 17.0.10 with PID 34608 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-warehouse-management\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 11:31:41 [main] INFO  c.y.w.WarehouseManagementApplication - The following 1 profile is active: "dev"
2025-07-26 11:31:41 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 11:31:41 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-26 11:31:41 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 11:31:46 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 11:31:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 11:31:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 11:31:47 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4115b8a7
2025-07-26 11:31:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 11:31:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sharding] success
2025-07-26 11:31:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 11:31:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-26 11:31:48 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 11:31:49 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 11:31:49 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 11:31:50 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 11:31:50 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 11:31:52 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 11:31:52 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 11:31:52 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 11:31:52 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 11:31:52 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-warehouse-management 192.168.124.4:9501 register finished
2025-07-26 11:31:54 [main] INFO  c.y.w.WarehouseManagementApplication - Started WarehouseManagementApplication in 15.894 seconds (process running for 17.155)
2025-07-26 11:31:54 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 11:31:54 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 11:31:54 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-26 11:31:55 [RMI TCP Connection(3)-192.168.124.4] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 11:34:12 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 新增商品类目:CategoryBo(id=null, name=5464, parentId=1948272566222303234, isParent=null, sort=3, isEnable=1, ancestors=null)
2025-07-26 11:34:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:34:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[72ms]
2025-07-26 11:34:25 [XNIO-1 task-2] INFO  c.y.w.controller.CategoryController - 修改商品类目:CategoryBo(id=1948949949427871746, name=54642, parentId=1948272566222303234, isParent=0, sort=3, isEnable=1, ancestors=0,1948272566222303234)
2025-07-26 11:34:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-26 11:34:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-26 11:43:55 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 11:43:55 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 11:43:55 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 11:43:55 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 11:43:55 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 11:43:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 11:43:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [sharding] success,
2025-07-26 11:43:55 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 11:43:56 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 11:43:56 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 11:43:56 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-26 17:05:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 17:05:29 [main] INFO  c.y.w.WarehouseManagementApplication - Starting WarehouseManagementApplication using Java 17.0.10 with PID 1836 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-warehouse-management\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 17:05:29 [main] INFO  c.y.w.WarehouseManagementApplication - The following 1 profile is active: "dev"
2025-07-26 17:05:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 17:05:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-26 17:05:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 17:05:33 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 17:05:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 17:05:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 17:05:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b4133d0
2025-07-26 17:05:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 17:05:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sharding] success
2025-07-26 17:05:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 17:05:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-26 17:05:36 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 17:05:37 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 17:05:37 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 17:05:37 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 17:05:38 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 17:05:39 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 17:05:39 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 17:05:39 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 17:05:39 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 17:05:39 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-warehouse-management 192.168.124.4:9501 register finished
2025-07-26 17:05:42 [main] INFO  c.y.w.WarehouseManagementApplication - Started WarehouseManagementApplication in 15.667 seconds (process running for 17.036)
2025-07-26 17:05:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 17:05:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 17:05:42 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-26 17:05:43 [RMI TCP Connection(4)-192.168.124.4] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [sharding] success,
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 17:18:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-26 20:08:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 20:08:18 [main] INFO  c.y.w.WarehouseManagementApplication - Starting WarehouseManagementApplication using Java 17.0.10 with PID 30184 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-warehouse-management\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 20:08:18 [main] INFO  c.y.w.WarehouseManagementApplication - The following 1 profile is active: "dev"
2025-07-26 20:08:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-26 20:08:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-26 20:08:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 20:08:27 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-26 20:08:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-26 20:08:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-26 20:08:29 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@191d69a
2025-07-26 20:08:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-26 20:08:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sharding] success
2025-07-26 20:08:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-26 20:08:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-26 20:08:31 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 20:08:33 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 20:08:33 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 20:08:33 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 20:08:33 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 20:08:36 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-26 20:08:36 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-26 20:08:36 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-26 20:08:36 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-26 20:08:36 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-warehouse-management 192.168.124.4:9501 register finished
2025-07-26 20:08:38 [main] INFO  c.y.w.WarehouseManagementApplication - Started WarehouseManagementApplication in 23.095 seconds (process running for 24.387)
2025-07-26 20:08:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-26 20:08:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 20:08:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-26 20:08:39 [RMI TCP Connection(1)-192.168.124.4] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 20:51:51 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [sharding] success,
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-26 20:51:52 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
