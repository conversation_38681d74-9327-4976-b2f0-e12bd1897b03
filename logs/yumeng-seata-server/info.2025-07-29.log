2025-07-29 09:20:14 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 09:20:14 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 09:20:14 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 09:20:14 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 09:20:15 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-29 09:20:15 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-29 09:20:15 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-29 09:20:15 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-29 09:20:16 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 9520 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 09:20:16 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-29 09:20:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-29 09:20:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-29 09:20:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 09:20:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-29 09:20:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 09:20:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1566 ms
2025-07-29 09:20:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-29 09:20:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-29 09:20:18 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-29 09:20:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3777fc44, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@79454d8e, org.springframework.security.web.context.SecurityContextPersistenceFilter@67b20b4c, org.springframework.security.web.header.HeaderWriterFilter@205b73d8, org.springframework.security.web.authentication.logout.LogoutFilter@7c59cf66, io.seata.console.filter.JwtAuthenticationTokenFilter@7ab2e018, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3e0a112f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63ccb1b2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4e13af1b, org.springframework.security.web.session.SessionManagementFilter@2b76ecd5, org.springframework.security.web.access.ExceptionTranslationFilter@f0d01c9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1fc386f8]
2025-07-29 09:20:18 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-29 09:20:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-29 09:20:18 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.755 seconds (JVM running for 6.271)
2025-07-29 09:20:18 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-29 09:20:19 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-29 09:20:19 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-29 09:20:19 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 09:20:19 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 09:20:19 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-29 09:20:19 [main] INFO  io.seata.server.ServerRunner - seata server started in 1212 millSeconds
2025-07-29 09:20:36 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,***************,1753752035775
timestamp=1753752035775
authVersion=V4
vgroup=yumeng-system-group
ip=***************
'},channel:[id: 0x2a391cc0, L:/***************:8091 - R:/***************:13846],client version:1.7.1
2025-07-29 09:20:36 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753752035904
timestamp=1753752035904
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'},channel:[id: 0x4529bbb0, L:/***************:8091 - R:/***************:13847],client version:1.7.1
2025-07-29 09:20:39 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xe559306b, L:/***************:8091 - R:/***************:13889],client version:1.7.1
2025-07-29 09:20:39 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xb09464d7, L:/***************:8091 - R:/***************:13891],client version:1.7.1
2025-07-29 09:20:41 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753752040730
timestamp=1753752040730
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'},channel:[id: 0x3e53bc02, L:/***************:8091 - R:/***************:13926],client version:1.7.1
2025-07-29 09:20:44 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xf02c2b93, L:/***************:8091 - R:/***************:13969],client version:1.7.1
2025-07-29 09:20:58 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1753752058391
timestamp=1753752058391
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'},channel:[id: 0xdfc9997e, L:/***************:8091 - R:/***************:14086],client version:1.7.1
2025-07-29 09:21:58 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x172ceb75, L:/***************:8091 - R:/***************:14283],client version:1.7.1
2025-07-29 10:13:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='approve(com.ym.system.domain.bo.ApprovalRecordBo)', timeout=60000}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:41 [ServerHandlerThread_1_5_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-system,transactionServiceGroup: yumeng-system-group, transactionName: approve(com.ym.system.domain.bo.ApprovalRecordBo),timeout:60000,xid:***************:8091:3234261670141763585
2025-07-29 10:13:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='***************:8091:3234261670141763585', extraData='null', resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:3234261670141763585', branchType=AT, resourceId='******************************************', lockKey='company_info:1950013234926407682', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:42 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:3234261670141763585, branchId = 3234261670141763588, resourceId = ****************************************** ,lockKeys = company_info:1950013234926407682
2025-07-29 10:13:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3234261670141763588, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:3234261670141763585', branchType=AT, resourceId='******************************************', lockKey='company_apply_approval:1950013235794628610', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:43 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:3234261670141763585, branchId = 3234261670141763590, resourceId = ****************************************** ,lockKeys = company_apply_approval:1950013235794628610
2025-07-29 10:13:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3234261670141763590, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:3234261670141763585', branchType=AT, resourceId='******************************************', lockKey='sys_notice:1950016860000440322', applicationData='{"skipCheckLock":true}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:44 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:3234261670141763585, branchId = 3234261670141763592, resourceId = ****************************************** ,lockKeys = sys_notice:1950016860000440322
2025-07-29 10:13:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3234261670141763592, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:3234261670141763585', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950016863209082882', applicationData='{"skipCheckLock":true}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:44 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:3234261670141763585, branchId = 3234261670141763594, resourceId = ****************************************** ,lockKeys = sys_dept:1950016863209082882
2025-07-29 10:13:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3234261670141763594, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:3234261670141763585', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950016863209082882', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:45 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:3234261670141763585, branchId = 3234261670141763596, resourceId = ****************************************** ,lockKeys = sys_dept:1950016863209082882
2025-07-29 10:13:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3234261670141763596, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:3234261670141763585', branchType=AT, resourceId='******************************************', lockKey='sys_dept:100', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:45 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:3234261670141763585, branchId = 3234261670141763598, resourceId = ****************************************** ,lockKeys = sys_dept:100
2025-07-29 10:13:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3234261670141763598, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:3234261670141763585', branchType=AT, resourceId='******************************************', lockKey='sys_user:1950009240501964802', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:46 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:3234261670141763585, branchId = 3234261670141763600, resourceId = ****************************************** ,lockKeys = sys_user:1950009240501964802
2025-07-29 10:13:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3234261670141763600, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:3234261670141763585', branchType=AT, resourceId='******************************************', lockKey='company_info:1950013234926407682', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:47 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:3234261670141763585, branchId = 3234261670141763602, resourceId = ****************************************** ,lockKeys = company_info:1950013234926407682
2025-07-29 10:13:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3234261670141763602, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='***************:8091:3234261670141763585', extraData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:3234261670141763585', branchId=3234261670141763588, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:47 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:3234261670141763585 branchId = 3234261670141763588
2025-07-29 10:13:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:3234261670141763585', branchId=3234261670141763590, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:47 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:3234261670141763585 branchId = 3234261670141763590
2025-07-29 10:13:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:3234261670141763585', branchId=3234261670141763592, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:47 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:3234261670141763585 branchId = 3234261670141763592
2025-07-29 10:13:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:3234261670141763585', branchId=3234261670141763594, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:48 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:3234261670141763585 branchId = 3234261670141763594
2025-07-29 10:13:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:3234261670141763585', branchId=3234261670141763596, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:48 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:3234261670141763585 branchId = 3234261670141763596
2025-07-29 10:13:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:3234261670141763585', branchId=3234261670141763598, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:48 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:3234261670141763585 branchId = 3234261670141763598
2025-07-29 10:13:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:3234261670141763585', branchId=3234261670141763600, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:48 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:3234261670141763585 branchId = 3234261670141763600
2025-07-29 10:13:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:3234261670141763585', branchId=3234261670141763602, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:13:48 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:3234261670141763585 branchId = 3234261670141763602
2025-07-29 10:13:48 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = ***************:8091:3234261670141763585.
2025-07-29 10:14:04 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-29 10:14:14 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 10:14:14 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 10:14:14 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 10:14:14 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 10:14:16 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-29 10:14:16 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-29 10:14:16 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-29 10:14:16 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-29 10:14:16 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 38436 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 10:14:16 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-29 10:14:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-29 10:14:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-29 10:14:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 10:14:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-29 10:14:18 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 10:14:18 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1758 ms
2025-07-29 10:14:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-29 10:14:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-29 10:14:18 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-29 10:14:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7402bfe7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1d5958d3, org.springframework.security.web.context.SecurityContextPersistenceFilter@64de9fa4, org.springframework.security.web.header.HeaderWriterFilter@7c3223aa, org.springframework.security.web.authentication.logout.LogoutFilter@341889a1, io.seata.console.filter.JwtAuthenticationTokenFilter@6a6e410c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@78141c58, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@10f60e36, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@200e6029, org.springframework.security.web.session.SessionManagementFilter@4626f584, org.springframework.security.web.access.ExceptionTranslationFilter@b859355, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5a95aaae]
2025-07-29 10:14:19 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-29 10:14:19 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-29 10:14:19 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 5.254 seconds (JVM running for 7.845)
2025-07-29 10:14:19 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-29 10:14:19 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-29 10:14:19 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-29 10:14:19 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 10:14:19 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 10:14:19 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-29 10:14:19 [main] INFO  io.seata.server.ServerRunner - seata server started in 720 millSeconds
2025-07-29 10:14:20 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x5cdaa4d3, L:/***************:8091 - R:/***************:9125],client version:1.7.1
2025-07-29 10:14:20 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1753755258078
timestamp=1753755258078
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'},channel:[id: 0x2c77e731, L:/***************:8091 - R:/***************:9124],client version:1.7.1
2025-07-29 10:14:20 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753755260385
timestamp=1753755260385
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'},channel:[id: 0xfb6697b9, L:/***************:8091 - R:/***************:9144],client version:1.7.1
2025-07-29 10:14:21 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xdd95c446, L:/***************:8091 - R:/***************:9148],client version:1.7.1
2025-07-29 10:14:25 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,***************,1753755265434
timestamp=1753755265434
authVersion=V4
vgroup=yumeng-system-group
ip=***************
'},channel:[id: 0x1d1b1023, L:/***************:8091 - R:/***************:9167],client version:1.7.1
2025-07-29 10:14:25 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753755265580
timestamp=1753755265580
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'},channel:[id: 0x34315f76, L:/***************:8091 - R:/***************:9168],client version:1.7.1
2025-07-29 10:14:25 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x38f700da, L:/***************:8091 - R:/***************:9169],client version:1.7.1
2025-07-29 10:14:26 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xf34788a4, L:/***************:8091 - R:/***************:9170],client version:1.7.1
2025-07-29 10:20:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='approve(com.ym.system.domain.bo.ApprovalRecordBo)', timeout=60000}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:46 [ServerHandlerThread_1_5_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-system,transactionServiceGroup: yumeng-system-group, transactionName: approve(com.ym.system.domain.bo.ApprovalRecordBo),timeout:60000,xid:***************:8091:5206838320203509761
2025-07-29 10:20:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='***************:8091:5206838320203509761', extraData='null', resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509761', branchType=AT, resourceId='******************************************', lockKey='company_info:1950013234926407682', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:46 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509761, branchId = 5206838320203509763, resourceId = ****************************************** ,lockKeys = company_info:1950013234926407682
2025-07-29 10:20:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509763, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509761', branchType=AT, resourceId='******************************************', lockKey='company_apply_approval:1950013235794628610', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:47 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509761, branchId = 5206838320203509766, resourceId = ****************************************** ,lockKeys = company_apply_approval:1950013235794628610
2025-07-29 10:20:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509766, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509761', branchType=AT, resourceId='******************************************', lockKey='sys_notice:1950018637710053378', applicationData='{"skipCheckLock":true}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:47 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509761, branchId = 5206838320203509768, resourceId = ****************************************** ,lockKeys = sys_notice:1950018637710053378
2025-07-29 10:20:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509768, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509761', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950018640188887041', applicationData='{"skipCheckLock":true}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:48 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509761, branchId = 5206838320203509771, resourceId = ****************************************** ,lockKeys = sys_dept:1950018640188887041
2025-07-29 10:20:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509771, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509761', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950018640188887041', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:48 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509761, branchId = 5206838320203509773, resourceId = ****************************************** ,lockKeys = sys_dept:1950018640188887041
2025-07-29 10:20:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509773, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509761', branchType=AT, resourceId='******************************************', lockKey='sys_dept:100', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:49 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509761, branchId = 5206838320203509776, resourceId = ****************************************** ,lockKeys = sys_dept:100
2025-07-29 10:20:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509776, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509761', branchType=AT, resourceId='******************************************', lockKey='sys_user:1950009240501964802', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:50 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509761, branchId = 5206838320203509779, resourceId = ****************************************** ,lockKeys = sys_user:1950009240501964802
2025-07-29 10:20:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509779, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509761', branchType=AT, resourceId='******************************************', lockKey='company_info:1950013234926407682', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:50 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509761, branchId = 5206838320203509781, resourceId = ****************************************** ,lockKeys = company_info:1950013234926407682
2025-07-29 10:20:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509781, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='***************:8091:5206838320203509761', extraData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:20:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='approve(com.ym.system.domain.bo.ApprovalRecordBo)', timeout=60000}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:51 [ServerHandlerThread_1_15_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-system,transactionServiceGroup: yumeng-system-group, transactionName: approve(com.ym.system.domain.bo.ApprovalRecordBo),timeout:60000,xid:***************:8091:5206838320203509784
2025-07-29 10:22:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='***************:8091:5206838320203509784', extraData='null', resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='company_info:1942551088412790786', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:51 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509786, resourceId = ****************************************** ,lockKeys = company_info:1942551088412790786
2025-07-29 10:22:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509786, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='company_apply_approval:1942551089029353474', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:52 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509789, resourceId = ****************************************** ,lockKeys = company_apply_approval:1942551089029353474
2025-07-29 10:22:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509789, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='sys_notice:1950019163092766721', applicationData='{"skipCheckLock":true}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:53 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509792, resourceId = ****************************************** ,lockKeys = sys_notice:1950019163092766721
2025-07-29 10:22:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509792, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950019165609349122', applicationData='{"skipCheckLock":true}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:53 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509794, resourceId = ****************************************** ,lockKeys = sys_dept:1950019165609349122
2025-07-29 10:22:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509794, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950019165609349122', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:54 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509797, resourceId = ****************************************** ,lockKeys = sys_dept:1950019165609349122
2025-07-29 10:22:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509797, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='sys_dept:100', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:54 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509799, resourceId = ****************************************** ,lockKeys = sys_dept:100
2025-07-29 10:22:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509799, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:55 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='sys_post:1950019175017172993,1950019175520489474,1950019175520489475,1950019175520489476,1950019175520489477,1950019175520489478,1950019175520489479,1950019175520489480,1950019175520489481,1950019175520489482,1950019175520489483,1950019175520489484,1950019175520489485,1950019175520489486,1950019175520489487,1950019175520489488,1950019175583404034,1950019175583404035,1950019175583404036,1950019175587598337,1950019175587598338', applicationData='{"skipCheckLock":true}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:56 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509803, resourceId = ****************************************** ,lockKeys = sys_post:1950019175017172993,1950019175520489474,1950019175520489475,1950019175520489476,1950019175520489477,1950019175520489478,1950019175520489479,1950019175520489480,1950019175520489481,1950019175520489482,1950019175520489483,1950019175520489484,1950019175520489485,1950019175520489486,1950019175520489487,1950019175520489488,1950019175583404034,1950019175583404035,1950019175583404036,1950019175587598337,1950019175587598338
2025-07-29 10:22:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509803, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='sys_user:1942550721562185729', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:57 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509805, resourceId = ****************************************** ,lockKeys = sys_user:1942550721562185729
2025-07-29 10:22:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509805, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='company_info:1942551088412790786', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:57 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509808, resourceId = ****************************************** ,lockKeys = company_info:1942551088412790786
2025-07-29 10:22:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509808, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509784', branchType=AT, resourceId='******************************************', lockKey='sys_user_role:1942550721562185729_1913482734521348097;sys_user_role:1942550721562185729_1913932785197473793', applicationData='{"autoCommit":false}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:58 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509784, branchId = 5206838320203509811, resourceId = ****************************************** ,lockKeys = sys_user_role:1942550721562185729_1913482734521348097;sys_user_role:1942550721562185729_1913932785197473793
2025-07-29 10:22:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509811, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='***************:8091:5206838320203509784', extraData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509786, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:58 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509786
2025-07-29 10:22:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509789, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509789
2025-07-29 10:22:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509792, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509792
2025-07-29 10:22:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509794, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509794
2025-07-29 10:22:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509797, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509797
2025-07-29 10:22:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509799, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509799
2025-07-29 10:22:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509803, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509803
2025-07-29 10:22:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509805, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509805
2025-07-29 10:22:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509808, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509808
2025-07-29 10:22:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='***************:8091:5206838320203509784', branchId=5206838320203509811, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ***************:8091:5206838320203509784 branchId = 5206838320203509811
2025-07-29 10:22:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = ***************:8091:5206838320203509784.
2025-07-29 11:26:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='approve(com.ym.system.domain.bo.ApprovalRecordBo)', timeout=60000}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:07 [ServerHandlerThread_1_27_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-system,transactionServiceGroup: yumeng-system-group, transactionName: approve(com.ym.system.domain.bo.ApprovalRecordBo),timeout:60000,xid:***************:8091:5206838320203509814
2025-07-29 11:26:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='***************:8091:5206838320203509814', extraData='null', resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509814', branchType=AT, resourceId='******************************************', lockKey='company_info:1950013234926407682', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:08 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509814, branchId = 5206838320203509817, resourceId = ****************************************** ,lockKeys = company_info:1950013234926407682
2025-07-29 11:26:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509817, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509814', branchType=AT, resourceId='******************************************', lockKey='company_apply_approval:1950013235794628610', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:08 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509814, branchId = 5206838320203509819, resourceId = ****************************************** ,lockKeys = company_apply_approval:1950013235794628610
2025-07-29 11:26:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509819, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509814', branchType=AT, resourceId='******************************************', lockKey='sys_notice:1950035085656412162', applicationData='{"skipCheckLock":true}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:09 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509814, branchId = 5206838320203509822, resourceId = ****************************************** ,lockKeys = sys_notice:1950035085656412162
2025-07-29 11:26:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509822, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509814', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950035088508538881', applicationData='{"skipCheckLock":true}'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:10 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509814, branchId = 5206838320203509825, resourceId = ****************************************** ,lockKeys = sys_dept:1950035088508538881
2025-07-29 11:26:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509825, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509814', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950035088508538881', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:10 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509814, branchId = 5206838320203509827, resourceId = ****************************************** ,lockKeys = sys_dept:1950035088508538881
2025-07-29 11:26:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509827, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509814', branchType=AT, resourceId='******************************************', lockKey='sys_dept:100', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:11 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509814, branchId = 5206838320203509829, resourceId = ****************************************** ,lockKeys = sys_dept:100
2025-07-29 11:26:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509829, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509814', branchType=AT, resourceId='******************************************', lockKey='sys_user:1950009240501964802', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:11 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509814, branchId = 5206838320203509832, resourceId = ****************************************** ,lockKeys = sys_user:1950009240501964802
2025-07-29 11:26:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509832, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='***************:8091:5206838320203509814', branchType=AT, resourceId='******************************************', lockKey='company_info:1950013234926407682', applicationData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:12 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ***************:8091:5206838320203509814, branchId = 5206838320203509835, resourceId = ****************************************** ,lockKeys = company_info:1950013234926407682
2025-07-29 11:26:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5206838320203509835, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='***************:8091:5206838320203509814', extraData='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:26:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: ***************, vgroup: yumeng-system-group
2025-07-29 11:30:43 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9167 to server channel inactive.
2025-07-29 11:30:43 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9169 to server channel inactive.
2025-07-29 11:30:43 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1d1b1023, L:/***************:8091 ! R:/***************:9167]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:***************:9167', channel=[id: 0x1d1b1023, L:/***************:8091 ! R:/***************:9167], resourceSets=null}
2025-07-29 11:30:43 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x38f700da, L:/***************:8091 ! R:/***************:9169]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:***************:9169', channel=[id: 0x38f700da, L:/***************:8091 ! R:/***************:9169], resourceSets=[]}
2025-07-29 11:30:55 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,***************,1753759855227
timestamp=1753759855227
authVersion=V4
vgroup=yumeng-system-group
ip=***************
'},channel:[id: 0xa87b492f, L:/***************:8091 - R:/***************:9580],client version:1.7.1
2025-07-29 11:30:59 [ServerHandlerThread_1_37_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x136ba624, L:/***************:8091 - R:/***************:9597],client version:1.7.1
2025-07-29 13:28:41 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x136ba624, L:/***************:8091 - R:/***************:9597] read idle.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9597 to server channel inactive.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x136ba624, L:/***************:8091 - R:/***************:9597]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:***************:9597', channel=[id: 0x136ba624, L:/***************:8091 - R:/***************:9597], resourceSets=[]}
2025-07-29 13:28:41 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x136ba624, L:/***************:8091 - R:/***************:9597]
2025-07-29 13:28:41 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9597 to server channel inactive.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x136ba624, L:/***************:8091 ! R:/***************:9597]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:***************:9597', channel=[id: 0x136ba624, L:/***************:8091 ! R:/***************:9597], resourceSets=[]}
2025-07-29 13:28:41 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9144 to server channel inactive.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9168 to server channel inactive.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9148 to server channel inactive.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfb6697b9, L:/***************:8091 ! R:/***************:9144]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:***************:9144', channel=[id: 0xfb6697b9, L:/***************:8091 ! R:/***************:9144], resourceSets=null}
2025-07-29 13:28:41 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdd95c446, L:/***************:8091 ! R:/***************:9148]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:***************:9148', channel=[id: 0xdd95c446, L:/***************:8091 ! R:/***************:9148], resourceSets=[]}
2025-07-29 13:28:41 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9170 to server channel inactive.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x34315f76, L:/***************:8091 ! R:/***************:9168]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:***************:9168', channel=[id: 0x34315f76, L:/***************:8091 ! R:/***************:9168], resourceSets=null}
2025-07-29 13:28:41 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf34788a4, L:/***************:8091 ! R:/***************:9170]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:***************:9170', channel=[id: 0xf34788a4, L:/***************:8091 ! R:/***************:9170], resourceSets=[]}
2025-07-29 13:28:41 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9580 to server channel inactive.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa87b492f, L:/***************:8091 ! R:/***************:9580]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:***************:9580', channel=[id: 0xa87b492f, L:/***************:8091 ! R:/***************:9580], resourceSets=null}
2025-07-29 13:28:41 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9125 to server channel inactive.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5cdaa4d3, L:/***************:8091 ! R:/***************:9125]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:***************:9125', channel=[id: 0x5cdaa4d3, L:/***************:8091 ! R:/***************:9125], resourceSets=null}
2025-07-29 13:28:41 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ***************:9124 to server channel inactive.
2025-07-29 13:28:41 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2c77e731, L:/***************:8091 ! R:/***************:9124]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:***************:9124', channel=[id: 0x2c77e731, L:/***************:8091 ! R:/***************:9124], resourceSets=null}
2025-07-29 13:31:14 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-29 13:31:24 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 13:31:24 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 13:31:25 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 13:31:25 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 13:31:26 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-29 13:31:26 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-29 13:31:26 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-29 13:31:26 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-29 13:31:26 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 40412 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 13:31:26 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-29 13:31:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-29 13:31:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-29 13:31:27 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 13:31:27 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-29 13:31:27 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 13:31:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1310 ms
2025-07-29 13:31:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-29 13:31:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-29 13:31:28 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-29 13:31:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5c261c74, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6d38a81d, org.springframework.security.web.context.SecurityContextPersistenceFilter@6520625f, org.springframework.security.web.header.HeaderWriterFilter@70044113, org.springframework.security.web.authentication.logout.LogoutFilter@2dec57c5, io.seata.console.filter.JwtAuthenticationTokenFilter@41f61188, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2f5a092e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2b76ecd5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@d7c4fcb, org.springframework.security.web.session.SessionManagementFilter@5bc40f5d, org.springframework.security.web.access.ExceptionTranslationFilter@1d686622, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@75566f4]
2025-07-29 13:31:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-29 13:31:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-29 13:31:28 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.918 seconds (JVM running for 5.145)
2025-07-29 13:31:28 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-29 13:31:28 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-29 13:31:28 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-29 13:31:28 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 13:31:28 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 13:31:28 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-29 13:31:28 [main] INFO  io.seata.server.ServerRunner - seata server started in 635 millSeconds
2025-07-29 13:31:31 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,***************,1753767091868
timestamp=1753767091868
authVersion=V4
vgroup=yumeng-system-group
ip=***************
'},channel:[id: 0x9f6f2b23, L:/*************:8091 - R:/*************:13389],client version:1.7.1
2025-07-29 13:31:32 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767092446
timestamp=1753767092446
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'},channel:[id: 0x535b08b2, L:/*************:8091 - R:/*************:13390],client version:1.7.1
2025-07-29 13:31:32 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xef5b4956, L:/*************:8091 - R:/*************:13391],client version:1.7.1
2025-07-29 13:31:32 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x5535d20e, L:/*************:8091 - R:/*************:13392],client version:1.7.1
2025-07-29 13:31:34 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,***************,1753767094936
timestamp=1753767094936
authVersion=V4
vgroup=yumeng-auth-group
ip=***************
'},channel:[id: 0x341caba8, L:/*************:8091 - R:/*************:13398],client version:1.7.1
2025-07-29 13:31:35 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x88fd9b71, L:/*************:8091 - R:/*************:13399],client version:1.7.1
2025-07-29 13:31:37 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767097252
timestamp=1753767097252
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'},channel:[id: 0xc1ba115d, L:/*************:8091 - R:/*************:13401],client version:1.7.1
2025-07-29 13:31:37 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xf08faf4d, L:/*************:8091 - R:/*************:13402],client version:1.7.1
2025-07-29 13:32:26 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13390 to server channel inactive.
2025-07-29 13:32:26 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x535b08b2, L:/*************:8091 ! R:/*************:13390]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13390', channel=[id: 0x535b08b2, L:/*************:8091 ! R:/*************:13390], resourceSets=null}
2025-07-29 13:32:26 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13392 to server channel inactive.
2025-07-29 13:32:26 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5535d20e, L:/*************:8091 ! R:/*************:13392]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13392', channel=[id: 0x5535d20e, L:/*************:8091 ! R:/*************:13392], resourceSets=[]}
2025-07-29 13:32:37 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13389 to server channel inactive.
2025-07-29 13:32:37 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9f6f2b23, L:/*************:8091 ! R:/*************:13389]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13389', channel=[id: 0x9f6f2b23, L:/*************:8091 ! R:/*************:13389], resourceSets=null}
2025-07-29 13:32:37 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13391 to server channel inactive.
2025-07-29 13:32:37 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xef5b4956, L:/*************:8091 ! R:/*************:13391]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13391', channel=[id: 0xef5b4956, L:/*************:8091 ! R:/*************:13391], resourceSets=[]}
2025-07-29 13:32:37 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753767157157
timestamp=1753767157157
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xf8c6d141, L:/*************:8091 - R:/*************:13531],client version:1.7.1
2025-07-29 13:32:40 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x9b955061, L:/*************:8091 - R:/*************:13545],client version:1.7.1
2025-07-29 13:32:48 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753767168382
timestamp=1753767168382
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x71029e74, L:/*************:8091 - R:/*************:13598],client version:1.7.1
2025-07-29 13:32:52 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xa463c9a8, L:/*************:8091 - R:/*************:13611],client version:1.7.1
2025-07-29 13:34:41 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13598 to server channel inactive.
2025-07-29 13:34:41 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x71029e74, L:/*************:8091 ! R:/*************:13598]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13598', channel=[id: 0x71029e74, L:/*************:8091 ! R:/*************:13598], resourceSets=null}
2025-07-29 13:34:41 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13611 to server channel inactive.
2025-07-29 13:34:41 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa463c9a8, L:/*************:8091 ! R:/*************:13611]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13611', channel=[id: 0xa463c9a8, L:/*************:8091 ! R:/*************:13611], resourceSets=[]}
2025-07-29 13:34:53 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753767292797
timestamp=1753767292797
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x9684ad7b, L:/*************:8091 - R:/*************:13897],client version:1.7.1
2025-07-29 13:34:56 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x7ce7f913, L:/*************:8091 - R:/*************:13907],client version:1.7.1
2025-07-29 13:36:46 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13897 to server channel inactive.
2025-07-29 13:36:46 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9684ad7b, L:/*************:8091 ! R:/*************:13897]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13897', channel=[id: 0x9684ad7b, L:/*************:8091 ! R:/*************:13897], resourceSets=null}
2025-07-29 13:36:46 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13907 to server channel inactive.
2025-07-29 13:36:46 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7ce7f913, L:/*************:8091 ! R:/*************:13907]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13907', channel=[id: 0x7ce7f913, L:/*************:8091 ! R:/*************:13907], resourceSets=[]}
2025-07-29 13:36:56 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753767416671
timestamp=1753767416671
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x9a6b240f, L:/*************:8091 - R:/*************:14263],client version:1.7.1
2025-07-29 13:37:01 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x7b9a364f, L:/*************:8091 - R:/*************:14283],client version:1.7.1
2025-07-29 13:37:07 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13531 to server channel inactive.
2025-07-29 13:37:07 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf8c6d141, L:/*************:8091 ! R:/*************:13531]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13531', channel=[id: 0xf8c6d141, L:/*************:8091 ! R:/*************:13531], resourceSets=null}
2025-07-29 13:37:07 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13545 to server channel inactive.
2025-07-29 13:37:07 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9b955061, L:/*************:8091 ! R:/*************:13545]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13545', channel=[id: 0x9b955061, L:/*************:8091 ! R:/*************:13545], resourceSets=[]}
2025-07-29 13:37:14 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13401 to server channel inactive.
2025-07-29 13:37:14 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc1ba115d, L:/*************:8091 ! R:/*************:13401]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13401', channel=[id: 0xc1ba115d, L:/*************:8091 ! R:/*************:13401], resourceSets=null}
2025-07-29 13:37:14 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13402 to server channel inactive.
2025-07-29 13:37:14 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf08faf4d, L:/*************:8091 ! R:/*************:13402]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13402', channel=[id: 0xf08faf4d, L:/*************:8091 ! R:/*************:13402], resourceSets=[]}
2025-07-29 13:37:16 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753767436275
timestamp=1753767436275
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x9597058e, L:/*************:8091 - R:/*************:14371],client version:1.7.1
2025-07-29 13:37:18 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13398 to server channel inactive.
2025-07-29 13:37:18 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x341caba8, L:/*************:8091 ! R:/*************:13398]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13398', channel=[id: 0x341caba8, L:/*************:8091 ! R:/*************:13398], resourceSets=null}
2025-07-29 13:37:18 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13399 to server channel inactive.
2025-07-29 13:37:18 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x88fd9b71, L:/*************:8091 ! R:/*************:13399]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13399', channel=[id: 0x88fd9b71, L:/*************:8091 ! R:/*************:13399], resourceSets=null}
2025-07-29 13:37:19 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x4a58922d, L:/*************:8091 - R:/*************:14386],client version:1.7.1
2025-07-29 13:37:24 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753767444718
timestamp=1753767444718
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x50769baf, L:/*************:8091 - R:/*************:14428],client version:1.7.1
2025-07-29 13:37:27 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xcf6cd47d, L:/*************:8091 - R:/*************:14440],client version:1.7.1
2025-07-29 13:37:27 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753767447439
timestamp=1753767447439
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x7db4a6be, L:/*************:8091 - R:/*************:14444],client version:1.7.1
2025-07-29 13:38:27 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xb4c088c0, L:/*************:8091 - R:/*************:14712],client version:1.7.1
2025-07-29 13:38:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='approve(com.ym.system.domain.bo.ApprovalRecordBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:43 [ServerHandlerThread_1_12_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-system,transactionServiceGroup: yumeng-system-group, transactionName: approve(com.ym.system.domain.bo.ApprovalRecordBo),timeout:60000,xid:*************:8091:1018490715201114113
2025-07-29 13:38:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:1018490715201114113', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:1018490715201114113', branchType=AT, resourceId='******************************************', lockKey='company_info:1950013234926407682', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:44 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:1018490715201114113, branchId = 1018490715201114116, resourceId = ****************************************** ,lockKeys = company_info:1950013234926407682
2025-07-29 13:38:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=1018490715201114116, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:1018490715201114113', branchType=AT, resourceId='******************************************', lockKey='company_apply_approval:1950013235794628610', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:44 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:1018490715201114113, branchId = 1018490715201114118, resourceId = ****************************************** ,lockKeys = company_apply_approval:1950013235794628610
2025-07-29 13:38:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=1018490715201114118, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:1018490715201114113', branchType=AT, resourceId='******************************************', lockKey='sys_notice:1950068455606620161', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:45 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:1018490715201114113, branchId = 1018490715201114121, resourceId = ****************************************** ,lockKeys = sys_notice:1950068455606620161
2025-07-29 13:38:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=1018490715201114121, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:1018490715201114113', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950068457963819009', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:45 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:1018490715201114113, branchId = 1018490715201114123, resourceId = ****************************************** ,lockKeys = sys_dept:1950068457963819009
2025-07-29 13:38:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=1018490715201114123, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:1018490715201114113', branchType=AT, resourceId='******************************************', lockKey='sys_dept:1950068457963819009', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:46 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:1018490715201114113, branchId = 1018490715201114126, resourceId = ****************************************** ,lockKeys = sys_dept:1950068457963819009
2025-07-29 13:38:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=1018490715201114126, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:1018490715201114113', branchType=AT, resourceId='******************************************', lockKey='sys_dept:100', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:46 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:1018490715201114113, branchId = 1018490715201114128, resourceId = ****************************************** ,lockKeys = sys_dept:100
2025-07-29 13:38:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=1018490715201114128, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:1018490715201114113', branchType=AT, resourceId='******************************************', lockKey='sys_user:1950009240501964802', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:47 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:1018490715201114113, branchId = 1018490715201114131, resourceId = ****************************************** ,lockKeys = sys_user:1950009240501964802
2025-07-29 13:38:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=1018490715201114131, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:1018490715201114113', branchType=AT, resourceId='******************************************', lockKey='company_info:1950013234926407682', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:47 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:1018490715201114113, branchId = 1018490715201114133, resourceId = ****************************************** ,lockKeys = company_info:1950013234926407682
2025-07-29 13:38:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=1018490715201114133, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:1018490715201114113', branchType=AT, resourceId='******************************************', lockKey='sys_user_role:1950009240501964802_1913932785197473793;sys_user_role:1950009240501964802_1950031675916128258', applicationData='{"autoCommit":false}'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:48 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:1018490715201114113, branchId = 1018490715201114136, resourceId = ****************************************** ,lockKeys = sys_user_role:1950009240501964802_1913932785197473793;sys_user_role:1950009240501964802_1950031675916128258
2025-07-29 13:38:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=1018490715201114136, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:1018490715201114113', extraData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:38:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-29 13:48:04 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14428 to server channel inactive.
2025-07-29 13:48:04 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x50769baf, L:/*************:8091 ! R:/*************:14428]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14428', channel=[id: 0x50769baf, L:/*************:8091 ! R:/*************:14428], resourceSets=null}
2025-07-29 13:48:04 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14371 to server channel inactive.
2025-07-29 13:48:04 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14283 to server channel inactive.
2025-07-29 13:48:04 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7b9a364f, L:/*************:8091 ! R:/*************:14283]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:14283', channel=[id: 0x7b9a364f, L:/*************:8091 ! R:/*************:14283], resourceSets=[]}
2025-07-29 13:48:04 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14444 to server channel inactive.
2025-07-29 13:48:04 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14440 to server channel inactive.
2025-07-29 13:48:04 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcf6cd47d, L:/*************:8091 ! R:/*************:14440]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14440', channel=[id: 0xcf6cd47d, L:/*************:8091 ! R:/*************:14440], resourceSets=[]}
2025-07-29 13:48:04 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14712 to server channel inactive.
2025-07-29 13:48:04 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb4c088c0, L:/*************:8091 ! R:/*************:14712]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:14712', channel=[id: 0xb4c088c0, L:/*************:8091 ! R:/*************:14712], resourceSets=null}
2025-07-29 13:48:04 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14386 to server channel inactive.
2025-07-29 13:48:04 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4a58922d, L:/*************:8091 ! R:/*************:14386]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:14386', channel=[id: 0x4a58922d, L:/*************:8091 ! R:/*************:14386], resourceSets=[]}
2025-07-29 13:48:04 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9597058e, L:/*************:8091 ! R:/*************:14371]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:14371', channel=[id: 0x9597058e, L:/*************:8091 ! R:/*************:14371], resourceSets=null}
2025-07-29 13:48:04 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14263 to server channel inactive.
2025-07-29 13:48:04 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9a6b240f, L:/*************:8091 ! R:/*************:14263]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:14263', channel=[id: 0x9a6b240f, L:/*************:8091 ! R:/*************:14263], resourceSets=null}
2025-07-29 13:48:04 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7db4a6be, L:/*************:8091 ! R:/*************:14444]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:14444', channel=[id: 0x7db4a6be, L:/*************:8091 ! R:/*************:14444], resourceSets=null}
2025-07-29 13:48:06 [ServerHandlerThread_1_23_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x60a6d495, L:/*************:8091 - R:/*************:3371],client version:1.7.1
2025-07-29 13:48:07 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753768087210
timestamp=1753768087210
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x9587e5ea, L:/*************:8091 - R:/*************:3374],client version:1.7.1
2025-07-29 13:48:07 [ServerHandlerThread_1_24_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xb5c71692, L:/*************:8091 - R:/*************:3427],client version:1.7.1
2025-07-29 13:48:14 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753768094515
timestamp=1753768094515
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xee3a0392, L:/*************:8091 - R:/*************:3484],client version:1.7.1
2025-07-29 13:48:14 [ServerHandlerThread_1_25_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xf64c82fb, L:/*************:8091 - R:/*************:3487],client version:1.7.1
2025-07-29 13:48:16 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753768096009
timestamp=1753768096009
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x34220b78, L:/*************:8091 - R:/*************:3494],client version:1.7.1
2025-07-29 13:48:16 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753768096428
timestamp=1753768096428
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x4be9a53d, L:/*************:8091 - R:/*************:3497],client version:1.7.1
2025-07-29 13:48:16 [ServerHandlerThread_1_26_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x8cdacc24, L:/*************:8091 - R:/*************:3498],client version:1.7.1
2025-07-29 13:53:06 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3374 to server channel inactive.
2025-07-29 13:53:06 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9587e5ea, L:/*************:8091 ! R:/*************:3374]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:3374', channel=[id: 0x9587e5ea, L:/*************:8091 ! R:/*************:3374], resourceSets=null}
2025-07-29 13:53:06 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3487 to server channel inactive.
2025-07-29 13:53:06 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3494 to server channel inactive.
2025-07-29 13:53:06 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3427 to server channel inactive.
2025-07-29 13:53:06 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf64c82fb, L:/*************:8091 ! R:/*************:3487]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:3487', channel=[id: 0xf64c82fb, L:/*************:8091 ! R:/*************:3487], resourceSets=[]}
2025-07-29 13:53:06 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x34220b78, L:/*************:8091 ! R:/*************:3494]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:3494', channel=[id: 0x34220b78, L:/*************:8091 ! R:/*************:3494], resourceSets=null}
2025-07-29 13:53:06 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb5c71692, L:/*************:8091 ! R:/*************:3427]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:3427', channel=[id: 0xb5c71692, L:/*************:8091 ! R:/*************:3427], resourceSets=null}
2025-07-29 13:53:06 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3484 to server channel inactive.
2025-07-29 13:53:06 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xee3a0392, L:/*************:8091 ! R:/*************:3484]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:3484', channel=[id: 0xee3a0392, L:/*************:8091 ! R:/*************:3484], resourceSets=null}
2025-07-29 13:53:06 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3498 to server channel inactive.
2025-07-29 13:53:06 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8cdacc24, L:/*************:8091 ! R:/*************:3498]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:3498', channel=[id: 0x8cdacc24, L:/*************:8091 ! R:/*************:3498], resourceSets=[]}
2025-07-29 13:53:06 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3497 to server channel inactive.
2025-07-29 13:53:06 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3371 to server channel inactive.
2025-07-29 13:53:06 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4be9a53d, L:/*************:8091 ! R:/*************:3497]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:3497', channel=[id: 0x4be9a53d, L:/*************:8091 ! R:/*************:3497], resourceSets=null}
2025-07-29 13:53:06 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x60a6d495, L:/*************:8091 ! R:/*************:3371]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:3371', channel=[id: 0x60a6d495, L:/*************:8091 ! R:/*************:3371], resourceSets=[]}
2025-07-29 13:53:06 [ServerHandlerThread_1_27_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x2a54a579, L:/*************:8091 - R:/*************:4183],client version:1.7.1
2025-07-29 13:53:07 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753768387209
timestamp=1753768387209
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x147ffbd9, L:/*************:8091 - R:/*************:4203],client version:1.7.1
2025-07-29 13:53:07 [ServerHandlerThread_1_28_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xe25e2e38, L:/*************:8091 - R:/*************:4218],client version:1.7.1
2025-07-29 13:53:14 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753768394506
timestamp=1753768394506
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x2990b050, L:/*************:8091 - R:/*************:4274],client version:1.7.1
2025-07-29 13:53:14 [ServerHandlerThread_1_29_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x9016d3da, L:/*************:8091 - R:/*************:4276],client version:1.7.1
2025-07-29 13:53:16 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753768396009
timestamp=1753768396009
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xee69e525, L:/*************:8091 - R:/*************:4283],client version:1.7.1
2025-07-29 13:53:16 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753768396432
timestamp=1753768396432
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x90d2da10, L:/*************:8091 - R:/*************:4286],client version:1.7.1
2025-07-29 13:53:16 [ServerHandlerThread_1_30_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xd5d0b372, L:/*************:8091 - R:/*************:4287],client version:1.7.1
2025-07-29 14:28:26 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4276 to server channel inactive.
2025-07-29 14:28:26 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4274 to server channel inactive.
2025-07-29 14:28:26 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2990b050, L:/*************:8091 ! R:/*************:4274]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4274', channel=[id: 0x2990b050, L:/*************:8091 ! R:/*************:4274], resourceSets=null}
2025-07-29 14:28:26 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9016d3da, L:/*************:8091 ! R:/*************:4276]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4276', channel=[id: 0x9016d3da, L:/*************:8091 ! R:/*************:4276], resourceSets=[]}
2025-07-29 14:58:52 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753772332251
timestamp=1753772332251
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x7d0e1df4, L:/*************:8091 - R:/*************:2146],client version:1.7.1
2025-07-29 14:58:54 [ServerHandlerThread_1_31_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xca0637ce, L:/*************:8091 - R:/*************:2154],client version:1.7.1
2025-07-29 15:19:18 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2154 to server channel inactive.
2025-07-29 15:19:18 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2146 to server channel inactive.
2025-07-29 15:19:18 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7d0e1df4, L:/*************:8091 ! R:/*************:2146]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2146', channel=[id: 0x7d0e1df4, L:/*************:8091 ! R:/*************:2146], resourceSets=null}
2025-07-29 15:19:18 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xca0637ce, L:/*************:8091 ! R:/*************:2154]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2154', channel=[id: 0xca0637ce, L:/*************:8091 ! R:/*************:2154], resourceSets=[]}
2025-07-29 15:19:43 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753773582748
timestamp=1753773582748
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x58d1ba0a, L:/*************:8091 - R:/*************:5782],client version:1.7.1
2025-07-29 15:19:47 [ServerHandlerThread_1_32_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x777e1189, L:/*************:8091 - R:/*************:5797],client version:1.7.1
2025-07-29 15:23:44 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5782 to server channel inactive.
2025-07-29 15:23:44 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5797 to server channel inactive.
2025-07-29 15:23:44 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x58d1ba0a, L:/*************:8091 ! R:/*************:5782]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5782', channel=[id: 0x58d1ba0a, L:/*************:8091 ! R:/*************:5782], resourceSets=null}
2025-07-29 15:23:44 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x777e1189, L:/*************:8091 ! R:/*************:5797]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5797', channel=[id: 0x777e1189, L:/*************:8091 ! R:/*************:5797], resourceSets=[]}
2025-07-29 15:23:56 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753773835921
timestamp=1753773835921
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x7e375408, L:/*************:8091 - R:/*************:6670],client version:1.7.1
2025-07-29 15:24:00 [ServerHandlerThread_1_33_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x0ec8b92f, L:/*************:8091 - R:/*************:6682],client version:1.7.1
2025-07-29 15:59:51 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6670 to server channel inactive.
2025-07-29 15:59:51 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6682 to server channel inactive.
2025-07-29 15:59:51 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7e375408, L:/*************:8091 ! R:/*************:6670]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6670', channel=[id: 0x7e375408, L:/*************:8091 ! R:/*************:6670], resourceSets=null}
2025-07-29 15:59:51 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0ec8b92f, L:/*************:8091 ! R:/*************:6682]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6682', channel=[id: 0x0ec8b92f, L:/*************:8091 ! R:/*************:6682], resourceSets=[]}
2025-07-29 16:00:15 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753776015131
timestamp=1753776015131
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x8db81497, L:/*************:8091 - R:/*************:12328],client version:1.7.1
2025-07-29 16:00:19 [ServerHandlerThread_1_34_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x68dfb961, L:/*************:8091 - R:/*************:12355],client version:1.7.1
2025-07-29 16:19:01 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12355 to server channel inactive.
2025-07-29 16:19:01 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12328 to server channel inactive.
2025-07-29 16:19:01 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8db81497, L:/*************:8091 ! R:/*************:12328]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12328', channel=[id: 0x8db81497, L:/*************:8091 ! R:/*************:12328], resourceSets=null}
2025-07-29 16:19:01 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x68dfb961, L:/*************:8091 ! R:/*************:12355]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12355', channel=[id: 0x68dfb961, L:/*************:8091 ! R:/*************:12355], resourceSets=[]}
2025-07-29 16:19:13 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753777152772
timestamp=1753777152772
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x49518312, L:/*************:8091 - R:/*************:1755],client version:1.7.1
2025-07-29 16:19:16 [ServerHandlerThread_1_35_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xd3501457, L:/*************:8091 - R:/*************:1767],client version:1.7.1
2025-07-29 16:20:04 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1755 to server channel inactive.
2025-07-29 16:20:04 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x49518312, L:/*************:8091 ! R:/*************:1755]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1755', channel=[id: 0x49518312, L:/*************:8091 ! R:/*************:1755], resourceSets=null}
2025-07-29 16:20:04 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1767 to server channel inactive.
2025-07-29 16:20:04 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd3501457, L:/*************:8091 ! R:/*************:1767]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1767', channel=[id: 0xd3501457, L:/*************:8091 ! R:/*************:1767], resourceSets=[]}
2025-07-29 16:20:20 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753777220047
timestamp=1753777220047
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x8fa9f40a, L:/*************:8091 - R:/*************:1903],client version:1.7.1
2025-07-29 16:20:23 [ServerHandlerThread_1_36_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x170d1587, L:/*************:8091 - R:/*************:1909],client version:1.7.1
2025-07-29 16:54:05 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1903 to server channel inactive.
2025-07-29 16:54:05 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1909 to server channel inactive.
2025-07-29 16:54:05 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8fa9f40a, L:/*************:8091 ! R:/*************:1903]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1903', channel=[id: 0x8fa9f40a, L:/*************:8091 ! R:/*************:1903], resourceSets=null}
2025-07-29 16:54:05 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x170d1587, L:/*************:8091 ! R:/*************:1909]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1909', channel=[id: 0x170d1587, L:/*************:8091 ! R:/*************:1909], resourceSets=[]}
2025-07-29 16:54:17 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753779256980
timestamp=1753779256980
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xab466bcb, L:/*************:8091 - R:/*************:6174],client version:1.7.1
2025-07-29 16:54:20 [ServerHandlerThread_1_37_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xcee27898, L:/*************:8091 - R:/*************:6185],client version:1.7.1
2025-07-29 21:49:29 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
