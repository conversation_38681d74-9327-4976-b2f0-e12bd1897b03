2025-07-23 09:48:23 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-23 09:48:23 [main] INFO  i.s.config.nacos.NacosConfiguration - <PERSON><PERSON> check auth with userName/password.
2025-07-23 09:48:23 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-23 09:48:23 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-23 09:48:24 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-23 09:48:24 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-23 09:48:24 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-23 09:48:24 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-23 09:48:24 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 5988 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-23 09:48:24 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-23 09:48:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-23 09:48:26 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-23 09:48:26 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 09:48:26 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-23 09:48:26 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 09:48:26 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1594 ms
2025-07-23 09:48:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-23 09:48:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-23 09:48:26 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-23 09:48:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5a23b9d1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3962b216, org.springframework.security.web.context.SecurityContextPersistenceFilter@25f73119, org.springframework.security.web.header.HeaderWriterFilter@400df2b3, org.springframework.security.web.authentication.logout.LogoutFilter@5a5b394, io.seata.console.filter.JwtAuthenticationTokenFilter@3183a37c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@f0d01c9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@20923380, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@31a80c88, org.springframework.security.web.session.SessionManagementFilter@17d45cfb, org.springframework.security.web.access.ExceptionTranslationFilter@71b32407, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2dd4a7a9]
2025-07-23 09:48:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-23 09:48:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-23 09:48:27 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.456 seconds (JVM running for 5.519)
2025-07-23 09:48:27 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-23 09:48:27 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-23 09:48:27 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-23 09:48:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-23 09:48:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-23 09:48:27 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-23 09:48:27 [main] INFO  io.seata.server.ServerRunner - seata server started in 673 millSeconds
2025-07-23 09:48:32 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753235312483
timestamp=1753235312483
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xac3436d0, L:/*************:8091 - R:/*************:9140],client version:1.7.1
2025-07-23 09:48:44 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753235324314
timestamp=1753235324314
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x07a9a1e3, L:/*************:8091 - R:/*************:9214],client version:1.7.1
2025-07-23 09:48:48 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xd3e62da7, L:/*************:8091 - R:/*************:9230],client version:1.7.1
2025-07-23 09:48:49 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753235329155
timestamp=1753235329155
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x0b0d7fa1, L:/*************:8091 - R:/*************:9237],client version:1.7.1
2025-07-23 09:48:53 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x12b55191, L:/*************:8091 - R:/*************:9267],client version:1.7.1
2025-07-23 09:49:32 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x1622cad6, L:/*************:8091 - R:/*************:9384],client version:1.7.1
2025-07-23 09:50:25 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753235425446
timestamp=1753235425446
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x45312133, L:/*************:8091 - R:/*************:9460],client version:1.7.1
2025-07-23 09:50:27 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x572c7729, L:/*************:8091 - R:/*************:9467],client version:1.7.1
2025-07-23 09:56:36 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9467 to server channel inactive.
2025-07-23 09:56:36 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9460 to server channel inactive.
2025-07-23 09:56:36 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x45312133, L:/*************:8091 ! R:/*************:9460]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9460', channel=[id: 0x45312133, L:/*************:8091 ! R:/*************:9460], resourceSets=null}
2025-07-23 09:56:36 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x572c7729, L:/*************:8091 ! R:/*************:9467]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9467', channel=[id: 0x572c7729, L:/*************:8091 ! R:/*************:9467], resourceSets=[]}
2025-07-23 09:56:50 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753235810459
timestamp=1753235810459
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x44c6d3fa, L:/*************:8091 - R:/*************:10004],client version:1.7.1
2025-07-23 09:56:54 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xa6c3e0be, L:/*************:8091 - R:/*************:10015],client version:1.7.1
2025-07-23 11:12:43 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10004 to server channel inactive.
2025-07-23 11:12:43 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x44c6d3fa, L:/*************:8091 ! R:/*************:10004]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10004', channel=[id: 0x44c6d3fa, L:/*************:8091 ! R:/*************:10004], resourceSets=null}
2025-07-23 11:12:43 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10015 to server channel inactive.
2025-07-23 11:12:43 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa6c3e0be, L:/*************:8091 ! R:/*************:10015]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10015', channel=[id: 0xa6c3e0be, L:/*************:8091 ! R:/*************:10015], resourceSets=[]}
2025-07-23 11:12:55 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753240375142
timestamp=1753240375142
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa3d37823, L:/*************:8091 - R:/*************:6281],client version:1.7.1
2025-07-23 11:12:59 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xd7ee6bc2, L:/*************:8091 - R:/*************:6287],client version:1.7.1
2025-07-23 12:37:27 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6287 to server channel inactive.
2025-07-23 12:37:27 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd7ee6bc2, L:/*************:8091 ! R:/*************:6287]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6287', channel=[id: 0xd7ee6bc2, L:/*************:8091 ! R:/*************:6287], resourceSets=[]}
2025-07-23 12:37:27 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xac3436d0, L:/*************:8091 - R:/*************:9140] read idle.
2025-07-23 12:37:27 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9140 to server channel inactive.
2025-07-23 12:37:27 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xac3436d0, L:/*************:8091 - R:/*************:9140]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:9140', channel=[id: 0xac3436d0, L:/*************:8091 - R:/*************:9140], resourceSets=null}
2025-07-23 12:37:27 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xac3436d0, L:/*************:8091 - R:/*************:9140]
2025-07-23 12:37:27 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9140 to server channel inactive.
2025-07-23 12:37:27 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xac3436d0, L:/*************:8091 ! R:/*************:9140]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:9140', channel=[id: 0xac3436d0, L:/*************:8091 ! R:/*************:9140], resourceSets=null}
2025-07-23 12:37:27 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9384 to server channel inactive.
2025-07-23 12:37:27 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1622cad6, L:/*************:8091 ! R:/*************:9384]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:9384', channel=[id: 0x1622cad6, L:/*************:8091 ! R:/*************:9384], resourceSets=null}
2025-07-23 12:37:28 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9230 to server channel inactive.
2025-07-23 12:37:28 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd3e62da7, L:/*************:8091 ! R:/*************:9230]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:9230', channel=[id: 0xd3e62da7, L:/*************:8091 ! R:/*************:9230], resourceSets=[]}
2025-07-23 12:37:28 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xcc1b65b1, L:/*************:8091 - R:/*************:7692],client version:1.7.1
2025-07-23 12:37:28 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9267 to server channel inactive.
2025-07-23 12:37:28 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x12b55191, L:/*************:8091 ! R:/*************:9267]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:9267', channel=[id: 0x12b55191, L:/*************:8091 ! R:/*************:9267], resourceSets=[]}
2025-07-23 12:37:28 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6281 to server channel inactive.
2025-07-23 12:37:28 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa3d37823, L:/*************:8091 ! R:/*************:6281]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6281', channel=[id: 0xa3d37823, L:/*************:8091 ! R:/*************:6281], resourceSets=null}
2025-07-23 12:37:29 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9237 to server channel inactive.
2025-07-23 12:37:29 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0b0d7fa1, L:/*************:8091 ! R:/*************:9237]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:9237', channel=[id: 0x0b0d7fa1, L:/*************:8091 ! R:/*************:9237], resourceSets=null}
2025-07-23 12:37:29 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9214 to server channel inactive.
2025-07-23 12:37:29 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x07a9a1e3, L:/*************:8091 ! R:/*************:9214]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:9214', channel=[id: 0x07a9a1e3, L:/*************:8091 ! R:/*************:9214], resourceSets=null}
2025-07-23 12:37:29 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753245449542
timestamp=1753245449542
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xaace9421, L:/*************:8091 - R:/*************:7749],client version:1.7.1
2025-07-23 12:37:29 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x6c4a6a8a, L:/*************:8091 - R:/*************:7759],client version:1.7.1
2025-07-23 12:37:31 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753245451246
timestamp=1753245451246
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x765aebae, L:/*************:8091 - R:/*************:7777],client version:1.7.1
2025-07-23 12:37:31 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xf5384147, L:/*************:8091 - R:/*************:7789],client version:1.7.1
2025-07-23 12:37:32 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753245452072
timestamp=1753245452072
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x7154a31e, L:/*************:8091 - R:/*************:7792],client version:1.7.1
2025-07-23 12:37:36 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753245456148
timestamp=1753245456148
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xa03c5883, L:/*************:8091 - R:/*************:8267],client version:1.7.1
2025-07-23 12:37:36 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x1d74b921, L:/*************:8091 - R:/*************:8277],client version:1.7.1
2025-07-23 13:11:10 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x1d74b921, L:/*************:8091 - R:/*************:8277] read idle.
2025-07-23 13:11:10 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7789 to server channel inactive.
2025-07-23 13:11:10 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7792 to server channel inactive.
2025-07-23 13:11:10 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7154a31e, L:/*************:8091 ! R:/*************:7792]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7792', channel=[id: 0x7154a31e, L:/*************:8091 ! R:/*************:7792], resourceSets=null}
2025-07-23 13:11:10 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8277 to server channel inactive.
2025-07-23 13:11:10 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1d74b921, L:/*************:8091 - R:/*************:8277]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:8277', channel=[id: 0x1d74b921, L:/*************:8091 - R:/*************:8277], resourceSets=[]}
2025-07-23 13:11:10 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x1d74b921, L:/*************:8091 - R:/*************:8277]
2025-07-23 13:11:10 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf5384147, L:/*************:8091 ! R:/*************:7789]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:7789', channel=[id: 0xf5384147, L:/*************:8091 ! R:/*************:7789], resourceSets=[]}
2025-07-23 13:11:10 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8277 to server channel inactive.
2025-07-23 13:11:10 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1d74b921, L:/*************:8091 ! R:/*************:8277]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:8277', channel=[id: 0x1d74b921, L:/*************:8091 ! R:/*************:8277], resourceSets=[]}
2025-07-23 13:11:11 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x9fdd543f, L:/*************:8091 - R:/*************:9759],client version:1.7.1
2025-07-23 13:11:11 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7692 to server channel inactive.
2025-07-23 13:11:11 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcc1b65b1, L:/*************:8091 ! R:/*************:7692]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7692', channel=[id: 0xcc1b65b1, L:/*************:8091 ! R:/*************:7692], resourceSets=[]}
2025-07-23 13:11:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xaace9421, L:/*************:8091 - R:/*************:7749] read idle.
2025-07-23 13:11:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7749 to server channel inactive.
2025-07-23 13:11:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xaace9421, L:/*************:8091 - R:/*************:7749]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:7749', channel=[id: 0xaace9421, L:/*************:8091 - R:/*************:7749], resourceSets=null}
2025-07-23 13:11:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xaace9421, L:/*************:8091 - R:/*************:7749]
2025-07-23 13:11:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7749 to server channel inactive.
2025-07-23 13:11:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xaace9421, L:/*************:8091 ! R:/*************:7749]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:7749', channel=[id: 0xaace9421, L:/*************:8091 ! R:/*************:7749], resourceSets=null}
2025-07-23 13:11:12 [ServerHandlerThread_1_12_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x4ef4ac06, L:/*************:8091 - R:/*************:9900],client version:1.7.1
2025-07-23 13:11:12 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7759 to server channel inactive.
2025-07-23 13:11:12 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6c4a6a8a, L:/*************:8091 ! R:/*************:7759]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:7759', channel=[id: 0x6c4a6a8a, L:/*************:8091 ! R:/*************:7759], resourceSets=null}
2025-07-23 13:11:14 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8267 to server channel inactive.
2025-07-23 13:11:14 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa03c5883, L:/*************:8091 ! R:/*************:8267]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:8267', channel=[id: 0xa03c5883, L:/*************:8091 ! R:/*************:8267], resourceSets=null}
2025-07-23 13:11:14 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7777 to server channel inactive.
2025-07-23 13:11:14 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x765aebae, L:/*************:8091 ! R:/*************:7777]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:7777', channel=[id: 0x765aebae, L:/*************:8091 ! R:/*************:7777], resourceSets=null}
2025-07-23 13:11:16 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753247476596
timestamp=1753247476596
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x40799617, L:/*************:8091 - R:/*************:9945],client version:1.7.1
2025-07-23 13:11:17 [ServerHandlerThread_1_13_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x8f6b8549, L:/*************:8091 - R:/*************:9948],client version:1.7.1
2025-07-23 13:11:18 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753247478298
timestamp=1753247478298
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x2bd17ee4, L:/*************:8091 - R:/*************:9951],client version:1.7.1
2025-07-23 13:11:19 [ServerHandlerThread_1_14_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x2f2ac846, L:/*************:8091 - R:/*************:9955],client version:1.7.1
2025-07-23 13:11:20 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753247480435
timestamp=1753247480435
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x4fadfd74, L:/*************:8091 - R:/*************:9960],client version:1.7.1
2025-07-23 13:11:23 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753247483201
timestamp=1753247483201
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xcaebf816, L:/*************:8091 - R:/*************:9972],client version:1.7.1
2025-07-23 13:18:51 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x2bd17ee4, L:/*************:8091 - R:/*************:9951] read idle.
2025-07-23 13:18:51 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9972 to server channel inactive.
2025-07-23 13:18:51 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcaebf816, L:/*************:8091 ! R:/*************:9972]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:9972', channel=[id: 0xcaebf816, L:/*************:8091 ! R:/*************:9972], resourceSets=null}
2025-07-23 13:18:51 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9951 to server channel inactive.
2025-07-23 13:18:51 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2bd17ee4, L:/*************:8091 - R:/*************:9951]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:9951', channel=[id: 0x2bd17ee4, L:/*************:8091 - R:/*************:9951], resourceSets=null}
2025-07-23 13:18:51 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x2bd17ee4, L:/*************:8091 - R:/*************:9951]
2025-07-23 13:18:51 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9951 to server channel inactive.
2025-07-23 13:18:51 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2bd17ee4, L:/*************:8091 ! R:/*************:9951]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:9951', channel=[id: 0x2bd17ee4, L:/*************:8091 ! R:/*************:9951], resourceSets=null}
2025-07-23 13:18:51 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753247931538
timestamp=1753247931538
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xaf5a5a89, L:/*************:8091 - R:/*************:10203],client version:1.7.1
2025-07-23 13:18:51 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9955 to server channel inactive.
2025-07-23 13:18:51 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2f2ac846, L:/*************:8091 ! R:/*************:9955]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9955', channel=[id: 0x2f2ac846, L:/*************:8091 ! R:/*************:9955], resourceSets=[]}
2025-07-23 13:18:52 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x5b1ae4b3, L:/*************:8091 - R:/*************:9959] read idle.
2025-07-23 13:18:52 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9960 to server channel inactive.
2025-07-23 13:18:52 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9959 to server channel inactive.
2025-07-23 13:18:52 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4fadfd74, L:/*************:8091 ! R:/*************:9960]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9960', channel=[id: 0x4fadfd74, L:/*************:8091 ! R:/*************:9960], resourceSets=null}
2025-07-23 13:18:52 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove unused channel:[id: 0x5b1ae4b3, L:/*************:8091 - R:/*************:9959]
2025-07-23 13:18:52 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x5b1ae4b3, L:/*************:8091 - R:/*************:9959]
2025-07-23 13:18:52 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9959 to server channel inactive.
2025-07-23 13:18:52 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove unused channel:[id: 0x5b1ae4b3, L:/*************:8091 ! R:/*************:9959]
2025-07-23 13:18:53 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753247933363
timestamp=1753247933363
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x8e4cab50, L:/*************:8091 - R:/*************:10225],client version:1.7.1
2025-07-23 13:18:53 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9759 to server channel inactive.
2025-07-23 13:18:53 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9fdd543f, L:/*************:8091 ! R:/*************:9759]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:9759', channel=[id: 0x9fdd543f, L:/*************:8091 ! R:/*************:9759], resourceSets=[]}
2025-07-23 13:18:53 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9945 to server channel inactive.
2025-07-23 13:18:53 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x40799617, L:/*************:8091 ! R:/*************:9945]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:9945', channel=[id: 0x40799617, L:/*************:8091 ! R:/*************:9945], resourceSets=null}
2025-07-23 13:18:53 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753247933880
timestamp=1753247933880
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x862cfe62, L:/*************:8091 - R:/*************:10228],client version:1.7.1
2025-07-23 13:18:54 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9948 to server channel inactive.
2025-07-23 13:18:54 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8f6b8549, L:/*************:8091 ! R:/*************:9948]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:9948', channel=[id: 0x8f6b8549, L:/*************:8091 ! R:/*************:9948], resourceSets=null}
2025-07-23 13:18:54 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9900 to server channel inactive.
2025-07-23 13:18:54 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4ef4ac06, L:/*************:8091 ! R:/*************:9900]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:9900', channel=[id: 0x4ef4ac06, L:/*************:8091 ! R:/*************:9900], resourceSets=[]}
2025-07-23 13:18:56 [ServerHandlerThread_1_15_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x0bf288bd, L:/*************:8091 - R:/*************:10248],client version:1.7.1
2025-07-23 13:18:56 [ServerHandlerThread_1_16_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xb53fd740, L:/*************:8091 - R:/*************:10251],client version:1.7.1
2025-07-23 13:19:00 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753247940494
timestamp=1753247940494
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x8cf5da36, L:/*************:8091 - R:/*************:10301],client version:1.7.1
2025-07-23 13:19:01 [ServerHandlerThread_1_17_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xf012ee60, L:/*************:8091 - R:/*************:10304],client version:1.7.1
2025-07-23 13:19:04 [ServerHandlerThread_1_18_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x9aa76877, L:/*************:8091 - R:/*************:10317],client version:1.7.1
2025-07-23 13:48:17 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10225 to server channel inactive.
2025-07-23 13:48:17 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8e4cab50, L:/*************:8091 ! R:/*************:10225]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10225', channel=[id: 0x8e4cab50, L:/*************:8091 ! R:/*************:10225], resourceSets=null}
2025-07-23 13:48:17 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10251 to server channel inactive.
2025-07-23 13:48:17 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb53fd740, L:/*************:8091 ! R:/*************:10251]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10251', channel=[id: 0xb53fd740, L:/*************:8091 ! R:/*************:10251], resourceSets=[]}
2025-07-23 13:48:32 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753249711858
timestamp=1753249711858
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x56cb3fbc, L:/*************:8091 - R:/*************:12972],client version:1.7.1
2025-07-23 13:48:35 [ServerHandlerThread_1_19_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xf39225b5, L:/*************:8091 - R:/*************:12982],client version:1.7.1
2025-07-23 14:30:44 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12972 to server channel inactive.
2025-07-23 14:30:44 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12982 to server channel inactive.
2025-07-23 14:30:44 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x56cb3fbc, L:/*************:8091 ! R:/*************:12972]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12972', channel=[id: 0x56cb3fbc, L:/*************:8091 ! R:/*************:12972], resourceSets=null}
2025-07-23 14:30:44 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf39225b5, L:/*************:8091 ! R:/*************:12982]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12982', channel=[id: 0xf39225b5, L:/*************:8091 ! R:/*************:12982], resourceSets=[]}
2025-07-23 14:30:55 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753252255319
timestamp=1753252255319
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x1dfa83f6, L:/*************:8091 - R:/*************:4809],client version:1.7.1
2025-07-23 14:30:59 [ServerHandlerThread_1_20_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xa0a8cf25, L:/*************:8091 - R:/*************:11497],client version:1.7.1
2025-07-23 14:33:52 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4809 to server channel inactive.
2025-07-23 14:33:52 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1dfa83f6, L:/*************:8091 ! R:/*************:4809]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4809', channel=[id: 0x1dfa83f6, L:/*************:8091 ! R:/*************:4809], resourceSets=null}
2025-07-23 14:33:52 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11497 to server channel inactive.
2025-07-23 14:33:52 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa0a8cf25, L:/*************:8091 ! R:/*************:11497]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11497', channel=[id: 0xa0a8cf25, L:/*************:8091 ! R:/*************:11497], resourceSets=[]}
2025-07-23 14:34:03 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753252443086
timestamp=1753252443086
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xc7b723f9, L:/*************:8091 - R:/*************:4289],client version:1.7.1
2025-07-23 14:34:06 [ServerHandlerThread_1_21_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x5b71cdbd, L:/*************:8091 - R:/*************:13820],client version:1.7.1
2025-07-23 15:44:08 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10203 to server channel inactive.
2025-07-23 15:44:08 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xaf5a5a89, L:/*************:8091 ! R:/*************:10203]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:10203', channel=[id: 0xaf5a5a89, L:/*************:8091 ! R:/*************:10203], resourceSets=null}
2025-07-23 15:44:08 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10228 to server channel inactive.
2025-07-23 15:44:08 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x862cfe62, L:/*************:8091 ! R:/*************:10228]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:10228', channel=[id: 0x862cfe62, L:/*************:8091 ! R:/*************:10228], resourceSets=null}
2025-07-23 15:44:08 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10248 to server channel inactive.
2025-07-23 15:44:08 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0bf288bd, L:/*************:8091 ! R:/*************:10248]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:10248', channel=[id: 0x0bf288bd, L:/*************:8091 ! R:/*************:10248], resourceSets=[]}
2025-07-23 15:44:08 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13820 to server channel inactive.
2025-07-23 15:44:08 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5b71cdbd, L:/*************:8091 ! R:/*************:13820]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13820', channel=[id: 0x5b71cdbd, L:/*************:8091 ! R:/*************:13820], resourceSets=[]}
2025-07-23 15:44:08 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4289 to server channel inactive.
2025-07-23 15:44:08 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc7b723f9, L:/*************:8091 ! R:/*************:4289]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4289', channel=[id: 0xc7b723f9, L:/*************:8091 ! R:/*************:4289], resourceSets=null}
2025-07-23 15:44:08 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10304 to server channel inactive.
2025-07-23 15:44:08 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf012ee60, L:/*************:8091 ! R:/*************:10304]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:10304', channel=[id: 0xf012ee60, L:/*************:8091 ! R:/*************:10304], resourceSets=[]}
2025-07-23 15:44:08 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10317 to server channel inactive.
2025-07-23 15:44:08 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9aa76877, L:/*************:8091 ! R:/*************:10317]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:10317', channel=[id: 0x9aa76877, L:/*************:8091 ! R:/*************:10317], resourceSets=null}
2025-07-23 15:44:08 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10301 to server channel inactive.
2025-07-23 15:44:08 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8cf5da36, L:/*************:8091 ! R:/*************:10301]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:10301', channel=[id: 0x8cf5da36, L:/*************:8091 ! R:/*************:10301], resourceSets=null}
2025-07-23 15:44:12 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753256652862
timestamp=1753256652862
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xdf4473c0, L:/*************:8091 - R:/*************:12511],client version:1.7.1
2025-07-23 15:44:13 [ServerHandlerThread_1_22_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xdd2b8e86, L:/*************:8091 - R:/*************:12562],client version:1.7.1
2025-07-23 15:44:13 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753256653880
timestamp=1753256653880
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x4bdae99f, L:/*************:8091 - R:/*************:12579],client version:1.7.1
2025-07-23 15:44:14 [ServerHandlerThread_1_23_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x02b50dab, L:/*************:8091 - R:/*************:12585],client version:1.7.1
2025-07-23 15:44:15 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753256655597
timestamp=1753256655597
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x8a24ac2e, L:/*************:8091 - R:/*************:12600],client version:1.7.1
2025-07-23 15:44:16 [ServerHandlerThread_1_24_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x5b7778ea, L:/*************:8091 - R:/*************:12604],client version:1.7.1
2025-07-23 15:44:20 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753256660486
timestamp=1753256660486
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x49b40083, L:/*************:8091 - R:/*************:12626],client version:1.7.1
2025-07-23 15:44:21 [ServerHandlerThread_1_25_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xab4f4adb, L:/*************:8091 - R:/*************:12628],client version:1.7.1
2025-07-23 15:54:36 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12511 to server channel inactive.
2025-07-23 15:54:36 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12562 to server channel inactive.
2025-07-23 15:54:36 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdf4473c0, L:/*************:8091 ! R:/*************:12511]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12511', channel=[id: 0xdf4473c0, L:/*************:8091 ! R:/*************:12511], resourceSets=null}
2025-07-23 15:54:36 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdd2b8e86, L:/*************:8091 ! R:/*************:12562]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12562', channel=[id: 0xdd2b8e86, L:/*************:8091 ! R:/*************:12562], resourceSets=[]}
2025-07-23 16:14:11 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-23 16:17:43 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-23 16:17:43 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-23 16:17:43 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-23 16:17:43 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-23 16:17:45 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-23 16:17:45 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-23 16:17:45 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-23 16:17:45 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-23 16:17:45 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 37396 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-23 16:17:45 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-23 16:17:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-23 16:17:47 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-23 16:17:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 16:17:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-23 16:17:47 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 16:17:47 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2167 ms
2025-07-23 16:17:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-23 16:17:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-23 16:17:48 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-23 16:17:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@75566f4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6785786d, org.springframework.security.web.context.SecurityContextPersistenceFilter@20923380, org.springframework.security.web.header.HeaderWriterFilter@5c13534a, org.springframework.security.web.authentication.logout.LogoutFilter@31ab75a5, io.seata.console.filter.JwtAuthenticationTokenFilter@5c261c74, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6520625f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@47fce2c4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d38a81d, org.springframework.security.web.session.SessionManagementFilter@205b73d8, org.springframework.security.web.access.ExceptionTranslationFilter@3b11deb6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5a5c2889]
2025-07-23 16:17:49 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-23 16:17:49 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-23 16:17:49 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 6.325 seconds (JVM running for 7.713)
2025-07-23 16:17:49 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-23 16:17:49 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-23 16:17:49 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-23 16:17:49 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-23 16:17:49 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-23 16:17:49 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-23 16:17:49 [main] INFO  io.seata.server.ServerRunner - seata server started in 912 millSeconds
2025-07-23 16:17:58 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753258678711
timestamp=1753258678711
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x369c6200, L:/*************:8091 - R:/*************:13690],client version:1.7.1
2025-07-23 16:18:58 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x1cf28157, L:/*************:8091 - R:/*************:13758],client version:1.7.1
2025-07-23 16:48:23 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753260503377
timestamp=1753260503377
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xdec9c143, L:/*************:8091 - R:/*************:1414],client version:1.7.1
2025-07-23 16:48:24 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753260504307
timestamp=1753260504307
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x25490ebf, L:/*************:8091 - R:/*************:1417],client version:1.7.1
2025-07-23 16:48:25 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753260505678
timestamp=1753260505678
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xd3d1ff6d, L:/*************:8091 - R:/*************:1422],client version:1.7.1
2025-07-23 16:48:26 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x1ee51272, L:/*************:8091 - R:/*************:1427],client version:1.7.1
2025-07-23 16:48:27 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x702fc9ed, L:/*************:8091 - R:/*************:1433],client version:1.7.1
2025-07-23 16:48:29 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xa2d63847, L:/*************:8091 - R:/*************:1453],client version:1.7.1
2025-07-23 17:20:22 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1433 to server channel inactive.
2025-07-23 17:20:22 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1417 to server channel inactive.
2025-07-23 17:20:22 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x25490ebf, L:/*************:8091 ! R:/*************:1417]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1417', channel=[id: 0x25490ebf, L:/*************:8091 ! R:/*************:1417], resourceSets=null}
2025-07-23 17:20:22 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x702fc9ed, L:/*************:8091 ! R:/*************:1433]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1433', channel=[id: 0x702fc9ed, L:/*************:8091 ! R:/*************:1433], resourceSets=[]}
2025-07-23 17:20:42 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753262442710
timestamp=1753262442710
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x39c774d4, L:/*************:8091 - R:/*************:5318],client version:1.7.1
2025-07-23 17:20:47 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xba47c3f2, L:/*************:8091 - R:/*************:5325],client version:1.7.1
2025-07-23 17:30:32 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xba47c3f2, L:/*************:8091 - R:/*************:5325] read idle.
2025-07-23 17:30:32 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5325 to server channel inactive.
2025-07-23 17:30:32 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xba47c3f2, L:/*************:8091 - R:/*************:5325]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5325', channel=[id: 0xba47c3f2, L:/*************:8091 - R:/*************:5325], resourceSets=[]}
2025-07-23 17:30:32 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xba47c3f2, L:/*************:8091 - R:/*************:5325]
2025-07-23 17:30:32 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5325 to server channel inactive.
2025-07-23 17:30:32 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xba47c3f2, L:/*************:8091 ! R:/*************:5325]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5325', channel=[id: 0xba47c3f2, L:/*************:8091 ! R:/*************:5325], resourceSets=[]}
2025-07-23 17:30:38 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x39c774d4, L:/*************:8091 - R:/*************:5318] read idle.
2025-07-23 17:30:38 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5318 to server channel inactive.
2025-07-23 17:30:38 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x39c774d4, L:/*************:8091 - R:/*************:5318]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5318', channel=[id: 0x39c774d4, L:/*************:8091 - R:/*************:5318], resourceSets=null}
2025-07-23 17:30:38 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x39c774d4, L:/*************:8091 - R:/*************:5318]
2025-07-23 17:30:38 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5318 to server channel inactive.
2025-07-23 17:30:38 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x39c774d4, L:/*************:8091 ! R:/*************:5318]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5318', channel=[id: 0x39c774d4, L:/*************:8091 ! R:/*************:5318], resourceSets=null}
2025-07-23 17:33:14 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x6e7a78f6, L:/*************:8091 - R:/*************:6489],client version:1.7.1
2025-07-23 17:33:14 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753263194416
timestamp=1753263194416
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x637167e3, L:/*************:8091 - R:/*************:6488],client version:1.7.1
2025-07-23 17:33:16 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6488 to server channel inactive.
2025-07-23 17:33:16 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x637167e3, L:/*************:8091 ! R:/*************:6488]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6488', channel=[id: 0x637167e3, L:/*************:8091 ! R:/*************:6488], resourceSets=null}
2025-07-23 17:33:16 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6489 to server channel inactive.
2025-07-23 17:33:16 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6e7a78f6, L:/*************:8091 ! R:/*************:6489]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6489', channel=[id: 0x6e7a78f6, L:/*************:8091 ! R:/*************:6489], resourceSets=[]}
2025-07-23 17:33:30 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753263210422
timestamp=1753263210422
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x9ac83d51, L:/*************:8091 - R:/*************:6533],client version:1.7.1
2025-07-23 17:33:34 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x691958ca, L:/*************:8091 - R:/*************:6547],client version:1.7.1
2025-07-23 17:34:05 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x9ac83d51, L:/*************:8091 - R:/*************:6533] read idle.
2025-07-23 17:34:05 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6533 to server channel inactive.
2025-07-23 17:34:05 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9ac83d51, L:/*************:8091 - R:/*************:6533]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6533', channel=[id: 0x9ac83d51, L:/*************:8091 - R:/*************:6533], resourceSets=null}
2025-07-23 17:34:05 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x9ac83d51, L:/*************:8091 - R:/*************:6533]
2025-07-23 17:34:05 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6533 to server channel inactive.
2025-07-23 17:34:05 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9ac83d51, L:/*************:8091 ! R:/*************:6533]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6533', channel=[id: 0x9ac83d51, L:/*************:8091 ! R:/*************:6533], resourceSets=null}
2025-07-23 17:34:09 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x691958ca, L:/*************:8091 - R:/*************:6547] read idle.
2025-07-23 17:34:09 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6547 to server channel inactive.
2025-07-23 17:34:09 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x691958ca, L:/*************:8091 - R:/*************:6547]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6547', channel=[id: 0x691958ca, L:/*************:8091 - R:/*************:6547], resourceSets=[]}
2025-07-23 17:34:09 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x691958ca, L:/*************:8091 - R:/*************:6547]
2025-07-23 17:34:09 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6547 to server channel inactive.
2025-07-23 17:34:09 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x691958ca, L:/*************:8091 ! R:/*************:6547]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6547', channel=[id: 0x691958ca, L:/*************:8091 ! R:/*************:6547], resourceSets=[]}
2025-07-23 17:34:30 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753263270199
timestamp=1753263270199
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x15443f32, L:/*************:8091 - R:/*************:6688],client version:1.7.1
2025-07-23 17:34:30 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xeb97b574, L:/*************:8091 - R:/*************:6689],client version:1.7.1
2025-07-23 17:36:07 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6688 to server channel inactive.
2025-07-23 17:36:07 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x15443f32, L:/*************:8091 ! R:/*************:6688]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6688', channel=[id: 0x15443f32, L:/*************:8091 ! R:/*************:6688], resourceSets=null}
2025-07-23 17:36:07 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6689 to server channel inactive.
2025-07-23 17:36:07 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xeb97b574, L:/*************:8091 ! R:/*************:6689]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6689', channel=[id: 0xeb97b574, L:/*************:8091 ! R:/*************:6689], resourceSets=[]}
2025-07-23 17:36:22 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753263382216
timestamp=1753263382216
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x692d7536, L:/*************:8091 - R:/*************:6901],client version:1.7.1
2025-07-23 17:36:25 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x25d98db4, L:/*************:8091 - R:/*************:6909],client version:1.7.1
2025-07-23 17:40:16 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6901 to server channel inactive.
2025-07-23 17:40:16 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x692d7536, L:/*************:8091 ! R:/*************:6901]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6901', channel=[id: 0x692d7536, L:/*************:8091 ! R:/*************:6901], resourceSets=null}
2025-07-23 17:40:16 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6909 to server channel inactive.
2025-07-23 17:40:16 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x25d98db4, L:/*************:8091 ! R:/*************:6909]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6909', channel=[id: 0x25d98db4, L:/*************:8091 ! R:/*************:6909], resourceSets=[]}
2025-07-23 17:40:27 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753263627013
timestamp=1753263627013
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x73f2a7d7, L:/*************:8091 - R:/*************:7366],client version:1.7.1
2025-07-23 17:40:30 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x07ba93e5, L:/*************:8091 - R:/*************:7371],client version:1.7.1
2025-07-23 17:44:07 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7366 to server channel inactive.
2025-07-23 17:44:07 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7371 to server channel inactive.
2025-07-23 17:44:07 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x07ba93e5, L:/*************:8091 ! R:/*************:7371]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7371', channel=[id: 0x07ba93e5, L:/*************:8091 ! R:/*************:7371], resourceSets=[]}
2025-07-23 17:44:07 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x73f2a7d7, L:/*************:8091 ! R:/*************:7366]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7366', channel=[id: 0x73f2a7d7, L:/*************:8091 ! R:/*************:7366], resourceSets=null}
2025-07-23 17:44:21 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753263861522
timestamp=1753263861522
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x5bf1b5a4, L:/*************:8091 - R:/*************:7779],client version:1.7.1
2025-07-23 17:44:25 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x7d6c624a, L:/*************:8091 - R:/*************:7787],client version:1.7.1
2025-07-23 17:46:46 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7787 to server channel inactive.
2025-07-23 17:46:46 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7779 to server channel inactive.
2025-07-23 17:46:46 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5bf1b5a4, L:/*************:8091 ! R:/*************:7779]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7779', channel=[id: 0x5bf1b5a4, L:/*************:8091 ! R:/*************:7779], resourceSets=null}
2025-07-23 17:46:46 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7d6c624a, L:/*************:8091 ! R:/*************:7787]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7787', channel=[id: 0x7d6c624a, L:/*************:8091 ! R:/*************:7787], resourceSets=[]}
2025-07-23 17:47:01 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753264021514
timestamp=1753264021514
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x64b325c9, L:/*************:8091 - R:/*************:8419],client version:1.7.1
2025-07-23 17:47:05 [ServerHandlerThread_1_12_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x95192189, L:/*************:8091 - R:/*************:8448],client version:1.7.1
2025-07-23 19:18:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xdec9c143, L:/*************:8091 - R:/*************:1414] read idle.
2025-07-23 19:18:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1414 to server channel inactive.
2025-07-23 19:18:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdec9c143, L:/*************:8091 - R:/*************:1414]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:1414', channel=[id: 0xdec9c143, L:/*************:8091 - R:/*************:1414], resourceSets=null}
2025-07-23 19:18:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xdec9c143, L:/*************:8091 - R:/*************:1414]
2025-07-23 19:18:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1414 to server channel inactive.
2025-07-23 19:18:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdec9c143, L:/*************:8091 ! R:/*************:1414]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:1414', channel=[id: 0xdec9c143, L:/*************:8091 ! R:/*************:1414], resourceSets=null}
2025-07-23 19:18:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13690 to server channel inactive.
2025-07-23 19:18:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x369c6200, L:/*************:8091 ! R:/*************:13690]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13690', channel=[id: 0x369c6200, L:/*************:8091 ! R:/*************:13690], resourceSets=null}
2025-07-23 19:18:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1453 to server channel inactive.
2025-07-23 19:18:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa2d63847, L:/*************:8091 ! R:/*************:1453]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:1453', channel=[id: 0xa2d63847, L:/*************:8091 ! R:/*************:1453], resourceSets=[]}
2025-07-23 19:18:47 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13758 to server channel inactive.
2025-07-23 19:18:47 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1cf28157, L:/*************:8091 ! R:/*************:13758]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13758', channel=[id: 0x1cf28157, L:/*************:8091 ! R:/*************:13758], resourceSets=null}
2025-07-23 19:18:48 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x95192189, L:/*************:8091 - R:/*************:8448] read idle.
2025-07-23 19:18:48 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8448 to server channel inactive.
2025-07-23 19:18:48 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x95192189, L:/*************:8091 - R:/*************:8448]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8448', channel=[id: 0x95192189, L:/*************:8091 - R:/*************:8448], resourceSets=[]}
2025-07-23 19:18:48 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x95192189, L:/*************:8091 - R:/*************:8448]
2025-07-23 19:18:48 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8448 to server channel inactive.
2025-07-23 19:18:48 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x95192189, L:/*************:8091 ! R:/*************:8448]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8448', channel=[id: 0x95192189, L:/*************:8091 ! R:/*************:8448], resourceSets=[]}
2025-07-23 19:18:49 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1422 to server channel inactive.
2025-07-23 19:18:49 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd3d1ff6d, L:/*************:8091 ! R:/*************:1422]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:1422', channel=[id: 0xd3d1ff6d, L:/*************:8091 ! R:/*************:1422], resourceSets=null}
2025-07-23 19:18:49 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8419 to server channel inactive.
2025-07-23 19:18:49 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x64b325c9, L:/*************:8091 ! R:/*************:8419]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8419', channel=[id: 0x64b325c9, L:/*************:8091 ! R:/*************:8419], resourceSets=null}
2025-07-23 19:18:49 [ServerHandlerThread_1_13_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x238a3d68, L:/*************:8091 - R:/*************:10454],client version:1.7.1
2025-07-23 19:18:50 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1427 to server channel inactive.
2025-07-23 19:18:50 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1ee51272, L:/*************:8091 ! R:/*************:1427]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:1427', channel=[id: 0x1ee51272, L:/*************:8091 ! R:/*************:1427], resourceSets=[]}
2025-07-23 19:18:50 [ServerHandlerThread_1_14_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x70822eac, L:/*************:8091 - R:/*************:10460],client version:1.7.1
2025-07-23 19:18:52 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753269532561
timestamp=1753269532561
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xb15474bd, L:/*************:8091 - R:/*************:10950],client version:1.7.1
2025-07-23 19:18:52 [ServerHandlerThread_1_15_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x8b17428c, L:/*************:8091 - R:/*************:10961],client version:1.7.1
2025-07-23 19:18:55 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753269535536
timestamp=1753269535536
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x00a2df65, L:/*************:8091 - R:/*************:11023],client version:1.7.1
2025-07-23 19:18:56 [ServerHandlerThread_1_16_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xf6fba5b0, L:/*************:8091 - R:/*************:11031],client version:1.7.1
2025-07-23 19:18:57 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753269537458
timestamp=1753269537458
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x4ebc2bdc, L:/*************:8091 - R:/*************:11051],client version:1.7.1
2025-07-23 19:18:58 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753269538332
timestamp=1753269538332
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x96d7b85d, L:/*************:8091 - R:/*************:11063],client version:1.7.1
2025-07-23 19:42:32 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-23 20:02:28 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-23 20:02:28 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-23 20:02:28 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-23 20:02:28 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-23 20:02:29 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-23 20:02:29 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-23 20:02:29 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-23 20:02:29 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-23 20:02:29 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 32736 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-23 20:02:29 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-23 20:02:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-23 20:02:30 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-23 20:02:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 20:02:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-23 20:02:30 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 20:02:30 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1209 ms
2025-07-23 20:02:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-23 20:02:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-23 20:02:31 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-23 20:02:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@31a80c88, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7602c65d, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c38e2bf, org.springframework.security.web.header.HeaderWriterFilter@4678ec43, org.springframework.security.web.authentication.logout.LogoutFilter@4889fbba, io.seata.console.filter.JwtAuthenticationTokenFilter@5a5c2889, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3c205259, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1ee52741, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7668f8fd, org.springframework.security.web.session.SessionManagementFilter@25f73119, org.springframework.security.web.access.ExceptionTranslationFilter@70044113, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3962b216]
2025-07-23 20:02:31 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-23 20:02:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-23 20:02:31 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.913 seconds (JVM running for 5.146)
2025-07-23 20:02:31 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-23 20:02:32 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-23 20:02:32 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-23 20:02:32 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-23 20:02:32 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-23 20:02:32 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-23 20:02:32 [main] INFO  io.seata.server.ServerRunner - seata server started in 632 millSeconds
2025-07-23 20:02:45 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753272165340
timestamp=1753272165340
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x9a85666c, L:/*************:8091 - R:/*************:1859],client version:1.7.1
2025-07-23 20:02:45 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753272165393
timestamp=1753272165393
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x1f0e1281, L:/*************:8091 - R:/*************:1860],client version:1.7.1
2025-07-23 20:02:48 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xdc1139da, L:/*************:8091 - R:/*************:1876],client version:1.7.1
2025-07-23 20:02:48 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x678393e6, L:/*************:8091 - R:/*************:1882],client version:1.7.1
2025-07-23 20:02:49 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753272169548
timestamp=1753272169548
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa7dc6a41, L:/*************:8091 - R:/*************:1897],client version:1.7.1
2025-07-23 20:02:52 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x7ab7e735, L:/*************:8091 - R:/*************:1927],client version:1.7.1
2025-07-23 20:03:50 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753272230654
timestamp=1753272230654
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xbfb18608, L:/*************:8091 - R:/*************:2069],client version:1.7.1
2025-07-23 20:04:50 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x31bdd4f7, L:/*************:8091 - R:/*************:2205],client version:1.7.1
2025-07-23 20:16:49 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1876 to server channel inactive.
2025-07-23 20:16:49 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2069 to server channel inactive.
2025-07-23 20:16:49 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1859 to server channel inactive.
2025-07-23 20:16:49 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1927 to server channel inactive.
2025-07-23 20:16:49 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2205 to server channel inactive.
2025-07-23 20:16:49 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1860 to server channel inactive.
2025-07-23 20:16:49 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1897 to server channel inactive.
2025-07-23 20:16:49 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1882 to server channel inactive.
2025-07-23 20:16:49 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x31bdd4f7, L:/*************:8091 ! R:/*************:2205]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2205', channel=[id: 0x31bdd4f7, L:/*************:8091 ! R:/*************:2205], resourceSets=null}
2025-07-23 20:16:49 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa7dc6a41, L:/*************:8091 ! R:/*************:1897]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1897', channel=[id: 0xa7dc6a41, L:/*************:8091 ! R:/*************:1897], resourceSets=null}
2025-07-23 20:16:49 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdc1139da, L:/*************:8091 ! R:/*************:1876]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:1876', channel=[id: 0xdc1139da, L:/*************:8091 ! R:/*************:1876], resourceSets=[]}
2025-07-23 20:16:49 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x678393e6, L:/*************:8091 ! R:/*************:1882]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:1882', channel=[id: 0x678393e6, L:/*************:8091 ! R:/*************:1882], resourceSets=[]}
2025-07-23 20:16:49 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1f0e1281, L:/*************:8091 ! R:/*************:1860]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:1860', channel=[id: 0x1f0e1281, L:/*************:8091 ! R:/*************:1860], resourceSets=null}
2025-07-23 20:16:49 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbfb18608, L:/*************:8091 ! R:/*************:2069]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2069', channel=[id: 0xbfb18608, L:/*************:8091 ! R:/*************:2069], resourceSets=null}
2025-07-23 20:16:49 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9a85666c, L:/*************:8091 ! R:/*************:1859]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:1859', channel=[id: 0x9a85666c, L:/*************:8091 ! R:/*************:1859], resourceSets=null}
2025-07-23 20:16:49 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7ab7e735, L:/*************:8091 ! R:/*************:1927]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1927', channel=[id: 0x7ab7e735, L:/*************:8091 ! R:/*************:1927], resourceSets=[]}
2025-07-23 21:07:02 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-23 21:07:09 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-23 21:07:09 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-23 21:07:09 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-23 21:07:09 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-23 21:07:10 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-23 21:07:10 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-23 21:07:10 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-23 21:07:10 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-23 21:07:10 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 31128 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-23 21:07:10 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-23 21:07:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-23 21:07:12 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-23 21:07:12 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 21:07:12 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-23 21:07:12 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-23 21:07:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1528 ms
2025-07-23 21:07:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-23 21:07:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-23 21:07:12 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-23 21:07:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@18139a43, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4df5f119, org.springframework.security.web.context.SecurityContextPersistenceFilter@64de9fa4, org.springframework.security.web.header.HeaderWriterFilter@7c3223aa, org.springframework.security.web.authentication.logout.LogoutFilter@641c8ba4, io.seata.console.filter.JwtAuthenticationTokenFilter@3dbb7bb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4a65c40, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5fd5d6d1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6277a496, org.springframework.security.web.session.SessionManagementFilter@4626f584, org.springframework.security.web.access.ExceptionTranslationFilter@b859355, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@23dda7a3]
2025-07-23 21:07:13 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-23 21:07:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-23 21:07:13 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.236 seconds (JVM running for 5.994)
2025-07-23 21:07:13 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-23 21:07:13 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-23 21:07:13 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-23 21:07:13 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-23 21:07:13 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-23 21:07:13 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-23 21:07:13 [main] INFO  io.seata.server.ServerRunner - seata server started in 622 millSeconds
2025-07-23 21:07:15 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753276035685
timestamp=1753276035685
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x78aed31d, L:/*************:8091 - R:/*************:12866],client version:1.7.1
2025-07-23 21:07:15 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753276035727
timestamp=1753276035727
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x79bdb928, L:/*************:8091 - R:/*************:12868],client version:1.7.1
2025-07-23 21:07:16 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xb1ee3b12, L:/*************:8091 - R:/*************:12869],client version:1.7.1
2025-07-23 21:07:16 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x0863c6b8, L:/*************:8091 - R:/*************:12870],client version:1.7.1
2025-07-23 21:07:20 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753276040990
timestamp=1753276040990
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x5cc68cd6, L:/*************:8091 - R:/*************:12880],client version:1.7.1
2025-07-23 21:07:21 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x32ca256a, L:/*************:8091 - R:/*************:12882],client version:1.7.1
2025-07-23 21:07:23 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753276043127
timestamp=1753276043127
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xfc324664, L:/*************:8091 - R:/*************:12885],client version:1.7.1
2025-07-23 21:07:23 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xa06c0fd2, L:/*************:8091 - R:/*************:12887],client version:1.7.1
2025-07-23 21:08:56 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12866 to server channel inactive.
2025-07-23 21:08:56 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x78aed31d, L:/*************:8091 ! R:/*************:12866]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:12866', channel=[id: 0x78aed31d, L:/*************:8091 ! R:/*************:12866], resourceSets=null}
2025-07-23 21:08:56 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12869 to server channel inactive.
2025-07-23 21:08:56 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb1ee3b12, L:/*************:8091 ! R:/*************:12869]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:12869', channel=[id: 0xb1ee3b12, L:/*************:8091 ! R:/*************:12869], resourceSets=[]}
2025-07-23 21:09:06 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753276145934
timestamp=1753276145934
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xb6acbcad, L:/*************:8091 - R:/*************:13308],client version:1.7.1
2025-07-23 21:09:08 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x80f467b4, L:/*************:8091 - R:/*************:13317],client version:1.7.1
2025-07-23 21:09:10 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
