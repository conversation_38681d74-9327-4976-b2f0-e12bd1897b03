2025-07-25 08:33:49 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 08:33:49 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 08:33:49 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 08:33:49 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 08:33:50 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-25 08:33:50 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-25 08:33:50 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-25 08:33:50 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-25 08:33:50 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 27600 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 08:33:50 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-25 08:33:52 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-25 08:33:52 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-25 08:33:52 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 08:33:52 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-25 08:33:52 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 08:33:52 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1663 ms
2025-07-25 08:33:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-25 08:33:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-25 08:33:52 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-25 08:33:53 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@75566f4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6785786d, org.springframework.security.web.context.SecurityContextPersistenceFilter@20923380, org.springframework.security.web.header.HeaderWriterFilter@18ffc008, org.springframework.security.web.authentication.logout.LogoutFilter@31ab75a5, io.seata.console.filter.JwtAuthenticationTokenFilter@5c261c74, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6520625f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@47fce2c4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d38a81d, org.springframework.security.web.session.SessionManagementFilter@205b73d8, org.springframework.security.web.access.ExceptionTranslationFilter@400df2b3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5a5c2889]
2025-07-25 08:33:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-25 08:33:53 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-25 08:33:53 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.538 seconds (JVM running for 5.605)
2025-07-25 08:33:53 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-25 08:33:53 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-25 08:33:53 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-25 08:33:53 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 08:33:53 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 08:33:53 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-25 08:33:53 [main] INFO  io.seata.server.ServerRunner - seata server started in 767 millSeconds
2025-07-25 08:34:02 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753403642577
timestamp=1753403642577
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x2c23d130, L:/*************:8091 - R:/*************:1988],client version:1.7.1
2025-07-25 08:34:07 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753403647068
timestamp=1753403647068
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x259e75d3, L:/*************:8091 - R:/*************:2029],client version:1.7.1
2025-07-25 08:34:11 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753403651804
timestamp=1753403651804
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xd695e187, L:/*************:8091 - R:/*************:2067],client version:1.7.1
2025-07-25 08:34:13 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x69f1fae8, L:/*************:8091 - R:/*************:2073],client version:1.7.1
2025-07-25 08:34:15 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x88c23912, L:/*************:8091 - R:/*************:2101],client version:1.7.1
2025-07-25 08:34:15 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753403654970
timestamp=1753403654970
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xd0a2c685, L:/*************:8091 - R:/*************:2100],client version:1.7.1
2025-07-25 08:34:18 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x8523ebb6, L:/*************:8091 - R:/*************:2136],client version:1.7.1
2025-07-25 08:34:27 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2067 to server channel inactive.
2025-07-25 08:34:27 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd695e187, L:/*************:8091 ! R:/*************:2067]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2067', channel=[id: 0xd695e187, L:/*************:8091 ! R:/*************:2067], resourceSets=null}
2025-07-25 08:34:27 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2101 to server channel inactive.
2025-07-25 08:34:27 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x88c23912, L:/*************:8091 ! R:/*************:2101]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2101', channel=[id: 0x88c23912, L:/*************:8091 ! R:/*************:2101], resourceSets=[]}
2025-07-25 08:35:02 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x0cb45787, L:/*************:8091 - R:/*************:2286],client version:1.7.1
2025-07-25 10:44:25 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-25 11:23:50 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 11:23:50 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 11:23:50 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 11:23:50 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 11:23:51 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-25 11:23:51 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-25 11:23:51 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-25 11:23:51 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-25 11:23:51 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 25592 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 11:23:51 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-25 11:23:53 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-25 11:23:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-25 11:23:53 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 11:23:53 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-25 11:23:53 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 11:23:53 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1817 ms
2025-07-25 11:23:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-25 11:23:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-25 11:23:54 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-25 11:23:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@75566f4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6785786d, org.springframework.security.web.context.SecurityContextPersistenceFilter@1c65ec63, org.springframework.security.web.header.HeaderWriterFilter@1d686622, org.springframework.security.web.authentication.logout.LogoutFilter@2a7b5925, io.seata.console.filter.JwtAuthenticationTokenFilter@5c261c74, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@10f192d8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5bc40f5d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d38a81d, org.springframework.security.web.session.SessionManagementFilter@2643ed03, org.springframework.security.web.access.ExceptionTranslationFilter@54ec8ab3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7ef3aa21]
2025-07-25 11:23:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-25 11:23:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-25 11:23:54 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 5.359 seconds (JVM running for 6.627)
2025-07-25 11:23:55 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-25 11:23:55 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-25 11:23:55 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-25 11:23:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 11:23:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 11:23:55 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-25 11:23:55 [main] INFO  io.seata.server.ServerRunner - seata server started in 908 millSeconds
2025-07-25 11:23:58 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753413837971
timestamp=1753413837971
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x6f6d8742, L:/*************:8091 - R:/*************:9933],client version:1.7.1
2025-07-25 11:24:00 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753413840019
timestamp=1753413840019
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x7cb37db8, L:/*************:8091 - R:/*************:9946],client version:1.7.1
2025-07-25 11:24:01 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xed990218, L:/*************:8091 - R:/*************:9959],client version:1.7.1
2025-07-25 11:24:05 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753413845321
timestamp=1753413845321
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xd71504ea, L:/*************:8091 - R:/*************:10006],client version:1.7.1
2025-07-25 11:24:08 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x6220e135, L:/*************:8091 - R:/*************:10032],client version:1.7.1
2025-07-25 11:25:00 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x227691e3, L:/*************:8091 - R:/*************:10117],client version:1.7.1
2025-07-25 13:27:26 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9933 to server channel inactive.
2025-07-25 13:27:26 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6f6d8742, L:/*************:8091 ! R:/*************:9933]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:9933', channel=[id: 0x6f6d8742, L:/*************:8091 ! R:/*************:9933], resourceSets=null}
2025-07-25 13:27:26 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10032 to server channel inactive.
2025-07-25 13:27:26 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6220e135, L:/*************:8091 ! R:/*************:10032]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:10032', channel=[id: 0x6220e135, L:/*************:8091 ! R:/*************:10032], resourceSets=[]}
2025-07-25 13:27:26 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753421246934
timestamp=1753421246934
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x72f6becb, L:/*************:8091 - R:/*************:11595],client version:1.7.1
2025-07-25 13:27:28 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9946 to server channel inactive.
2025-07-25 13:27:28 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7cb37db8, L:/*************:8091 ! R:/*************:9946]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:9946', channel=[id: 0x7cb37db8, L:/*************:8091 ! R:/*************:9946], resourceSets=null}
2025-07-25 13:27:28 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10117 to server channel inactive.
2025-07-25 13:27:28 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x227691e3, L:/*************:8091 ! R:/*************:10117]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:10117', channel=[id: 0x227691e3, L:/*************:8091 ! R:/*************:10117], resourceSets=null}
2025-07-25 13:27:28 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10006 to server channel inactive.
2025-07-25 13:27:28 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd71504ea, L:/*************:8091 ! R:/*************:10006]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:10006', channel=[id: 0xd71504ea, L:/*************:8091 ! R:/*************:10006], resourceSets=null}
2025-07-25 13:27:30 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9959 to server channel inactive.
2025-07-25 13:27:30 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xed990218, L:/*************:8091 ! R:/*************:9959]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:9959', channel=[id: 0xed990218, L:/*************:8091 ! R:/*************:9959], resourceSets=[]}
2025-07-25 13:27:31 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753421251986
timestamp=1753421251986
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x0f91b850, L:/*************:8091 - R:/*************:11674],client version:1.7.1
2025-07-25 13:27:32 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xba5d41bb, L:/*************:8091 - R:/*************:11676],client version:1.7.1
2025-07-25 13:27:35 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x25e98584, L:/*************:8091 - R:/*************:11686],client version:1.7.1
2025-07-25 13:27:36 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753421256677
timestamp=1753421256677
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xd508af3d, L:/*************:8091 - R:/*************:11699],client version:1.7.1
2025-07-25 13:27:37 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xbef73361, L:/*************:8091 - R:/*************:11701],client version:1.7.1
2025-07-25 13:29:17 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-25 13:29:20 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11701 to server channel inactive.
2025-07-25 13:29:20 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11676 to server channel inactive.
2025-07-25 13:29:20 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11686 to server channel inactive.
2025-07-25 13:29:20 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbef73361, L:/*************:8091 ! R:/*************:11701]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11701', channel=[id: 0xbef73361, L:/*************:8091 ! R:/*************:11701], resourceSets=null}
2025-07-25 13:29:20 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x25e98584, L:/*************:8091 ! R:/*************:11686]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11686', channel=[id: 0x25e98584, L:/*************:8091 ! R:/*************:11686], resourceSets=[]}
2025-07-25 13:29:20 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xba5d41bb, L:/*************:8091 ! R:/*************:11676]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11676', channel=[id: 0xba5d41bb, L:/*************:8091 ! R:/*************:11676], resourceSets=[]}
2025-07-25 13:29:20 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11595 to server channel inactive.
2025-07-25 13:29:20 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11674 to server channel inactive.
2025-07-25 13:29:20 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11699 to server channel inactive.
2025-07-25 13:29:20 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x72f6becb, L:/*************:8091 ! R:/*************:11595]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11595', channel=[id: 0x72f6becb, L:/*************:8091 ! R:/*************:11595], resourceSets=null}
2025-07-25 13:29:20 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0f91b850, L:/*************:8091 ! R:/*************:11674]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11674', channel=[id: 0x0f91b850, L:/*************:8091 ! R:/*************:11674], resourceSets=null}
2025-07-25 13:29:20 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd508af3d, L:/*************:8091 ! R:/*************:11699]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11699', channel=[id: 0xd508af3d, L:/*************:8091 ! R:/*************:11699], resourceSets=null}
2025-07-25 13:29:24 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 13:29:24 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 13:29:24 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 13:29:24 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 13:29:25 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-25 13:29:25 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-25 13:29:25 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-25 13:29:25 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-25 13:29:25 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 11016 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 13:29:25 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-25 13:29:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-25 13:29:26 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-25 13:29:26 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 13:29:26 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-25 13:29:26 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 13:29:26 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1391 ms
2025-07-25 13:29:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-25 13:29:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-25 13:29:27 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-25 13:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3cd89c72, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7fb46c10, org.springframework.security.web.context.SecurityContextPersistenceFilter@1f370472, org.springframework.security.web.header.HeaderWriterFilter@6802c10e, org.springframework.security.web.authentication.logout.LogoutFilter@5d782a4a, io.seata.console.filter.JwtAuthenticationTokenFilter@5a079446, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3ace65df, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@445b4594, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@694c0ed1, org.springframework.security.web.session.SessionManagementFilter@4b808427, org.springframework.security.web.access.ExceptionTranslationFilter@626ff077, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6258f9d1]
2025-07-25 13:29:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-25 13:29:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-25 13:29:27 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.994 seconds (JVM running for 5.288)
2025-07-25 13:29:27 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-25 13:29:27 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-25 13:29:27 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-25 13:29:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 13:29:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 13:29:27 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-25 13:29:27 [main] INFO  io.seata.server.ServerRunner - seata server started in 558 millSeconds
2025-07-25 13:29:28 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x665a1555, L:/*************:8091 - R:/*************:12024],client version:1.7.1
2025-07-25 13:29:28 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753421366676
timestamp=1753421366676
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x47d9249a, L:/*************:8091 - R:/*************:12020],client version:1.7.1
2025-07-25 13:29:31 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753421371986
timestamp=1753421371986
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x3f4be59c, L:/*************:8091 - R:/*************:12040],client version:1.7.1
2025-07-25 13:29:32 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x6fcbf8de, L:/*************:8091 - R:/*************:12041],client version:1.7.1
2025-07-25 13:29:34 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753421374556
timestamp=1753421374556
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xbf431653, L:/*************:8091 - R:/*************:12044],client version:1.7.1
2025-07-25 13:29:35 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x667b46c6, L:/*************:8091 - R:/*************:12045],client version:1.7.1
2025-07-25 14:55:20 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-25 15:12:47 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 15:12:47 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 15:12:47 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 15:12:47 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 15:12:48 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-25 15:12:48 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-25 15:12:48 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-25 15:12:48 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-25 15:12:48 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 19844 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 15:12:48 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-25 15:12:50 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-25 15:12:50 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-25 15:12:50 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-25 15:12:50 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-25 15:12:50 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-25 15:12:50 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1668 ms
2025-07-25 15:12:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-25 15:12:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-25 15:12:51 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-25 15:12:51 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@78ab63b5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2b9370cc, org.springframework.security.web.context.SecurityContextPersistenceFilter@1ae23815, org.springframework.security.web.header.HeaderWriterFilter@1ee52741, org.springframework.security.web.authentication.logout.LogoutFilter@5a8835c6, io.seata.console.filter.JwtAuthenticationTokenFilter@1a56f608, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1165a952, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5a5b394, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2809e38a, org.springframework.security.web.session.SessionManagementFilter@abe7d36, org.springframework.security.web.access.ExceptionTranslationFilter@5b2c41f9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@468eff41]
2025-07-25 15:12:51 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-25 15:12:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-25 15:12:51 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.999 seconds (JVM running for 6.119)
2025-07-25 15:12:52 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-25 15:12:52 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-25 15:12:52 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-25 15:12:52 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 15:12:52 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 15:12:52 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-25 15:12:52 [main] INFO  io.seata.server.ServerRunner - seata server started in 866 millSeconds
2025-07-25 15:12:54 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753427574056
timestamp=1753427574056
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x470ab6e4, L:/*************:8091 - R:/*************:12152],client version:1.7.1
2025-07-25 15:12:58 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x68729ac8, L:/*************:8091 - R:/*************:12165],client version:1.7.1
2025-07-25 15:12:59 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753427579430
timestamp=1753427579430
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x752c5e07, L:/*************:8091 - R:/*************:12175],client version:1.7.1
2025-07-25 15:13:13 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753427593834
timestamp=1753427593834
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x5fb5f74d, L:/*************:8091 - R:/*************:12253],client version:1.7.1
2025-07-25 15:13:16 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x0165b81b, L:/*************:8091 - R:/*************:12268],client version:1.7.1
2025-07-25 15:13:59 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xe00b19f1, L:/*************:8091 - R:/*************:12328],client version:1.7.1
2025-07-25 17:47:35 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12175 to server channel inactive.
2025-07-25 17:47:35 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12328 to server channel inactive.
2025-07-25 17:47:35 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe00b19f1, L:/*************:8091 ! R:/*************:12328]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:12328', channel=[id: 0xe00b19f1, L:/*************:8091 ! R:/*************:12328], resourceSets=null}
2025-07-25 17:47:35 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x752c5e07, L:/*************:8091 ! R:/*************:12175]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:12175', channel=[id: 0x752c5e07, L:/*************:8091 ! R:/*************:12175], resourceSets=null}
2025-07-25 17:47:43 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753436863675
timestamp=1753436863675
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x190d2ae6, L:/*************:8091 - R:/*************:7276],client version:1.7.1
2025-07-25 17:47:50 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12152 to server channel inactive.
2025-07-25 17:47:50 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x470ab6e4, L:/*************:8091 ! R:/*************:12152]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:12152', channel=[id: 0x470ab6e4, L:/*************:8091 ! R:/*************:12152], resourceSets=null}
2025-07-25 17:47:50 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12165 to server channel inactive.
2025-07-25 17:47:50 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x68729ac8, L:/*************:8091 ! R:/*************:12165]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:12165', channel=[id: 0x68729ac8, L:/*************:8091 ! R:/*************:12165], resourceSets=[]}
2025-07-25 17:47:59 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753436879632
timestamp=1753436879632
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xb43f42c1, L:/*************:8091 - R:/*************:7333],client version:1.7.1
2025-07-25 17:48:02 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x2e31961d, L:/*************:8091 - R:/*************:7342],client version:1.7.1
2025-07-25 17:48:43 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x047ded05, L:/*************:8091 - R:/*************:7465],client version:1.7.1
2025-07-25 17:53:13 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7333 to server channel inactive.
2025-07-25 17:53:13 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7342 to server channel inactive.
2025-07-25 17:53:13 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb43f42c1, L:/*************:8091 ! R:/*************:7333]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:7333', channel=[id: 0xb43f42c1, L:/*************:8091 ! R:/*************:7333], resourceSets=null}
2025-07-25 17:53:13 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2e31961d, L:/*************:8091 ! R:/*************:7342]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:7342', channel=[id: 0x2e31961d, L:/*************:8091 ! R:/*************:7342], resourceSets=[]}
2025-07-25 17:53:22 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753437201969
timestamp=1753437201969
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x8ec5fe18, L:/*************:8091 - R:/*************:9293],client version:1.7.1
2025-07-25 17:53:24 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x13b3ab4d, L:/*************:8091 - R:/*************:9299],client version:1.7.1
2025-07-25 17:56:55 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9293 to server channel inactive.
2025-07-25 17:56:55 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8ec5fe18, L:/*************:8091 ! R:/*************:9293]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:9293', channel=[id: 0x8ec5fe18, L:/*************:8091 ! R:/*************:9293], resourceSets=null}
2025-07-25 17:56:55 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9299 to server channel inactive.
2025-07-25 17:56:55 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x13b3ab4d, L:/*************:8091 ! R:/*************:9299]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:9299', channel=[id: 0x13b3ab4d, L:/*************:8091 ! R:/*************:9299], resourceSets=[]}
2025-07-25 17:57:03 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753437423066
timestamp=1753437423066
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x6eb73e37, L:/*************:8091 - R:/*************:11017],client version:1.7.1
2025-07-25 17:57:05 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x03554c67, L:/*************:8091 - R:/*************:11029],client version:1.7.1
2025-07-25 19:13:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x047ded05, L:/*************:8091 - R:/*************:7465] read idle.
2025-07-25 19:13:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7465 to server channel inactive.
2025-07-25 19:13:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x047ded05, L:/*************:8091 - R:/*************:7465]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:7465', channel=[id: 0x047ded05, L:/*************:8091 - R:/*************:7465], resourceSets=null}
2025-07-25 19:13:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x047ded05, L:/*************:8091 - R:/*************:7465]
2025-07-25 19:13:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7465 to server channel inactive.
2025-07-25 19:13:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x047ded05, L:/*************:8091 ! R:/*************:7465]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:7465', channel=[id: 0x047ded05, L:/*************:8091 ! R:/*************:7465], resourceSets=null}
2025-07-25 19:13:11 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x190d2ae6, L:/*************:8091 - R:/*************:7276] read idle.
2025-07-25 19:13:11 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7276 to server channel inactive.
2025-07-25 19:13:11 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x190d2ae6, L:/*************:8091 - R:/*************:7276]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:7276', channel=[id: 0x190d2ae6, L:/*************:8091 - R:/*************:7276], resourceSets=null}
2025-07-25 19:13:11 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x190d2ae6, L:/*************:8091 - R:/*************:7276]
2025-07-25 19:13:11 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7276 to server channel inactive.
2025-07-25 19:13:11 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x190d2ae6, L:/*************:8091 ! R:/*************:7276]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:7276', channel=[id: 0x190d2ae6, L:/*************:8091 ! R:/*************:7276], resourceSets=null}
2025-07-25 19:13:11 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12268 to server channel inactive.
2025-07-25 19:13:11 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0165b81b, L:/*************:8091 ! R:/*************:12268]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12268', channel=[id: 0x0165b81b, L:/*************:8091 ! R:/*************:12268], resourceSets=[]}
2025-07-25 19:13:12 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753441992368
timestamp=1753441992368
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x18b54f1f, L:/*************:8091 - R:/*************:11181],client version:1.7.1
2025-07-25 19:13:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11029 to server channel inactive.
2025-07-25 19:13:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x03554c67, L:/*************:8091 ! R:/*************:11029]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11029', channel=[id: 0x03554c67, L:/*************:8091 ! R:/*************:11029], resourceSets=[]}
2025-07-25 19:13:12 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x2b17f80e, L:/*************:8091 - R:/*************:11184],client version:1.7.1
2025-07-25 19:13:12 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12253 to server channel inactive.
2025-07-25 19:13:12 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5fb5f74d, L:/*************:8091 ! R:/*************:12253]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12253', channel=[id: 0x5fb5f74d, L:/*************:8091 ! R:/*************:12253], resourceSets=null}
2025-07-25 19:13:12 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x3b245348, L:/*************:8091 - R:/*************:11185],client version:1.7.1
2025-07-25 19:13:15 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11017 to server channel inactive.
2025-07-25 19:13:15 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6eb73e37, L:/*************:8091 ! R:/*************:11017]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11017', channel=[id: 0x6eb73e37, L:/*************:8091 ! R:/*************:11017], resourceSets=null}
2025-07-25 19:13:20 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753442000066
timestamp=1753442000066
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x51e1312a, L:/*************:8091 - R:/*************:11489],client version:1.7.1
2025-07-25 19:13:20 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x798fdb43, L:/*************:8091 - R:/*************:11784],client version:1.7.1
2025-07-25 19:13:20 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753442000803
timestamp=1753442000803
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x43b09b40, L:/*************:8091 - R:/*************:11787],client version:1.7.1
2025-07-25 20:23:08 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753446188167
timestamp=1753446188167
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x5c7ee714, L:/*************:8091 - R:/*************:4819],client version:1.7.1
2025-07-25 20:23:11 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x07f3fcde, L:/*************:8091 - R:/*************:4830],client version:1.7.1
2025-07-25 20:41:38 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
