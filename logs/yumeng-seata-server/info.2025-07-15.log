2025-07-15 10:06:55 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-15 10:06:55 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-15 10:06:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-15 10:06:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-15 10:06:56 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-15 10:06:56 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-15 10:06:56 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-15 10:06:56 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-15 10:06:57 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 28416 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-15 10:06:57 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-15 10:06:58 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-15 10:06:58 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-15 10:06:58 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 10:06:58 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-15 10:06:58 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 10:06:58 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1648 ms
2025-07-15 10:06:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 10:06:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 10:06:59 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-15 10:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2809e38a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@b9c9bf5, org.springframework.security.web.context.SecurityContextPersistenceFilter@1b868ef0, org.springframework.security.web.header.HeaderWriterFilter@70940cb8, org.springframework.security.web.authentication.logout.LogoutFilter@c7a1db6, io.seata.console.filter.JwtAuthenticationTokenFilter@3b399f5a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@16e84d93, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4889fbba, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@16f2d883, org.springframework.security.web.session.SessionManagementFilter@1ae23815, org.springframework.security.web.access.ExceptionTranslationFilter@2f5a092e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b9370cc]
2025-07-15 10:06:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-15 10:06:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-15 10:06:59 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.818 seconds (JVM running for 6.058)
2025-07-15 10:07:00 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-15 10:07:00 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-15 10:07:00 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-15 10:07:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-15 10:07:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-15 10:07:00 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-15 10:07:00 [main] INFO  io.seata.server.ServerRunner - seata server started in 820 millSeconds
2025-07-15 10:07:13 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752545232849
timestamp=1752545232849
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'},channel:[id: 0xb511ae8e, L:/************:8091 - R:/************:7464],client version:1.7.1
2025-07-15 10:07:15 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xff97a7ec, L:/************:8091 - R:/************:7471],client version:1.7.1
2025-07-15 10:07:49 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752545269389
timestamp=1752545269389
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x942e472c, L:/************:8091 - R:/************:7583],client version:1.7.1
2025-07-15 10:07:49 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x0e5486ce, L:/************:8091 - R:/************:7584],client version:1.7.1
2025-07-15 10:07:49 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752545269779
timestamp=1752545269779
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0xfe769578, L:/************:8091 - R:/************:7585],client version:1.7.1
2025-07-15 10:07:49 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xe32ddccd, L:/************:8091 - R:/************:7586],client version:1.7.1
2025-07-15 10:07:55 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752545275482
timestamp=1752545275482
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'},channel:[id: 0xe0cc0fd7, L:/************:8091 - R:/************:7589],client version:1.7.1
2025-07-15 10:07:55 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x577f4898, L:/************:8091 - R:/************:7590],client version:1.7.1
2025-07-15 11:02:03 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7586 to server channel inactive.
2025-07-15 11:02:03 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7585 to server channel inactive.
2025-07-15 11:02:03 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfe769578, L:/************:8091 ! R:/************:7585]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:7585', channel=[id: 0xfe769578, L:/************:8091 ! R:/************:7585], resourceSets=null}
2025-07-15 11:02:03 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe32ddccd, L:/************:8091 ! R:/************:7586]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:7586', channel=[id: 0xe32ddccd, L:/************:8091 ! R:/************:7586], resourceSets=[]}
2025-07-15 11:02:15 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752548535710
timestamp=1752548535710
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0x7aa6713e, L:/************:8091 - R:/************:14739],client version:1.7.1
2025-07-15 11:02:18 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xe896b7cc, L:/************:8091 - R:/************:14747],client version:1.7.1
2025-07-15 12:03:59 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7464 to server channel inactive.
2025-07-15 12:03:59 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb511ae8e, L:/************:8091 ! R:/************:7464]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:7464', channel=[id: 0xb511ae8e, L:/************:8091 ! R:/************:7464], resourceSets=null}
2025-07-15 12:03:59 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752552239711
timestamp=1752552239711
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'},channel:[id: 0xc7e2f6ce, L:/************:8091 - R:/************:3679],client version:1.7.1
2025-07-15 12:03:59 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7583 to server channel inactive.
2025-07-15 12:03:59 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x942e472c, L:/************:8091 ! R:/************:7583]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:7583', channel=[id: 0x942e472c, L:/************:8091 ! R:/************:7583], resourceSets=null}
2025-07-15 12:04:00 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7584 to server channel inactive.
2025-07-15 12:04:00 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0e5486ce, L:/************:8091 ! R:/************:7584]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:7584', channel=[id: 0x0e5486ce, L:/************:8091 ! R:/************:7584], resourceSets=[]}
2025-07-15 12:04:00 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:14739 to server channel inactive.
2025-07-15 12:04:00 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7aa6713e, L:/************:8091 ! R:/************:14739]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:14739', channel=[id: 0x7aa6713e, L:/************:8091 ! R:/************:14739], resourceSets=null}
2025-07-15 12:04:01 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7589 to server channel inactive.
2025-07-15 12:04:01 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe0cc0fd7, L:/************:8091 ! R:/************:7589]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:7589', channel=[id: 0xe0cc0fd7, L:/************:8091 ! R:/************:7589], resourceSets=null}
2025-07-15 12:04:01 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7471 to server channel inactive.
2025-07-15 12:04:01 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xff97a7ec, L:/************:8091 ! R:/************:7471]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:7471', channel=[id: 0xff97a7ec, L:/************:8091 ! R:/************:7471], resourceSets=[]}
2025-07-15 12:04:01 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7590 to server channel inactive.
2025-07-15 12:04:01 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x577f4898, L:/************:8091 ! R:/************:7590]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:7590', channel=[id: 0x577f4898, L:/************:8091 ! R:/************:7590], resourceSets=null}
2025-07-15 12:04:02 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752552242508
timestamp=1752552242508
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'},channel:[id: 0x0c362402, L:/************:8091 - R:/************:5634],client version:1.7.1
2025-07-15 12:04:02 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752552242615
timestamp=1752552242615
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0xecc3c0d6, L:/************:8091 - R:/************:5639],client version:1.7.1
2025-07-15 12:04:02 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x8450d424, L:/************:8091 - R:/************:5641],client version:1.7.1
2025-07-15 12:04:03 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:14747 to server channel inactive.
2025-07-15 12:04:03 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe896b7cc, L:/************:8091 ! R:/************:14747]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:14747', channel=[id: 0xe896b7cc, L:/************:8091 ! R:/************:14747], resourceSets=[]}
2025-07-15 12:04:06 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752552246417
timestamp=1752552246417
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x60ba4954, L:/************:8091 - R:/************:5669],client version:1.7.1
2025-07-15 12:04:06 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x5d24fefd, L:/************:8091 - R:/************:5671],client version:1.7.1
2025-07-15 12:04:10 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x6ab3869b, L:/************:8091 - R:/************:13724],client version:1.7.1
2025-07-15 12:04:12 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x26af6a69, L:/************:8091 - R:/************:13744],client version:1.7.1
2025-07-15 12:05:57 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-15 13:32:57 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-15 13:32:57 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-15 13:32:58 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-15 13:32:58 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-15 13:32:59 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-15 13:32:59 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-15 13:32:59 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-15 13:32:59 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-15 13:32:59 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 34668 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-15 13:32:59 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-15 13:33:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-15 13:33:01 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-15 13:33:01 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 13:33:01 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-15 13:33:01 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 13:33:01 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2079 ms
2025-07-15 13:33:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 13:33:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 13:33:02 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-15 13:33:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7b6b99c5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2e1c8027, org.springframework.security.web.context.SecurityContextPersistenceFilter@5d08a65c, org.springframework.security.web.header.HeaderWriterFilter@47f416d0, org.springframework.security.web.authentication.logout.LogoutFilter@7da1ef46, io.seata.console.filter.JwtAuthenticationTokenFilter@72d7afff, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4d7f9b33, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1fb00a6d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5c41f6a0, org.springframework.security.web.session.SessionManagementFilter@4678ec43, org.springframework.security.web.access.ExceptionTranslationFilter@6ffa8126, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@c7a1db6]
2025-07-15 13:33:02 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-15 13:33:02 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-15 13:33:02 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 5.224 seconds (JVM running for 6.447)
2025-07-15 13:33:02 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-15 13:33:02 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-15 13:33:03 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-15 13:33:03 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-15 13:33:03 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-15 13:33:03 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-15 13:33:03 [main] INFO  io.seata.server.ServerRunner - seata server started in 779 millSeconds
2025-07-15 13:33:05 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x01c40dd2, L:/************:8091 - R:/************:4948],client version:1.7.1
2025-07-15 13:33:06 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752557586398
timestamp=1752557586398
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'},channel:[id: 0x45421c9f, L:/************:8091 - R:/************:4954],client version:1.7.1
2025-07-15 13:33:46 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752557626427
timestamp=1752557626427
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xd32ec009, L:/************:8091 - R:/************:5064],client version:1.7.1
2025-07-15 13:33:46 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xba06cf94, L:/************:8091 - R:/************:5065],client version:1.7.1
2025-07-15 13:33:46 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752557626762
timestamp=1752557626762
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0x24c8aa7c, L:/************:8091 - R:/************:5066],client version:1.7.1
2025-07-15 13:33:46 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xf62dbf1c, L:/************:8091 - R:/************:5067],client version:1.7.1
2025-07-15 13:34:02 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752557642989
timestamp=1752557642989
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'},channel:[id: 0x89b3001c, L:/************:8091 - R:/************:5103],client version:1.7.1
2025-07-15 13:34:06 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x90102a21, L:/************:8091 - R:/************:5105],client version:1.7.1
2025-07-15 13:36:36 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5067 to server channel inactive.
2025-07-15 13:36:36 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5066 to server channel inactive.
2025-07-15 13:36:36 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x24c8aa7c, L:/************:8091 ! R:/************:5066]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:5066', channel=[id: 0x24c8aa7c, L:/************:8091 ! R:/************:5066], resourceSets=null}
2025-07-15 13:36:36 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf62dbf1c, L:/************:8091 ! R:/************:5067]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:5067', channel=[id: 0xf62dbf1c, L:/************:8091 ! R:/************:5067], resourceSets=[]}
2025-07-15 13:36:52 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752557812278
timestamp=1752557812278
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0xef706f31, L:/************:8091 - R:/************:5280],client version:1.7.1
2025-07-15 13:36:56 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x53e66dcd, L:/************:8091 - R:/************:5289],client version:1.7.1
2025-07-15 13:58:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x53e66dcd, L:/************:8091 - R:/************:5289] read idle.
2025-07-15 13:58:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5289 to server channel inactive.
2025-07-15 13:58:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x53e66dcd, L:/************:8091 - R:/************:5289]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:5289', channel=[id: 0x53e66dcd, L:/************:8091 - R:/************:5289], resourceSets=[]}
2025-07-15 13:58:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x53e66dcd, L:/************:8091 - R:/************:5289]
2025-07-15 13:58:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5289 to server channel inactive.
2025-07-15 13:58:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x53e66dcd, L:/************:8091 ! R:/************:5289]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:5289', channel=[id: 0x53e66dcd, L:/************:8091 ! R:/************:5289], resourceSets=[]}
2025-07-15 13:58:17 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xef706f31, L:/************:8091 - R:/************:5280] read idle.
2025-07-15 13:58:17 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5280 to server channel inactive.
2025-07-15 13:58:17 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xef706f31, L:/************:8091 - R:/************:5280]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:5280', channel=[id: 0xef706f31, L:/************:8091 - R:/************:5280], resourceSets=null}
2025-07-15 13:58:17 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xef706f31, L:/************:8091 - R:/************:5280]
2025-07-15 13:58:17 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5280 to server channel inactive.
2025-07-15 13:58:17 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xef706f31, L:/************:8091 ! R:/************:5280]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:5280', channel=[id: 0xef706f31, L:/************:8091 ! R:/************:5280], resourceSets=null}
2025-07-15 13:59:42 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752559182055
timestamp=1752559182055
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0x05e90936, L:/************:8091 - R:/************:6873],client version:1.7.1
2025-07-15 13:59:42 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x75afefbd, L:/************:8091 - R:/************:6875],client version:1.7.1
2025-07-15 14:35:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:35:17 [ServerHandlerThread_1_7_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:************:8091:4450228677121183745
2025-07-15 14:35:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='************:8091:4450228677121183745', extraData='null', resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:35:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183745', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:35:19 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ************:8091:4450228677121183745, branchId = 4450228677121183749, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1945008058683940866
2025-07-15 14:35:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4450228677121183749, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:35:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183745', branchType=AT, resourceId='*******************************************', lockKey='rmb_workorder:1945007984226656257', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:35:30 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[branch register request failed. xid=************:8091:4450228677121183745, msg=HikariPool-1 - Connection is not available, request timed out after 10006ms.]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:35:30 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='************:8091:4450228677121183745', extraData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:35:31 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchRollbackResponse{xid='************:8091:4450228677121183745', branchId=4450228677121183749, branchStatus=PhaseTwo_Rollbacked, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='************:8091:4450228677121183745', extraData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacking, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:01 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacking, resultCode=Failed, msg='TransactionException[global rollback request failed. xid=************:8091:4450228677121183745, msg=HikariPool-1 - Connection is not available, request timed out after 10009ms.]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:10 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:6873 to server channel inactive.
2025-07-15 14:36:10 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:6875 to server channel inactive.
2025-07-15 14:36:10 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x05e90936, L:/************:8091 ! R:/************:6873]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:6873', channel=[id: 0x05e90936, L:/************:8091 ! R:/************:6873], resourceSets=null}
2025-07-15 14:36:10 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x75afefbd, L:/************:8091 ! R:/************:6875]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:6875', channel=[id: 0x75afefbd, L:/************:8091 ! R:/************:6875], resourceSets=[]}
2025-07-15 14:36:23 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752561383031
timestamp=1752561383031
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0x2b47349e, L:/************:8091 - R:/************:12454],client version:1.7.1
2025-07-15 14:36:25 [ServerHandlerThread_1_12_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x922c0f53, L:/************:8091 - R:/************:12467],client version:1.7.1
2025-07-15 14:36:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:40 [ServerHandlerThread_1_13_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:************:8091:4450228677121183800
2025-07-15 14:36:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='************:8091:4450228677121183800', extraData='null', resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:42 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183802]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:42 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183804]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:43 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183807]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:43 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183809]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:44 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183812]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:44 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183815]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:44 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183817]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:44 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183819]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:45 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183821]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:45 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183823]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:45 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183825]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:46 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183827]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:46 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183829]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:46 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183831]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:46 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183833]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:47 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183835]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:47 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183837]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:47 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183839]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:47 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:48 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183841]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:48 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183843]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:48 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183845]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:48 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183847]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:49 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183849]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:49 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183851]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:49 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183854]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:50 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183856]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:50 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183859]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:50 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183861]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:51 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183863]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:51 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183866]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4450228677121183800', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:51 [ForkJoinPool.commonPool-worker-3] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1945008058683940866] is holding by xid ************:8091:4450228677121183745 branchId 4450228677121183749
2025-07-15 14:36:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = ************:8091:4450228677121183800 branchId = 4450228677121183868]'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='************:8091:4450228677121183800', extraData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:36:52 [ServerHandlerThread_1_45_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = ************:8091:4450228677121183800.
2025-07-15 14:36:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:37:27 [ForkJoinPool.commonPool-worker-3] INFO  i.s.core.rpc.netty.ChannelManager - Choose [id: 0x922c0f53, L:/************:8091 - R:/************:12467] on the same IP[************] as alternative of yumeng-salary-settlement:************:6875
2025-07-15 14:37:27 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchRollbackResponse{xid='************:8091:4450228677121183745', branchId=4450228677121183749, branchStatus=PhaseTwo_Rollbacked, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:37:28 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.DefaultCore - Rollback branch transaction successfully, xid = ************:8091:4450228677121183745 branchId = 4450228677121183749
2025-07-15 14:37:28 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = ************:8091:4450228677121183745.
2025-07-15 14:37:31 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12454 to server channel inactive.
2025-07-15 14:37:31 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2b47349e, L:/************:8091 ! R:/************:12454]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:12454', channel=[id: 0x2b47349e, L:/************:8091 ! R:/************:12454], resourceSets=null}
2025-07-15 14:37:31 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12467 to server channel inactive.
2025-07-15 14:37:31 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x922c0f53, L:/************:8091 ! R:/************:12467]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:12467', channel=[id: 0x922c0f53, L:/************:8091 ! R:/************:12467], resourceSets=[]}
2025-07-15 14:37:40 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752561460100
timestamp=1752561460100
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0xf841134c, L:/************:8091 - R:/************:12685],client version:1.7.1
2025-07-15 14:37:43 [ServerHandlerThread_1_46_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x8e402d14, L:/************:8091 - R:/************:12693],client version:1.7.1
2025-07-15 14:37:44 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-15 14:37:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5105 to server channel inactive.
2025-07-15 14:37:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4948 to server channel inactive.
2025-07-15 14:37:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5064 to server channel inactive.
2025-07-15 14:37:47 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12685 to server channel inactive.
2025-07-15 14:37:47 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12693 to server channel inactive.
2025-07-15 14:37:47 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4954 to server channel inactive.
2025-07-15 14:37:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5065 to server channel inactive.
2025-07-15 14:37:47 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:5103 to server channel inactive.
2025-07-15 14:37:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd32ec009, L:/************:8091 ! R:/************:5064]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:5064', channel=[id: 0xd32ec009, L:/************:8091 ! R:/************:5064], resourceSets=null}
2025-07-15 14:37:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x01c40dd2, L:/************:8091 ! R:/************:4948]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:4948', channel=[id: 0x01c40dd2, L:/************:8091 ! R:/************:4948], resourceSets=[]}
2025-07-15 14:37:47 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8e402d14, L:/************:8091 ! R:/************:12693]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:12693', channel=[id: 0x8e402d14, L:/************:8091 ! R:/************:12693], resourceSets=[]}
2025-07-15 14:37:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x90102a21, L:/************:8091 ! R:/************:5105]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:5105', channel=[id: 0x90102a21, L:/************:8091 ! R:/************:5105], resourceSets=null}
2025-07-15 14:37:47 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf841134c, L:/************:8091 ! R:/************:12685]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:12685', channel=[id: 0xf841134c, L:/************:8091 ! R:/************:12685], resourceSets=null}
2025-07-15 14:37:47 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x45421c9f, L:/************:8091 ! R:/************:4954]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:4954', channel=[id: 0x45421c9f, L:/************:8091 ! R:/************:4954], resourceSets=null}
2025-07-15 14:37:47 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x89b3001c, L:/************:8091 ! R:/************:5103]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:5103', channel=[id: 0x89b3001c, L:/************:8091 ! R:/************:5103], resourceSets=null}
2025-07-15 14:37:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xba06cf94, L:/************:8091 ! R:/************:5065]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:5065', channel=[id: 0xba06cf94, L:/************:8091 ! R:/************:5065], resourceSets=[]}
2025-07-15 14:37:51 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-15 14:37:51 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-15 14:37:51 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-15 14:37:51 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-15 14:37:52 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-15 14:37:52 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-15 14:37:52 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-15 14:37:52 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-15 14:37:52 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 21588 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-15 14:37:52 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-15 14:37:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-15 14:37:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-15 14:37:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 14:37:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-15 14:37:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 14:37:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1596 ms
2025-07-15 14:37:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 14:37:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 14:37:54 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-15 14:37:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4df5f119, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3dbb7bb, org.springframework.security.web.context.SecurityContextPersistenceFilter@15a5cc6e, org.springframework.security.web.header.HeaderWriterFilter@7683d632, org.springframework.security.web.authentication.logout.LogoutFilter@52667676, io.seata.console.filter.JwtAuthenticationTokenFilter@6277a496, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4fc84c92, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5e643402, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6632eb19, org.springframework.security.web.session.SessionManagementFilter@2396408a, org.springframework.security.web.access.ExceptionTranslationFilter@1f24e33d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2d399e88]
2025-07-15 14:37:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-15 14:37:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-15 14:37:55 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.218 seconds (JVM running for 6.059)
2025-07-15 14:37:55 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-15 14:37:55 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-15 14:37:55 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-15 14:37:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-15 14:37:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-15 14:37:55 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-15 14:37:55 [main] INFO  io.seata.server.ServerRunner - seata server started in 627 millSeconds
2025-07-15 14:37:56 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752561476214
timestamp=1752561476214
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'},channel:[id: 0xa8c0bbe8, L:/************:8091 - R:/************:12778],client version:1.7.1
2025-07-15 14:37:56 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752561476392
timestamp=1752561476392
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x60c26599, L:/************:8091 - R:/************:12785],client version:1.7.1
2025-07-15 14:37:56 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xc62a1130, L:/************:8091 - R:/************:12789],client version:1.7.1
2025-07-15 14:37:56 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xb0065f93, L:/************:8091 - R:/************:12790],client version:1.7.1
2025-07-15 14:38:02 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752561482954
timestamp=1752561482954
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'},channel:[id: 0x550db8f3, L:/************:8091 - R:/************:12797],client version:1.7.1
2025-07-15 14:38:03 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xc4f814e8, L:/************:8091 - R:/************:12798],client version:1.7.1
2025-07-15 14:38:20 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752561500362
timestamp=1752561500362
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0x7dd947e2, L:/************:8091 - R:/************:12887],client version:1.7.1
2025-07-15 14:38:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:20 [ServerHandlerThread_1_4_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:************:8091:4279091907225710605
2025-07-15 14:38:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='************:8091:4279091907225710605', extraData='null', resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:21 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x657aa5d8, L:/************:8091 - R:/************:12889],client version:1.7.1
2025-07-15 14:38:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4279091907225710605', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945008058683940866', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:22 [ForkJoinPool.commonPool-worker-1] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ************:8091:4279091907225710605, branchId = 4279091907225710610, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1945008058683940866
2025-07-15 14:38:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4279091907225710610, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4279091907225710605', branchType=AT, resourceId='*******************************************', lockKey='rmb_workorder:1945007984226656257', applicationData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:23 [ForkJoinPool.commonPool-worker-1] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ************:8091:4279091907225710605, branchId = 4279091907225710615, resourceId = ******************************************* ,lockKeys = rmb_workorder:1945007984226656257
2025-07-15 14:38:23 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4279091907225710615, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:23 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='************:8091:4279091907225710605', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1945010033894916097', applicationData='{"skipCheckLock":true}'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:23 [ForkJoinPool.commonPool-worker-1] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = ************:8091:4279091907225710605, branchId = 4279091907225710617, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1945010033894916097
2025-07-15 14:38:23 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4279091907225710617, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:23 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='************:8091:4279091907225710605', extraData='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:23 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:24 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='************:8091:4279091907225710605', branchId=4279091907225710610, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:24 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ************:8091:4279091907225710605 branchId = 4279091907225710610
2025-07-15 14:38:24 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='************:8091:4279091907225710605', branchId=4279091907225710615, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:24 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ************:8091:4279091907225710605 branchId = 4279091907225710615
2025-07-15 14:38:24 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='************:8091:4279091907225710605', branchId=4279091907225710617, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: ************, vgroup: yumeng-salary-settlement-group
2025-07-15 14:38:24 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = ************:8091:4279091907225710605 branchId = 4279091907225710617
2025-07-15 14:38:24 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = ************:8091:4279091907225710605.
2025-07-15 15:41:33 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12887 to server channel inactive.
2025-07-15 15:41:33 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12778 to server channel inactive.
2025-07-15 15:41:33 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12790 to server channel inactive.
2025-07-15 15:41:33 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12797 to server channel inactive.
2025-07-15 15:41:33 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12785 to server channel inactive.
2025-07-15 15:41:33 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12889 to server channel inactive.
2025-07-15 15:41:33 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12789 to server channel inactive.
2025-07-15 15:41:33 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12798 to server channel inactive.
2025-07-15 15:41:33 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7dd947e2, L:/************:8091 ! R:/************:12887]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:12887', channel=[id: 0x7dd947e2, L:/************:8091 ! R:/************:12887], resourceSets=null}
2025-07-15 15:41:33 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa8c0bbe8, L:/************:8091 ! R:/************:12778]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:12778', channel=[id: 0xa8c0bbe8, L:/************:8091 ! R:/************:12778], resourceSets=null}
2025-07-15 15:41:33 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x60c26599, L:/************:8091 ! R:/************:12785]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:12785', channel=[id: 0x60c26599, L:/************:8091 ! R:/************:12785], resourceSets=null}
2025-07-15 15:41:33 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x550db8f3, L:/************:8091 ! R:/************:12797]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:12797', channel=[id: 0x550db8f3, L:/************:8091 ! R:/************:12797], resourceSets=null}
2025-07-15 15:41:33 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc62a1130, L:/************:8091 ! R:/************:12789]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:12789', channel=[id: 0xc62a1130, L:/************:8091 ! R:/************:12789], resourceSets=null}
2025-07-15 15:41:33 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc4f814e8, L:/************:8091 ! R:/************:12798]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:12798', channel=[id: 0xc4f814e8, L:/************:8091 ! R:/************:12798], resourceSets=[]}
2025-07-15 15:41:33 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb0065f93, L:/************:8091 ! R:/************:12790]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:12790', channel=[id: 0xb0065f93, L:/************:8091 ! R:/************:12790], resourceSets=[]}
2025-07-15 15:41:33 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x657aa5d8, L:/************:8091 ! R:/************:12889]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:12889', channel=[id: 0x657aa5d8, L:/************:8091 ! R:/************:12889], resourceSets=[]}
2025-07-15 15:41:36 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752565296229
timestamp=1752565296229
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'},channel:[id: 0x46671f88, L:/************:8091 - R:/************:8111],client version:1.7.1
2025-07-15 15:41:36 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752565296392
timestamp=1752565296392
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xda9d0e01, L:/************:8091 - R:/************:8113],client version:1.7.1
2025-07-15 15:41:36 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x81d2502d, L:/************:8091 - R:/************:8115],client version:1.7.1
2025-07-15 15:41:36 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x4e8cb87e, L:/************:8091 - R:/************:8116],client version:1.7.1
2025-07-15 15:41:39 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:8115 to server channel inactive.
2025-07-15 15:41:39 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:8116 to server channel inactive.
2025-07-15 15:41:39 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:8111 to server channel inactive.
2025-07-15 15:41:39 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x46671f88, L:/************:8091 ! R:/************:8111]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:8111', channel=[id: 0x46671f88, L:/************:8091 ! R:/************:8111], resourceSets=null}
2025-07-15 15:41:39 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4e8cb87e, L:/************:8091 ! R:/************:8116]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:8116', channel=[id: 0x4e8cb87e, L:/************:8091 ! R:/************:8116], resourceSets=[]}
2025-07-15 15:41:39 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x81d2502d, L:/************:8091 ! R:/************:8115]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:8115', channel=[id: 0x81d2502d, L:/************:8091 ! R:/************:8115], resourceSets=null}
2025-07-15 15:41:39 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:8113 to server channel inactive.
2025-07-15 15:41:39 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xda9d0e01, L:/************:8091 ! R:/************:8113]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:8113', channel=[id: 0xda9d0e01, L:/************:8091 ! R:/************:8113], resourceSets=null}
2025-07-15 16:08:46 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
