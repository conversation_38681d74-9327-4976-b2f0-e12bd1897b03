2025-07-30 08:45:22 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-30 08:45:22 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-30 08:45:23 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 08:45:23 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 08:45:24 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-30 08:45:24 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-30 08:45:24 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-30 08:45:24 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-30 08:45:25 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 26552 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 08:45:25 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-30 08:45:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-30 08:45:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-30 08:45:27 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 08:45:27 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 08:45:27 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 08:45:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2416 ms
2025-07-30 08:45:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 08:45:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 08:45:28 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-30 08:45:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-30 08:45:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@72d7afff, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c41f6a0, org.springframework.security.web.context.SecurityContextPersistenceFilter@4d7f9b33, org.springframework.security.web.header.HeaderWriterFilter@1af6d656, org.springframework.security.web.authentication.logout.LogoutFilter@1ae23815, io.seata.console.filter.JwtAuthenticationTokenFilter@7b43f95c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@608c7b5b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@25f73119, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@311453ab, org.springframework.security.web.session.SessionManagementFilter@3b11deb6, org.springframework.security.web.access.ExceptionTranslationFilter@47f416d0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7b6b99c5]
2025-07-30 08:45:29 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-30 08:45:29 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-30 08:45:29 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 7.632 seconds (JVM running for 10.511)
2025-07-30 08:45:29 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-30 08:45:29 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-30 08:45:30 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-30 08:45:30 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 08:45:30 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 08:45:30 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-30 08:45:30 [main] INFO  io.seata.server.ServerRunner - seata server started in 1452 millSeconds
2025-07-30 08:45:38 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753836338379
timestamp=1753836338379
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x91f0f40f, L:/*************:8091 - R:/*************:5905],client version:1.7.1
2025-07-30 08:45:41 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753836341685
timestamp=1753836341685
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x96342ee6, L:/*************:8091 - R:/*************:5912],client version:1.7.1
2025-07-30 08:45:45 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xde6696ab, L:/*************:8091 - R:/*************:5946],client version:1.7.1
2025-07-30 08:45:58 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753836358729
timestamp=1753836358729
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xbcc522a4, L:/*************:8091 - R:/*************:6006],client version:1.7.1
2025-07-30 08:46:03 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x1fdd853e, L:/*************:8091 - R:/*************:6021],client version:1.7.1
2025-07-30 08:46:03 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753836363520
timestamp=1753836363520
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xbcd9dded, L:/*************:8091 - R:/*************:6023],client version:1.7.1
2025-07-30 08:46:07 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xf8378e24, L:/*************:8091 - R:/*************:6036],client version:1.7.1
2025-07-30 08:46:38 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x7bb157b2, L:/*************:8091 - R:/*************:6135],client version:1.7.1
2025-07-30 09:14:57 [RetryRollbacking_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@29714dce (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 09:14:57 [RetryRollbacking_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryRollbacking
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10079ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$5(DefaultCoordinator.java:495)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 09:26:01 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6036 to server channel inactive.
2025-07-30 09:26:01 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6023 to server channel inactive.
2025-07-30 09:26:01 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbcd9dded, L:/*************:8091 ! R:/*************:6023]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:6023', channel=[id: 0xbcd9dded, L:/*************:8091 ! R:/*************:6023], resourceSets=null}
2025-07-30 09:26:01 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf8378e24, L:/*************:8091 ! R:/*************:6036]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:6036', channel=[id: 0xf8378e24, L:/*************:8091 ! R:/*************:6036], resourceSets=[]}
2025-07-30 09:26:17 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753838777430
timestamp=1753838777430
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x645a49da, L:/*************:8091 - R:/*************:11956],client version:1.7.1
2025-07-30 09:26:21 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x7c251cae, L:/*************:8091 - R:/*************:11966],client version:1.7.1
2025-07-30 09:44:15 [RetryRollbacking_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@640aee49 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 09:44:15 [RetryRollbacking_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryRollbacking
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10015ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$5(DefaultCoordinator.java:495)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 09:50:54 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5946 to server channel inactive.
2025-07-30 09:50:54 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xde6696ab, L:/*************:8091 ! R:/*************:5946]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:5946', channel=[id: 0xde6696ab, L:/*************:8091 ! R:/*************:5946], resourceSets=[]}
2025-07-30 09:50:54 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11966 to server channel inactive.
2025-07-30 09:50:54 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7c251cae, L:/*************:8091 ! R:/*************:11966]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11966', channel=[id: 0x7c251cae, L:/*************:8091 ! R:/*************:11966], resourceSets=[]}
2025-07-30 09:50:55 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11956 to server channel inactive.
2025-07-30 09:50:55 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x645a49da, L:/*************:8091 ! R:/*************:11956]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11956', channel=[id: 0x645a49da, L:/*************:8091 ! R:/*************:11956], resourceSets=null}
2025-07-30 09:50:56 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x96342ee6, L:/*************:8091 - R:/*************:5912] read idle.
2025-07-30 09:50:56 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5912 to server channel inactive.
2025-07-30 09:50:56 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x96342ee6, L:/*************:8091 - R:/*************:5912]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:5912', channel=[id: 0x96342ee6, L:/*************:8091 - R:/*************:5912], resourceSets=null}
2025-07-30 09:50:56 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x96342ee6, L:/*************:8091 - R:/*************:5912]
2025-07-30 09:50:56 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5912 to server channel inactive.
2025-07-30 09:50:56 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x96342ee6, L:/*************:8091 ! R:/*************:5912]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:5912', channel=[id: 0x96342ee6, L:/*************:8091 ! R:/*************:5912], resourceSets=null}
2025-07-30 09:50:57 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m669ms372µs300ns).
2025-07-30 09:50:57 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x1fdd853e, L:/*************:8091 - R:/*************:6021] read idle.
2025-07-30 09:50:57 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6021 to server channel inactive.
2025-07-30 09:50:57 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1fdd853e, L:/*************:8091 - R:/*************:6021]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6021', channel=[id: 0x1fdd853e, L:/*************:8091 - R:/*************:6021], resourceSets=[]}
2025-07-30 09:50:57 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x1fdd853e, L:/*************:8091 - R:/*************:6021]
2025-07-30 09:50:57 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6021 to server channel inactive.
2025-07-30 09:50:57 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1fdd853e, L:/*************:8091 ! R:/*************:6021]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6021', channel=[id: 0x1fdd853e, L:/*************:8091 ! R:/*************:6021], resourceSets=[]}
2025-07-30 09:50:57 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5905 to server channel inactive.
2025-07-30 09:50:57 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x91f0f40f, L:/*************:8091 ! R:/*************:5905]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:5905', channel=[id: 0x91f0f40f, L:/*************:8091 ! R:/*************:5905], resourceSets=null}
2025-07-30 09:50:57 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6135 to server channel inactive.
2025-07-30 09:50:57 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7bb157b2, L:/*************:8091 ! R:/*************:6135]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:6135', channel=[id: 0x7bb157b2, L:/*************:8091 ! R:/*************:6135], resourceSets=null}
2025-07-30 09:50:57 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6006 to server channel inactive.
2025-07-30 09:50:57 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbcc522a4, L:/*************:8091 ! R:/*************:6006]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6006', channel=[id: 0xbcc522a4, L:/*************:8091 ! R:/*************:6006], resourceSets=null}
2025-07-30 09:50:58 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753840258410
timestamp=1753840258410
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xf1f062c5, L:/*************:8091 - R:/*************:2648],client version:1.7.1
2025-07-30 09:50:58 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x7fa7238c, L:/*************:8091 - R:/*************:2652],client version:1.7.1
2025-07-30 09:51:04 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753840264206
timestamp=1753840264206
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xd5aca276, L:/*************:8091 - R:/*************:2661],client version:1.7.1
2025-07-30 09:51:04 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x1c8e0fbf, L:/*************:8091 - R:/*************:2665],client version:1.7.1
2025-07-30 09:51:05 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753840265048
timestamp=1753840265048
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xea1d7321, L:/*************:8091 - R:/*************:2668],client version:1.7.1
2025-07-30 09:51:05 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753840265451
timestamp=1753840265451
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xc302c271, L:/*************:8091 - R:/*************:5751],client version:1.7.1
2025-07-30 09:51:05 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xc396488b, L:/*************:8091 - R:/*************:5753],client version:1.7.1
2025-07-30 09:51:05 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xdb9207bb, L:/*************:8091 - R:/*************:5754],client version:1.7.1
2025-07-30 10:10:48 [TxTimeoutCheck_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@593f5c77 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 10:10:48 [TxTimeoutCheck_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: TxTimeoutCheck
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10034ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 10:12:55 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:12:56 [ServerHandlerThread_1_10_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:964447803284549633
2025-07-30 10:12:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:964447803284549633', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:12:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:964447803284549633', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:12:56 [ServerHandlerThread_1_11_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284549633.
2025-07-30 10:12:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:13:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:13:34 [ServerHandlerThread_1_12_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:964447803284549675
2025-07-30 10:13:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:964447803284549675', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:13:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:964447803284549675', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:13:35 [ServerHandlerThread_1_13_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284549675.
2025-07-30 10:13:35 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:14:31 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:14:31 [ServerHandlerThread_1_14_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:964447803284549791
2025-07-30 10:14:31 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:964447803284549791', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:14:31 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:964447803284549791', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:14:31 [ServerHandlerThread_1_15_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284549791.
2025-07-30 10:14:31 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:15:06 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284549633.
2025-07-30 10:15:45 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284549675.
2025-07-30 10:15:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:15:57 [ServerHandlerThread_1_16_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:964447803284549990
2025-07-30 10:15:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:964447803284549990', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:15:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:964447803284549990', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:15:58 [ServerHandlerThread_1_17_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284549990.
2025-07-30 10:15:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:16:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:16:06 [ServerHandlerThread_1_18_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:964447803284550010
2025-07-30 10:16:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:964447803284550010', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:16:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:964447803284550010', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:16:06 [ServerHandlerThread_1_19_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284550010.
2025-07-30 10:16:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:16:08 [RetryCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2fafa97f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 10:16:08 [RetryCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10016ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$6(DefaultCoordinator.java:499)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 10:16:42 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284549791.
2025-07-30 10:18:08 [RetryRollbacking_1_1] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284549990.
2025-07-30 10:18:17 [RetryRollbacking_1_1] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284550010.
2025-07-30 10:21:36 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:21:36 [ServerHandlerThread_1_20_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:964447803284550304
2025-07-30 10:21:36 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:964447803284550304', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:21:36 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:964447803284550304', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:21:36 [ServerHandlerThread_1_21_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284550304.
2025-07-30 10:21:36 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:22:29 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5751 to server channel inactive.
2025-07-30 10:22:29 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5754 to server channel inactive.
2025-07-30 10:22:29 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc302c271, L:/*************:8091 ! R:/*************:5751]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5751', channel=[id: 0xc302c271, L:/*************:8091 ! R:/*************:5751], resourceSets=null}
2025-07-30 10:22:29 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdb9207bb, L:/*************:8091 ! R:/*************:5754]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5754', channel=[id: 0xdb9207bb, L:/*************:8091 ! R:/*************:5754], resourceSets=[]}
2025-07-30 10:22:40 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753842160668
timestamp=1753842160668
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xe5fc38d5, L:/*************:8091 - R:/*************:9523],client version:1.7.1
2025-07-30 10:22:44 [ServerHandlerThread_1_22_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xef2c8358, L:/*************:8091 - R:/*************:9529],client version:1.7.1
2025-07-30 10:23:47 [RetryRollbacking_1_1] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:964447803284550304.
2025-07-30 10:26:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:26:56 [ServerHandlerThread_1_23_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:964447803284550438
2025-07-30 10:26:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:964447803284550438', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:26:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:964447803284550438', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:27:07 [ServerHandlerThread_1_24_500] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@713f3b83 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 10:27:07 [ServerHandlerThread_1_24_500] ERROR i.s.c.e.AbstractExceptionHandler - Catch TransactionException while do RPC, request: GlobalRollbackRequest{xid='*************:8091:964447803284550438', extraData='null'}
io.seata.core.exception.TransactionException: global rollback request failed. xid=*************:8091:964447803284550438, msg=HikariPool-1 - Connection is not available, request timed out after 10014ms.
	at io.seata.server.AbstractTCInboundHandler$3.execute(AbstractTCInboundHandler.java:143)
	at io.seata.server.AbstractTCInboundHandler$3.execute(AbstractTCInboundHandler.java:135)
	at io.seata.core.exception.AbstractExceptionHandler.exceptionHandleTemplate(AbstractExceptionHandler.java:131)
	at io.seata.server.AbstractTCInboundHandler.handle(AbstractTCInboundHandler.java:135)
	at io.seata.core.protocol.transaction.GlobalRollbackRequest.handle(GlobalRollbackRequest.java:34)
	at io.seata.server.coordinator.DefaultCoordinator.onRequest(DefaultCoordinator.java:523)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.onRequestMessage(ServerOnRequestProcessor.java:206)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.process(ServerOnRequestProcessor.java:122)
	at io.seata.core.rpc.netty.AbstractNettyRemoting.lambda$processMessage$2(AbstractNettyRemoting.java:281)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: io.seata.common.exception.DataAccessException: HikariPool-1 - Connection is not available, request timed out after 10014ms.
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:127)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.readSession(DataBaseTransactionStoreManager.java:156)
	at io.seata.server.storage.db.session.DataBaseSessionManager.findGlobalSession(DataBaseSessionManager.java:161)
	at io.seata.server.session.SessionHolder.findGlobalSession(SessionHolder.java:355)
	at io.seata.server.session.SessionHolder.findGlobalSession(SessionHolder.java:344)
	at io.seata.server.coordinator.DefaultCore.rollback(DefaultCore.java:279)
	at io.seata.server.coordinator.DefaultCoordinator.doGlobalRollback(DefaultCoordinator.java:272)
	at io.seata.server.AbstractTCInboundHandler$3.execute(AbstractTCInboundHandler.java:140)
	... 12 common frames omitted
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10014ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:116)
	... 19 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 22 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 25 common frames omitted
2025-07-30 10:27:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Begin, resultCode=Failed, msg='TransactionException[global rollback request failed. xid=*************:8091:964447803284550438, msg=HikariPool-1 - Connection is not available, request timed out after 10014ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:27:57 [TxTimeoutCheck_1_1] WARN  i.s.s.coordinator.DefaultCoordinator - Global transaction[*************:8091:964447803284550438] is timeout and will be rollback,transaction begin time:2025-07-30 and now:2025-07-30
2025-07-30 10:28:16 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-30 10:28:16 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 10:28:16 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-30 10:28:16 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-30 10:28:16 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-30 10:28:24 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-30 10:28:24 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-30 10:28:24 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 10:28:24 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 10:28:25 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-30 10:28:25 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-30 10:28:25 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-30 10:28:25 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-30 10:28:25 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 38604 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 10:28:25 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-30 10:28:27 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-30 10:28:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-30 10:28:27 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 10:28:27 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 10:28:27 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 10:28:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1455 ms
2025-07-30 10:28:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 10:28:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 10:28:27 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-30 10:28:28 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-30 10:28:28 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6785786d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c261c74, org.springframework.security.web.context.SecurityContextPersistenceFilter@5b2c41f9, org.springframework.security.web.header.HeaderWriterFilter@4d7f9b33, org.springframework.security.web.authentication.logout.LogoutFilter@17541204, io.seata.console.filter.JwtAuthenticationTokenFilter@6d38a81d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1ee52741, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3810806c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@41f61188, org.springframework.security.web.session.SessionManagementFilter@3c205259, org.springframework.security.web.access.ExceptionTranslationFilter@5d08a65c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7668f8fd]
2025-07-30 10:28:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-30 10:28:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-30 10:28:28 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.516 seconds (JVM running for 6.315)
2025-07-30 10:28:28 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-30 10:28:28 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-30 10:28:28 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-30 10:28:28 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 10:28:28 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 10:28:28 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-30 10:28:28 [main] INFO  io.seata.server.ServerRunner - seata server started in 812 millSeconds
2025-07-30 10:28:28 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753842508410
timestamp=1753842508410
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x21068838, L:/*************:8091 - R:/*************:10125],client version:1.7.1
2025-07-30 10:28:28 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xe81911c7, L:/*************:8091 - R:/*************:10132],client version:1.7.1
2025-07-30 10:28:30 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753842510412
timestamp=1753842510412
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x7779e412, L:/*************:8091 - R:/*************:10135],client version:1.7.1
2025-07-30 10:28:30 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xd317e304, L:/*************:8091 - R:/*************:10138],client version:1.7.1
2025-07-30 10:28:34 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753842514206
timestamp=1753842514206
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x25f2adb0, L:/*************:8091 - R:/*************:10142],client version:1.7.1
2025-07-30 10:28:34 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xb989caeb, L:/*************:8091 - R:/*************:10144],client version:1.7.1
2025-07-30 10:28:35 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753842515049
timestamp=1753842515049
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xa24f0dd9, L:/*************:8091 - R:/*************:10146],client version:1.7.1
2025-07-30 10:28:35 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x952740ea, L:/*************:8091 - R:/*************:10149],client version:1.7.1
2025-07-30 10:29:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:29:20 [ServerHandlerThread_1_5_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:1576937377914585089
2025-07-30 10:29:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:1576937377914585089', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:29:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:1576937377914585089', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:29:20 [ServerHandlerThread_1_6_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:1576937377914585089.
2025-07-30 10:29:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:31:31 [RetryRollbacking_1_1] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:1576937377914585089.
2025-07-30 10:31:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:31:55 [ServerHandlerThread_1_7_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:1576937377914585223
2025-07-30 10:31:55 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:1576937377914585223', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:31:55 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:1576937377914585223', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:31:55 [ServerHandlerThread_1_8_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:1576937377914585223.
2025-07-30 10:31:55 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 10:57:55 [TxTimeoutCheck_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@286ffc9a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 10:57:55 [TxTimeoutCheck_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: TxTimeoutCheck
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10070ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 10:58:01 [AsyncCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2a49b96d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 10:58:01 [AsyncCommitting_1_1] ERROR i.seata.server.session.SessionHolder - Exception running function with key = AsyncCommitting
io.seata.common.exception.DataAccessException: HikariPool-1 - Connection is not available, request timed out after 10023ms.
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:182)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.readSession(DataBaseTransactionStoreManager.java:187)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.readSession(DataBaseTransactionStoreManager.java:222)
	at io.seata.server.storage.db.session.DataBaseSessionManager.findGlobalSessions(DataBaseSessionManager.java:185)
	at io.seata.server.coordinator.DefaultCoordinator.handleAsyncCommitting(DefaultCoordinator.java:446)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:400)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$7(DefaultCoordinator.java:503)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10023ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:164)
	... 13 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 16 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 19 common frames omitted
2025-07-30 10:58:05 [TxTimeoutCheck_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@733210f3 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 10:58:05 [TxTimeoutCheck_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: TxTimeoutCheck
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10002ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 10:58:11 [AsyncCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@596030af (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 10:58:11 [AsyncCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute release lock failure, key is: AsyncCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10031ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.releaseLock(DataBaseDistributedLocker.java:182)
	at io.seata.server.session.SessionHolder.releaseDistributedLock(SessionHolder.java:386)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:407)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$7(DefaultCoordinator.java:503)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 11:27:19 [AsyncCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7e35ef36 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 11:27:19 [AsyncCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: AsyncCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10036ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$7(DefaultCoordinator.java:503)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 11:46:03 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-30 11:46:03 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 11:46:03 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-30 11:46:03 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-30 11:46:03 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-30 11:46:04 [SpringApplicationShutdownHook] ERROR i.s.c.rpc.netty.NettyServerBootstrap - shutdown execute error: Client not connected, current status:UNHEALTHY
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:644)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:623)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:447)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:308)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:293)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:275)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:122)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:204)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:192)
	at io.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:120)
	at io.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:192)
	at io.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:127)
	at io.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:128)
	at io.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:559)
	at io.seata.server.ServerRunner.destroy(ServerRunner.java:86)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
2025-07-30 13:56:34 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-30 13:56:35 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-30 13:56:35 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 13:56:35 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 13:56:36 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-30 13:56:36 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-30 13:56:36 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-30 13:56:36 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-30 13:56:36 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 19132 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 13:56:36 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-30 13:56:37 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-30 13:56:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-30 13:56:37 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 13:56:37 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 13:56:37 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 13:56:37 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1150 ms
2025-07-30 13:56:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 13:56:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 13:56:38 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-30 13:56:38 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-30 13:56:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7602c65d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5a5c2889, org.springframework.security.web.context.SecurityContextPersistenceFilter@205b73d8, org.springframework.security.web.header.HeaderWriterFilter@694f0655, org.springframework.security.web.authentication.logout.LogoutFilter@6018d82c, io.seata.console.filter.JwtAuthenticationTokenFilter@7668f8fd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5bc40f5d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f5a092e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@75566f4, org.springframework.security.web.session.SessionManagementFilter@10f192d8, org.springframework.security.web.access.ExceptionTranslationFilter@18ffc008, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3183a37c]
2025-07-30 13:56:38 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-30 13:56:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-30 13:56:38 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.84 seconds (JVM running for 4.964)
2025-07-30 13:56:38 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-30 13:56:39 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-30 13:56:39 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-30 13:56:39 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 13:56:39 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 13:56:39 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-30 13:56:39 [main] INFO  io.seata.server.ServerRunner - seata server started in 1609 millSeconds
2025-07-30 14:15:38 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753856138748
timestamp=1753856138748
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x95e82578, L:/*************:8091 - R:/*************:10743],client version:1.7.1
2025-07-30 14:15:43 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753856143323
timestamp=1753856143323
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xc95a0f64, L:/*************:8091 - R:/*************:10762],client version:1.7.1
2025-07-30 14:15:46 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x73c930a4, L:/*************:8091 - R:/*************:10774],client version:1.7.1
2025-07-30 14:16:39 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x27097f8d, L:/*************:8091 - R:/*************:10875],client version:1.7.1
2025-07-30 14:18:03 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753856283852
timestamp=1753856283852
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xe45d59b0, L:/*************:8091 - R:/*************:11038],client version:1.7.1
2025-07-30 14:18:06 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x4b7871ea, L:/*************:8091 - R:/*************:11046],client version:1.7.1
2025-07-30 14:19:34 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753856374469
timestamp=1753856374469
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x6fe0c4a0, L:/*************:8091 - R:/*************:11705],client version:1.7.1
2025-07-30 14:19:37 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xb49134ad, L:/*************:8091 - R:/*************:11763],client version:1.7.1
2025-07-30 14:19:51 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11763 to server channel inactive.
2025-07-30 14:19:51 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11705 to server channel inactive.
2025-07-30 14:19:51 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6fe0c4a0, L:/*************:8091 ! R:/*************:11705]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11705', channel=[id: 0x6fe0c4a0, L:/*************:8091 ! R:/*************:11705], resourceSets=null}
2025-07-30 14:19:51 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb49134ad, L:/*************:8091 ! R:/*************:11763]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11763', channel=[id: 0xb49134ad, L:/*************:8091 ! R:/*************:11763], resourceSets=[]}
2025-07-30 14:22:19 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753856539434
timestamp=1753856539434
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xf97e0174, L:/*************:8091 - R:/*************:13764],client version:1.7.1
2025-07-30 14:22:22 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x059a6126, L:/*************:8091 - R:/*************:13769],client version:1.7.1
2025-07-30 14:26:15 [RetryRollbacking_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@26c607c4 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 14:26:15 [TxTimeoutCheck_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4a9d4fed (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 14:26:15 [TxTimeoutCheck_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: TxTimeoutCheck
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10021ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 14:26:15 [RetryRollbacking_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryRollbacking
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10021ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$5(DefaultCoordinator.java:495)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	... 7 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 14:35:41 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10774 to server channel inactive.
2025-07-30 14:35:41 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10762 to server channel inactive.
2025-07-30 14:35:41 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc95a0f64, L:/*************:8091 ! R:/*************:10762]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10762', channel=[id: 0xc95a0f64, L:/*************:8091 ! R:/*************:10762], resourceSets=null}
2025-07-30 14:35:41 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x73c930a4, L:/*************:8091 ! R:/*************:10774]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10774', channel=[id: 0x73c930a4, L:/*************:8091 ! R:/*************:10774], resourceSets=[]}
2025-07-30 14:36:01 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753857360999
timestamp=1753857360999
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x0a8ccc69, L:/*************:8091 - R:/*************:14761],client version:1.7.1
2025-07-30 14:36:04 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x9dd2afd6, L:/*************:8091 - R:/*************:14770],client version:1.7.1
2025-07-30 14:45:22 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14761 to server channel inactive.
2025-07-30 14:45:22 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14770 to server channel inactive.
2025-07-30 14:45:22 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9dd2afd6, L:/*************:8091 ! R:/*************:14770]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14770', channel=[id: 0x9dd2afd6, L:/*************:8091 ! R:/*************:14770], resourceSets=[]}
2025-07-30 14:45:22 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0a8ccc69, L:/*************:8091 ! R:/*************:14761]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14761', channel=[id: 0x0a8ccc69, L:/*************:8091 ! R:/*************:14761], resourceSets=null}
2025-07-30 14:45:33 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753857933596
timestamp=1753857933596
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x5682fe1a, L:/*************:8091 - R:/*************:2070],client version:1.7.1
2025-07-30 14:45:37 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x2eebd774, L:/*************:8091 - R:/*************:2076],client version:1.7.1
2025-07-30 14:51:48 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x5682fe1a, L:/*************:8091 - R:/*************:2070] read idle.
2025-07-30 14:51:48 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2070 to server channel inactive.
2025-07-30 14:51:48 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5682fe1a, L:/*************:8091 - R:/*************:2070]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2070', channel=[id: 0x5682fe1a, L:/*************:8091 - R:/*************:2070], resourceSets=null}
2025-07-30 14:51:48 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x5682fe1a, L:/*************:8091 - R:/*************:2070]
2025-07-30 14:51:48 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2070 to server channel inactive.
2025-07-30 14:51:48 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5682fe1a, L:/*************:8091 ! R:/*************:2070]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2070', channel=[id: 0x5682fe1a, L:/*************:8091 ! R:/*************:2070], resourceSets=null}
2025-07-30 14:51:52 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2076 to server channel inactive.
2025-07-30 14:51:52 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2eebd774, L:/*************:8091 ! R:/*************:2076]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2076', channel=[id: 0x2eebd774, L:/*************:8091 ! R:/*************:2076], resourceSets=[]}
2025-07-30 14:51:53 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753858313354
timestamp=1753858313354
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x85089312, L:/*************:8091 - R:/*************:2773],client version:1.7.1
2025-07-30 14:51:53 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x30b33a60, L:/*************:8091 - R:/*************:2776],client version:1.7.1
2025-07-30 14:55:42 [RetryCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7b86a36c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 14:55:42 [RetryCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10040ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$6(DefaultCoordinator.java:499)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 14:55:43 [AsyncCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3190d9ee (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 14:55:43 [AsyncCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: AsyncCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10008ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$7(DefaultCoordinator.java:503)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 14:55:48 [TxTimeoutCheck_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5eaddcdf (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 14:55:48 [TxTimeoutCheck_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: TxTimeoutCheck
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10018ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 14:55:52 [RetryCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@23d77c3a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 14:55:52 [RetryCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10023ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$6(DefaultCoordinator.java:499)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 14:55:53 [AsyncCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4cd77ff (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 14:55:53 [AsyncCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: AsyncCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10011ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$7(DefaultCoordinator.java:503)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 15:07:48 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2773 to server channel inactive.
2025-07-30 15:07:48 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x85089312, L:/*************:8091 ! R:/*************:2773]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2773', channel=[id: 0x85089312, L:/*************:8091 ! R:/*************:2773], resourceSets=null}
2025-07-30 15:07:48 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2776 to server channel inactive.
2025-07-30 15:07:48 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x30b33a60, L:/*************:8091 ! R:/*************:2776]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2776', channel=[id: 0x30b33a60, L:/*************:8091 ! R:/*************:2776], resourceSets=[]}
2025-07-30 15:08:04 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753859284060
timestamp=1753859284060
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x4d3b70a3, L:/*************:8091 - R:/*************:4757],client version:1.7.1
2025-07-30 15:08:08 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xa2792932, L:/*************:8091 - R:/*************:4791],client version:1.7.1
2025-07-30 15:25:06 [RetryCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5c960efa (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 15:25:06 [RetryCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10027ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$6(DefaultCoordinator.java:499)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 15:54:54 [TxTimeoutCheck_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7a59df63 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 15:54:54 [TxTimeoutCheck_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: TxTimeoutCheck
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10012ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 15:55:01 [RetryRollbacking_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@24bd69a3 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 15:55:01 [RetryRollbacking_1_1] ERROR i.seata.server.session.SessionHolder - Exception running function with key = RetryRollbacking
io.seata.common.exception.DataAccessException: HikariPool-1 - Connection is not available, request timed out after 10008ms.
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:182)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.readSession(DataBaseTransactionStoreManager.java:187)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.readSession(DataBaseTransactionStoreManager.java:222)
	at io.seata.server.storage.db.session.DataBaseSessionManager.findGlobalSessions(DataBaseSessionManager.java:185)
	at io.seata.server.coordinator.DefaultCoordinator.handleRetryRollbacking(DefaultCoordinator.java:370)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:400)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$5(DefaultCoordinator.java:495)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10008ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:164)
	... 13 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 16 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 19 common frames omitted
2025-07-30 15:55:03 [RetryCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3d360edc (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 15:55:03 [RetryCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10034ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$6(DefaultCoordinator.java:499)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 15:55:04 [AsyncCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6bd3f394 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 15:55:04 [AsyncCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: AsyncCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10012ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$7(DefaultCoordinator.java:503)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 15:55:04 [TxTimeoutCheck_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1f887184 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 15:55:04 [TxTimeoutCheck_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: TxTimeoutCheck
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10013ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 13 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 16 common frames omitted
2025-07-30 15:59:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 15:59:11 [ServerHandlerThread_1_10_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:126778349061156865
2025-07-30 15:59:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:126778349061156865', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 15:59:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:126778349061156865', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1950369194603769857', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 15:59:21 [ForkJoinPool.commonPool-worker-2] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1f6b0dde (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 15:59:21 [ForkJoinPool.commonPool-worker-2] ERROR i.s.c.e.AbstractExceptionHandler - Catch TransactionException while do RPC, request: BranchRegisterRequest{xid='*************:8091:126778349061156865', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1950369194603769857', applicationData='null'}
io.seata.core.exception.TransactionException: branch register request failed. xid=*************:8091:126778349061156865, msg=HikariPool-1 - Connection is not available, request timed out after 10015ms.
	at io.seata.server.AbstractTCInboundHandler$4.execute(AbstractTCInboundHandler.java:187)
	at io.seata.server.AbstractTCInboundHandler$4.execute(AbstractTCInboundHandler.java:179)
	at io.seata.core.exception.AbstractExceptionHandler.exceptionHandleTemplate(AbstractExceptionHandler.java:131)
	at io.seata.server.AbstractTCInboundHandler.handle(AbstractTCInboundHandler.java:179)
	at io.seata.core.protocol.transaction.BranchRegisterRequest.handle(BranchRegisterRequest.java:136)
	at io.seata.server.coordinator.DefaultCoordinator.onRequest(DefaultCoordinator.java:523)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.handleRequestsByMergedWarpMessage(ServerOnRequestProcessor.java:288)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.lambda$onRequestMessage$1(ServerOnRequestProcessor.java:178)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: io.seata.common.exception.DataAccessException: HikariPool-1 - Connection is not available, request timed out after 10015ms.
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:127)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.readSession(DataBaseTransactionStoreManager.java:156)
	at io.seata.server.storage.db.session.DataBaseSessionManager.findGlobalSession(DataBaseSessionManager.java:161)
	at io.seata.server.session.SessionHolder.findGlobalSession(SessionHolder.java:355)
	at io.seata.server.coordinator.AbstractCore.assertGlobalSessionNotNull(AbstractCore.java:123)
	at io.seata.server.coordinator.AbstractCore.branchRegister(AbstractCore.java:76)
	at io.seata.server.coordinator.DefaultCore.branchRegister(DefaultCore.java:103)
	at io.seata.server.coordinator.DefaultCoordinator.doBranchRegister(DefaultCoordinator.java:294)
	at io.seata.server.AbstractTCInboundHandler$4.execute(AbstractTCInboundHandler.java:184)
	... 14 common frames omitted
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10015ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:116)
	... 22 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 25 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 28 common frames omitted
2025-07-30 15:59:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[branch register request failed. xid=*************:8091:126778349061156865, msg=HikariPool-1 - Connection is not available, request timed out after 10015ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 15:59:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:126778349061156865', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 15:59:22 [ServerHandlerThread_1_12_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:126778349061156865.
2025-07-30 15:59:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 15:59:35 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4791 to server channel inactive.
2025-07-30 15:59:35 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4757 to server channel inactive.
2025-07-30 15:59:35 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4d3b70a3, L:/*************:8091 ! R:/*************:4757]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4757', channel=[id: 0x4d3b70a3, L:/*************:8091 ! R:/*************:4757], resourceSets=null}
2025-07-30 15:59:35 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa2792932, L:/*************:8091 ! R:/*************:4791]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4791', channel=[id: 0xa2792932, L:/*************:8091 ! R:/*************:4791], resourceSets=[]}
2025-07-30 15:59:44 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-30 15:59:44 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 15:59:44 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-30 15:59:44 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-30 15:59:45 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-30 15:59:48 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11038 to server channel inactive.
2025-07-30 15:59:48 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13764 to server channel inactive.
2025-07-30 15:59:48 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10875 to server channel inactive.
2025-07-30 15:59:48 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13769 to server channel inactive.
2025-07-30 15:59:48 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11046 to server channel inactive.
2025-07-30 15:59:48 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe45d59b0, L:/*************:8091 ! R:/*************:11038]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11038', channel=[id: 0xe45d59b0, L:/*************:8091 ! R:/*************:11038], resourceSets=null}
2025-07-30 15:59:48 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10743 to server channel inactive.
2025-07-30 15:59:48 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4b7871ea, L:/*************:8091 ! R:/*************:11046]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11046', channel=[id: 0x4b7871ea, L:/*************:8091 ! R:/*************:11046], resourceSets=[]}
2025-07-30 15:59:48 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf97e0174, L:/*************:8091 ! R:/*************:13764]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13764', channel=[id: 0xf97e0174, L:/*************:8091 ! R:/*************:13764], resourceSets=null}
2025-07-30 15:59:48 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x95e82578, L:/*************:8091 ! R:/*************:10743]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:10743', channel=[id: 0x95e82578, L:/*************:8091 ! R:/*************:10743], resourceSets=null}
2025-07-30 15:59:48 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x27097f8d, L:/*************:8091 ! R:/*************:10875]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:10875', channel=[id: 0x27097f8d, L:/*************:8091 ! R:/*************:10875], resourceSets=null}
2025-07-30 15:59:48 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x059a6126, L:/*************:8091 ! R:/*************:13769]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13769', channel=[id: 0x059a6126, L:/*************:8091 ! R:/*************:13769], resourceSets=[]}
2025-07-30 15:59:51 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-30 15:59:51 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-30 15:59:51 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 15:59:51 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 15:59:52 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-30 15:59:52 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-30 15:59:52 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-30 15:59:52 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-30 15:59:52 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 680 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 15:59:52 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-30 15:59:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-30 15:59:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-30 15:59:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 15:59:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 15:59:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 15:59:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1459 ms
2025-07-30 15:59:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 15:59:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 15:59:55 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-30 15:59:55 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-30 15:59:55 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@18139a43, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4df5f119, org.springframework.security.web.context.SecurityContextPersistenceFilter@64de9fa4, org.springframework.security.web.header.HeaderWriterFilter@7c3223aa, org.springframework.security.web.authentication.logout.LogoutFilter@641c8ba4, io.seata.console.filter.JwtAuthenticationTokenFilter@3dbb7bb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4a65c40, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5fd5d6d1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6277a496, org.springframework.security.web.session.SessionManagementFilter@4626f584, org.springframework.security.web.access.ExceptionTranslationFilter@b859355, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@23dda7a3]
2025-07-30 15:59:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-30 15:59:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-30 15:59:55 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.215 seconds (JVM running for 6.077)
2025-07-30 15:59:55 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-30 15:59:55 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-30 15:59:55 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-30 15:59:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 15:59:55 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 15:59:56 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-30 15:59:56 [main] INFO  io.seata.server.ServerRunner - seata server started in 678 millSeconds
2025-07-30 15:59:56 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xc7e989f4, L:/*************:8091 - R:/*************:13876],client version:1.7.1
2025-07-30 15:59:58 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753862398537
timestamp=1753862398537
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x09262c88, L:/*************:8091 - R:/*************:13903],client version:1.7.1
2025-07-30 15:59:59 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x9a9e77b4, L:/*************:8091 - R:/*************:13904],client version:1.7.1
2025-07-30 15:59:59 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753862399234
timestamp=1753862399234
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x30c5f67f, L:/*************:8091 - R:/*************:13905],client version:1.7.1
2025-07-30 15:59:59 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xc2a43adc, L:/*************:8091 - R:/*************:13908],client version:1.7.1
2025-07-30 16:00:03 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753862403660
timestamp=1753862403660
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x3c4fb559, L:/*************:8091 - R:/*************:13920],client version:1.7.1
2025-07-30 16:00:48 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753862448363
timestamp=1753862448363
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x4c15f96e, L:/*************:8091 - R:/*************:14028],client version:1.7.1
2025-07-30 16:00:48 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x6d63cae5, L:/*************:8091 - R:/*************:14029],client version:1.7.1
2025-07-30 16:01:08 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-30 16:01:08 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 16:01:08 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-30 16:01:08 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-30 16:01:09 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-30 16:01:12 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13905 to server channel inactive.
2025-07-30 16:01:12 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13920 to server channel inactive.
2025-07-30 16:01:12 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14028 to server channel inactive.
2025-07-30 16:01:12 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14029 to server channel inactive.
2025-07-30 16:01:12 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13876 to server channel inactive.
2025-07-30 16:01:12 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13904 to server channel inactive.
2025-07-30 16:01:12 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x30c5f67f, L:/*************:8091 ! R:/*************:13905]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13905', channel=[id: 0x30c5f67f, L:/*************:8091 ! R:/*************:13905], resourceSets=null}
2025-07-30 16:01:12 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13903 to server channel inactive.
2025-07-30 16:01:12 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13908 to server channel inactive.
2025-07-30 16:01:12 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6d63cae5, L:/*************:8091 ! R:/*************:14029]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14029', channel=[id: 0x6d63cae5, L:/*************:8091 ! R:/*************:14029], resourceSets=[]}
2025-07-30 16:01:12 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3c4fb559, L:/*************:8091 ! R:/*************:13920]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13920', channel=[id: 0x3c4fb559, L:/*************:8091 ! R:/*************:13920], resourceSets=null}
2025-07-30 16:01:12 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc7e989f4, L:/*************:8091 ! R:/*************:13876]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13876', channel=[id: 0xc7e989f4, L:/*************:8091 ! R:/*************:13876], resourceSets=[]}
2025-07-30 16:01:12 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x09262c88, L:/*************:8091 ! R:/*************:13903]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13903', channel=[id: 0x09262c88, L:/*************:8091 ! R:/*************:13903], resourceSets=null}
2025-07-30 16:01:12 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9a9e77b4, L:/*************:8091 ! R:/*************:13904]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13904', channel=[id: 0x9a9e77b4, L:/*************:8091 ! R:/*************:13904], resourceSets=null}
2025-07-30 16:01:12 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4c15f96e, L:/*************:8091 ! R:/*************:14028]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14028', channel=[id: 0x4c15f96e, L:/*************:8091 ! R:/*************:14028], resourceSets=null}
2025-07-30 16:01:12 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc2a43adc, L:/*************:8091 ! R:/*************:13908]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13908', channel=[id: 0xc2a43adc, L:/*************:8091 ! R:/*************:13908], resourceSets=[]}
2025-07-30 16:01:15 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-30 16:01:15 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-30 16:01:15 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 16:01:15 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 16:01:16 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-30 16:01:16 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-30 16:01:16 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-30 16:01:16 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-30 16:01:16 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 25024 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 16:01:16 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-30 16:01:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-30 16:01:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-30 16:01:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 16:01:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 16:01:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 16:01:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1289 ms
2025-07-30 16:01:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 16:01:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 16:01:18 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-30 16:01:18 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-30 16:01:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6d94a966, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@24bdd996, org.springframework.security.web.context.SecurityContextPersistenceFilter@693ed09d, org.springframework.security.web.header.HeaderWriterFilter@6dbdbb69, org.springframework.security.web.authentication.logout.LogoutFilter@612531a3, io.seata.console.filter.JwtAuthenticationTokenFilter@445b4594, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6c26e588, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1d6014a7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1f602930, org.springframework.security.web.session.SessionManagementFilter@7c3223aa, org.springframework.security.web.access.ExceptionTranslationFilter@7ba623d1, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@52b6561b]
2025-07-30 16:01:18 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-30 16:01:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-30 16:01:18 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.885 seconds (JVM running for 5.406)
2025-07-30 16:01:18 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-30 16:01:18 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-30 16:01:19 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-30 16:01:19 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 16:01:19 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 16:01:19 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-30 16:01:19 [main] INFO  io.seata.server.ServerRunner - seata server started in 631 millSeconds
2025-07-30 16:01:19 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753862479226
timestamp=1753862479226
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x87258994, L:/*************:8091 - R:/*************:14125],client version:1.7.1
2025-07-30 16:01:19 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753862478295
timestamp=1753862478295
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa0a82e08, L:/*************:8091 - R:/*************:14117],client version:1.7.1
2025-07-30 16:01:19 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x5bc69d31, L:/*************:8091 - R:/*************:14118],client version:1.7.1
2025-07-30 16:01:19 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xfec29a9d, L:/*************:8091 - R:/*************:14123],client version:1.7.1
2025-07-30 16:01:19 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753862478551
timestamp=1753862478551
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xb20554fe, L:/*************:8091 - R:/*************:14119],client version:1.7.1
2025-07-30 16:01:19 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xea72429d, L:/*************:8091 - R:/*************:14131],client version:1.7.1
2025-07-30 16:01:23 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753862483655
timestamp=1753862483655
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xd5ececbc, L:/*************:8091 - R:/*************:14136],client version:1.7.1
2025-07-30 16:01:24 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x577564d6, L:/*************:8091 - R:/*************:14138],client version:1.7.1
2025-07-30 16:02:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:54 [ServerHandlerThread_1_5_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:5954436297517916163
2025-07-30 16:02:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:5954436297517916163', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:55 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:5954436297517916163', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1950369194603769857', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:55 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:5954436297517916163, branchId = 5954436297517916166, resourceId = ******************************************* ,lockKeys = usd_config:1950369194603769857
2025-07-30 16:02:55 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5954436297517916166, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:5954436297517916163', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1950467129584668673', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:56 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:5954436297517916163, branchId = 5954436297517916169, resourceId = ******************************************* ,lockKeys = usd_workorder:1950467129584668673
2025-07-30 16:02:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5954436297517916169, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:5954436297517916163', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:57 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:5954436297517916163, branchId = 5954436297517916171, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1950467131853787138
2025-07-30 16:02:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5954436297517916171, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:5954436297517916163', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1950467133976104961', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:57 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:5954436297517916163, branchId = 5954436297517916174, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1950467133976104961
2025-07-30 16:02:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5954436297517916174, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:5954436297517916163', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1950467135985176578', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:58 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:5954436297517916163, branchId = 5954436297517916176, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1950467135985176578
2025-07-30 16:02:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5954436297517916176, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:5954436297517916163', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:5954436297517916163', branchId=5954436297517916166, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:5954436297517916163 branchId = 5954436297517916166
2025-07-30 16:02:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:5954436297517916163', branchId=5954436297517916169, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:5954436297517916163 branchId = 5954436297517916169
2025-07-30 16:02:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:5954436297517916163', branchId=5954436297517916171, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:5954436297517916163 branchId = 5954436297517916171
2025-07-30 16:02:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:5954436297517916163', branchId=5954436297517916174, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:5954436297517916163 branchId = 5954436297517916174
2025-07-30 16:02:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:5954436297517916163', branchId=5954436297517916176, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:02:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:5954436297517916163 branchId = 5954436297517916176
2025-07-30 16:02:59 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:5954436297517916163.
2025-07-30 16:11:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:11:51 [ServerHandlerThread_1_12_500] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2a7f866 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 16:11:52 [ServerHandlerThread_1_12_500] ERROR i.s.c.e.AbstractExceptionHandler - Catch TransactionException while do RPC, request: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}
io.seata.core.exception.TransactionException: begin global request failed. xid=null, msg=HikariPool-1 - Connection is not available, request timed out after 10027ms.
	at io.seata.server.AbstractTCInboundHandler$1.execute(AbstractTCInboundHandler.java:67)
	at io.seata.server.AbstractTCInboundHandler$1.execute(AbstractTCInboundHandler.java:60)
	at io.seata.core.exception.AbstractExceptionHandler.exceptionHandleTemplate(AbstractExceptionHandler.java:131)
	at io.seata.server.AbstractTCInboundHandler.handle(AbstractTCInboundHandler.java:60)
	at io.seata.core.protocol.transaction.GlobalBeginRequest.handle(GlobalBeginRequest.java:76)
	at io.seata.server.coordinator.DefaultCoordinator.onRequest(DefaultCoordinator.java:523)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.onRequestMessage(ServerOnRequestProcessor.java:206)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.process(ServerOnRequestProcessor.java:122)
	at io.seata.core.rpc.netty.AbstractNettyRemoting.lambda$processMessage$2(AbstractNettyRemoting.java:281)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: io.seata.common.exception.StoreException: HikariPool-1 - Connection is not available, request timed out after 10027ms.
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.insertGlobalTransactionDO(LogStoreDataBaseDAO.java:213)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.writeSession(DataBaseTransactionStoreManager.java:101)
	at io.seata.server.storage.db.session.DataBaseSessionManager.addGlobalSession(DataBaseSessionManager.java:82)
	at io.seata.server.session.AbstractSessionManager.onBegin(AbstractSessionManager.java:121)
	at io.seata.server.session.GlobalSession.begin(GlobalSession.java:196)
	at io.seata.server.coordinator.DefaultCore.begin(DefaultCore.java:136)
	at io.seata.server.coordinator.DefaultCoordinator.doGlobalBegin(DefaultCoordinator.java:253)
	at io.seata.server.AbstractTCInboundHandler$1.execute(AbstractTCInboundHandler.java:64)
	... 12 common frames omitted
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10027ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.insertGlobalTransactionDO(LogStoreDataBaseDAO.java:195)
	... 19 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 22 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 25 common frames omitted
2025-07-30 16:11:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='null', extraData='null', resultCode=Failed, msg='TransactionException[begin global request failed. xid=null, msg=HikariPool-1 - Connection is not available, request timed out after 10027ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:00 [ServerHandlerThread_1_13_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:5954436297517916181
2025-07-30 16:13:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:5954436297517916181', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:5954436297517916181', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:00 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:5954436297517916181, branchId = 5954436297517916183, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1950467131853787138
2025-07-30 16:13:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=5954436297517916183, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:01 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:5954436297517916181', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1950467129584668673', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:11 [ForkJoinPool.commonPool-worker-3] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5bf6095 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 16:13:11 [ForkJoinPool.commonPool-worker-3] ERROR i.s.c.e.AbstractExceptionHandler - Catch TransactionException while do RPC, request: BranchRegisterRequest{xid='*************:8091:5954436297517916181', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1950467129584668673', applicationData='null'}
io.seata.core.exception.TransactionException: branch register request failed. xid=*************:8091:5954436297517916181, msg=HikariPool-1 - Connection is not available, request timed out after 10009ms.
	at io.seata.server.AbstractTCInboundHandler$4.execute(AbstractTCInboundHandler.java:187)
	at io.seata.server.AbstractTCInboundHandler$4.execute(AbstractTCInboundHandler.java:179)
	at io.seata.core.exception.AbstractExceptionHandler.exceptionHandleTemplate(AbstractExceptionHandler.java:131)
	at io.seata.server.AbstractTCInboundHandler.handle(AbstractTCInboundHandler.java:179)
	at io.seata.core.protocol.transaction.BranchRegisterRequest.handle(BranchRegisterRequest.java:136)
	at io.seata.server.coordinator.DefaultCoordinator.onRequest(DefaultCoordinator.java:523)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.handleRequestsByMergedWarpMessage(ServerOnRequestProcessor.java:288)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.lambda$onRequestMessage$1(ServerOnRequestProcessor.java:178)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760)
	at java.base/java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:373)
	at java.base/java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.base/java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182)
	at java.base/java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655)
	at java.base/java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622)
	at java.base/java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165)
Caused by: io.seata.common.exception.DataAccessException: HikariPool-1 - Connection is not available, request timed out after 10009ms.
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:127)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.readSession(DataBaseTransactionStoreManager.java:156)
	at io.seata.server.storage.db.session.DataBaseSessionManager.findGlobalSession(DataBaseSessionManager.java:161)
	at io.seata.server.session.SessionHolder.findGlobalSession(SessionHolder.java:355)
	at io.seata.server.coordinator.AbstractCore.assertGlobalSessionNotNull(AbstractCore.java:123)
	at io.seata.server.coordinator.AbstractCore.branchRegister(AbstractCore.java:76)
	at io.seata.server.coordinator.DefaultCore.branchRegister(DefaultCore.java:103)
	at io.seata.server.coordinator.DefaultCoordinator.doBranchRegister(DefaultCoordinator.java:294)
	at io.seata.server.AbstractTCInboundHandler$4.execute(AbstractTCInboundHandler.java:184)
	... 16 common frames omitted
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10009ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:116)
	... 24 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 27 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 30 common frames omitted
2025-07-30 16:13:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[branch register request failed. xid=*************:8091:5954436297517916181, msg=HikariPool-1 - Connection is not available, request timed out after 10009ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:5954436297517916181', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchRollbackResponse{xid='*************:8091:5954436297517916181', branchId=5954436297517916183, branchStatus=PhaseTwo_Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:22 [ServerHandlerThread_1_16_500] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@2e32928a (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 16:13:22 [ServerHandlerThread_1_16_500] ERROR i.s.server.lock.AbstractLockManager - unLock error, xid *************:8091:5954436297517916181, branchId:5954436297517916183
io.seata.common.exception.StoreException: HikariPool-1 - Connection is not available, request timed out after 10012ms.
	at io.seata.server.storage.db.lock.LockStoreDataBaseDAO.unLock(LockStoreDataBaseDAO.java:276)
	at io.seata.server.storage.db.lock.DataBaseLocker.releaseLock(DataBaseLocker.java:92)
	at io.seata.server.storage.db.lock.DataBaseLockManager.releaseLock(DataBaseLockManager.java:55)
	at io.seata.server.session.BranchSession.unlock(BranchSession.java:295)
	at io.seata.server.session.GlobalSession.unlockBranch(GlobalSession.java:311)
	at io.seata.server.session.SessionHelper.removeBranch(SessionHelper.java:305)
	at io.seata.server.coordinator.DefaultCore.lambda$doGlobalRollback$3(DefaultCore.java:324)
	at io.seata.server.session.SessionHelper.forEach(SessionHelper.java:284)
	at io.seata.server.coordinator.DefaultCore.doGlobalRollback(DefaultCore.java:310)
	at io.seata.server.coordinator.DefaultCore.rollback(DefaultCore.java:297)
	at io.seata.server.coordinator.DefaultCoordinator.doGlobalRollback(DefaultCoordinator.java:272)
	at io.seata.server.AbstractTCInboundHandler$3.execute(AbstractTCInboundHandler.java:140)
	at io.seata.server.AbstractTCInboundHandler$3.execute(AbstractTCInboundHandler.java:135)
	at io.seata.core.exception.AbstractExceptionHandler.exceptionHandleTemplate(AbstractExceptionHandler.java:131)
	at io.seata.server.AbstractTCInboundHandler.handle(AbstractTCInboundHandler.java:135)
	at io.seata.core.protocol.transaction.GlobalRollbackRequest.handle(GlobalRollbackRequest.java:34)
	at io.seata.server.coordinator.DefaultCoordinator.onRequest(DefaultCoordinator.java:523)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.onRequestMessage(ServerOnRequestProcessor.java:206)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.process(ServerOnRequestProcessor.java:122)
	at io.seata.core.rpc.netty.AbstractNettyRemoting.lambda$processMessage$2(AbstractNettyRemoting.java:281)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10012ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.LockStoreDataBaseDAO.unLock(LockStoreDataBaseDAO.java:268)
	... 23 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 26 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 29 common frames omitted
2025-07-30 16:13:22 [ServerHandlerThread_1_16_500] ERROR i.s.server.coordinator.DefaultCore - Rollback branch transaction exception, xid = *************:8091:5954436297517916181 branchId = 5954436297517916183 exception = Unlock branch lock failed, xid = *************:8091:5954436297517916181, branchId = 5954436297517916183
2025-07-30 16:13:32 [ServerHandlerThread_1_16_500] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1781c7d (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 16:13:32 [ServerHandlerThread_1_16_500] ERROR i.s.c.e.AbstractExceptionHandler - Catch TransactionException while do RPC, request: GlobalRollbackRequest{xid='*************:8091:5954436297517916181', extraData='null'}
io.seata.core.exception.TransactionException: global rollback request failed. xid=*************:8091:5954436297517916181, msg=HikariPool-1 - Connection is not available, request timed out after 10010ms.
	at io.seata.server.AbstractTCInboundHandler$3.execute(AbstractTCInboundHandler.java:143)
	at io.seata.server.AbstractTCInboundHandler$3.execute(AbstractTCInboundHandler.java:135)
	at io.seata.core.exception.AbstractExceptionHandler.exceptionHandleTemplate(AbstractExceptionHandler.java:131)
	at io.seata.server.AbstractTCInboundHandler.handle(AbstractTCInboundHandler.java:135)
	at io.seata.core.protocol.transaction.GlobalRollbackRequest.handle(GlobalRollbackRequest.java:34)
	at io.seata.server.coordinator.DefaultCoordinator.onRequest(DefaultCoordinator.java:523)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.onRequestMessage(ServerOnRequestProcessor.java:206)
	at io.seata.core.rpc.processor.server.ServerOnRequestProcessor.process(ServerOnRequestProcessor.java:122)
	at io.seata.core.rpc.netty.AbstractNettyRemoting.lambda$processMessage$2(AbstractNettyRemoting.java:281)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: io.seata.common.exception.StoreException: HikariPool-1 - Connection is not available, request timed out after 10010ms.
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.updateGlobalTransactionDO(LogStoreDataBaseDAO.java:233)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.writeSession(DataBaseTransactionStoreManager.java:103)
	at io.seata.server.storage.db.session.DataBaseSessionManager.addGlobalSession(DataBaseSessionManager.java:87)
	at io.seata.server.session.GlobalSession.queueToRetryRollback(GlobalSession.java:766)
	at io.seata.server.coordinator.DefaultCore.lambda$doGlobalRollback$3(DefaultCore.java:343)
	at io.seata.server.session.SessionHelper.forEach(SessionHelper.java:284)
	at io.seata.server.coordinator.DefaultCore.doGlobalRollback(DefaultCore.java:310)
	at io.seata.server.coordinator.DefaultCore.rollback(DefaultCore.java:297)
	at io.seata.server.coordinator.DefaultCoordinator.doGlobalRollback(DefaultCoordinator.java:272)
	at io.seata.server.AbstractTCInboundHandler$3.execute(AbstractTCInboundHandler.java:140)
	... 12 common frames omitted
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10010ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.updateGlobalTransactionDO(LogStoreDataBaseDAO.java:226)
	... 21 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 24 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 27 common frames omitted
2025-07-30 16:13:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:5954436297517916181', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacking, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:42 [ServerHandlerThread_1_16_500] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@632a3322 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 16:13:42 [ServerHandlerThread_1_16_500] ERROR i.s.server.AbstractTCInboundHandler - check transaction status error,HikariPool-1 - Connection is not available, request timed out after 10029ms.]
2025-07-30 16:13:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacking, resultCode=Failed, msg='TransactionException[global rollback request failed. xid=*************:8091:5954436297517916181, msg=HikariPool-1 - Connection is not available, request timed out after 10010ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalStatusRequest{xid='*************:8091:5954436297517916181', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:13:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalStatusResponse{globalStatus=Rollbacking, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:14:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalStatusRequest{xid='*************:8091:5954436297517916181', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:14:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalStatusResponse{globalStatus=Rollbacking, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:14:14 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalStatusRequest{xid='*************:8091:5954436297517916181', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:14:14 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalStatusResponse{globalStatus=Rollbacking, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:14:18 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-30 16:14:18 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-30 16:14:18 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 16:14:18 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-30 16:14:18 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-30 16:14:27 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-30 16:14:27 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-30 16:14:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 16:14:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 16:14:28 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-30 16:14:28 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-30 16:14:28 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-30 16:14:28 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-30 16:14:28 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 23844 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 16:14:28 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-30 16:14:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-30 16:14:30 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-30 16:14:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 16:14:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 16:14:30 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 16:14:30 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1588 ms
2025-07-30 16:14:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 16:14:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 16:14:30 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-30 16:14:31 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-30 16:14:31 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@167f9043, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6771fc29, org.springframework.security.web.context.SecurityContextPersistenceFilter@7f839ec5, org.springframework.security.web.header.HeaderWriterFilter@52a0bc48, org.springframework.security.web.authentication.logout.LogoutFilter@7010c9e4, io.seata.console.filter.JwtAuthenticationTokenFilter@1f370472, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b859355, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@693ed09d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@50d91a0f, org.springframework.security.web.session.SessionManagementFilter@1239c268, org.springframework.security.web.access.ExceptionTranslationFilter@10f384a2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@11ad327f]
2025-07-30 16:14:31 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-30 16:14:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-30 16:14:31 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.904 seconds (JVM running for 6.823)
2025-07-30 16:14:31 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-30 16:14:31 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-30 16:14:31 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-30 16:14:31 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 16:14:31 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 16:14:32 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-30 16:14:32 [main] INFO  io.seata.server.ServerRunner - seata server started in 713 millSeconds
2025-07-30 16:14:33 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753863273655
timestamp=1753863273655
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xc9d0ddbd, L:/*************:8091 - R:/*************:3187],client version:1.7.1
2025-07-30 16:14:34 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x8702235f, L:/*************:8091 - R:/*************:3188],client version:1.7.1
2025-07-30 16:14:38 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753863278295
timestamp=1753863278295
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x847fb9b6, L:/*************:8091 - R:/*************:3197],client version:1.7.1
2025-07-30 16:14:38 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x5bb43b31, L:/*************:8091 - R:/*************:3199],client version:1.7.1
2025-07-30 16:14:38 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753863278539
timestamp=1753863278539
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x133429b0, L:/*************:8091 - R:/*************:3200],client version:1.7.1
2025-07-30 16:14:39 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xf272be96, L:/*************:8091 - R:/*************:3201],client version:1.7.1
2025-07-30 16:14:39 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753863279226
timestamp=1753863279226
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x5c7b240a, L:/*************:8091 - R:/*************:3202],client version:1.7.1
2025-07-30 16:14:39 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x2140373b, L:/*************:8091 - R:/*************:3203],client version:1.7.1
2025-07-30 16:15:01 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:02 [ServerHandlerThread_1_5_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:3747672483353514015
2025-07-30 16:15:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:3747672483353514015', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:02 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:02 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514018]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:02 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:02 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514020]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:02 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:03 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514022]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:03 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:03 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514026]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:03 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:03 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514028]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:03 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:03 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514030]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:04 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:04 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514032]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:04 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:04 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514036]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:04 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:04 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514038]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:04 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:04 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514040]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:05 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:05 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514044]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:05 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:05 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514046]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:05 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:05 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514048]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:05 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:05 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514050]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:06 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:06 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514054]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:06 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:06 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514056]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:06 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:06 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514058]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:06 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:06 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514060]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:07 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:07 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514063]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:07 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:07 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514066]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:07 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:07 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514068]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:07 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:07 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514070]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:08 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:08 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514072]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:08 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:08 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514076]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:08 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:08 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514078]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:08 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:08 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514080]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:09 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:09 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514082]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:09 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:09 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514086]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:09 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:09 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514088]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:09 [ForkJoinPool.commonPool-worker-1] INFO  i.s.s.s.db.lock.LockStoreDataBaseDAO - Global lock on [auditing_workorder_info:1950467131853787138] is holding by xid *************:8091:5954436297517916181 branchId 5954436297517916183
2025-07-30 16:15:09 [ForkJoinPool.commonPool-worker-1] ERROR i.s.c.e.AbstractExceptionHandler - this request cannot acquire global lock, you can let Seata retry by setting config [client.rm.lock.retryPolicyBranchRollbackOnConflict] = false or manually retry by yourself. request: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}
2025-07-30 16:15:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[Global lock acquire failed xid = *************:8091:3747672483353514015 branchId = 3747672483353514090]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950467131853787138', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:10 [ForkJoinPool.commonPool-worker-1] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:3747672483353514015, branchId = 3747672483353514092, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1950467131853787138
2025-07-30 16:15:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3747672483353514092, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1950467129584668673', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:10 [ForkJoinPool.commonPool-worker-1] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:3747672483353514015, branchId = 3747672483353514095, resourceId = ******************************************* ,lockKeys = usd_workorder:1950467129584668673
2025-07-30 16:15:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3747672483353514095, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1950470210405814274', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:11 [ForkJoinPool.commonPool-worker-1] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:3747672483353514015, branchId = 3747672483353514098, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1950470210405814274
2025-07-30 16:15:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3747672483353514098, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:3747672483353514015', branchType=AT, resourceId='******************************************', lockKey='todo_info:1950470212880519170', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-30 16:15:11 [ForkJoinPool.commonPool-worker-1] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:3747672483353514015, branchId = 3747672483353514100, resourceId = ****************************************** ,lockKeys = todo_info:1950470212880519170
2025-07-30 16:15:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=3747672483353514100, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-30 16:15:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:3747672483353514015', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:15:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-30 16:35:27 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-30 16:35:27 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 16:35:27 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-30 16:35:27 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-30 16:35:27 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-30 16:35:37 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-30 16:35:37 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-30 16:35:37 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 16:35:37 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 16:35:39 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-30 16:35:39 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-30 16:35:39 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-30 16:35:39 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-30 16:35:39 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 17760 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 16:35:39 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-30 16:35:40 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-30 16:35:40 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-30 16:35:40 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 16:35:40 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-30 16:35:40 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 16:35:40 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1631 ms
2025-07-30 16:35:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 16:35:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 16:35:41 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.css']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.js']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.html']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.map']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.svg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.png']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.jpeg']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/**/*.ico']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-30 16:35:41 [main] WARN  o.s.s.c.a.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='/api/v1/auth/login']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-30 16:35:41 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@626ff077, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3b850bb7, org.springframework.security.web.context.SecurityContextPersistenceFilter@52a0bc48, org.springframework.security.web.header.HeaderWriterFilter@68fc1e7f, org.springframework.security.web.authentication.logout.LogoutFilter@2396408a, io.seata.console.filter.JwtAuthenticationTokenFilter@6802c10e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@24ce5d4c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6dbdbb69, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@341889a1, org.springframework.security.web.session.SessionManagementFilter@8de4206, org.springframework.security.web.access.ExceptionTranslationFilter@590336ed, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@39a09c0]
2025-07-30 16:35:41 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-30 16:35:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-30 16:35:41 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 5.576 seconds (JVM running for 8.654)
2025-07-30 16:35:42 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-30 16:35:42 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-30 16:35:42 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-30 16:35:42 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 16:35:42 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 16:35:42 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-30 16:35:42 [main] INFO  io.seata.server.ServerRunner - seata server started in 695 millSeconds
2025-07-30 16:35:43 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753864543665
timestamp=1753864543665
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xccd55a74, L:/*************:8091 - R:/*************:6498],client version:1.7.1
2025-07-30 16:35:44 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xfb4e583e, L:/*************:8091 - R:/*************:6500],client version:1.7.1
2025-07-30 16:35:48 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753864548295
timestamp=1753864548295
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x425fd493, L:/*************:8091 - R:/*************:6507],client version:1.7.1
2025-07-30 16:35:48 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x1adee855, L:/*************:8091 - R:/*************:6508],client version:1.7.1
2025-07-30 16:35:48 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753864548537
timestamp=1753864548537
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x99e42d4f, L:/*************:8091 - R:/*************:6509],client version:1.7.1
2025-07-30 16:35:49 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x58f4bdf5, L:/*************:8091 - R:/*************:6510],client version:1.7.1
2025-07-30 16:35:49 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753864549226
timestamp=1753864549226
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xd9617c25, L:/*************:8091 - R:/*************:6511],client version:1.7.1
2025-07-30 16:35:49 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xe83bffe6, L:/*************:8091 - R:/*************:6513],client version:1.7.1
2025-07-30 17:05:44 [AsyncCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@54651b96 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 17:05:44 [AsyncCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: AsyncCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10056ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$7(DefaultCoordinator.java:503)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 14 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 17 common frames omitted
2025-07-30 17:34:44 [RetryCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@52eb357c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 17:34:44 [RetryCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10019ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$6(DefaultCoordinator.java:499)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 14 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 17 common frames omitted
2025-07-30 18:04:10 [TxTimeoutCheck_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@68b7a4c2 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 18:04:10 [TxTimeoutCheck_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: TxTimeoutCheck
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10020ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 14 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 17 common frames omitted
2025-07-30 18:04:13 [RetryCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@65a82098 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 18:04:13 [RetryCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10016ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$6(DefaultCoordinator.java:499)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 14 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 17 common frames omitted
2025-07-30 18:33:43 [TxTimeoutCheck_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@3e5af050 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 18:33:43 [TxTimeoutCheck_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: TxTimeoutCheck
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10004ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$8(DefaultCoordinator.java:507)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 14 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 17 common frames omitted
2025-07-30 19:03:03 [RetryRollbacking_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6cc9e68b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 19:03:04 [RetryRollbacking_1_1] ERROR i.seata.server.session.SessionHolder - Exception running function with key = RetryRollbacking
io.seata.common.exception.DataAccessException: HikariPool-1 - Connection is not available, request timed out after 10022ms.
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:182)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.readSession(DataBaseTransactionStoreManager.java:187)
	at io.seata.server.storage.db.store.DataBaseTransactionStoreManager.readSession(DataBaseTransactionStoreManager.java:222)
	at io.seata.server.storage.db.session.DataBaseSessionManager.findGlobalSessions(DataBaseSessionManager.java:185)
	at io.seata.server.coordinator.DefaultCoordinator.handleRetryRollbacking(DefaultCoordinator.java:370)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:400)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$5(DefaultCoordinator.java:495)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10022ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.store.LogStoreDataBaseDAO.queryGlobalTransactionDO(LogStoreDataBaseDAO.java:164)
	... 14 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 17 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 20 common frames omitted
2025-07-30 19:03:14 [RetryRollbacking_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@68c531c0 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 19:03:14 [RetryRollbacking_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute release lock failure, key is: RetryRollbacking
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10032ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.releaseLock(DataBaseDistributedLocker.java:182)
	at io.seata.server.session.SessionHolder.releaseDistributedLock(SessionHolder.java:386)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:407)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$5(DefaultCoordinator.java:495)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 14 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 17 common frames omitted
2025-07-30 19:03:15 [AsyncCommitting_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@26d141c9 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 19:03:15 [AsyncCommitting_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute release lock failure, key is: AsyncCommitting
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10019ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.releaseLock(DataBaseDistributedLocker.java:182)
	at io.seata.server.session.SessionHolder.releaseDistributedLock(SessionHolder.java:386)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:407)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$7(DefaultCoordinator.java:503)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 14 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 17 common frames omitted
2025-07-30 19:03:24 [RetryRollbacking_1_1] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7f4e5171 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-07-30 19:03:24 [RetryRollbacking_1_1] ERROR i.s.s.s.d.l.DataBaseDistributedLocker - execute acquire lock failure, key is: RetryRollbacking
java.sql.SQLTransientConnectionException: HikariPool-1 - Connection is not available, request timed out after 10019ms.
	at com.zaxxer.hikari.pool.HikariPool.createTimeoutException(HikariPool.java:696)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:197)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:100)
	at io.seata.server.storage.db.lock.DataBaseDistributedLocker.acquireLock(DataBaseDistributedLocker.java:127)
	at io.seata.server.session.SessionHolder.acquireDistributedLock(SessionHolder.java:377)
	at io.seata.server.session.SessionHolder.distributedLockAndExecute(SessionHolder.java:399)
	at io.seata.server.coordinator.DefaultCoordinator.lambda$init$5(DefaultCoordinator.java:495)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLNonTransientConnectionException: No operations allowed after connection closed.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:89)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:63)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:73)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:73)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2488)
	at com.zaxxer.hikari.pool.PoolBase.setNetworkTimeout(PoolBase.java:566)
	at com.zaxxer.hikari.pool.PoolBase.isConnectionAlive(PoolBase.java:173)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:186)
	... 14 common frames omitted
Caused by: com.mysql.cj.exceptions.ConnectionIsClosedException: No operations allowed after connection closed.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:499)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:480)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.NativeSession.checkClosed(NativeSession.java:762)
	at com.mysql.cj.jdbc.ConnectionImpl.checkClosed(ConnectionImpl.java:569)
	at com.mysql.cj.jdbc.ConnectionImpl.setNetworkTimeout(ConnectionImpl.java:2484)
	... 17 common frames omitted
2025-07-30 19:16:34 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 19:16:34 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-30 19:16:34 [Thread-8] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-30 19:16:34 [Thread-6] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-30 19:16:34 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-30 19:16:35 [SpringApplicationShutdownHook] ERROR i.s.c.rpc.netty.NettyServerBootstrap - shutdown execute error: Client not connected, current status:UNHEALTHY
com.alibaba.nacos.api.exception.NacosException: Client not connected, current status:UNHEALTHY
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:644)
	at com.alibaba.nacos.common.remote.client.RpcClient.request(RpcClient.java:623)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.requestToServer(NamingGrpcClientProxy.java:447)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.doDeregisterService(NamingGrpcClientProxy.java:308)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterServiceForEphemeral(NamingGrpcClientProxy.java:293)
	at com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcClientProxy.deregisterService(NamingGrpcClientProxy.java:275)
	at com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate.deregisterService(NamingClientProxyDelegate.java:122)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:204)
	at com.alibaba.nacos.client.naming.NacosNamingService.deregisterInstance(NacosNamingService.java:192)
	at io.seata.discovery.registry.nacos.NacosRegistryServiceImpl.unregister(NacosRegistryServiceImpl.java:120)
	at io.seata.core.rpc.netty.NettyServerBootstrap.shutdown(NettyServerBootstrap.java:192)
	at io.seata.core.rpc.netty.AbstractNettyRemotingServer.destroy(AbstractNettyRemotingServer.java:127)
	at io.seata.core.rpc.netty.NettyRemotingServer.destroy(NettyRemotingServer.java:128)
	at io.seata.server.coordinator.DefaultCoordinator.destroy(DefaultCoordinator.java:559)
	at io.seata.server.ServerRunner.destroy(ServerRunner.java:86)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:213)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1163)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1156)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1120)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1086)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:174)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:1032)
	at org.springframework.boot.SpringApplicationShutdownHook.closeAndWait(SpringApplicationShutdownHook.java:145)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationShutdownHook.run(SpringApplicationShutdownHook.java:114)
	at java.base/java.lang.Thread.run(Thread.java:842)
