2025-07-24 08:32:02 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-24 08:32:02 [main] INFO  i.s.config.nacos.NacosConfiguration - <PERSON><PERSON> check auth with userName/password.
2025-07-24 08:32:02 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-24 08:32:02 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-24 08:32:03 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-24 08:32:03 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-24 08:32:03 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-24 08:32:03 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-24 08:32:03 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 24048 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-24 08:32:03 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-24 08:32:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-24 08:32:05 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-24 08:32:05 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 08:32:05 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-24 08:32:05 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 08:32:05 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1808 ms
2025-07-24 08:32:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 08:32:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 08:32:05 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-24 08:32:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5a5c2889, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7668f8fd, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c205259, org.springframework.security.web.header.HeaderWriterFilter@3b11deb6, org.springframework.security.web.authentication.logout.LogoutFilter@572db5ee, io.seata.console.filter.JwtAuthenticationTokenFilter@75566f4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@20923380, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1640a6b5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6785786d, org.springframework.security.web.session.SessionManagementFilter@f0d01c9, org.springframework.security.web.access.ExceptionTranslationFilter@4678ec43, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@31a80c88]
2025-07-24 08:32:06 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-24 08:32:06 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-24 08:32:06 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 5.046 seconds (JVM running for 6.188)
2025-07-24 08:32:06 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-24 08:32:06 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-24 08:32:07 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-24 08:32:07 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-24 08:32:07 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-24 08:32:07 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-24 08:32:07 [main] INFO  io.seata.server.ServerRunner - seata server started in 880 millSeconds
2025-07-24 08:32:25 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753317144896
timestamp=1753317144896
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x9bfe623a, L:/*************:8091 - R:/*************:1996],client version:1.7.1
2025-07-24 08:32:28 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x92ce9da3, L:/*************:8091 - R:/*************:2002],client version:1.7.1
2025-07-24 08:32:31 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753317151043
timestamp=1753317151043
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x2cfb25e4, L:/*************:8091 - R:/*************:2014],client version:1.7.1
2025-07-24 08:32:49 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753317169796
timestamp=1753317169796
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x28d500ae, L:/*************:8091 - R:/*************:2084],client version:1.7.1
2025-07-24 08:32:52 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xc454f925, L:/*************:8091 - R:/*************:2097],client version:1.7.1
2025-07-24 08:33:00 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753317180476
timestamp=1753317180476
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xd058a132, L:/*************:8091 - R:/*************:2123],client version:1.7.1
2025-07-24 08:33:00 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xe33839f9, L:/*************:8091 - R:/*************:2127],client version:1.7.1
2025-07-24 08:33:31 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xaad9d9bd, L:/*************:8091 - R:/*************:2221],client version:1.7.1
2025-07-24 08:41:43 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1996 to server channel inactive.
2025-07-24 08:41:43 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2002 to server channel inactive.
2025-07-24 08:41:43 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9bfe623a, L:/*************:8091 ! R:/*************:1996]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1996', channel=[id: 0x9bfe623a, L:/*************:8091 ! R:/*************:1996], resourceSets=null}
2025-07-24 08:41:43 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x92ce9da3, L:/*************:8091 ! R:/*************:2002]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2002', channel=[id: 0x92ce9da3, L:/*************:8091 ! R:/*************:2002], resourceSets=[]}
2025-07-24 08:42:45 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753317765176
timestamp=1753317765176
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xc873c803, L:/*************:8091 - R:/*************:4064],client version:1.7.1
2025-07-24 08:42:47 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x1503118d, L:/*************:8091 - R:/*************:4074],client version:1.7.1
2025-07-24 08:52:33 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4064 to server channel inactive.
2025-07-24 08:52:33 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc873c803, L:/*************:8091 ! R:/*************:4064]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4064', channel=[id: 0xc873c803, L:/*************:8091 ! R:/*************:4064], resourceSets=null}
2025-07-24 08:52:33 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4074 to server channel inactive.
2025-07-24 08:52:33 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1503118d, L:/*************:8091 ! R:/*************:4074]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4074', channel=[id: 0x1503118d, L:/*************:8091 ! R:/*************:4074], resourceSets=[]}
2025-07-24 08:52:46 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753318366680
timestamp=1753318366680
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x5ce10639, L:/*************:8091 - R:/*************:4999],client version:1.7.1
2025-07-24 08:52:50 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x0d98e141, L:/*************:8091 - R:/*************:5016],client version:1.7.1
2025-07-24 08:59:09 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4999 to server channel inactive.
2025-07-24 08:59:09 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5ce10639, L:/*************:8091 ! R:/*************:4999]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4999', channel=[id: 0x5ce10639, L:/*************:8091 ! R:/*************:4999], resourceSets=null}
2025-07-24 08:59:09 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5016 to server channel inactive.
2025-07-24 08:59:09 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0d98e141, L:/*************:8091 ! R:/*************:5016]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5016', channel=[id: 0x0d98e141, L:/*************:8091 ! R:/*************:5016], resourceSets=[]}
2025-07-24 08:59:20 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753318760123
timestamp=1753318760123
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x70a2ddc8, L:/*************:8091 - R:/*************:5658],client version:1.7.1
2025-07-24 08:59:23 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x2cc37164, L:/*************:8091 - R:/*************:5667],client version:1.7.1
2025-07-24 09:01:15 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5658 to server channel inactive.
2025-07-24 09:01:15 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x70a2ddc8, L:/*************:8091 ! R:/*************:5658]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5658', channel=[id: 0x70a2ddc8, L:/*************:8091 ! R:/*************:5658], resourceSets=null}
2025-07-24 09:01:15 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5667 to server channel inactive.
2025-07-24 09:01:15 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2cc37164, L:/*************:8091 ! R:/*************:5667]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5667', channel=[id: 0x2cc37164, L:/*************:8091 ! R:/*************:5667], resourceSets=[]}
2025-07-24 09:01:25 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753318885750
timestamp=1753318885750
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x68f5cfb4, L:/*************:8091 - R:/*************:5937],client version:1.7.1
2025-07-24 09:01:29 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xe7c70646, L:/*************:8091 - R:/*************:5948],client version:1.7.1
2025-07-24 09:02:36 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5937 to server channel inactive.
2025-07-24 09:02:36 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x68f5cfb4, L:/*************:8091 ! R:/*************:5937]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5937', channel=[id: 0x68f5cfb4, L:/*************:8091 ! R:/*************:5937], resourceSets=null}
2025-07-24 09:02:36 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5948 to server channel inactive.
2025-07-24 09:02:36 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe7c70646, L:/*************:8091 ! R:/*************:5948]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5948', channel=[id: 0xe7c70646, L:/*************:8091 ! R:/*************:5948], resourceSets=[]}
2025-07-24 09:05:24 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753319124850
timestamp=1753319124850
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x185b9ed0, L:/*************:8091 - R:/*************:6351],client version:1.7.1
2025-07-24 09:05:27 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x8c860161, L:/*************:8091 - R:/*************:6357],client version:1.7.1
2025-07-24 09:11:39 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6351 to server channel inactive.
2025-07-24 09:11:39 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x185b9ed0, L:/*************:8091 ! R:/*************:6351]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6351', channel=[id: 0x185b9ed0, L:/*************:8091 ! R:/*************:6351], resourceSets=null}
2025-07-24 09:11:39 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6357 to server channel inactive.
2025-07-24 09:11:39 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8c860161, L:/*************:8091 ! R:/*************:6357]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6357', channel=[id: 0x8c860161, L:/*************:8091 ! R:/*************:6357], resourceSets=[]}
2025-07-24 09:11:53 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753319513674
timestamp=1753319513674
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xe2e83cd6, L:/*************:8091 - R:/*************:6974],client version:1.7.1
2025-07-24 09:11:57 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x7be54399, L:/*************:8091 - R:/*************:6982],client version:1.7.1
2025-07-24 09:13:33 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6974 to server channel inactive.
2025-07-24 09:13:33 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe2e83cd6, L:/*************:8091 ! R:/*************:6974]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6974', channel=[id: 0xe2e83cd6, L:/*************:8091 ! R:/*************:6974], resourceSets=null}
2025-07-24 09:13:33 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6982 to server channel inactive.
2025-07-24 09:13:33 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7be54399, L:/*************:8091 ! R:/*************:6982]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6982', channel=[id: 0x7be54399, L:/*************:8091 ! R:/*************:6982], resourceSets=[]}
2025-07-24 09:13:43 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753319623069
timestamp=1753319623069
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x433d679d, L:/*************:8091 - R:/*************:7189],client version:1.7.1
2025-07-24 09:13:46 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x7ce09c84, L:/*************:8091 - R:/*************:7197],client version:1.7.1
2025-07-24 09:15:35 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7189 to server channel inactive.
2025-07-24 09:15:35 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x433d679d, L:/*************:8091 ! R:/*************:7189]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7189', channel=[id: 0x433d679d, L:/*************:8091 ! R:/*************:7189], resourceSets=null}
2025-07-24 09:15:35 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7197 to server channel inactive.
2025-07-24 09:15:35 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7ce09c84, L:/*************:8091 ! R:/*************:7197]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7197', channel=[id: 0x7ce09c84, L:/*************:8091 ! R:/*************:7197], resourceSets=[]}
2025-07-24 09:15:45 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753319745507
timestamp=1753319745507
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xdc29f5d0, L:/*************:8091 - R:/*************:7392],client version:1.7.1
2025-07-24 09:15:49 [ServerHandlerThread_1_12_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xbd06b5c2, L:/*************:8091 - R:/*************:7400],client version:1.7.1
2025-07-24 09:17:46 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7392 to server channel inactive.
2025-07-24 09:17:46 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdc29f5d0, L:/*************:8091 ! R:/*************:7392]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7392', channel=[id: 0xdc29f5d0, L:/*************:8091 ! R:/*************:7392], resourceSets=null}
2025-07-24 09:17:46 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7400 to server channel inactive.
2025-07-24 09:17:46 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbd06b5c2, L:/*************:8091 ! R:/*************:7400]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7400', channel=[id: 0xbd06b5c2, L:/*************:8091 ! R:/*************:7400], resourceSets=[]}
2025-07-24 09:17:56 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753319876656
timestamp=1753319876656
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x619e3921, L:/*************:8091 - R:/*************:7714],client version:1.7.1
2025-07-24 09:18:00 [ServerHandlerThread_1_13_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x09daf9f8, L:/*************:8091 - R:/*************:7720],client version:1.7.1
2025-07-24 09:20:37 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7714 to server channel inactive.
2025-07-24 09:20:37 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7720 to server channel inactive.
2025-07-24 09:20:37 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x09daf9f8, L:/*************:8091 ! R:/*************:7720]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7720', channel=[id: 0x09daf9f8, L:/*************:8091 ! R:/*************:7720], resourceSets=[]}
2025-07-24 09:20:37 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x619e3921, L:/*************:8091 ! R:/*************:7714]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7714', channel=[id: 0x619e3921, L:/*************:8091 ! R:/*************:7714], resourceSets=null}
2025-07-24 09:20:51 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753320051189
timestamp=1753320051189
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xed0f5209, L:/*************:8091 - R:/*************:8008],client version:1.7.1
2025-07-24 09:20:55 [ServerHandlerThread_1_14_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xaac1abf3, L:/*************:8091 - R:/*************:8016],client version:1.7.1
2025-07-24 09:22:00 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8008 to server channel inactive.
2025-07-24 09:22:00 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xed0f5209, L:/*************:8091 ! R:/*************:8008]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8008', channel=[id: 0xed0f5209, L:/*************:8091 ! R:/*************:8008], resourceSets=null}
2025-07-24 09:22:00 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8016 to server channel inactive.
2025-07-24 09:22:00 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xaac1abf3, L:/*************:8091 ! R:/*************:8016]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8016', channel=[id: 0xaac1abf3, L:/*************:8091 ! R:/*************:8016], resourceSets=[]}
2025-07-24 09:23:42 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753320222024
timestamp=1753320222024
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xf61bec27, L:/*************:8091 - R:/*************:8394],client version:1.7.1
2025-07-24 09:23:44 [ServerHandlerThread_1_15_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x7b1c5297, L:/*************:8091 - R:/*************:8399],client version:1.7.1
2025-07-24 09:28:06 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8394 to server channel inactive.
2025-07-24 09:28:06 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf61bec27, L:/*************:8091 ! R:/*************:8394]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8394', channel=[id: 0xf61bec27, L:/*************:8091 ! R:/*************:8394], resourceSets=null}
2025-07-24 09:28:06 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8399 to server channel inactive.
2025-07-24 09:28:06 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7b1c5297, L:/*************:8091 ! R:/*************:8399]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8399', channel=[id: 0x7b1c5297, L:/*************:8091 ! R:/*************:8399], resourceSets=[]}
2025-07-24 09:28:16 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753320496708
timestamp=1753320496708
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x8da97ca7, L:/*************:8091 - R:/*************:8854],client version:1.7.1
2025-07-24 09:28:20 [ServerHandlerThread_1_16_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x71035290, L:/*************:8091 - R:/*************:8874],client version:1.7.1
2025-07-24 09:30:49 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8854 to server channel inactive.
2025-07-24 09:30:49 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8874 to server channel inactive.
2025-07-24 09:30:49 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8da97ca7, L:/*************:8091 ! R:/*************:8854]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8854', channel=[id: 0x8da97ca7, L:/*************:8091 ! R:/*************:8854], resourceSets=null}
2025-07-24 09:30:49 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x71035290, L:/*************:8091 ! R:/*************:8874]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8874', channel=[id: 0x71035290, L:/*************:8091 ! R:/*************:8874], resourceSets=[]}
2025-07-24 09:31:00 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753320660259
timestamp=1753320660259
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x227f1f5d, L:/*************:8091 - R:/*************:9153],client version:1.7.1
2025-07-24 09:31:03 [ServerHandlerThread_1_17_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x5b06b5cb, L:/*************:8091 - R:/*************:9160],client version:1.7.1
2025-07-24 09:34:07 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9153 to server channel inactive.
2025-07-24 09:34:07 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x227f1f5d, L:/*************:8091 ! R:/*************:9153]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9153', channel=[id: 0x227f1f5d, L:/*************:8091 ! R:/*************:9153], resourceSets=null}
2025-07-24 09:34:07 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9160 to server channel inactive.
2025-07-24 09:34:07 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5b06b5cb, L:/*************:8091 ! R:/*************:9160]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9160', channel=[id: 0x5b06b5cb, L:/*************:8091 ! R:/*************:9160], resourceSets=[]}
2025-07-24 09:36:44 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753321004331
timestamp=1753321004331
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x0f0933c4, L:/*************:8091 - R:/*************:9611],client version:1.7.1
2025-07-24 09:36:48 [ServerHandlerThread_1_18_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x647f22b0, L:/*************:8091 - R:/*************:9620],client version:1.7.1
2025-07-24 09:39:32 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9611 to server channel inactive.
2025-07-24 09:39:32 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0f0933c4, L:/*************:8091 ! R:/*************:9611]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9611', channel=[id: 0x0f0933c4, L:/*************:8091 ! R:/*************:9611], resourceSets=null}
2025-07-24 09:39:32 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9620 to server channel inactive.
2025-07-24 09:39:32 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x647f22b0, L:/*************:8091 ! R:/*************:9620]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9620', channel=[id: 0x647f22b0, L:/*************:8091 ! R:/*************:9620], resourceSets=[]}
2025-07-24 09:39:44 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753321183795
timestamp=1753321183795
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x3ea8cfb4, L:/*************:8091 - R:/*************:9868],client version:1.7.1
2025-07-24 09:39:47 [ServerHandlerThread_1_19_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x526a0968, L:/*************:8091 - R:/*************:9882],client version:1.7.1
2025-07-24 09:40:40 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9868 to server channel inactive.
2025-07-24 09:40:40 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3ea8cfb4, L:/*************:8091 ! R:/*************:9868]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9868', channel=[id: 0x3ea8cfb4, L:/*************:8091 ! R:/*************:9868], resourceSets=null}
2025-07-24 09:40:40 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:9882 to server channel inactive.
2025-07-24 09:40:40 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x526a0968, L:/*************:8091 ! R:/*************:9882]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:9882', channel=[id: 0x526a0968, L:/*************:8091 ! R:/*************:9882], resourceSets=[]}
2025-07-24 09:40:54 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753321253799
timestamp=1753321253799
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x2f6e2e37, L:/*************:8091 - R:/*************:10075],client version:1.7.1
2025-07-24 09:40:57 [ServerHandlerThread_1_20_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xd4de64aa, L:/*************:8091 - R:/*************:10087],client version:1.7.1
2025-07-24 09:44:20 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10075 to server channel inactive.
2025-07-24 09:44:20 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2f6e2e37, L:/*************:8091 ! R:/*************:10075]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10075', channel=[id: 0x2f6e2e37, L:/*************:8091 ! R:/*************:10075], resourceSets=null}
2025-07-24 09:44:20 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10087 to server channel inactive.
2025-07-24 09:44:20 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd4de64aa, L:/*************:8091 ! R:/*************:10087]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10087', channel=[id: 0xd4de64aa, L:/*************:8091 ! R:/*************:10087], resourceSets=[]}
2025-07-24 09:44:30 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753321470122
timestamp=1753321470122
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xc4ac83d0, L:/*************:8091 - R:/*************:10444],client version:1.7.1
2025-07-24 09:44:34 [ServerHandlerThread_1_21_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x941247b4, L:/*************:8091 - R:/*************:10454],client version:1.7.1
2025-07-24 09:46:59 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10444 to server channel inactive.
2025-07-24 09:46:59 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc4ac83d0, L:/*************:8091 ! R:/*************:10444]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10444', channel=[id: 0xc4ac83d0, L:/*************:8091 ! R:/*************:10444], resourceSets=null}
2025-07-24 09:46:59 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10454 to server channel inactive.
2025-07-24 09:46:59 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x941247b4, L:/*************:8091 ! R:/*************:10454]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10454', channel=[id: 0x941247b4, L:/*************:8091 ! R:/*************:10454], resourceSets=[]}
2025-07-24 09:47:10 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753321629919
timestamp=1753321629919
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xdcdc5256, L:/*************:8091 - R:/*************:10812],client version:1.7.1
2025-07-24 09:47:13 [ServerHandlerThread_1_22_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x087e14d5, L:/*************:8091 - R:/*************:10817],client version:1.7.1
2025-07-24 09:48:01 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10812 to server channel inactive.
2025-07-24 09:48:01 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdcdc5256, L:/*************:8091 ! R:/*************:10812]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10812', channel=[id: 0xdcdc5256, L:/*************:8091 ! R:/*************:10812], resourceSets=null}
2025-07-24 09:48:01 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10817 to server channel inactive.
2025-07-24 09:48:01 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x087e14d5, L:/*************:8091 ! R:/*************:10817]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10817', channel=[id: 0x087e14d5, L:/*************:8091 ! R:/*************:10817], resourceSets=[]}
2025-07-24 09:48:10 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753321690728
timestamp=1753321690728
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x04ac9567, L:/*************:8091 - R:/*************:10940],client version:1.7.1
2025-07-24 09:48:14 [ServerHandlerThread_1_23_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x80df9101, L:/*************:8091 - R:/*************:10944],client version:1.7.1
2025-07-24 10:24:34 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10940 to server channel inactive.
2025-07-24 10:24:34 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10944 to server channel inactive.
2025-07-24 10:24:34 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x04ac9567, L:/*************:8091 ! R:/*************:10940]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10940', channel=[id: 0x04ac9567, L:/*************:8091 ! R:/*************:10940], resourceSets=null}
2025-07-24 10:24:34 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x80df9101, L:/*************:8091 ! R:/*************:10944]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10944', channel=[id: 0x80df9101, L:/*************:8091 ! R:/*************:10944], resourceSets=[]}
2025-07-24 10:35:56 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2084 to server channel inactive.
2025-07-24 10:35:56 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2097 to server channel inactive.
2025-07-24 10:35:56 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x28d500ae, L:/*************:8091 ! R:/*************:2084]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2084', channel=[id: 0x28d500ae, L:/*************:8091 ! R:/*************:2084], resourceSets=null}
2025-07-24 10:35:56 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc454f925, L:/*************:8091 ! R:/*************:2097]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2097', channel=[id: 0xc454f925, L:/*************:8091 ! R:/*************:2097], resourceSets=[]}
2025-07-24 10:36:10 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753324569881
timestamp=1753324569881
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x474d5fc6, L:/*************:8091 - R:/*************:2989],client version:1.7.1
2025-07-24 10:36:14 [ServerHandlerThread_1_24_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x3d00d0ae, L:/*************:8091 - R:/*************:2994],client version:1.7.1
2025-07-24 10:53:18 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753325598602
timestamp=1753325598602
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xe395aa47, L:/*************:8091 - R:/*************:4768],client version:1.7.1
2025-07-24 10:53:22 [ServerHandlerThread_1_25_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x50e845f8, L:/*************:8091 - R:/*************:4779],client version:1.7.1
2025-07-24 11:04:20 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4768 to server channel inactive.
2025-07-24 11:04:20 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4779 to server channel inactive.
2025-07-24 11:04:20 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe395aa47, L:/*************:8091 ! R:/*************:4768]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4768', channel=[id: 0xe395aa47, L:/*************:8091 ! R:/*************:4768], resourceSets=null}
2025-07-24 11:04:20 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x50e845f8, L:/*************:8091 ! R:/*************:4779]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4779', channel=[id: 0x50e845f8, L:/*************:8091 ! R:/*************:4779], resourceSets=[]}
2025-07-24 13:27:36 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xe33839f9, L:/*************:8091 - R:/*************:2127] read idle.
2025-07-24 13:27:36 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xd058a132, L:/*************:8091 - R:/*************:2123] read idle.
2025-07-24 13:27:36 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2123 to server channel inactive.
2025-07-24 13:27:36 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd058a132, L:/*************:8091 - R:/*************:2123]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:2123', channel=[id: 0xd058a132, L:/*************:8091 - R:/*************:2123], resourceSets=null}
2025-07-24 13:27:36 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xd058a132, L:/*************:8091 - R:/*************:2123]
2025-07-24 13:27:36 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2123 to server channel inactive.
2025-07-24 13:27:36 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd058a132, L:/*************:8091 ! R:/*************:2123]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:2123', channel=[id: 0xd058a132, L:/*************:8091 ! R:/*************:2123], resourceSets=null}
2025-07-24 13:27:36 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2127 to server channel inactive.
2025-07-24 13:27:36 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe33839f9, L:/*************:8091 - R:/*************:2127]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:2127', channel=[id: 0xe33839f9, L:/*************:8091 - R:/*************:2127], resourceSets=[]}
2025-07-24 13:27:36 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xe33839f9, L:/*************:8091 - R:/*************:2127]
2025-07-24 13:27:36 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2127 to server channel inactive.
2025-07-24 13:27:36 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe33839f9, L:/*************:8091 ! R:/*************:2127]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:2127', channel=[id: 0xe33839f9, L:/*************:8091 ! R:/*************:2127], resourceSets=[]}
2025-07-24 13:27:36 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xaad9d9bd, L:/*************:8091 - R:/*************:2221] read idle.
2025-07-24 13:27:36 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2221 to server channel inactive.
2025-07-24 13:27:36 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xaad9d9bd, L:/*************:8091 - R:/*************:2221]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2221', channel=[id: 0xaad9d9bd, L:/*************:8091 - R:/*************:2221], resourceSets=null}
2025-07-24 13:27:36 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x2cfb25e4, L:/*************:8091 - R:/*************:2014] read idle.
2025-07-24 13:27:37 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xaad9d9bd, L:/*************:8091 - R:/*************:2221]
2025-07-24 13:27:37 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2014 to server channel inactive.
2025-07-24 13:27:37 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2cfb25e4, L:/*************:8091 - R:/*************:2014]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2014', channel=[id: 0x2cfb25e4, L:/*************:8091 - R:/*************:2014], resourceSets=null}
2025-07-24 13:27:37 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x2cfb25e4, L:/*************:8091 - R:/*************:2014]
2025-07-24 13:27:37 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2221 to server channel inactive.
2025-07-24 13:27:37 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xaad9d9bd, L:/*************:8091 ! R:/*************:2221]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2221', channel=[id: 0xaad9d9bd, L:/*************:8091 ! R:/*************:2221], resourceSets=null}
2025-07-24 13:27:37 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2014 to server channel inactive.
2025-07-24 13:27:37 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2cfb25e4, L:/*************:8091 ! R:/*************:2014]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2014', channel=[id: 0x2cfb25e4, L:/*************:8091 ! R:/*************:2014], resourceSets=null}
2025-07-24 13:27:37 [ServerHandlerThread_1_26_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x32651c2f, L:/*************:8091 - R:/*************:11939],client version:1.7.1
2025-07-24 13:27:37 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753334857453
timestamp=1753334857453
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x277cca4c, L:/*************:8091 - R:/*************:12066],client version:1.7.1
2025-07-24 13:27:37 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753334857681
timestamp=1753334857681
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x6ab158fd, L:/*************:8091 - R:/*************:12196],client version:1.7.1
2025-07-24 13:27:38 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x3d00d0ae, L:/*************:8091 - R:/*************:2994] read idle.
2025-07-24 13:27:38 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2994 to server channel inactive.
2025-07-24 13:27:38 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3d00d0ae, L:/*************:8091 - R:/*************:2994]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2994', channel=[id: 0x3d00d0ae, L:/*************:8091 - R:/*************:2994], resourceSets=[]}
2025-07-24 13:27:38 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x3d00d0ae, L:/*************:8091 - R:/*************:2994]
2025-07-24 13:27:38 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2994 to server channel inactive.
2025-07-24 13:27:38 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3d00d0ae, L:/*************:8091 ! R:/*************:2994]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2994', channel=[id: 0x3d00d0ae, L:/*************:8091 ! R:/*************:2994], resourceSets=[]}
2025-07-24 13:27:38 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2989 to server channel inactive.
2025-07-24 13:27:38 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x474d5fc6, L:/*************:8091 ! R:/*************:2989]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2989', channel=[id: 0x474d5fc6, L:/*************:8091 ! R:/*************:2989], resourceSets=null}
2025-07-24 13:27:41 [ServerHandlerThread_1_27_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x7e702f9e, L:/*************:8091 - R:/*************:12408],client version:1.7.1
2025-07-24 13:27:46 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753334866651
timestamp=1753334866651
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x5322c44f, L:/*************:8091 - R:/*************:12478],client version:1.7.1
2025-07-24 13:27:47 [ServerHandlerThread_1_28_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xe99a18d5, L:/*************:8091 - R:/*************:12485],client version:1.7.1
2025-07-24 14:10:22 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-24 14:10:29 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-24 14:10:29 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-24 14:10:29 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-24 14:10:29 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-24 14:10:30 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-24 14:10:30 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-24 14:10:30 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-24 14:10:30 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-24 14:10:30 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 16352 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-24 14:10:30 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-24 14:10:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-24 14:10:31 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-24 14:10:31 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 14:10:31 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-24 14:10:32 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 14:10:32 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1376 ms
2025-07-24 14:10:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 14:10:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 14:10:32 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-24 14:10:32 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1f370472, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@50d91a0f, org.springframework.security.web.context.SecurityContextPersistenceFilter@b859355, org.springframework.security.web.header.HeaderWriterFilter@24ce5d4c, org.springframework.security.web.authentication.logout.LogoutFilter@10f60e36, io.seata.console.filter.JwtAuthenticationTokenFilter@3ace65df, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7c3223aa, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c26e588, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@52b6561b, org.springframework.security.web.session.SessionManagementFilter@2d97344c, org.springframework.security.web.access.ExceptionTranslationFilter@52a0bc48, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@167f9043]
2025-07-24 14:10:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-24 14:10:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-24 14:10:32 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.051 seconds (JVM running for 5.582)
2025-07-24 14:10:33 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-24 14:10:33 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-24 14:10:33 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-24 14:10:33 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-24 14:10:33 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-24 14:10:33 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-24 14:10:33 [main] INFO  io.seata.server.ServerRunner - seata server started in 595 millSeconds
2025-07-24 14:10:36 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753337436652
timestamp=1753337436652
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xb96a97a4, L:/*************:8091 - R:/*************:3515],client version:1.7.1
2025-07-24 14:10:37 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x9aec0150, L:/*************:8091 - R:/*************:3516],client version:1.7.1
2025-07-24 14:10:37 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753337437333
timestamp=1753337437333
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xc547aec0, L:/*************:8091 - R:/*************:3517],client version:1.7.1
2025-07-24 14:10:37 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x8318f84c, L:/*************:8091 - R:/*************:3518],client version:1.7.1
2025-07-24 14:10:37 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753337437681
timestamp=1753337437681
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xe11880d8, L:/*************:8091 - R:/*************:3519],client version:1.7.1
2025-07-24 14:10:38 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x27475eb6, L:/*************:8091 - R:/*************:3520],client version:1.7.1
2025-07-24 14:10:39 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3515 to server channel inactive.
2025-07-24 14:10:39 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb96a97a4, L:/*************:8091 ! R:/*************:3515]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:3515', channel=[id: 0xb96a97a4, L:/*************:8091 ! R:/*************:3515], resourceSets=null}
2025-07-24 14:10:39 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3516 to server channel inactive.
2025-07-24 14:10:39 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9aec0150, L:/*************:8091 ! R:/*************:3516]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:3516', channel=[id: 0x9aec0150, L:/*************:8091 ! R:/*************:3516], resourceSets=[]}
2025-07-24 14:10:50 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753337450579
timestamp=1753337450579
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x20dddd7c, L:/*************:8091 - R:/*************:3555],client version:1.7.1
2025-07-24 14:10:54 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xcfb09ab8, L:/*************:8091 - R:/*************:3563],client version:1.7.1
2025-07-24 14:11:12 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3519 to server channel inactive.
2025-07-24 14:11:12 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe11880d8, L:/*************:8091 ! R:/*************:3519]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:3519', channel=[id: 0xe11880d8, L:/*************:8091 ! R:/*************:3519], resourceSets=null}
2025-07-24 14:11:12 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3520 to server channel inactive.
2025-07-24 14:11:12 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x27475eb6, L:/*************:8091 ! R:/*************:3520]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:3520', channel=[id: 0x27475eb6, L:/*************:8091 ! R:/*************:3520], resourceSets=null}
2025-07-24 14:11:23 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753337483530
timestamp=1753337483530
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xdc53a6ea, L:/*************:8091 - R:/*************:3649],client version:1.7.1
2025-07-24 14:12:23 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x1241c976, L:/*************:8091 - R:/*************:3776],client version:1.7.1
2025-07-24 14:16:04 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3649 to server channel inactive.
2025-07-24 14:16:04 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdc53a6ea, L:/*************:8091 ! R:/*************:3649]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:3649', channel=[id: 0xdc53a6ea, L:/*************:8091 ! R:/*************:3649], resourceSets=null}
2025-07-24 14:16:04 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3776 to server channel inactive.
2025-07-24 14:16:04 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1241c976, L:/*************:8091 ! R:/*************:3776]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:3776', channel=[id: 0x1241c976, L:/*************:8091 ! R:/*************:3776], resourceSets=null}
2025-07-24 14:16:13 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753337773275
timestamp=1753337773275
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xe8ce40eb, L:/*************:8091 - R:/*************:4270],client version:1.7.1
2025-07-24 14:16:17 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3517 to server channel inactive.
2025-07-24 14:16:17 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc547aec0, L:/*************:8091 ! R:/*************:3517]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:3517', channel=[id: 0xc547aec0, L:/*************:8091 ! R:/*************:3517], resourceSets=null}
2025-07-24 14:16:17 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3518 to server channel inactive.
2025-07-24 14:16:17 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8318f84c, L:/*************:8091 ! R:/*************:3518]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:3518', channel=[id: 0x8318f84c, L:/*************:8091 ! R:/*************:3518], resourceSets=[]}
2025-07-24 14:16:27 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753337786943
timestamp=1753337786943
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x8718f50c, L:/*************:8091 - R:/*************:4328],client version:1.7.1
2025-07-24 14:16:29 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x99ebd234, L:/*************:8091 - R:/*************:4336],client version:1.7.1
2025-07-24 14:16:42 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3555 to server channel inactive.
2025-07-24 14:16:42 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x20dddd7c, L:/*************:8091 ! R:/*************:3555]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:3555', channel=[id: 0x20dddd7c, L:/*************:8091 ! R:/*************:3555], resourceSets=null}
2025-07-24 14:16:42 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3563 to server channel inactive.
2025-07-24 14:16:42 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcfb09ab8, L:/*************:8091 ! R:/*************:3563]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:3563', channel=[id: 0xcfb09ab8, L:/*************:8091 ! R:/*************:3563], resourceSets=[]}
2025-07-24 14:16:56 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753337815849
timestamp=1753337815849
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x2f4667bd, L:/*************:8091 - R:/*************:4416],client version:1.7.1
2025-07-24 14:17:00 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xd324f63f, L:/*************:8091 - R:/*************:4438],client version:1.7.1
2025-07-24 14:17:13 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x9653359e, L:/*************:8091 - R:/*************:4496],client version:1.7.1
2025-07-24 16:42:16 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4416 to server channel inactive.
2025-07-24 16:42:16 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4438 to server channel inactive.
2025-07-24 16:42:16 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2f4667bd, L:/*************:8091 ! R:/*************:4416]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:4416', channel=[id: 0x2f4667bd, L:/*************:8091 ! R:/*************:4416], resourceSets=null}
2025-07-24 16:42:16 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd324f63f, L:/*************:8091 ! R:/*************:4438]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:4438', channel=[id: 0xd324f63f, L:/*************:8091 ! R:/*************:4438], resourceSets=[]}
2025-07-24 16:42:44 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753346564579
timestamp=1753346564579
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x7039fd12, L:/*************:8091 - R:/*************:7435],client version:1.7.1
2025-07-24 16:42:48 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xb64f1ee1, L:/*************:8091 - R:/*************:7454],client version:1.7.1
2025-07-24 17:10:17 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-24 17:17:53 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-24 17:17:53 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-24 17:17:53 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-24 17:17:53 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-24 17:17:54 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-24 17:17:54 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-24 17:17:54 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-24 17:17:54 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-24 17:17:54 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 17144 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-24 17:17:54 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-24 17:17:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-24 17:17:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-24 17:17:55 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 17:17:55 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-24 17:17:55 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 17:17:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1157 ms
2025-07-24 17:17:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 17:17:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 17:17:56 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-24 17:17:56 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7eb94590, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3777fc44, org.springframework.security.web.context.SecurityContextPersistenceFilter@5b9df3b3, org.springframework.security.web.header.HeaderWriterFilter@3c38e2bf, org.springframework.security.web.authentication.logout.LogoutFilter@6d6d81c, io.seata.console.filter.JwtAuthenticationTokenFilter@79454d8e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7bbe532b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1ae23815, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7ab2e018, org.springframework.security.web.session.SessionManagementFilter@3810806c, org.springframework.security.web.access.ExceptionTranslationFilter@10f192d8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@576323ff]
2025-07-24 17:17:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-24 17:17:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-24 17:17:56 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.593 seconds (JVM running for 4.49)
2025-07-24 17:17:56 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-24 17:17:56 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-24 17:17:56 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-24 17:17:56 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-24 17:17:56 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-24 17:17:57 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-24 17:17:57 [main] INFO  io.seata.server.ServerRunner - seata server started in 553 millSeconds
2025-07-24 17:18:12 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753348692397
timestamp=1753348692397
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x5725ba11, L:/*************:8091 - R:/*************:11269],client version:1.7.1
2025-07-24 17:18:15 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x806afdbb, L:/*************:8091 - R:/*************:11286],client version:1.7.1
2025-07-24 17:19:04 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753348743893
timestamp=1753348743893
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x7054a6b8, L:/*************:8091 - R:/*************:11429],client version:1.7.1
2025-07-24 17:19:10 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753348749759
timestamp=1753348749759
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xfee9787c, L:/*************:8091 - R:/*************:11461],client version:1.7.1
2025-07-24 17:19:14 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x0a29267a, L:/*************:8091 - R:/*************:11492],client version:1.7.1
2025-07-24 17:20:04 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x3f2d6f06, L:/*************:8091 - R:/*************:11574],client version:1.7.1
2025-07-24 18:30:27 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-24 19:06:07 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-24 19:06:07 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-24 19:06:07 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-24 19:06:07 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-24 19:06:08 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-24 19:06:08 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-24 19:06:08 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-24 19:06:08 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-24 19:06:08 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 33976 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-24 19:06:08 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-24 19:06:10 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-24 19:06:10 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-24 19:06:10 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-24 19:06:10 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-24 19:06:10 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-24 19:06:10 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1820 ms
2025-07-24 19:06:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-24 19:06:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-24 19:06:11 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-24 19:06:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@231c521e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@296949c8, org.springframework.security.web.context.SecurityContextPersistenceFilter@170f0fd6, org.springframework.security.web.header.HeaderWriterFilter@5d8112e6, org.springframework.security.web.authentication.logout.LogoutFilter@543196f9, io.seata.console.filter.JwtAuthenticationTokenFilter@1be3a294, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@695c8b32, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@577f81a0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@33502cfe, org.springframework.security.web.session.SessionManagementFilter@6c167296, org.springframework.security.web.access.ExceptionTranslationFilter@16a15261, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7d49fe37]
2025-07-24 19:06:12 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-24 19:06:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-24 19:06:12 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 5.51 seconds (JVM running for 6.781)
2025-07-24 19:06:12 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-24 19:06:12 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-24 19:06:12 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-24 19:06:12 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-24 19:06:12 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-24 19:06:13 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-24 19:06:13 [main] INFO  io.seata.server.ServerRunner - seata server started in 976 millSeconds
2025-07-24 19:06:13 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753355173227
timestamp=1753355173227
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x6535a482, L:/*************:8091 - R:/*************:5825],client version:1.7.1
2025-07-24 19:06:16 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xc9185200, L:/*************:8091 - R:/*************:5847],client version:1.7.1
2025-07-24 19:06:21 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753355181805
timestamp=1753355181805
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x69ca523e, L:/*************:8091 - R:/*************:5889],client version:1.7.1
2025-07-24 19:06:22 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753355182137
timestamp=1753355182137
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x5f5e7971, L:/*************:8091 - R:/*************:5892],client version:1.7.1
2025-07-24 19:06:25 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x0aea0ca7, L:/*************:8091 - R:/*************:5913],client version:1.7.1
2025-07-24 19:07:22 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xa4b2057d, L:/*************:8091 - R:/*************:5996],client version:1.7.1
2025-07-24 19:33:24 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753356804476
timestamp=1753356804476
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xfda1123c, L:/*************:8091 - R:/*************:8372],client version:1.7.1
2025-07-24 19:33:27 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xa474982b, L:/*************:8091 - R:/*************:8377],client version:1.7.1
2025-07-24 19:35:23 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8372 to server channel inactive.
2025-07-24 19:35:23 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8377 to server channel inactive.
2025-07-24 19:35:23 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfda1123c, L:/*************:8091 ! R:/*************:8372]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8372', channel=[id: 0xfda1123c, L:/*************:8091 ! R:/*************:8372], resourceSets=null}
2025-07-24 19:35:23 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa474982b, L:/*************:8091 ! R:/*************:8377]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8377', channel=[id: 0xa474982b, L:/*************:8091 ! R:/*************:8377], resourceSets=[]}
2025-07-24 19:35:37 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753356937486
timestamp=1753356937486
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x779dd2ec, L:/*************:8091 - R:/*************:8691],client version:1.7.1
2025-07-24 19:35:41 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xf9712099, L:/*************:8091 - R:/*************:8699],client version:1.7.1
2025-07-24 19:48:41 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8691 to server channel inactive.
2025-07-24 19:48:41 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8699 to server channel inactive.
2025-07-24 19:48:41 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x779dd2ec, L:/*************:8091 ! R:/*************:8691]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8691', channel=[id: 0x779dd2ec, L:/*************:8091 ! R:/*************:8691], resourceSets=null}
2025-07-24 19:48:41 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf9712099, L:/*************:8091 ! R:/*************:8699]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8699', channel=[id: 0xf9712099, L:/*************:8091 ! R:/*************:8699], resourceSets=[]}
2025-07-24 19:48:54 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753357733822
timestamp=1753357733822
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x970935f8, L:/*************:8091 - R:/*************:9634],client version:1.7.1
2025-07-24 19:48:57 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x362b4d59, L:/*************:8091 - R:/*************:9639],client version:1.7.1
2025-07-24 20:35:34 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
