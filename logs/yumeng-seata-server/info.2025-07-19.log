2025-07-19 08:53:54 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 08:53:54 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 08:53:54 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 08:53:54 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 08:53:55 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-19 08:53:55 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-19 08:53:55 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-19 08:53:55 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-19 08:53:55 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 22628 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 08:53:55 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-19 08:53:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-19 08:53:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-19 08:53:56 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 08:53:56 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-19 08:53:57 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 08:53:57 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1254 ms
2025-07-19 08:53:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-19 08:53:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-19 08:53:57 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-19 08:53:57 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@468eff41, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1574f323, org.springframework.security.web.context.SecurityContextPersistenceFilter@7da1ef46, org.springframework.security.web.header.HeaderWriterFilter@5b2c41f9, org.springframework.security.web.authentication.logout.LogoutFilter@35b79b1f, io.seata.console.filter.JwtAuthenticationTokenFilter@78ab63b5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1ae23815, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@16e84d93, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2b9370cc, org.springframework.security.web.session.SessionManagementFilter@7bbe532b, org.springframework.security.web.access.ExceptionTranslationFilter@5bc40f5d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7ab2e018]
2025-07-19 08:53:57 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-19 08:53:57 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-19 08:53:57 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.957 seconds (JVM running for 4.963)
2025-07-19 08:53:58 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-19 08:53:58 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-19 08:53:58 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-19 08:53:58 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 08:53:58 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 08:53:58 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-19 08:53:58 [main] INFO  io.seata.server.ServerRunner - seata server started in 644 millSeconds
2025-07-19 08:55:32 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752886531998
timestamp=1752886531998
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x09ffdbb1, L:/*************:8091 - R:/*************:4766],client version:1.7.1
2025-07-19 08:55:42 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752886542734
timestamp=1752886542734
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x79650a93, L:/*************:8091 - R:/*************:4828],client version:1.7.1
2025-07-19 08:55:43 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752886543292
timestamp=1752886543292
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x8d406c56, L:/*************:8091 - R:/*************:4834],client version:1.7.1
2025-07-19 08:55:46 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x1f54a1a6, L:/*************:8091 - R:/*************:4856],client version:1.7.1
2025-07-19 08:55:46 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x258548db, L:/*************:8091 - R:/*************:4863],client version:1.7.1
2025-07-19 08:55:48 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752886548277
timestamp=1752886548277
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x77ea5770, L:/*************:8091 - R:/*************:4884],client version:1.7.1
2025-07-19 08:55:51 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x8edb829a, L:/*************:8091 - R:/*************:4907],client version:1.7.1
2025-07-19 08:56:32 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xb0e0d8db, L:/*************:8091 - R:/*************:4976],client version:1.7.1
2025-07-19 10:03:54 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752890634083
timestamp=1752890634083
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x1b5f5698, L:/*************:8091 - R:/*************:12469],client version:1.7.1
2025-07-19 10:03:56 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xbadb93b6, L:/*************:8091 - R:/*************:12474],client version:1.7.1
2025-07-19 10:04:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12474 to server channel inactive.
2025-07-19 10:04:11 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12469 to server channel inactive.
2025-07-19 10:04:11 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1b5f5698, L:/*************:8091 ! R:/*************:12469]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12469', channel=[id: 0x1b5f5698, L:/*************:8091 ! R:/*************:12469], resourceSets=null}
2025-07-19 10:04:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbadb93b6, L:/*************:8091 ! R:/*************:12474]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12474', channel=[id: 0xbadb93b6, L:/*************:8091 ! R:/*************:12474], resourceSets=[]}
2025-07-19 10:05:34 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752890734014
timestamp=1752890734014
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x59372cb2, L:/*************:8091 - R:/*************:12657],client version:1.7.1
2025-07-19 10:05:36 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x72bcdf78, L:/*************:8091 - R:/*************:12662],client version:1.7.1
2025-07-19 10:05:50 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12657 to server channel inactive.
2025-07-19 10:05:50 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x59372cb2, L:/*************:8091 ! R:/*************:12657]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12657', channel=[id: 0x59372cb2, L:/*************:8091 ! R:/*************:12657], resourceSets=null}
2025-07-19 10:05:50 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12662 to server channel inactive.
2025-07-19 10:05:50 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x72bcdf78, L:/*************:8091 ! R:/*************:12662]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12662', channel=[id: 0x72bcdf78, L:/*************:8091 ! R:/*************:12662], resourceSets=[]}
2025-07-19 10:07:06 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752890826190
timestamp=1752890826190
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xe1fd2e78, L:/*************:8091 - R:/*************:12756],client version:1.7.1
2025-07-19 10:07:08 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xa55f8c42, L:/*************:8091 - R:/*************:12765],client version:1.7.1
2025-07-19 10:07:23 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12756 to server channel inactive.
2025-07-19 10:07:23 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe1fd2e78, L:/*************:8091 ! R:/*************:12756]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12756', channel=[id: 0xe1fd2e78, L:/*************:8091 ! R:/*************:12756], resourceSets=null}
2025-07-19 10:07:23 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12765 to server channel inactive.
2025-07-19 10:07:23 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa55f8c42, L:/*************:8091 ! R:/*************:12765]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12765', channel=[id: 0xa55f8c42, L:/*************:8091 ! R:/*************:12765], resourceSets=[]}
2025-07-19 10:09:36 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752890976295
timestamp=1752890976295
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xe65fa7e6, L:/*************:8091 - R:/*************:12934],client version:1.7.1
2025-07-19 10:09:38 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xa2421d26, L:/*************:8091 - R:/*************:12939],client version:1.7.1
2025-07-19 10:09:53 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12934 to server channel inactive.
2025-07-19 10:09:53 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe65fa7e6, L:/*************:8091 ! R:/*************:12934]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12934', channel=[id: 0xe65fa7e6, L:/*************:8091 ! R:/*************:12934], resourceSets=null}
2025-07-19 10:09:53 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12939 to server channel inactive.
2025-07-19 10:09:53 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa2421d26, L:/*************:8091 ! R:/*************:12939]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12939', channel=[id: 0xa2421d26, L:/*************:8091 ! R:/*************:12939], resourceSets=[]}
2025-07-19 10:10:41 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752891041037
timestamp=1752891041037
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xfddb2c5c, L:/*************:8091 - R:/*************:13044],client version:1.7.1
2025-07-19 10:10:45 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xdec196d0, L:/*************:8091 - R:/*************:13048],client version:1.7.1
2025-07-19 10:10:53 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13044 to server channel inactive.
2025-07-19 10:10:53 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfddb2c5c, L:/*************:8091 ! R:/*************:13044]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13044', channel=[id: 0xfddb2c5c, L:/*************:8091 ! R:/*************:13044], resourceSets=null}
2025-07-19 10:10:53 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13048 to server channel inactive.
2025-07-19 10:10:53 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdec196d0, L:/*************:8091 ! R:/*************:13048]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13048', channel=[id: 0xdec196d0, L:/*************:8091 ! R:/*************:13048], resourceSets=[]}
2025-07-19 10:11:26 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752891086037
timestamp=1752891086037
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x438aadce, L:/*************:8091 - R:/*************:13100],client version:1.7.1
2025-07-19 10:11:30 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xd60cb37f, L:/*************:8091 - R:/*************:13111],client version:1.7.1
2025-07-19 10:11:51 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x438aadce, L:/*************:8091 - R:/*************:13100] read idle.
2025-07-19 10:11:51 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13100 to server channel inactive.
2025-07-19 10:11:51 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x438aadce, L:/*************:8091 - R:/*************:13100]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13100', channel=[id: 0x438aadce, L:/*************:8091 - R:/*************:13100], resourceSets=null}
2025-07-19 10:11:51 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x438aadce, L:/*************:8091 - R:/*************:13100]
2025-07-19 10:11:51 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13100 to server channel inactive.
2025-07-19 10:11:51 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x438aadce, L:/*************:8091 ! R:/*************:13100]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13100', channel=[id: 0x438aadce, L:/*************:8091 ! R:/*************:13100], resourceSets=null}
2025-07-19 10:11:55 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xd60cb37f, L:/*************:8091 - R:/*************:13111] read idle.
2025-07-19 10:11:55 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13111 to server channel inactive.
2025-07-19 10:11:55 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd60cb37f, L:/*************:8091 - R:/*************:13111]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13111', channel=[id: 0xd60cb37f, L:/*************:8091 - R:/*************:13111], resourceSets=[]}
2025-07-19 10:11:55 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xd60cb37f, L:/*************:8091 - R:/*************:13111]
2025-07-19 10:11:55 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13111 to server channel inactive.
2025-07-19 10:11:55 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd60cb37f, L:/*************:8091 ! R:/*************:13111]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13111', channel=[id: 0xd60cb37f, L:/*************:8091 ! R:/*************:13111], resourceSets=[]}
2025-07-19 10:15:16 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752891316638
timestamp=1752891316638
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x4944bc85, L:/*************:8091 - R:/*************:13582],client version:1.7.1
2025-07-19 10:15:30 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752891330062
timestamp=1752891330062
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x422d939e, L:/*************:8091 - R:/*************:13608],client version:1.7.1
2025-07-19 10:15:31 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x4944bc85, L:/*************:8091 - R:/*************:13582] read idle.
2025-07-19 10:15:31 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13582 to server channel inactive.
2025-07-19 10:15:31 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4944bc85, L:/*************:8091 - R:/*************:13582]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13582', channel=[id: 0x4944bc85, L:/*************:8091 - R:/*************:13582], resourceSets=null}
2025-07-19 10:15:31 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x4944bc85, L:/*************:8091 - R:/*************:13582]
2025-07-19 10:15:31 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13582 to server channel inactive.
2025-07-19 10:15:31 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4944bc85, L:/*************:8091 ! R:/*************:13582]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13582', channel=[id: 0x4944bc85, L:/*************:8091 ! R:/*************:13582], resourceSets=null}
2025-07-19 10:15:34 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xc8c0a115, L:/*************:8091 - R:/*************:13612],client version:1.7.1
2025-07-19 10:15:51 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13608 to server channel inactive.
2025-07-19 10:15:51 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x422d939e, L:/*************:8091 ! R:/*************:13608]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13608', channel=[id: 0x422d939e, L:/*************:8091 ! R:/*************:13608], resourceSets=null}
2025-07-19 10:15:51 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13612 to server channel inactive.
2025-07-19 10:15:51 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc8c0a115, L:/*************:8091 ! R:/*************:13612]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13612', channel=[id: 0xc8c0a115, L:/*************:8091 ! R:/*************:13612], resourceSets=[]}
2025-07-19 10:16:38 [ServerHandlerThread_1_12_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x4aaa7272, L:/*************:8091 - R:/*************:13708],client version:1.7.1
2025-07-19 10:16:42 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752891402548
timestamp=1752891402548
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xb9efb5f4, L:/*************:8091 - R:/*************:13735],client version:1.7.1
2025-07-19 10:16:58 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13735 to server channel inactive.
2025-07-19 10:16:58 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb9efb5f4, L:/*************:8091 ! R:/*************:13735]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13735', channel=[id: 0xb9efb5f4, L:/*************:8091 ! R:/*************:13735], resourceSets=null}
2025-07-19 10:16:58 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13708 to server channel inactive.
2025-07-19 10:16:58 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4aaa7272, L:/*************:8091 ! R:/*************:13708]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13708', channel=[id: 0x4aaa7272, L:/*************:8091 ! R:/*************:13708], resourceSets=[]}
2025-07-19 10:19:57 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752891597387
timestamp=1752891597387
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xcac484f0, L:/*************:8091 - R:/*************:13902],client version:1.7.1
2025-07-19 10:20:01 [ServerHandlerThread_1_13_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x5fd6a466, L:/*************:8091 - R:/*************:13907],client version:1.7.1
2025-07-19 10:20:26 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x5fd6a466, L:/*************:8091 - R:/*************:13907] read idle.
2025-07-19 10:20:26 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13907 to server channel inactive.
2025-07-19 10:20:26 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5fd6a466, L:/*************:8091 - R:/*************:13907]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13907', channel=[id: 0x5fd6a466, L:/*************:8091 - R:/*************:13907], resourceSets=[]}
2025-07-19 10:20:26 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x5fd6a466, L:/*************:8091 - R:/*************:13907]
2025-07-19 10:20:26 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13907 to server channel inactive.
2025-07-19 10:20:26 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5fd6a466, L:/*************:8091 ! R:/*************:13907]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13907', channel=[id: 0x5fd6a466, L:/*************:8091 ! R:/*************:13907], resourceSets=[]}
2025-07-19 10:20:33 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xcac484f0, L:/*************:8091 - R:/*************:13902] read idle.
2025-07-19 10:20:33 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13902 to server channel inactive.
2025-07-19 10:20:33 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcac484f0, L:/*************:8091 - R:/*************:13902]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13902', channel=[id: 0xcac484f0, L:/*************:8091 - R:/*************:13902], resourceSets=null}
2025-07-19 10:20:33 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xcac484f0, L:/*************:8091 - R:/*************:13902]
2025-07-19 10:20:33 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13902 to server channel inactive.
2025-07-19 10:20:33 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcac484f0, L:/*************:8091 ! R:/*************:13902]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13902', channel=[id: 0xcac484f0, L:/*************:8091 ! R:/*************:13902], resourceSets=null}
2025-07-19 10:21:15 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752891675405
timestamp=1752891675405
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xf0569097, L:/*************:8091 - R:/*************:13988],client version:1.7.1
2025-07-19 10:21:19 [ServerHandlerThread_1_14_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x4db16499, L:/*************:8091 - R:/*************:13992],client version:1.7.1
2025-07-19 10:21:40 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13988 to server channel inactive.
2025-07-19 10:21:40 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf0569097, L:/*************:8091 ! R:/*************:13988]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13988', channel=[id: 0xf0569097, L:/*************:8091 ! R:/*************:13988], resourceSets=null}
2025-07-19 10:21:40 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13992 to server channel inactive.
2025-07-19 10:21:40 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4db16499, L:/*************:8091 ! R:/*************:13992]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13992', channel=[id: 0x4db16499, L:/*************:8091 ! R:/*************:13992], resourceSets=[]}
2025-07-19 10:33:41 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752892421335
timestamp=1752892421335
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x6ace040b, L:/*************:8091 - R:/*************:14353],client version:1.7.1
2025-07-19 10:33:44 [ServerHandlerThread_1_15_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x341b2d13, L:/*************:8091 - R:/*************:14357],client version:1.7.1
2025-07-19 10:33:50 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14353 to server channel inactive.
2025-07-19 10:33:50 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6ace040b, L:/*************:8091 ! R:/*************:14353]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14353', channel=[id: 0x6ace040b, L:/*************:8091 ! R:/*************:14353], resourceSets=null}
2025-07-19 10:33:50 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14357 to server channel inactive.
2025-07-19 10:33:50 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x341b2d13, L:/*************:8091 ! R:/*************:14357]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14357', channel=[id: 0x341b2d13, L:/*************:8091 ! R:/*************:14357], resourceSets=[]}
2025-07-19 10:35:40 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4884 to server channel inactive.
2025-07-19 10:35:40 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4907 to server channel inactive.
2025-07-19 10:35:40 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x77ea5770, L:/*************:8091 ! R:/*************:4884]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4884', channel=[id: 0x77ea5770, L:/*************:8091 ! R:/*************:4884], resourceSets=null}
2025-07-19 10:35:40 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8edb829a, L:/*************:8091 ! R:/*************:4907]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4907', channel=[id: 0x8edb829a, L:/*************:8091 ! R:/*************:4907], resourceSets=[]}
2025-07-19 10:35:56 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752892556730
timestamp=1752892556730
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xcdda6ad8, L:/*************:8091 - R:/*************:14491],client version:1.7.1
2025-07-19 10:36:00 [ServerHandlerThread_1_16_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x12e8bc1f, L:/*************:8091 - R:/*************:14495],client version:1.7.1
2025-07-19 10:59:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 10:59:42 [ServerHandlerThread_1_17_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:4369165230820315137
2025-07-19 10:59:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:4369165230820315137', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 10:59:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4369165230820315137', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946403795146670081', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 10:59:43 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4369165230820315137, branchId = 4369165230820315140, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946403795146670081
2025-07-19 10:59:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4369165230820315140, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 10:59:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4369165230820315137', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 10:59:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=0, resultCode=Failed, msg='TransactionException[branch register request failed. xid=*************:8091:4369165230820315137, msg=HikariPool-1 - Connection is not available, request timed out after 10003ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 10:59:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalRollbackRequest{xid='*************:8091:4369165230820315137', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 10:59:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchRollbackResponse{xid='*************:8091:4369165230820315137', branchId=4369165230820315140, branchStatus=PhaseTwo_Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 10:59:54 [ServerHandlerThread_1_20_500] INFO  i.s.server.coordinator.DefaultCore - Rollback branch transaction successfully, xid = *************:8091:4369165230820315137 branchId = 4369165230820315140
2025-07-19 10:59:54 [ServerHandlerThread_1_20_500] INFO  i.s.server.coordinator.DefaultCore - Rollback global transaction successfully, xid = *************:8091:4369165230820315137.
2025-07-19 10:59:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalRollbackResponse{globalStatus=Rollbacked, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:00:16 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 11:00:16 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 11:00:16 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 11:00:16 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 11:00:17 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-19 11:00:17 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-19 11:00:17 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-19 11:00:17 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-19 11:00:17 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 34380 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 11:00:17 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-19 11:00:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-19 11:00:18 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 11:00:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-19 11:00:19 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 11:00:19 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1315 ms
2025-07-19 11:00:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-19 11:00:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-19 11:00:19 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-19 11:00:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7207f3c2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@574134ba, org.springframework.security.web.context.SecurityContextPersistenceFilter@3f3a529f, org.springframework.security.web.header.HeaderWriterFilter@5b1cedfd, org.springframework.security.web.authentication.logout.LogoutFilter@39f7cc46, io.seata.console.filter.JwtAuthenticationTokenFilter@57063e08, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5aa5b3af, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@34525143, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6afb240d, org.springframework.security.web.session.SessionManagementFilter@46aea9f7, org.springframework.security.web.access.ExceptionTranslationFilter@208b8425, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7d3faf1d]
2025-07-19 11:00:19 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:19 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:19 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-19 11:00:19 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:19 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:19 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-19 11:00:34 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 11:00:34 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 11:00:34 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 11:00:34 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 11:00:35 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-19 11:00:35 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-19 11:00:35 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-19 11:00:35 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-19 11:00:35 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 27872 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 11:00:35 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-19 11:00:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-19 11:00:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 11:00:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-19 11:00:36 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 11:00:36 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1234 ms
2025-07-19 11:00:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-19 11:00:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-19 11:00:36 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-19 11:00:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3a225534, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@44d6f9cf, org.springframework.security.web.context.SecurityContextPersistenceFilter@7ba623d1, org.springframework.security.web.header.HeaderWriterFilter@3da55998, org.springframework.security.web.authentication.logout.LogoutFilter@4fc84c92, io.seata.console.filter.JwtAuthenticationTokenFilter@34f32575, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6dbdbb69, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1239c268, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@54224125, org.springframework.security.web.session.SessionManagementFilter@24ce5d4c, org.springframework.security.web.access.ExceptionTranslationFilter@6b468710, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@341889a1]
2025-07-19 11:00:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:37 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-19 11:00:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-7091"]
2025-07-19 11:00:37 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-19 11:00:55 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-19 11:03:36 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 11:03:36 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 11:03:36 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 11:03:36 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 11:03:37 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-19 11:03:37 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-19 11:03:37 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-19 11:03:37 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-19 11:03:37 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 26848 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 11:03:37 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-19 11:03:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-19 11:03:38 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-19 11:03:38 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 11:03:38 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-19 11:03:38 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 11:03:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1140 ms
2025-07-19 11:03:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-19 11:03:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-19 11:03:39 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-19 11:03:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6046fba0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@248ba4fc, org.springframework.security.web.context.SecurityContextPersistenceFilter@4ef10d3b, org.springframework.security.web.header.HeaderWriterFilter@736a8aea, org.springframework.security.web.authentication.logout.LogoutFilter@325236f5, io.seata.console.filter.JwtAuthenticationTokenFilter@54cce500, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@690e7b89, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7d49fe37, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@755033c5, org.springframework.security.web.session.SessionManagementFilter@7878459f, org.springframework.security.web.access.ExceptionTranslationFilter@2487b621, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4c678a1f]
2025-07-19 11:03:39 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-19 11:03:39 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-19 11:03:39 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.474 seconds (JVM running for 4.455)
2025-07-19 11:03:39 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-19 11:03:39 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-19 11:03:40 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-19 11:03:40 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 11:03:40 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 11:03:40 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-19 11:03:40 [main] INFO  io.seata.server.ServerRunner - seata server started in 570 millSeconds
2025-07-19 11:07:57 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 11:07:57 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 11:07:57 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 11:07:57 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 11:07:58 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-19 11:07:58 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-19 11:07:58 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-19 11:07:58 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-19 11:07:58 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 36464 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 11:07:58 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-19 11:07:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-19 11:07:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-19 11:07:59 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 11:07:59 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-19 11:07:59 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 11:07:59 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1156 ms
2025-07-19 11:07:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-19 11:07:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-19 11:08:00 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-19 11:08:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7602c65d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5a5c2889, org.springframework.security.web.context.SecurityContextPersistenceFilter@205b73d8, org.springframework.security.web.header.HeaderWriterFilter@694f0655, org.springframework.security.web.authentication.logout.LogoutFilter@6018d82c, io.seata.console.filter.JwtAuthenticationTokenFilter@7668f8fd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5bc40f5d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f5a092e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@75566f4, org.springframework.security.web.session.SessionManagementFilter@10f192d8, org.springframework.security.web.access.ExceptionTranslationFilter@18ffc008, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3183a37c]
2025-07-19 11:08:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-19 11:08:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-19 11:08:00 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.594 seconds (JVM running for 4.589)
2025-07-19 11:08:00 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-19 11:08:00 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-19 11:08:00 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-19 11:08:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 11:08:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 11:08:00 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-19 11:08:00 [main] INFO  io.seata.server.ServerRunner - seata server started in 569 millSeconds
2025-07-19 11:08:08 [http-nio-7091-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 11:08:08 [http-nio-7091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 11:08:08 [http-nio-7091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-19 11:08:41 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752894521520
timestamp=1752894521520
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xb446d61a, L:/*************:8091 - R:/*************:2766],client version:1.7.1
2025-07-19 11:09:14 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752894554070
timestamp=1752894554070
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x8fe27faf, L:/*************:8091 - R:/*************:2830],client version:1.7.1
2025-07-19 11:09:17 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x9a594306, L:/*************:8091 - R:/*************:2841],client version:1.7.1
2025-07-19 11:09:18 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752894558050
timestamp=1752894558050
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x8134cfc4, L:/*************:8091 - R:/*************:2851],client version:1.7.1
2025-07-19 11:09:21 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x3fabce61, L:/*************:8091 - R:/*************:2877],client version:1.7.1
2025-07-19 11:09:21 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752894561655
timestamp=1752894561655
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x7d115a27, L:/*************:8091 - R:/*************:2884],client version:1.7.1
2025-07-19 11:09:24 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xfe6daabe, L:/*************:8091 - R:/*************:2906],client version:1.7.1
2025-07-19 11:09:41 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x60f9e233, L:/*************:8091 - R:/*************:2973],client version:1.7.1
2025-07-19 11:12:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:41 [ServerHandlerThread_1_5_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:4540302049602232321
2025-07-19 11:12:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:4540302049602232321', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232321', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946403795146670081', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:42 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232321, branchId = 4540302049602232324, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946403795146670081
2025-07-19 11:12:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232324, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232321', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:42 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232321, branchId = 4540302049602232326, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 11:12:42 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232326, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232321', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:43 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232321, branchId = 4540302049602232329, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 11:12:43 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232329, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232321', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946407830599839745', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:44 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232321, branchId = 4540302049602232332, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946407830599839745
2025-07-19 11:12:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232332, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232321', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946407833011564545', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:44 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232321, branchId = 4540302049602232334, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946407833011564545
2025-07-19 11:12:44 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232334, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:4540302049602232321', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:4540302049602232321', branchId=4540302049602232324, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:45 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:4540302049602232321 branchId = 4540302049602232324
2025-07-19 11:12:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:4540302049602232321', branchId=4540302049602232326, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:45 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:4540302049602232321 branchId = 4540302049602232326
2025-07-19 11:12:45 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:4540302049602232321', branchId=4540302049602232329, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:46 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:4540302049602232321 branchId = 4540302049602232329
2025-07-19 11:12:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:4540302049602232321', branchId=4540302049602232332, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:46 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:4540302049602232321 branchId = 4540302049602232332
2025-07-19 11:12:46 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:4540302049602232321', branchId=4540302049602232334, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:12:46 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:4540302049602232321 branchId = 4540302049602232334
2025-07-19 11:12:46 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:4540302049602232321.
2025-07-19 11:16:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='null', extraData='null', resultCode=Failed, msg='TransactionException[begin global request failed. xid=null, msg=HikariPool-1 - Connection is not available, request timed out after 10027ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:20 [ServerHandlerThread_1_13_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:4540302049602232339
2025-07-19 11:17:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:4540302049602232339', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232339', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946407830599839745', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:21 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232339, branchId = 4540302049602232342, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946407830599839745
2025-07-19 11:17:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232342, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232339', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:21 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232339, branchId = 4540302049602232344, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 11:17:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232344, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232339', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946408997128056834', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:22 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232339, branchId = 4540302049602232347, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946408997128056834
2025-07-19 11:17:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232347, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:23 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232339', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946409000001093634', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:17:23 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232339, branchId = 4540302049602232350, resourceId = ****************************************** ,lockKeys = todo_info:1946409000001093634
2025-07-19 11:17:23 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232350, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:17:23 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232339', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946409002656088066', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:17:23 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232339, branchId = 4540302049602232352, resourceId = ****************************************** ,lockKeys = todo_info:1946409002656088066
2025-07-19 11:17:23 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232352, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:17:24 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232339', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946409004694519809', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:17:24 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232339, branchId = 4540302049602232355, resourceId = ****************************************** ,lockKeys = todo_info:1946409004694519809
2025-07-19 11:17:24 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232355, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:17:24 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:4540302049602232339', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:17:24 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:39 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='processWorkorder(java.lang.Long, java.lang.String, java.util.List)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:39 [ServerHandlerThread_1_21_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: processWorkorder(java.lang.Long, java.lang.String, java.util.List),timeout:60000,xid:*************:8091:4540302049602232358
2025-07-19 11:26:39 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:4540302049602232358', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:39 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232358', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:39 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232358, branchId = 4540302049602232360, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 11:26:39 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232360, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232358', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946411336941514754', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:40 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232358, branchId = 4540302049602232363, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946411336941514754
2025-07-19 11:26:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232363, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232358', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:40 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232358, branchId = 4540302049602232365, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 11:26:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232365, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232358', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946409000001093634,1946409002656088066,1946409004694519809', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:26:41 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232358, branchId = 4540302049602232368, resourceId = ****************************************** ,lockKeys = todo_info:1946409000001093634,1946409002656088066,1946409004694519809
2025-07-19 11:26:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232368, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:26:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:4540302049602232358', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:26:41 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='reject(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:14 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='null', extraData='null', resultCode=Failed, msg='TransactionException[begin global request failed. xid=null, msg=HikariPool-1 - Connection is not available, request timed out after 10015ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='reject(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:18 [ServerHandlerThread_1_28_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: reject(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:4540302049602232371
2025-07-19 11:33:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:4540302049602232371', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232371', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946411336941514754', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:18 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232371, branchId = 4540302049602232373, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946411336941514754
2025-07-19 11:33:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232373, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:4540302049602232371', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:19 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:4540302049602232371, branchId = 4540302049602232376, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 11:33:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=4540302049602232376, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:4540302049602232371', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:33:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:48:51 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-19 11:48:59 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 11:48:59 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 11:48:59 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 11:48:59 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 11:49:00 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-19 11:49:00 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-19 11:49:00 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-19 11:49:00 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-19 11:49:00 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 33408 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 11:49:00 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-19 11:49:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-19 11:49:01 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-19 11:49:01 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 11:49:01 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-19 11:49:01 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 11:49:01 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1407 ms
2025-07-19 11:49:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-19 11:49:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-19 11:49:02 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-19 11:49:02 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4a3404fa, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3fb4beb1, org.springframework.security.web.context.SecurityContextPersistenceFilter@1f602930, org.springframework.security.web.header.HeaderWriterFilter@feab3ae, org.springframework.security.web.authentication.logout.LogoutFilter@3889c343, io.seata.console.filter.JwtAuthenticationTokenFilter@34cb1310, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1d4c6e32, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@23dda7a3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2b5a04b0, org.springframework.security.web.session.SessionManagementFilter@6d94a966, org.springframework.security.web.access.ExceptionTranslationFilter@54224125, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b441609]
2025-07-19 11:49:02 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-19 11:49:02 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-19 11:49:02 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.171 seconds (JVM running for 6.265)
2025-07-19 11:49:03 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-19 11:49:03 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-19 11:49:03 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-19 11:49:03 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 11:49:03 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 11:49:03 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-19 11:49:03 [main] INFO  io.seata.server.ServerRunner - seata server started in 582 millSeconds
2025-07-19 11:49:03 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752896941343
timestamp=1752896941343
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x0aa97850, L:/*************:8091 - R:/*************:5149],client version:1.7.1
2025-07-19 11:49:03 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xfef9de8e, L:/*************:8091 - R:/*************:5152],client version:1.7.1
2025-07-19 11:49:03 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752896941469
timestamp=1752896941469
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x8536fb68, L:/*************:8091 - R:/*************:5150],client version:1.7.1
2025-07-19 11:49:03 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x7907923b, L:/*************:8091 - R:/*************:5151],client version:1.7.1
2025-07-19 11:49:03 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752896943845
timestamp=1752896943845
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x7926516c, L:/*************:8091 - R:/*************:5163],client version:1.7.1
2025-07-19 11:49:04 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x5db6a1cd, L:/*************:8091 - R:/*************:5165],client version:1.7.1
2025-07-19 11:49:07 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752896947847
timestamp=1752896947847
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x7876dd5f, L:/*************:8091 - R:/*************:5166],client version:1.7.1
2025-07-19 11:49:08 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x66dddfed, L:/*************:8091 - R:/*************:5167],client version:1.7.1
2025-07-19 11:53:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='rejectWorkorder(java.lang.Long, java.lang.String)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:48 [ServerHandlerThread_1_5_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: rejectWorkorder(java.lang.Long, java.lang.String),timeout:60000,xid:*************:8091:775292771207004161
2025-07-19 11:53:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004161', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004161', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946418167327830017', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:48 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004161, branchId = 775292771207004164, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946418167327830017
2025-07-19 11:53:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004164, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004161', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946418169701806081', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:49 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004161, branchId = 775292771207004166, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946418169701806081
2025-07-19 11:53:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004166, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004161', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:50 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004161, branchId = 775292771207004169, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 11:53:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004169, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004161', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946418175632490497', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:51 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004161, branchId = 775292771207004172, resourceId = ****************************************** ,lockKeys = todo_info:1946418175632490497
2025-07-19 11:53:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004172, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004161', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946418178463645698', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:51 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004161, branchId = 775292771207004174, resourceId = ****************************************** ,lockKeys = todo_info:1946418178463645698
2025-07-19 11:53:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004174, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004161', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946418180422385666', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:52 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004161, branchId = 775292771207004177, resourceId = ****************************************** ,lockKeys = todo_info:1946418180422385666
2025-07-19 11:53:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004177, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004161', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946418182683115522', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:52 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004161, branchId = 775292771207004179, resourceId = ****************************************** ,lockKeys = todo_info:1946418182683115522
2025-07-19 11:53:52 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004179, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004161', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:53 [ForkJoinPool.commonPool-worker-2] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004161, branchId = 775292771207004183, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 11:53:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004183, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004161', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:53 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004161', branchId=775292771207004164, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:54 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004161 branchId = 775292771207004164
2025-07-19 11:53:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004161', branchId=775292771207004166, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:54 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004161 branchId = 775292771207004166
2025-07-19 11:53:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004161', branchId=775292771207004169, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:54 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004161 branchId = 775292771207004169
2025-07-19 11:53:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004161', branchId=775292771207004172, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:54 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004161 branchId = 775292771207004172
2025-07-19 11:53:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004161', branchId=775292771207004174, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:54 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004161 branchId = 775292771207004174
2025-07-19 11:53:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004161', branchId=775292771207004177, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:54 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004161 branchId = 775292771207004177
2025-07-19 11:53:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004161', branchId=775292771207004179, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 11:53:54 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004161 branchId = 775292771207004179
2025-07-19 11:53:54 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004161', branchId=775292771207004183, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 11:53:54 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004161 branchId = 775292771207004183
2025-07-19 11:53:54 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004161.
2025-07-19 12:00:57 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5167 to server channel inactive.
2025-07-19 12:00:57 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5166 to server channel inactive.
2025-07-19 12:00:57 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7876dd5f, L:/*************:8091 ! R:/*************:5166]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5166', channel=[id: 0x7876dd5f, L:/*************:8091 ! R:/*************:5166], resourceSets=null}
2025-07-19 12:00:57 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x66dddfed, L:/*************:8091 ! R:/*************:5167]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5167', channel=[id: 0x66dddfed, L:/*************:8091 ! R:/*************:5167], resourceSets=[]}
2025-07-19 12:01:11 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752897671666
timestamp=1752897671666
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x4e8b870f, L:/*************:8091 - R:/*************:5818],client version:1.7.1
2025-07-19 12:01:15 [ServerHandlerThread_1_15_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x7f17ecb3, L:/*************:8091 - R:/*************:5822],client version:1.7.1
2025-07-19 13:39:15 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:25 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='null', extraData='null', resultCode=Failed, msg='TransactionException[begin global request failed. xid=null, msg=HikariPool-1 - Connection is not available, request timed out after 10001ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:33 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:34 [ServerHandlerThread_1_17_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:775292771207004187
2025-07-19 13:39:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004187', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004187', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946407833011564545', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:34 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004187, branchId = 775292771207004190, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946407833011564545
2025-07-19 13:39:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004190, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:35 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004187', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:35 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004187, branchId = 775292771207004192, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 13:39:35 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004192, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:36 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004187', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:36 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004187, branchId = 775292771207004195, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:39:36 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004195, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:36 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004187', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946444792337608706', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:36 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004187, branchId = 775292771207004198, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946444792337608706
2025-07-19 13:39:36 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004198, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:37 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004187', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946444794531229698', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:37 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004187, branchId = 775292771207004200, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946444794531229698
2025-07-19 13:39:37 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004200, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:37 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004187', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946418175632490497,1946418178463645698,1946418180422385666,1946418182683115522', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:39:38 [ForkJoinPool.commonPool-worker-3] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004187, branchId = 775292771207004203, resourceId = ****************************************** ,lockKeys = todo_info:1946418175632490497,1946418178463645698,1946418180422385666,1946418182683115522
2025-07-19 13:39:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004203, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:39:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004187', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004187', branchId=775292771207004190, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:38 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004187 branchId = 775292771207004190
2025-07-19 13:39:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004187', branchId=775292771207004192, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:38 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004187 branchId = 775292771207004192
2025-07-19 13:39:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004187', branchId=775292771207004195, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:38 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004187 branchId = 775292771207004195
2025-07-19 13:39:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004187', branchId=775292771207004198, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:38 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004187 branchId = 775292771207004198
2025-07-19 13:39:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004187', branchId=775292771207004200, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:39:38 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004187 branchId = 775292771207004200
2025-07-19 13:39:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004187', branchId=775292771207004203, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:39:38 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004187 branchId = 775292771207004203
2025-07-19 13:39:38 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004187.
2025-07-19 13:41:14 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:14 [ServerHandlerThread_1_25_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:775292771207004206
2025-07-19 13:41:14 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004206', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:14 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004206', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946444792337608706', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:15 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004206, branchId = 775292771207004208, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946444792337608706
2025-07-19 13:41:15 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004208, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:15 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004206', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:15 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004206, branchId = 775292771207004211, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:41:15 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004211, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:15 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004206', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946445209037516801', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:16 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004206, branchId = 775292771207004213, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946445209037516801
2025-07-19 13:41:16 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004213, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:16 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004206', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946445211650506753', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:41:16 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004206, branchId = 775292771207004216, resourceId = ****************************************** ,lockKeys = todo_info:1946445211650506753
2025-07-19 13:41:16 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004216, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:41:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004206', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946445213722492930', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:41:17 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004206, branchId = 775292771207004218, resourceId = ****************************************** ,lockKeys = todo_info:1946445213722492930
2025-07-19 13:41:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004218, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:41:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004206', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946445215882559489', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:41:17 [ForkJoinPool.commonPool-worker-4] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004206, branchId = 775292771207004221, resourceId = ****************************************** ,lockKeys = todo_info:1946445215882559489
2025-07-19 13:41:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004221, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:41:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004206', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004206', branchId=775292771207004208, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:18 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004206 branchId = 775292771207004208
2025-07-19 13:41:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004206', branchId=775292771207004211, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:18 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004206 branchId = 775292771207004211
2025-07-19 13:41:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004206', branchId=775292771207004213, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:41:18 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004206 branchId = 775292771207004213
2025-07-19 13:41:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004206', branchId=775292771207004216, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:41:18 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004206 branchId = 775292771207004216
2025-07-19 13:41:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004206', branchId=775292771207004218, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:41:18 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004206 branchId = 775292771207004218
2025-07-19 13:41:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004206', branchId=775292771207004221, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:41:18 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004206 branchId = 775292771207004221
2025-07-19 13:41:18 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004206.
2025-07-19 13:44:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='rejectWorkorder(java.lang.Long, java.lang.String)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:17 [ServerHandlerThread_1_33_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: rejectWorkorder(java.lang.Long, java.lang.String),timeout:60000,xid:*************:8091:775292771207004224
2025-07-19 13:44:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004224', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004224', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946445972006580225', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:17 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004224, branchId = 775292771207004227, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946445972006580225
2025-07-19 13:44:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004227, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004224', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946445974158258177', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:18 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004224, branchId = 775292771207004229, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946445974158258177
2025-07-19 13:44:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004229, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004224', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:18 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004224, branchId = 775292771207004232, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 13:44:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004232, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004224', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946445978868400129', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:19 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004224, branchId = 775292771207004235, resourceId = ****************************************** ,lockKeys = todo_info:1946445978868400129
2025-07-19 13:44:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004235, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004224', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946445980936192001', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:20 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004224, branchId = 775292771207004237, resourceId = ****************************************** ,lockKeys = todo_info:1946445980936192001
2025-07-19 13:44:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004237, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004224', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946445982727159809', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:20 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004224, branchId = 775292771207004240, resourceId = ****************************************** ,lockKeys = todo_info:1946445982727159809
2025-07-19 13:44:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004240, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004224', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946445984484573186', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:20 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004224, branchId = 775292771207004242, resourceId = ****************************************** ,lockKeys = todo_info:1946445984484573186
2025-07-19 13:44:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004242, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004224', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946445211650506753,1946445213722492930,1946445215882559489', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:21 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004224, branchId = 775292771207004245, resourceId = ****************************************** ,lockKeys = todo_info:1946445211650506753,1946445213722492930,1946445215882559489
2025-07-19 13:44:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004245, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004224', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:21 [ForkJoinPool.commonPool-worker-5] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004224, branchId = 775292771207004247, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:44:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004247, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004224', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004224', branchId=775292771207004227, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:22 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004224 branchId = 775292771207004227
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004224', branchId=775292771207004229, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:22 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004224 branchId = 775292771207004229
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004224', branchId=775292771207004232, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:22 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004224 branchId = 775292771207004232
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004224', branchId=775292771207004235, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:22 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004224 branchId = 775292771207004235
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004224', branchId=775292771207004237, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:22 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004224 branchId = 775292771207004237
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004224', branchId=775292771207004240, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:22 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004224 branchId = 775292771207004240
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004224', branchId=775292771207004242, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:22 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004224 branchId = 775292771207004242
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004224', branchId=775292771207004245, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:44:22 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004224 branchId = 775292771207004245
2025-07-19 13:44:22 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004224', branchId=775292771207004247, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:44:22 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004224 branchId = 775292771207004247
2025-07-19 13:44:23 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004224.
2025-07-19 13:45:06 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5818 to server channel inactive.
2025-07-19 13:45:06 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5822 to server channel inactive.
2025-07-19 13:45:06 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4e8b870f, L:/*************:8091 ! R:/*************:5818]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5818', channel=[id: 0x4e8b870f, L:/*************:8091 ! R:/*************:5818], resourceSets=null}
2025-07-19 13:45:06 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7f17ecb3, L:/*************:8091 ! R:/*************:5822]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:5822', channel=[id: 0x7f17ecb3, L:/*************:8091 ! R:/*************:5822], resourceSets=[]}
2025-07-19 13:45:18 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752903917832
timestamp=1752903917832
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa6abb274, L:/*************:8091 - R:/*************:8987],client version:1.7.1
2025-07-19 13:45:21 [ServerHandlerThread_1_44_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x29f1445a, L:/*************:8091 - R:/*************:8995],client version:1.7.1
2025-07-19 13:46:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:02 [ServerHandlerThread_1_45_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:775292771207004250
2025-07-19 13:46:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004250', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004250', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946444794531229698', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:03 [ForkJoinPool.commonPool-worker-6] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004250, branchId = 775292771207004253, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946444794531229698
2025-07-19 13:46:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004253, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004250', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:04 [ForkJoinPool.commonPool-worker-6] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004250, branchId = 775292771207004255, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 13:46:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004255, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004250', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:04 [ForkJoinPool.commonPool-worker-6] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004250, branchId = 775292771207004258, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:46:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004258, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004250', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946446422399307778', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:05 [ForkJoinPool.commonPool-worker-6] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004250, branchId = 775292771207004260, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946446422399307778
2025-07-19 13:46:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004260, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004250', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946446424748118018', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:05 [ForkJoinPool.commonPool-worker-6] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004250, branchId = 775292771207004263, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946446424748118018
2025-07-19 13:46:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004263, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004250', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946445978868400129,1946445980936192001,1946445982727159809,1946445984484573186', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:46:06 [ForkJoinPool.commonPool-worker-6] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004250, branchId = 775292771207004266, resourceId = ****************************************** ,lockKeys = todo_info:1946445978868400129,1946445980936192001,1946445982727159809,1946445984484573186
2025-07-19 13:46:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004266, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:46:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004250', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004250', branchId=775292771207004253, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:07 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004250 branchId = 775292771207004253
2025-07-19 13:46:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004250', branchId=775292771207004255, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:07 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004250 branchId = 775292771207004255
2025-07-19 13:46:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004250', branchId=775292771207004258, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:07 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004250 branchId = 775292771207004258
2025-07-19 13:46:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004250', branchId=775292771207004260, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:07 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004250 branchId = 775292771207004260
2025-07-19 13:46:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004250', branchId=775292771207004263, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:46:07 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004250 branchId = 775292771207004263
2025-07-19 13:46:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004250', branchId=775292771207004266, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:46:07 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004250 branchId = 775292771207004266
2025-07-19 13:46:07 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004250.
2025-07-19 13:47:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:47:06 [ServerHandlerThread_1_3_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:775292771207004269
2025-07-19 13:47:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004269', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:47:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004269', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946446422399307778', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:47:06 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004269, branchId = 775292771207004271, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946446422399307778
2025-07-19 13:47:06 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004271, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:47:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004269', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:47:07 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004269, branchId = 775292771207004273, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:47:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004273, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:47:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004269', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946446684761411585', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:47:07 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004269, branchId = 775292771207004276, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946446684761411585
2025-07-19 13:47:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004276, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:47:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004269', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946446687227625474', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:47:08 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004269, branchId = 775292771207004279, resourceId = ****************************************** ,lockKeys = todo_info:1946446687227625474
2025-07-19 13:47:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004279, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:47:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004269', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946446689161199617', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:47:08 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004269, branchId = 775292771207004281, resourceId = ****************************************** ,lockKeys = todo_info:1946446689161199617
2025-07-19 13:47:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004281, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:47:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004269', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946446690981527554', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:47:09 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004269, branchId = 775292771207004283, resourceId = ****************************************** ,lockKeys = todo_info:1946446690981527554
2025-07-19 13:47:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004283, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:47:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004269', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:47:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='rejectWorkorder(java.lang.Long, java.lang.String)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:07 [ServerHandlerThread_1_11_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: rejectWorkorder(java.lang.Long, java.lang.String),timeout:60000,xid:*************:8091:775292771207004286
2025-07-19 13:48:07 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004286', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004286', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946446938172870658', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:08 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004286, branchId = 775292771207004288, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946446938172870658
2025-07-19 13:48:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004288, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004286', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946446940316160001', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:08 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004286, branchId = 775292771207004291, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946446940316160001
2025-07-19 13:48:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004291, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004286', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:09 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004286, branchId = 775292771207004293, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 13:48:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004293, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004286', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946446944917274626', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:10 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004286, branchId = 775292771207004296, resourceId = ****************************************** ,lockKeys = todo_info:1946446944917274626
2025-07-19 13:48:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004296, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004286', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946446947182198785', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:10 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004286, branchId = 775292771207004298, resourceId = ****************************************** ,lockKeys = todo_info:1946446947182198785
2025-07-19 13:48:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004298, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004286', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946446949161910273', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:11 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004286, branchId = 775292771207004301, resourceId = ****************************************** ,lockKeys = todo_info:1946446949161910273
2025-07-19 13:48:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004301, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004286', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946446951355531265', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:11 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004286, branchId = 775292771207004303, resourceId = ****************************************** ,lockKeys = todo_info:1946446951355531265
2025-07-19 13:48:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004303, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:11 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004286', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946446687227625474,1946446689161199617,1946446690981527554', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:12 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004286, branchId = 775292771207004306, resourceId = ****************************************** ,lockKeys = todo_info:1946446687227625474,1946446689161199617,1946446690981527554
2025-07-19 13:48:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004306, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004286', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:12 [ForkJoinPool.commonPool-worker-7] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004286, branchId = 775292771207004309, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:48:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004309, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004286', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:12 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:13 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004286', branchId=775292771207004288, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:13 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004286 branchId = 775292771207004288
2025-07-19 13:48:13 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004286', branchId=775292771207004291, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:13 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004286 branchId = 775292771207004291
2025-07-19 13:48:13 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004286', branchId=775292771207004293, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:13 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004286 branchId = 775292771207004293
2025-07-19 13:48:13 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004286', branchId=775292771207004296, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:13 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004286 branchId = 775292771207004296
2025-07-19 13:48:13 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004286', branchId=775292771207004298, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:13 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004286 branchId = 775292771207004298
2025-07-19 13:48:13 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004286', branchId=775292771207004301, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:13 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004286 branchId = 775292771207004301
2025-07-19 13:48:13 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004286', branchId=775292771207004303, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:13 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004286 branchId = 775292771207004303
2025-07-19 13:48:13 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004286', branchId=775292771207004306, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:48:13 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004286 branchId = 775292771207004306
2025-07-19 13:48:13 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004286', branchId=775292771207004309, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:48:13 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004286 branchId = 775292771207004309
2025-07-19 13:48:14 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004286.
2025-07-19 13:54:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:15 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='null', extraData='null', resultCode=Failed, msg='TransactionException[begin global request failed. xid=null, msg=HikariPool-1 - Connection is not available, request timed out after 10003ms.]'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:25 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:25 [ServerHandlerThread_1_23_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:775292771207004313
2025-07-19 13:54:25 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004313', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:25 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004313', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946446424748118018', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:26 [ForkJoinPool.commonPool-worker-8] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004313, branchId = 775292771207004315, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946446424748118018
2025-07-19 13:54:26 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004315, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:26 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004313', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:26 [ForkJoinPool.commonPool-worker-8] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004313, branchId = 775292771207004318, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 13:54:26 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004318, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:27 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004313', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:27 [ForkJoinPool.commonPool-worker-8] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004313, branchId = 775292771207004320, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:54:27 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004320, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:27 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004313', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946448529596665857', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:27 [ForkJoinPool.commonPool-worker-8] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004313, branchId = 775292771207004323, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946448529596665857
2025-07-19 13:54:27 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004323, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:28 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004313', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946448531727372289', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:28 [ForkJoinPool.commonPool-worker-8] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004313, branchId = 775292771207004325, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946448531727372289
2025-07-19 13:54:28 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004325, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:28 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004313', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946446944917274626,1946446947182198785,1946446949161910273,1946446951355531265', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:54:28 [ForkJoinPool.commonPool-worker-8] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004313, branchId = 775292771207004328, resourceId = ****************************************** ,lockKeys = todo_info:1946446944917274626,1946446947182198785,1946446949161910273,1946446951355531265
2025-07-19 13:54:28 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004328, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:54:28 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004313', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:29 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:29 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004313', branchId=775292771207004315, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:29 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004313 branchId = 775292771207004315
2025-07-19 13:54:29 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004313', branchId=775292771207004318, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:29 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004313 branchId = 775292771207004318
2025-07-19 13:54:29 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004313', branchId=775292771207004320, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:29 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004313 branchId = 775292771207004320
2025-07-19 13:54:29 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004313', branchId=775292771207004323, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:29 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004313 branchId = 775292771207004323
2025-07-19 13:54:29 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004313', branchId=775292771207004325, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:54:29 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004313 branchId = 775292771207004325
2025-07-19 13:54:29 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004313', branchId=775292771207004328, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:54:29 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004313 branchId = 775292771207004328
2025-07-19 13:54:29 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004313.
2025-07-19 13:56:32 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:56:32 [ServerHandlerThread_1_31_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:775292771207004331
2025-07-19 13:56:32 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004331', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:56:32 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004331', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946448529596665857', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:56:32 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004331, branchId = 775292771207004334, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946448529596665857
2025-07-19 13:56:32 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004334, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:56:33 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004331', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:56:33 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004331, branchId = 775292771207004336, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:56:33 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004336, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:56:33 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004331', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946449057881837570', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:56:33 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004331, branchId = 775292771207004339, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946449057881837570
2025-07-19 13:56:33 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004339, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:56:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004331', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946449060234805250', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:56:34 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004331, branchId = 775292771207004341, resourceId = ****************************************** ,lockKeys = todo_info:1946449060234805250
2025-07-19 13:56:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004341, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:56:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004331', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946449062592004098', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:56:34 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004331, branchId = 775292771207004344, resourceId = ****************************************** ,lockKeys = todo_info:1946449062592004098
2025-07-19 13:56:34 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004344, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:56:35 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004331', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946449064299085825', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:56:35 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004331, branchId = 775292771207004346, resourceId = ****************************************** ,lockKeys = todo_info:1946449064299085825
2025-07-19 13:56:35 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004346, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:56:35 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004331', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:56:35 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='processWorkorder(java.lang.Long, java.lang.String, java.util.List)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:18 [ServerHandlerThread_1_39_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: processWorkorder(java.lang.Long, java.lang.String, java.util.List),timeout:60000,xid:*************:8091:775292771207004349
2025-07-19 13:57:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004349', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004349', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:19 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004349, branchId = 775292771207004351, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 13:57:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004351, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004349', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946449251390246914', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:19 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004349, branchId = 775292771207004354, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946449251390246914
2025-07-19 13:57:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004354, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004349', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:20 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004349, branchId = 775292771207004356, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:57:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004356, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004349', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946449060234805250,1946449062592004098,1946449064299085825', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:57:20 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004349, branchId = 775292771207004359, resourceId = ****************************************** ,lockKeys = todo_info:1946449060234805250,1946449062592004098,1946449064299085825
2025-07-19 13:57:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004359, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:57:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004349', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004349', branchId=775292771207004351, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:21 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004349 branchId = 775292771207004351
2025-07-19 13:57:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004349', branchId=775292771207004354, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:21 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004349 branchId = 775292771207004354
2025-07-19 13:57:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004349', branchId=775292771207004356, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:21 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004349 branchId = 775292771207004356
2025-07-19 13:57:21 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004349', branchId=775292771207004359, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 13:57:21 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004349 branchId = 775292771207004359
2025-07-19 13:57:21 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004349.
2025-07-19 13:57:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:38 [ServerHandlerThread_1_45_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:775292771207004362
2025-07-19 13:57:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004362', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004362', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946449251390246914', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:38 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004362, branchId = 775292771207004364, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946449251390246914
2025-07-19 13:57:38 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004364, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:39 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004362', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946403794622382082', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:39 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004362, branchId = 775292771207004366, resourceId = ******************************************* ,lockKeys = usd_workorder:1946403794622382082
2025-07-19 13:57:39 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004366, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:39 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004362', branchType=AT, resourceId='*******************************************', lockKey='usd_fee:1946449335167275010', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:39 [ForkJoinPool.commonPool-worker-9] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004362, branchId = 775292771207004369, resourceId = ******************************************* ,lockKeys = usd_fee:1946449335167275010
2025-07-19 13:57:39 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004369, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:39 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004362', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004362', branchId=775292771207004364, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:40 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004362 branchId = 775292771207004364
2025-07-19 13:57:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004362', branchId=775292771207004366, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:40 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004362 branchId = 775292771207004366
2025-07-19 13:57:40 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004362', branchId=775292771207004369, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 13:57:40 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004362 branchId = 775292771207004369
2025-07-19 13:57:40 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004362.
2025-07-19 14:02:01 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8987 to server channel inactive.
2025-07-19 14:02:01 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa6abb274, L:/*************:8091 ! R:/*************:8987]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8987', channel=[id: 0xa6abb274, L:/*************:8091 ! R:/*************:8987], resourceSets=null}
2025-07-19 14:02:01 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8995 to server channel inactive.
2025-07-19 14:02:01 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x29f1445a, L:/*************:8091 ! R:/*************:8995]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8995', channel=[id: 0x29f1445a, L:/*************:8091 ! R:/*************:8995], resourceSets=[]}
2025-07-19 14:02:16 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752904935806
timestamp=1752904935806
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x378a1b5e, L:/*************:8091 - R:/*************:10389],client version:1.7.1
2025-07-19 14:02:19 [ServerHandlerThread_1_50_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x2051b530, L:/*************:8091 - R:/*************:10395],client version:1.7.1
2025-07-19 14:23:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:23:56 [ServerHandlerThread_1_1_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: submitWorkorderWithDetail(com.ym.salary.domain.bo.UsdWorkorderWithDetailBo),timeout:60000,xid:*************:8091:775292771207004372
2025-07-19 14:23:56 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004372', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:23:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004372', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:23:57 [ForkJoinPool.commonPool-worker-10] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004372, branchId = 775292771207004375, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 14:23:57 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004375, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:23:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004372', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946455957600002050', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:23:58 [ForkJoinPool.commonPool-worker-10] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004372, branchId = 775292771207004378, resourceId = ******************************************* ,lockKeys = usd_workorder:1946455957600002050
2025-07-19 14:23:58 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004378, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:23:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004372', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946455960372436993', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:23:59 [ForkJoinPool.commonPool-worker-10] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004372, branchId = 775292771207004381, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946455960372436993
2025-07-19 14:23:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004381, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:23:59 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004372', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946455963115511810', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:00 [ForkJoinPool.commonPool-worker-10] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004372, branchId = 775292771207004383, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946455963115511810
2025-07-19 14:24:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004383, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004372', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946455965745340417', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:00 [ForkJoinPool.commonPool-worker-10] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004372, branchId = 775292771207004386, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946455965745340417
2025-07-19 14:24:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004386, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:00 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004372', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder_detail:1946455967825715201', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:01 [ForkJoinPool.commonPool-worker-10] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004372, branchId = 775292771207004388, resourceId = ******************************************* ,lockKeys = usd_workorder_detail:1946455967825715201
2025-07-19 14:24:01 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004388, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:01 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004372', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004372', branchId=775292771207004375, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:02 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004372 branchId = 775292771207004375
2025-07-19 14:24:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004372', branchId=775292771207004378, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:02 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004372 branchId = 775292771207004378
2025-07-19 14:24:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004372', branchId=775292771207004381, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:02 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004372 branchId = 775292771207004381
2025-07-19 14:24:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004372', branchId=775292771207004383, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:02 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004372 branchId = 775292771207004383
2025-07-19 14:24:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004372', branchId=775292771207004386, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:02 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004372 branchId = 775292771207004386
2025-07-19 14:24:02 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004372', branchId=775292771207004388, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:24:02 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004372 branchId = 775292771207004388
2025-07-19 14:24:02 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004372.
2025-07-19 14:26:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:03 [ServerHandlerThread_1_9_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:775292771207004392
2025-07-19 14:26:03 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004392', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004392', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946455960372436993', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:04 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004392, branchId = 775292771207004394, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946455960372436993
2025-07-19 14:26:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004394, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004392', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946455957600002050', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:04 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004392, branchId = 775292771207004397, resourceId = ******************************************* ,lockKeys = usd_workorder:1946455957600002050
2025-07-19 14:26:04 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004397, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004392', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946456489337085954', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:05 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004392, branchId = 775292771207004400, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946456489337085954
2025-07-19 14:26:05 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004400, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:08 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004392', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946456504394596353', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 14:26:09 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004392, branchId = 775292771207004404, resourceId = ****************************************** ,lockKeys = todo_info:1946456504394596353
2025-07-19 14:26:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004404, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 14:26:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004392', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946456507053785090', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 14:26:09 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004392, branchId = 775292771207004408, resourceId = ****************************************** ,lockKeys = todo_info:1946456507053785090
2025-07-19 14:26:09 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004408, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 14:26:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004392', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946456509289349122', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 14:26:10 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004392, branchId = 775292771207004410, resourceId = ****************************************** ,lockKeys = todo_info:1946456509289349122
2025-07-19 14:26:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004410, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 14:26:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004392', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:10 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='processWorkorder(java.lang.Long, java.lang.String, java.util.List)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:48 [ServerHandlerThread_1_17_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: processWorkorder(java.lang.Long, java.lang.String, java.util.List),timeout:60000,xid:*************:8091:775292771207004413
2025-07-19 14:26:48 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004413', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004413', branchType=AT, resourceId='*******************************************', lockKey='usd_config:1934273477204611073', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:49 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004413, branchId = 775292771207004415, resourceId = ******************************************* ,lockKeys = usd_config:1934273477204611073
2025-07-19 14:26:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004415, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004413', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946456675769704449', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:49 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004413, branchId = 775292771207004418, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946456675769704449
2025-07-19 14:26:49 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004418, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004413', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946455957600002050', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:50 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004413, branchId = 775292771207004420, resourceId = ******************************************* ,lockKeys = usd_workorder:1946455957600002050
2025-07-19 14:26:50 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004420, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004413', branchType=AT, resourceId='******************************************', lockKey='todo_info:1946456504394596353,1946456507053785090,1946456509289349122', applicationData='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 14:26:51 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004413, branchId = 775292771207004423, resourceId = ****************************************** ,lockKeys = todo_info:1946456504394596353,1946456507053785090,1946456509289349122
2025-07-19 14:26:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004423, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-system-group
2025-07-19 14:26:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004413', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:26:51 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalBeginRequest{transactionName='pass(java.lang.Long, java.lang.String, java.lang.Long)', timeout=60000}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:17 [ServerHandlerThread_1_23_500] INFO  i.s.s.coordinator.DefaultCoordinator - Begin new global transaction applicationId: yumeng-salary-settlement,transactionServiceGroup: yumeng-salary-settlement-group, transactionName: pass(java.lang.Long, java.lang.String, java.lang.Long),timeout:60000,xid:*************:8091:775292771207004426
2025-07-19 14:27:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalBeginResponse{xid='*************:8091:775292771207004426', extraData='null', resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004426', branchType=AT, resourceId='*******************************************', lockKey='auditing_workorder_info:1946456675769704449', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:17 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004426, branchId = 775292771207004429, resourceId = ******************************************* ,lockKeys = auditing_workorder_info:1946456675769704449
2025-07-19 14:27:17 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004429, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004426', branchType=AT, resourceId='*******************************************', lockKey='usd_workorder:1946455957600002050', applicationData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:18 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004426, branchId = 775292771207004431, resourceId = ******************************************* ,lockKeys = usd_workorder:1946455957600002050
2025-07-19 14:27:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004431, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004426', branchType=AT, resourceId='*******************************************', lockKey='usd_fee:1946456796641157122', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:18 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004426, branchId = 775292771207004434, resourceId = ******************************************* ,lockKeys = usd_fee:1946456796641157122
2025-07-19 14:27:18 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004434, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004426', branchType=AT, resourceId='*******************************************', lockKey='usd_fee:1946456798771863554', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:19 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004426, branchId = 775292771207004436, resourceId = ******************************************* ,lockKeys = usd_fee:1946456798771863554
2025-07-19 14:27:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004436, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[merged]: BranchRegisterRequest{xid='*************:8091:775292771207004426', branchType=AT, resourceId='*******************************************', lockKey='usd_fee:1946456800537665537', applicationData='{"skipCheckLock":true}'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:19 [ForkJoinPool.commonPool-worker-11] INFO  i.s.server.coordinator.AbstractCore - Register branch successfully, xid = *************:8091:775292771207004426, branchId = 775292771207004439, resourceId = ******************************************* ,lockKeys = usd_fee:1946456800537665537
2025-07-19 14:27:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[merged]: BranchRegisterResponse{branchId=775292771207004439, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: GlobalCommitRequest{xid='*************:8091:775292771207004426', extraData='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:19 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - result msg[single]: GlobalCommitResponse{globalStatus=Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004426', branchId=775292771207004429, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:20 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004426 branchId = 775292771207004429
2025-07-19 14:27:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004426', branchId=775292771207004431, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:20 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004426 branchId = 775292771207004431
2025-07-19 14:27:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004426', branchId=775292771207004434, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:20 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004426 branchId = 775292771207004434
2025-07-19 14:27:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004426', branchId=775292771207004436, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:20 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004426 branchId = 775292771207004436
2025-07-19 14:27:20 [batchLoggerPrint_1_1] INFO  i.s.c.r.p.server.BatchLogHandler - receive msg[single]: BranchCommitResponse{xid='*************:8091:775292771207004426', branchId=775292771207004439, branchStatus=PhaseTwo_Committed, resultCode=Success, msg='null'}, clientIp: *************, vgroup: yumeng-salary-settlement-group
2025-07-19 14:27:20 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Commit branch transaction successfully, xid = *************:8091:775292771207004426 branchId = 775292771207004439
2025-07-19 14:27:20 [AsyncCommitting_1_1] INFO  i.s.server.coordinator.DefaultCore - Committing global transaction is successfully done, xid = *************:8091:775292771207004426.
2025-07-19 15:00:08 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10389 to server channel inactive.
2025-07-19 15:00:08 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10395 to server channel inactive.
2025-07-19 15:00:08 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x378a1b5e, L:/*************:8091 ! R:/*************:10389]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10389', channel=[id: 0x378a1b5e, L:/*************:8091 ! R:/*************:10389], resourceSets=null}
2025-07-19 15:00:08 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2051b530, L:/*************:8091 ! R:/*************:10395]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10395', channel=[id: 0x2051b530, L:/*************:8091 ! R:/*************:10395], resourceSets=[]}
2025-07-19 15:00:23 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752908423381
timestamp=1752908423381
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x43e20146, L:/*************:8091 - R:/*************:3204],client version:1.7.1
2025-07-19 15:00:27 [ServerHandlerThread_1_30_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x8bf1264e, L:/*************:8091 - R:/*************:3265],client version:1.7.1
2025-07-19 15:17:17 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5165 to server channel inactive.
2025-07-19 15:17:17 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5163 to server channel inactive.
2025-07-19 15:17:17 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7926516c, L:/*************:8091 ! R:/*************:5163]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:5163', channel=[id: 0x7926516c, L:/*************:8091 ! R:/*************:5163], resourceSets=null}
2025-07-19 15:17:17 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5db6a1cd, L:/*************:8091 ! R:/*************:5165]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:5165', channel=[id: 0x5db6a1cd, L:/*************:8091 ! R:/*************:5165], resourceSets=[]}
2025-07-19 15:17:34 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752909454426
timestamp=1752909454426
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x53bfb340, L:/*************:8091 - R:/*************:7520],client version:1.7.1
2025-07-19 15:17:38 [ServerHandlerThread_1_31_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x7376900e, L:/*************:8091 - R:/*************:7531],client version:1.7.1
2025-07-19 16:40:53 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-19 16:44:56 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-19 16:44:56 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-19 16:44:56 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 16:44:56 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 16:44:57 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-19 16:44:57 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-19 16:44:57 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-19 16:44:57 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-19 16:44:57 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 15112 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-19 16:44:57 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-19 16:44:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-19 16:44:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-19 16:44:59 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 16:44:59 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-19 16:44:59 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-19 16:44:59 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1671 ms
2025-07-19 16:44:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-19 16:44:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-19 16:45:00 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-19 16:45:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@72d7afff, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5c41f6a0, org.springframework.security.web.context.SecurityContextPersistenceFilter@4d7f9b33, org.springframework.security.web.header.HeaderWriterFilter@1af6d656, org.springframework.security.web.authentication.logout.LogoutFilter@1ae23815, io.seata.console.filter.JwtAuthenticationTokenFilter@7b43f95c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@608c7b5b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@25f73119, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@311453ab, org.springframework.security.web.session.SessionManagementFilter@3b11deb6, org.springframework.security.web.access.ExceptionTranslationFilter@47f416d0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7b6b99c5]
2025-07-19 16:45:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-19 16:45:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-19 16:45:00 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 5.123 seconds (JVM running for 6.197)
2025-07-19 16:45:00 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-19 16:45:00 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-19 16:45:01 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-19 16:45:01 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-19 16:45:01 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-19 16:45:01 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-19 16:45:01 [main] INFO  io.seata.server.ServerRunner - seata server started in 779 millSeconds
2025-07-19 16:45:08 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752914708009
timestamp=1752914708009
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xbba4fc9f, L:/*************:8091 - R:/*************:11609],client version:1.7.1
2025-07-19 16:45:12 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752914711912
timestamp=1752914711912
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x2d0f02d5, L:/*************:8091 - R:/*************:11624],client version:1.7.1
2025-07-19 16:45:16 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xdfa5d6d2, L:/*************:8091 - R:/*************:11641],client version:1.7.1
2025-07-19 16:45:21 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752914721600
timestamp=1752914721600
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x27a65557, L:/*************:8091 - R:/*************:11690],client version:1.7.1
2025-07-19 16:45:25 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x49575c52, L:/*************:8091 - R:/*************:11702],client version:1.7.1
2025-07-19 16:45:26 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752914726198
timestamp=1752914726198
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x796749fc, L:/*************:8091 - R:/*************:11708],client version:1.7.1
2025-07-19 16:45:29 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xdfd6f739, L:/*************:8091 - R:/*************:11744],client version:1.7.1
2025-07-19 16:46:08 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x83bca442, L:/*************:8091 - R:/*************:11871],client version:1.7.1
2025-07-19 16:53:14 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11708 to server channel inactive.
2025-07-19 16:53:14 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11744 to server channel inactive.
2025-07-19 16:53:14 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x796749fc, L:/*************:8091 ! R:/*************:11708]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11708', channel=[id: 0x796749fc, L:/*************:8091 ! R:/*************:11708], resourceSets=null}
2025-07-19 16:53:14 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdfd6f739, L:/*************:8091 ! R:/*************:11744]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11744', channel=[id: 0xdfd6f739, L:/*************:8091 ! R:/*************:11744], resourceSets=[]}
2025-07-19 16:53:27 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752915206929
timestamp=1752915206929
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x668f5910, L:/*************:8091 - R:/*************:13262],client version:1.7.1
2025-07-19 16:53:31 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x0f0b4f34, L:/*************:8091 - R:/*************:13288],client version:1.7.1
2025-07-19 18:45:49 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11702 to server channel inactive.
2025-07-19 18:45:49 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x49575c52, L:/*************:8091 ! R:/*************:11702]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11702', channel=[id: 0x49575c52, L:/*************:8091 ! R:/*************:11702], resourceSets=[]}
2025-07-19 18:45:50 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11641 to server channel inactive.
2025-07-19 18:45:50 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdfa5d6d2, L:/*************:8091 ! R:/*************:11641]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11641', channel=[id: 0xdfa5d6d2, L:/*************:8091 ! R:/*************:11641], resourceSets=[]}
2025-07-19 18:45:50 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x0f0b4f34, L:/*************:8091 - R:/*************:13288] read idle.
2025-07-19 18:45:50 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13288 to server channel inactive.
2025-07-19 18:45:50 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0f0b4f34, L:/*************:8091 - R:/*************:13288]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13288', channel=[id: 0x0f0b4f34, L:/*************:8091 - R:/*************:13288], resourceSets=[]}
2025-07-19 18:45:50 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x0f0b4f34, L:/*************:8091 - R:/*************:13288]
2025-07-19 18:45:50 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13288 to server channel inactive.
2025-07-19 18:45:50 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0f0b4f34, L:/*************:8091 ! R:/*************:13288]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13288', channel=[id: 0x0f0b4f34, L:/*************:8091 ! R:/*************:13288], resourceSets=[]}
2025-07-19 18:45:50 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xb832bc73, L:/*************:8091 - R:/*************:3026],client version:1.7.1
2025-07-19 18:45:51 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x27a65557, L:/*************:8091 - R:/*************:11690] read idle.
2025-07-19 18:45:51 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11690 to server channel inactive.
2025-07-19 18:45:51 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x27a65557, L:/*************:8091 - R:/*************:11690]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11690', channel=[id: 0x27a65557, L:/*************:8091 - R:/*************:11690], resourceSets=null}
2025-07-19 18:45:51 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x27a65557, L:/*************:8091 - R:/*************:11690]
2025-07-19 18:45:51 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11690 to server channel inactive.
2025-07-19 18:45:51 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x27a65557, L:/*************:8091 ! R:/*************:11690]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11690', channel=[id: 0x27a65557, L:/*************:8091 ! R:/*************:11690], resourceSets=null}
2025-07-19 18:45:51 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13262 to server channel inactive.
2025-07-19 18:45:51 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x668f5910, L:/*************:8091 ! R:/*************:13262]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13262', channel=[id: 0x668f5910, L:/*************:8091 ! R:/*************:13262], resourceSets=null}
2025-07-19 18:45:51 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752921951160
timestamp=1752921951160
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xe997e990, L:/*************:8091 - R:/*************:3044],client version:1.7.1
2025-07-19 18:45:51 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11624 to server channel inactive.
2025-07-19 18:45:51 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2d0f02d5, L:/*************:8091 ! R:/*************:11624]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11624', channel=[id: 0x2d0f02d5, L:/*************:8091 ! R:/*************:11624], resourceSets=null}
2025-07-19 18:45:52 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11609 to server channel inactive.
2025-07-19 18:45:52 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbba4fc9f, L:/*************:8091 ! R:/*************:11609]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11609', channel=[id: 0xbba4fc9f, L:/*************:8091 ! R:/*************:11609], resourceSets=null}
2025-07-19 18:45:52 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11871 to server channel inactive.
2025-07-19 18:45:52 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x83bca442, L:/*************:8091 ! R:/*************:11871]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11871', channel=[id: 0x83bca442, L:/*************:8091 ! R:/*************:11871], resourceSets=null}
2025-07-19 18:45:53 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752921953565
timestamp=1752921953565
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x12209f59, L:/*************:8091 - R:/*************:3307],client version:1.7.1
2025-07-19 18:45:54 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xc5bbdf08, L:/*************:8091 - R:/*************:3309],client version:1.7.1
2025-07-19 18:45:54 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752921954585
timestamp=1752921954585
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x2160bcf3, L:/*************:8091 - R:/*************:3315],client version:1.7.1
2025-07-19 18:45:55 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xc01c3757, L:/*************:8091 - R:/*************:3318],client version:1.7.1
2025-07-19 18:45:58 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752921958566
timestamp=1752921958566
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x6e4e977c, L:/*************:8091 - R:/*************:3333],client version:1.7.1
2025-07-19 18:45:58 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xc080b141, L:/*************:8091 - R:/*************:3334],client version:1.7.1
2025-07-19 18:46:14 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
