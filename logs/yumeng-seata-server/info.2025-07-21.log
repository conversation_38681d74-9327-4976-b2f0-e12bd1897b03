2025-07-21 08:32:06 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-21 08:32:06 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-21 08:32:06 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-21 08:32:06 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-21 08:32:07 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 21236 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-21 08:32:07 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-21 08:32:10 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-21 08:32:10 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-21 08:32:10 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-21 08:32:10 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-21 08:32:10 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-21 08:32:10 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2986 ms
2025-07-21 08:32:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-21 08:32:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-21 08:32:12 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-21 08:32:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@43b7bd5a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@37fd496a, org.springframework.security.web.context.SecurityContextPersistenceFilter@73b10975, org.springframework.security.web.header.HeaderWriterFilter@63e648a0, org.springframework.security.web.authentication.logout.LogoutFilter@1a34772e, io.seata.console.filter.JwtAuthenticationTokenFilter@6c4e486e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@69b80603, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@71a7cf7c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@27ac3b6d, org.springframework.security.web.session.SessionManagementFilter@1fdd5517, org.springframework.security.web.access.ExceptionTranslationFilter@38834000, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5d10df04]
2025-07-21 08:32:12 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-21 08:32:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-21 08:32:12 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 7.465 seconds (JVM running for 8.397)
2025-07-21 08:32:13 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-21 08:32:14 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-21 08:32:15 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-21 08:32:15 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-21 08:32:15 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-21 08:32:16 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-21 08:32:30 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-21 08:32:30 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-21 08:32:30 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-21 08:32:30 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-21 08:32:31 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-21 08:32:31 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-21 08:32:31 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-21 08:32:31 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-21 08:32:31 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 2284 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-21 08:32:31 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-21 08:32:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-21 08:32:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-21 08:32:32 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-21 08:32:32 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-21 08:32:33 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-21 08:32:33 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1262 ms
2025-07-21 08:32:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-21 08:32:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-21 08:32:33 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-21 08:32:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6802c10e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@341889a1, org.springframework.security.web.context.SecurityContextPersistenceFilter@24ce5d4c, org.springframework.security.web.header.HeaderWriterFilter@496e0f9d, org.springframework.security.web.authentication.logout.LogoutFilter@64de9fa4, io.seata.console.filter.JwtAuthenticationTokenFilter@29693b1d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7369208e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1d988297, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3a225534, org.springframework.security.web.session.SessionManagementFilter@57fae911, org.springframework.security.web.access.ExceptionTranslationFilter@68fc1e7f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@626ff077]
2025-07-21 08:32:33 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-21 08:32:33 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-21 08:32:33 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.964 seconds (JVM running for 5.753)
2025-07-21 08:32:34 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-21 08:32:34 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-21 08:32:34 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-21 08:32:34 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-21 08:32:34 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-21 08:32:34 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-21 08:32:34 [main] INFO  io.seata.server.ServerRunner - seata server started in 596 millSeconds
2025-07-21 08:33:35 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753058014984
timestamp=1753058014984
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x94c94fcf, L:/*************:8091 - R:/*************:2910],client version:1.7.1
2025-07-21 08:34:35 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x3bc47398, L:/*************:8091 - R:/*************:3122],client version:1.7.1
2025-07-21 08:37:37 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753058256975
timestamp=1753058256975
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xe85119c0, L:/*************:8091 - R:/*************:3576],client version:1.7.1
2025-07-21 08:37:40 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x2480d242, L:/*************:8091 - R:/*************:3597],client version:1.7.1
2025-07-21 08:37:42 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753058262291
timestamp=1753058262291
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x7ae6c1d3, L:/*************:8091 - R:/*************:3612],client version:1.7.1
2025-07-21 08:37:46 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753058265785
timestamp=1753058265785
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xc98d880f, L:/*************:8091 - R:/*************:3643],client version:1.7.1
2025-07-21 08:37:47 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x7d8ed1ad, L:/*************:8091 - R:/*************:3651],client version:1.7.1
2025-07-21 08:37:49 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x9c08ef35, L:/*************:8091 - R:/*************:3671],client version:1.7.1
2025-07-21 08:37:57 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3643 to server channel inactive.
2025-07-21 08:37:57 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3671 to server channel inactive.
2025-07-21 08:37:57 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc98d880f, L:/*************:8091 ! R:/*************:3643]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:3643', channel=[id: 0xc98d880f, L:/*************:8091 ! R:/*************:3643], resourceSets=null}
2025-07-21 08:37:57 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9c08ef35, L:/*************:8091 ! R:/*************:3671]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:3671', channel=[id: 0x9c08ef35, L:/*************:8091 ! R:/*************:3671], resourceSets=[]}
2025-07-21 08:40:59 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753058459632
timestamp=1753058459632
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa08f1a82, L:/*************:8091 - R:/*************:4224],client version:1.7.1
2025-07-21 08:41:03 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xb56741ed, L:/*************:8091 - R:/*************:4238],client version:1.7.1
2025-07-21 09:16:26 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4238 to server channel inactive.
2025-07-21 09:16:26 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4224 to server channel inactive.
2025-07-21 09:16:26 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa08f1a82, L:/*************:8091 ! R:/*************:4224]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4224', channel=[id: 0xa08f1a82, L:/*************:8091 ! R:/*************:4224], resourceSets=null}
2025-07-21 09:16:26 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb56741ed, L:/*************:8091 ! R:/*************:4238]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4238', channel=[id: 0xb56741ed, L:/*************:8091 ! R:/*************:4238], resourceSets=[]}
2025-07-21 09:16:43 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753060603449
timestamp=1753060603449
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x00f7206f, L:/*************:8091 - R:/*************:11382],client version:1.7.1
2025-07-21 09:16:47 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x13d1991f, L:/*************:8091 - R:/*************:11395],client version:1.7.1
2025-07-21 09:21:13 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11395 to server channel inactive.
2025-07-21 09:21:13 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11382 to server channel inactive.
2025-07-21 09:21:13 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x00f7206f, L:/*************:8091 ! R:/*************:11382]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11382', channel=[id: 0x00f7206f, L:/*************:8091 ! R:/*************:11382], resourceSets=null}
2025-07-21 09:21:13 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x13d1991f, L:/*************:8091 ! R:/*************:11395]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11395', channel=[id: 0x13d1991f, L:/*************:8091 ! R:/*************:11395], resourceSets=[]}
2025-07-21 09:21:22 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753060882056
timestamp=1753060882056
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xb3acd575, L:/*************:8091 - R:/*************:12452],client version:1.7.1
2025-07-21 09:21:24 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x76d6af5c, L:/*************:8091 - R:/*************:12471],client version:1.7.1
2025-07-21 09:24:12 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12452 to server channel inactive.
2025-07-21 09:24:12 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb3acd575, L:/*************:8091 ! R:/*************:12452]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12452', channel=[id: 0xb3acd575, L:/*************:8091 ! R:/*************:12452], resourceSets=null}
2025-07-21 09:24:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12471 to server channel inactive.
2025-07-21 09:24:12 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x76d6af5c, L:/*************:8091 ! R:/*************:12471]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12471', channel=[id: 0x76d6af5c, L:/*************:8091 ! R:/*************:12471], resourceSets=[]}
2025-07-21 09:24:31 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753061071613
timestamp=1753061071613
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x4e974476, L:/*************:8091 - R:/*************:13175],client version:1.7.1
2025-07-21 09:24:35 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xd14dac6c, L:/*************:8091 - R:/*************:13187],client version:1.7.1
2025-07-21 09:24:35 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13175 to server channel inactive.
2025-07-21 09:24:35 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4e974476, L:/*************:8091 ! R:/*************:13175]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13175', channel=[id: 0x4e974476, L:/*************:8091 ! R:/*************:13175], resourceSets=null}
2025-07-21 09:24:35 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13187 to server channel inactive.
2025-07-21 09:24:35 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd14dac6c, L:/*************:8091 ! R:/*************:13187]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13187', channel=[id: 0xd14dac6c, L:/*************:8091 ! R:/*************:13187], resourceSets=[]}
2025-07-21 09:25:34 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753061134234
timestamp=1753061134234
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x88e6fa1a, L:/*************:8091 - R:/*************:13380],client version:1.7.1
2025-07-21 09:25:38 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x1adf3e98, L:/*************:8091 - R:/*************:13394],client version:1.7.1
2025-07-21 09:25:39 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13380 to server channel inactive.
2025-07-21 09:25:39 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x88e6fa1a, L:/*************:8091 ! R:/*************:13380]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13380', channel=[id: 0x88e6fa1a, L:/*************:8091 ! R:/*************:13380], resourceSets=null}
2025-07-21 09:25:39 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13394 to server channel inactive.
2025-07-21 09:25:39 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1adf3e98, L:/*************:8091 ! R:/*************:13394]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13394', channel=[id: 0x1adf3e98, L:/*************:8091 ! R:/*************:13394], resourceSets=[]}
2025-07-21 09:27:12 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3612 to server channel inactive.
2025-07-21 09:27:12 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7ae6c1d3, L:/*************:8091 ! R:/*************:3612]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:3612', channel=[id: 0x7ae6c1d3, L:/*************:8091 ! R:/*************:3612], resourceSets=null}
2025-07-21 09:27:12 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3651 to server channel inactive.
2025-07-21 09:27:12 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7d8ed1ad, L:/*************:8091 ! R:/*************:3651]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:3651', channel=[id: 0x7d8ed1ad, L:/*************:8091 ! R:/*************:3651], resourceSets=[]}
2025-07-21 09:27:23 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753061243655
timestamp=1753061243655
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x47a9def0, L:/*************:8091 - R:/*************:13750],client version:1.7.1
2025-07-21 09:27:28 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x5297a714, L:/*************:8091 - R:/*************:13773],client version:1.7.1
2025-07-21 09:28:13 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753061293778
timestamp=1753061293778
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xc3b4c0c0, L:/*************:8091 - R:/*************:13946],client version:1.7.1
2025-07-21 09:28:16 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xbb502e2b, L:/*************:8091 - R:/*************:13958],client version:1.7.1
2025-07-21 09:28:16 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13946 to server channel inactive.
2025-07-21 09:28:16 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc3b4c0c0, L:/*************:8091 ! R:/*************:13946]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13946', channel=[id: 0xc3b4c0c0, L:/*************:8091 ! R:/*************:13946], resourceSets=null}
2025-07-21 09:28:16 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13958 to server channel inactive.
2025-07-21 09:28:16 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbb502e2b, L:/*************:8091 ! R:/*************:13958]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13958', channel=[id: 0xbb502e2b, L:/*************:8091 ! R:/*************:13958], resourceSets=[]}
2025-07-21 09:28:31 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-21 09:32:50 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-21 09:32:50 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-21 09:32:50 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-21 09:32:50 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-21 09:32:51 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-21 09:32:51 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-21 09:32:51 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-21 09:32:51 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-21 09:32:51 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 33656 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-21 09:32:51 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-21 09:32:53 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-21 09:32:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-21 09:32:53 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-21 09:32:53 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-21 09:32:53 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-21 09:32:53 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1702 ms
2025-07-21 09:32:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-21 09:32:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-21 09:32:53 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-21 09:32:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2809e38a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@b9c9bf5, org.springframework.security.web.context.SecurityContextPersistenceFilter@1b868ef0, org.springframework.security.web.header.HeaderWriterFilter@70940cb8, org.springframework.security.web.authentication.logout.LogoutFilter@c7a1db6, io.seata.console.filter.JwtAuthenticationTokenFilter@3b399f5a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@16e84d93, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4889fbba, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@16f2d883, org.springframework.security.web.session.SessionManagementFilter@1ae23815, org.springframework.security.web.access.ExceptionTranslationFilter@2f5a092e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b9370cc]
2025-07-21 09:32:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-21 09:32:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-21 09:32:54 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.591 seconds (JVM running for 5.623)
2025-07-21 09:32:54 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-21 09:32:54 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-21 09:32:54 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-21 09:32:54 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-21 09:32:54 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-21 09:32:54 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-21 09:32:54 [main] INFO  io.seata.server.ServerRunner - seata server started in 720 millSeconds
2025-07-21 09:33:02 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753061582278
timestamp=1753061582278
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa9c0d4b7, L:/*************:8091 - R:/*************:1447],client version:1.7.1
2025-07-21 09:33:03 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753061583186
timestamp=1753061583186
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xd2095c9d, L:/*************:8091 - R:/*************:1452],client version:1.7.1
2025-07-21 09:33:06 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x595fb2ee, L:/*************:8091 - R:/*************:1460],client version:1.7.1
2025-07-21 09:33:06 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xe5fe6fc1, L:/*************:8091 - R:/*************:1468],client version:1.7.1
2025-07-21 09:33:13 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1447 to server channel inactive.
2025-07-21 09:33:13 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa9c0d4b7, L:/*************:8091 ! R:/*************:1447]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1447', channel=[id: 0xa9c0d4b7, L:/*************:8091 ! R:/*************:1447], resourceSets=null}
2025-07-21 09:33:13 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1468 to server channel inactive.
2025-07-21 09:33:13 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe5fe6fc1, L:/*************:8091 ! R:/*************:1468]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1468', channel=[id: 0xe5fe6fc1, L:/*************:8091 ! R:/*************:1468], resourceSets=[]}
2025-07-21 09:33:46 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753061626467
timestamp=1753061626467
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xb228b3f0, L:/*************:8091 - R:/*************:1605],client version:1.7.1
2025-07-21 09:33:49 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753061629096
timestamp=1753061629096
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x70a521c0, L:/*************:8091 - R:/*************:1630],client version:1.7.1
2025-07-21 09:33:51 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x79db108a, L:/*************:8091 - R:/*************:1638],client version:1.7.1
2025-07-21 09:33:52 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753061632794
timestamp=1753061632794
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xfe232d77, L:/*************:8091 - R:/*************:1652],client version:1.7.1
2025-07-21 09:33:55 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x56ecb8be, L:/*************:8091 - R:/*************:1690],client version:1.7.1
2025-07-21 09:34:49 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x0daefa0a, L:/*************:8091 - R:/*************:1890],client version:1.7.1
2025-07-21 09:46:26 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1638 to server channel inactive.
2025-07-21 09:46:26 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1605 to server channel inactive.
2025-07-21 09:46:26 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x79db108a, L:/*************:8091 ! R:/*************:1638]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1638', channel=[id: 0x79db108a, L:/*************:8091 ! R:/*************:1638], resourceSets=[]}
2025-07-21 09:46:26 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb228b3f0, L:/*************:8091 ! R:/*************:1605]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1605', channel=[id: 0xb228b3f0, L:/*************:8091 ! R:/*************:1605], resourceSets=null}
2025-07-21 09:46:37 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753062397099
timestamp=1753062397099
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x5f6b102c, L:/*************:8091 - R:/*************:4005],client version:1.7.1
2025-07-21 09:46:41 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x3ccf2d4a, L:/*************:8091 - R:/*************:4013],client version:1.7.1
2025-07-21 10:31:17 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4013 to server channel inactive.
2025-07-21 10:31:17 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4005 to server channel inactive.
2025-07-21 10:31:17 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5f6b102c, L:/*************:8091 ! R:/*************:4005]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4005', channel=[id: 0x5f6b102c, L:/*************:8091 ! R:/*************:4005], resourceSets=null}
2025-07-21 10:31:17 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3ccf2d4a, L:/*************:8091 ! R:/*************:4013]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4013', channel=[id: 0x3ccf2d4a, L:/*************:8091 ! R:/*************:4013], resourceSets=[]}
2025-07-21 10:41:00 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753065660187
timestamp=1753065660187
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x35bddafc, L:/*************:8091 - R:/*************:1287],client version:1.7.1
2025-07-21 10:41:04 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x1cc30d8b, L:/*************:8091 - R:/*************:1298],client version:1.7.1
2025-07-21 10:46:30 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1287 to server channel inactive.
2025-07-21 10:46:30 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1298 to server channel inactive.
2025-07-21 10:46:30 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x35bddafc, L:/*************:8091 ! R:/*************:1287]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1287', channel=[id: 0x35bddafc, L:/*************:8091 ! R:/*************:1287], resourceSets=null}
2025-07-21 10:46:30 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1cc30d8b, L:/*************:8091 ! R:/*************:1298]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1298', channel=[id: 0x1cc30d8b, L:/*************:8091 ! R:/*************:1298], resourceSets=[]}
2025-07-21 10:46:57 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753066017176
timestamp=1753066017176
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x92405b41, L:/*************:8091 - R:/*************:2464],client version:1.7.1
2025-07-21 10:47:01 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x7b2ebc53, L:/*************:8091 - R:/*************:2486],client version:1.7.1
2025-07-21 11:14:45 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2464 to server channel inactive.
2025-07-21 11:14:45 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2486 to server channel inactive.
2025-07-21 11:14:45 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x92405b41, L:/*************:8091 ! R:/*************:2464]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2464', channel=[id: 0x92405b41, L:/*************:8091 ! R:/*************:2464], resourceSets=null}
2025-07-21 11:14:45 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7b2ebc53, L:/*************:8091 ! R:/*************:2486]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2486', channel=[id: 0x7b2ebc53, L:/*************:8091 ! R:/*************:2486], resourceSets=[]}
2025-07-21 11:23:52 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753068232466
timestamp=1753068232466
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x96b2576b, L:/*************:8091 - R:/*************:8550],client version:1.7.1
2025-07-21 11:23:56 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x90cb21e9, L:/*************:8091 - R:/*************:8574],client version:1.7.1
2025-07-21 13:27:53 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x96b2576b, L:/*************:8091 - R:/*************:8550] read idle.
2025-07-21 13:27:53 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8550 to server channel inactive.
2025-07-21 13:27:53 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x96b2576b, L:/*************:8091 - R:/*************:8550]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8550', channel=[id: 0x96b2576b, L:/*************:8091 - R:/*************:8550], resourceSets=null}
2025-07-21 13:27:53 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x96b2576b, L:/*************:8091 - R:/*************:8550]
2025-07-21 13:27:53 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8550 to server channel inactive.
2025-07-21 13:27:53 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x96b2576b, L:/*************:8091 ! R:/*************:8550]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8550', channel=[id: 0x96b2576b, L:/*************:8091 ! R:/*************:8550], resourceSets=null}
2025-07-21 13:27:53 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8574 to server channel inactive.
2025-07-21 13:27:53 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x90cb21e9, L:/*************:8091 ! R:/*************:8574]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:8574', channel=[id: 0x90cb21e9, L:/*************:8091 ! R:/*************:8574], resourceSets=[]}
2025-07-21 13:27:53 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1690 to server channel inactive.
2025-07-21 13:27:53 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x56ecb8be, L:/*************:8091 ! R:/*************:1690]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:1690', channel=[id: 0x56ecb8be, L:/*************:8091 ! R:/*************:1690], resourceSets=[]}
2025-07-21 13:27:53 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1460 to server channel inactive.
2025-07-21 13:27:53 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x595fb2ee, L:/*************:8091 ! R:/*************:1460]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:1460', channel=[id: 0x595fb2ee, L:/*************:8091 ! R:/*************:1460], resourceSets=[]}
2025-07-21 13:27:54 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753075673955
timestamp=1753075673955
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xcff3ecb8, L:/*************:8091 - R:/*************:10703],client version:1.7.1
2025-07-21 13:27:54 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x9e81b982, L:/*************:8091 - R:/*************:1743],client version:1.7.1
2025-07-21 13:27:54 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x6adf7598, L:/*************:8091 - R:/*************:1760],client version:1.7.1
2025-07-21 13:27:55 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1652 to server channel inactive.
2025-07-21 13:27:55 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfe232d77, L:/*************:8091 ! R:/*************:1652]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:1652', channel=[id: 0xfe232d77, L:/*************:8091 ! R:/*************:1652], resourceSets=null}
2025-07-21 13:27:56 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1452 to server channel inactive.
2025-07-21 13:27:56 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd2095c9d, L:/*************:8091 ! R:/*************:1452]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:1452', channel=[id: 0xd2095c9d, L:/*************:8091 ! R:/*************:1452], resourceSets=null}
2025-07-21 13:27:56 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1890 to server channel inactive.
2025-07-21 13:27:56 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0daefa0a, L:/*************:8091 ! R:/*************:1890]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:1890', channel=[id: 0x0daefa0a, L:/*************:8091 ! R:/*************:1890], resourceSets=null}
2025-07-21 13:27:56 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1630 to server channel inactive.
2025-07-21 13:27:56 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x70a521c0, L:/*************:8091 ! R:/*************:1630]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:1630', channel=[id: 0x70a521c0, L:/*************:8091 ! R:/*************:1630], resourceSets=null}
2025-07-21 13:27:59 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753075679454
timestamp=1753075679454
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xbad7824d, L:/*************:8091 - R:/*************:1840],client version:1.7.1
2025-07-21 13:27:59 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753075679858
timestamp=1753075679858
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x0f2833aa, L:/*************:8091 - R:/*************:1842],client version:1.7.1
2025-07-21 13:28:00 [ServerHandlerThread_1_12_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x7d0586ae, L:/*************:8091 - R:/*************:2521],client version:1.7.1
2025-07-21 13:28:05 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753075685758
timestamp=1753075685758
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x5d4fc9a5, L:/*************:8091 - R:/*************:4625],client version:1.7.1
2025-07-21 13:28:06 [ServerHandlerThread_1_13_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xec4f900b, L:/*************:8091 - R:/*************:4631],client version:1.7.1
2025-07-21 15:46:44 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:10703 to server channel inactive.
2025-07-21 15:46:44 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1743 to server channel inactive.
2025-07-21 15:46:44 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcff3ecb8, L:/*************:8091 ! R:/*************:10703]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:10703', channel=[id: 0xcff3ecb8, L:/*************:8091 ! R:/*************:10703], resourceSets=null}
2025-07-21 15:46:44 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9e81b982, L:/*************:8091 ! R:/*************:1743]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1743', channel=[id: 0x9e81b982, L:/*************:8091 ! R:/*************:1743], resourceSets=[]}
2025-07-21 15:46:55 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753084015159
timestamp=1753084015159
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x99e05084, L:/*************:8091 - R:/*************:11774],client version:1.7.1
2025-07-21 15:46:59 [ServerHandlerThread_1_14_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xad5486c0, L:/*************:8091 - R:/*************:11778],client version:1.7.1
2025-07-21 15:57:53 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11774 to server channel inactive.
2025-07-21 15:57:53 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11778 to server channel inactive.
2025-07-21 15:57:53 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x99e05084, L:/*************:8091 ! R:/*************:11774]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11774', channel=[id: 0x99e05084, L:/*************:8091 ! R:/*************:11774], resourceSets=null}
2025-07-21 15:57:53 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xad5486c0, L:/*************:8091 ! R:/*************:11778]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11778', channel=[id: 0xad5486c0, L:/*************:8091 ! R:/*************:11778], resourceSets=[]}
2025-07-21 15:58:03 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753084683641
timestamp=1753084683641
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x5b2e6c69, L:/*************:8091 - R:/*************:13036],client version:1.7.1
2025-07-21 15:58:07 [ServerHandlerThread_1_15_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xd69b26e0, L:/*************:8091 - R:/*************:13042],client version:1.7.1
2025-07-21 16:12:48 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13042 to server channel inactive.
2025-07-21 16:12:48 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13036 to server channel inactive.
2025-07-21 16:12:48 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5b2e6c69, L:/*************:8091 ! R:/*************:13036]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13036', channel=[id: 0x5b2e6c69, L:/*************:8091 ! R:/*************:13036], resourceSets=null}
2025-07-21 16:12:48 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd69b26e0, L:/*************:8091 ! R:/*************:13042]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13042', channel=[id: 0xd69b26e0, L:/*************:8091 ! R:/*************:13042], resourceSets=[]}
2025-07-21 16:12:59 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753085579300
timestamp=1753085579300
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa10a15ec, L:/*************:8091 - R:/*************:2657],client version:1.7.1
2025-07-21 16:13:02 [ServerHandlerThread_1_16_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xfae37905, L:/*************:8091 - R:/*************:2667],client version:1.7.1
2025-07-21 16:32:01 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2657 to server channel inactive.
2025-07-21 16:32:01 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2667 to server channel inactive.
2025-07-21 16:32:01 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa10a15ec, L:/*************:8091 ! R:/*************:2657]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2657', channel=[id: 0xa10a15ec, L:/*************:8091 ! R:/*************:2657], resourceSets=null}
2025-07-21 16:32:01 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfae37905, L:/*************:8091 ! R:/*************:2667]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2667', channel=[id: 0xfae37905, L:/*************:8091 ! R:/*************:2667], resourceSets=[]}
2025-07-21 16:32:13 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753086733358
timestamp=1753086733358
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xc187d4c5, L:/*************:8091 - R:/*************:4947],client version:1.7.1
2025-07-21 16:32:16 [ServerHandlerThread_1_17_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x45528c07, L:/*************:8091 - R:/*************:4954],client version:1.7.1
2025-07-21 16:59:01 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4947 to server channel inactive.
2025-07-21 16:59:01 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4954 to server channel inactive.
2025-07-21 16:59:01 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc187d4c5, L:/*************:8091 ! R:/*************:4947]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4947', channel=[id: 0xc187d4c5, L:/*************:8091 ! R:/*************:4947], resourceSets=null}
2025-07-21 16:59:01 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x45528c07, L:/*************:8091 ! R:/*************:4954]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4954', channel=[id: 0x45528c07, L:/*************:8091 ! R:/*************:4954], resourceSets=[]}
2025-07-21 16:59:14 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753088354211
timestamp=1753088354211
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x80e014ca, L:/*************:8091 - R:/*************:7965],client version:1.7.1
2025-07-21 16:59:17 [ServerHandlerThread_1_18_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xf5350208, L:/*************:8091 - R:/*************:7971],client version:1.7.1
2025-07-21 18:29:28 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x0f2833aa, L:/*************:8091 - R:/*************:1842] read idle.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1842 to server channel inactive.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xbad7824d, L:/*************:8091 - R:/*************:1840] read idle.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1840 to server channel inactive.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbad7824d, L:/*************:8091 - R:/*************:1840]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:1840', channel=[id: 0xbad7824d, L:/*************:8091 - R:/*************:1840], resourceSets=null}
2025-07-21 18:29:28 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xbad7824d, L:/*************:8091 - R:/*************:1840]
2025-07-21 18:29:28 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1840 to server channel inactive.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbad7824d, L:/*************:8091 ! R:/*************:1840]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:1840', channel=[id: 0xbad7824d, L:/*************:8091 ! R:/*************:1840], resourceSets=null}
2025-07-21 18:29:28 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0f2833aa, L:/*************:8091 - R:/*************:1842]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:1842', channel=[id: 0x0f2833aa, L:/*************:8091 - R:/*************:1842], resourceSets=null}
2025-07-21 18:29:28 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x0f2833aa, L:/*************:8091 - R:/*************:1842]
2025-07-21 18:29:28 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1842 to server channel inactive.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0f2833aa, L:/*************:8091 ! R:/*************:1842]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:1842', channel=[id: 0x0f2833aa, L:/*************:8091 ! R:/*************:1842], resourceSets=null}
2025-07-21 18:29:28 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1760 to server channel inactive.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6adf7598, L:/*************:8091 ! R:/*************:1760]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:1760', channel=[id: 0x6adf7598, L:/*************:8091 ! R:/*************:1760], resourceSets=[]}
2025-07-21 18:29:28 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x7d0586ae, L:/*************:8091 - R:/*************:2521] read idle.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2521 to server channel inactive.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7d0586ae, L:/*************:8091 - R:/*************:2521]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2521', channel=[id: 0x7d0586ae, L:/*************:8091 - R:/*************:2521], resourceSets=[]}
2025-07-21 18:29:28 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x7d0586ae, L:/*************:8091 - R:/*************:2521]
2025-07-21 18:29:28 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2521 to server channel inactive.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7d0586ae, L:/*************:8091 ! R:/*************:2521]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2521', channel=[id: 0x7d0586ae, L:/*************:8091 ! R:/*************:2521], resourceSets=[]}
2025-07-21 18:29:28 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4625 to server channel inactive.
2025-07-21 18:29:28 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5d4fc9a5, L:/*************:8091 ! R:/*************:4625]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:4625', channel=[id: 0x5d4fc9a5, L:/*************:8091 ! R:/*************:4625], resourceSets=null}
2025-07-21 18:29:28 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753093768881
timestamp=1753093768881
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xf7b2fd27, L:/*************:8091 - R:/*************:12448],client version:1.7.1
2025-07-21 18:29:28 [ServerHandlerThread_1_19_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xb1b9be4f, L:/*************:8091 - R:/*************:12447],client version:1.7.1
2025-07-21 18:29:29 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7965 to server channel inactive.
2025-07-21 18:29:29 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x80e014ca, L:/*************:8091 ! R:/*************:7965]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7965', channel=[id: 0x80e014ca, L:/*************:8091 ! R:/*************:7965], resourceSets=null}
2025-07-21 18:29:29 [ServerHandlerThread_1_20_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x3f7401a5, L:/*************:8091 - R:/*************:12466],client version:1.7.1
2025-07-21 18:29:29 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4631 to server channel inactive.
2025-07-21 18:29:29 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xec4f900b, L:/*************:8091 ! R:/*************:4631]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:4631', channel=[id: 0xec4f900b, L:/*************:8091 ! R:/*************:4631], resourceSets=null}
2025-07-21 18:29:30 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753093770827
timestamp=1753093770827
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x348786c4, L:/*************:8091 - R:/*************:12776],client version:1.7.1
2025-07-21 18:29:32 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7971 to server channel inactive.
2025-07-21 18:29:32 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf5350208, L:/*************:8091 ! R:/*************:7971]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7971', channel=[id: 0xf5350208, L:/*************:8091 ! R:/*************:7971], resourceSets=[]}
2025-07-21 18:29:32 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753093772639
timestamp=1753093772639
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xf04dd4b0, L:/*************:8091 - R:/*************:12791],client version:1.7.1
2025-07-21 18:29:33 [ServerHandlerThread_1_21_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x64852d96, L:/*************:8091 - R:/*************:12794],client version:1.7.1
2025-07-21 18:29:36 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753093776264
timestamp=1753093776264
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x8eac5b48, L:/*************:8091 - R:/*************:12817],client version:1.7.1
2025-07-21 18:29:41 [ServerHandlerThread_1_22_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x6a18d51f, L:/*************:8091 - R:/*************:12850],client version:1.7.1
2025-07-21 18:29:53 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12850 to server channel inactive.
2025-07-21 18:29:53 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12791 to server channel inactive.
2025-07-21 18:29:53 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12448 to server channel inactive.
2025-07-21 18:29:53 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6a18d51f, L:/*************:8091 ! R:/*************:12850]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12850', channel=[id: 0x6a18d51f, L:/*************:8091 ! R:/*************:12850], resourceSets=[]}
2025-07-21 18:29:53 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf04dd4b0, L:/*************:8091 ! R:/*************:12791]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:12791', channel=[id: 0xf04dd4b0, L:/*************:8091 ! R:/*************:12791], resourceSets=null}
2025-07-21 18:29:53 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12447 to server channel inactive.
2025-07-21 18:29:53 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12466 to server channel inactive.
2025-07-21 18:29:53 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf7b2fd27, L:/*************:8091 ! R:/*************:12448]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12448', channel=[id: 0xf7b2fd27, L:/*************:8091 ! R:/*************:12448], resourceSets=null}
2025-07-21 18:29:53 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12794 to server channel inactive.
2025-07-21 18:29:53 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3f7401a5, L:/*************:8091 ! R:/*************:12466]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:12466', channel=[id: 0x3f7401a5, L:/*************:8091 ! R:/*************:12466], resourceSets=[]}
2025-07-21 18:29:53 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb1b9be4f, L:/*************:8091 ! R:/*************:12447]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:12447', channel=[id: 0xb1b9be4f, L:/*************:8091 ! R:/*************:12447], resourceSets=[]}
2025-07-21 18:29:53 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x64852d96, L:/*************:8091 ! R:/*************:12794]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:12794', channel=[id: 0x64852d96, L:/*************:8091 ! R:/*************:12794], resourceSets=null}
2025-07-21 18:29:53 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12817 to server channel inactive.
2025-07-21 18:29:53 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8eac5b48, L:/*************:8091 ! R:/*************:12817]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:12817', channel=[id: 0x8eac5b48, L:/*************:8091 ! R:/*************:12817], resourceSets=null}
2025-07-21 18:29:53 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:12776 to server channel inactive.
2025-07-21 18:29:53 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x348786c4, L:/*************:8091 ! R:/*************:12776]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:12776', channel=[id: 0x348786c4, L:/*************:8091 ! R:/*************:12776], resourceSets=null}
2025-07-21 18:30:00 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753093800798
timestamp=1753093800798
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa00dcbdb, L:/*************:8091 - R:/*************:13252],client version:1.7.1
2025-07-21 18:30:01 [ServerHandlerThread_1_23_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x57b88968, L:/*************:8091 - R:/*************:13259],client version:1.7.1
2025-07-21 18:30:02 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1753093802556
timestamp=1753093802556
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xacc5c99d, L:/*************:8091 - R:/*************:13263],client version:1.7.1
2025-07-21 18:30:02 [ServerHandlerThread_1_24_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x2558bf18, L:/*************:8091 - R:/*************:13267],client version:1.7.1
2025-07-21 18:30:06 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753093806253
timestamp=1753093806253
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xa7948ee8, L:/*************:8091 - R:/*************:13282],client version:1.7.1
2025-07-21 18:30:06 [ServerHandlerThread_1_25_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x9efae4ee, L:/*************:8091 - R:/*************:13287],client version:1.7.1
2025-07-21 18:30:06 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1753093806659
timestamp=1753093806659
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xdc13da74, L:/*************:8091 - R:/*************:13288],client version:1.7.1
2025-07-21 18:30:07 [ServerHandlerThread_1_26_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x066f1f12, L:/*************:8091 - R:/*************:13291],client version:1.7.1
2025-07-21 20:31:19 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
