2025-07-18 09:47:00 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 09:47:00 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 09:47:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 09:47:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 09:47:01 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 09:47:01 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 09:47:01 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 09:47:01 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 09:47:01 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 26996 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 09:47:01 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 09:47:03 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 09:47:03 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 09:47:03 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 09:47:03 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 09:47:03 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 09:47:03 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1288 ms
2025-07-18 09:47:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 09:47:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 09:47:03 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 09:47:03 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7668f8fd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@75566f4, org.springframework.security.web.context.SecurityContextPersistenceFilter@5bc40f5d, org.springframework.security.web.header.HeaderWriterFilter@5d08a65c, org.springframework.security.web.authentication.logout.LogoutFilter@36f80ceb, io.seata.console.filter.JwtAuthenticationTokenFilter@6785786d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b2c41f9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@70940cb8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5c261c74, org.springframework.security.web.session.SessionManagementFilter@3c38e2bf, org.springframework.security.web.access.ExceptionTranslationFilter@694f0655, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7602c65d]
2025-07-18 09:47:03 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 09:47:04 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 09:47:04 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.042 seconds (JVM running for 5.179)
2025-07-18 09:47:04 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 09:47:04 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 09:47:04 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 09:47:09 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 09:47:09 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 09:47:09 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 09:47:09 [main] INFO  io.seata.server.ServerRunner - seata server started in 5242 millSeconds
2025-07-18 09:47:17 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752803236858
timestamp=1752803236858
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x1ef17624, L:/*************:8091 - R:/*************:13683],client version:1.7.1
2025-07-18 09:47:19 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x4dc370c9, L:/*************:8091 - R:/*************:13726],client version:1.7.1
2025-07-18 10:07:14 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752804434505
timestamp=1752804434505
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x47e42d24, L:/*************:8091 - R:/*************:11461],client version:1.7.1
2025-07-18 10:07:17 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752804437021
timestamp=1752804437021
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x1a25f1d8, L:/*************:8091 - R:/*************:11470],client version:1.7.1
2025-07-18 10:07:18 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752804437633
timestamp=1752804437633
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x0e3905f6, L:/*************:8091 - R:/*************:11489],client version:1.7.1
2025-07-18 10:07:22 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11489 to server channel inactive.
2025-07-18 10:07:22 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0e3905f6, L:/*************:8091 ! R:/*************:11489]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11489', channel=[id: 0x0e3905f6, L:/*************:8091 ! R:/*************:11489], resourceSets=null}
2025-07-18 10:07:22 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11470 to server channel inactive.
2025-07-18 10:07:22 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1a25f1d8, L:/*************:8091 ! R:/*************:11470]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11470', channel=[id: 0x1a25f1d8, L:/*************:8091 ! R:/*************:11470], resourceSets=null}
2025-07-18 10:07:38 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11461 to server channel inactive.
2025-07-18 10:07:38 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x47e42d24, L:/*************:8091 ! R:/*************:11461]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11461', channel=[id: 0x47e42d24, L:/*************:8091 ! R:/*************:11461], resourceSets=null}
2025-07-18 10:10:17 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752804617039
timestamp=1752804617039
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xcf01bd2d, L:/*************:8091 - R:/*************:13401],client version:1.7.1
2025-07-18 10:10:19 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752804619507
timestamp=1752804619507
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x761929f3, L:/*************:8091 - R:/*************:13427],client version:1.7.1
2025-07-18 10:10:19 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752804619743
timestamp=1752804619743
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x079d0d53, L:/*************:8091 - R:/*************:13429],client version:1.7.1
2025-07-18 10:10:20 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13401 to server channel inactive.
2025-07-18 10:10:20 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcf01bd2d, L:/*************:8091 ! R:/*************:13401]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13401', channel=[id: 0xcf01bd2d, L:/*************:8091 ! R:/*************:13401], resourceSets=null}
2025-07-18 10:10:23 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13427 to server channel inactive.
2025-07-18 10:10:23 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x761929f3, L:/*************:8091 ! R:/*************:13427]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13427', channel=[id: 0x761929f3, L:/*************:8091 ! R:/*************:13427], resourceSets=null}
2025-07-18 10:10:42 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13429 to server channel inactive.
2025-07-18 10:10:42 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x079d0d53, L:/*************:8091 ! R:/*************:13429]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13429', channel=[id: 0x079d0d53, L:/*************:8091 ! R:/*************:13429], resourceSets=null}
2025-07-18 10:11:41 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752804701212
timestamp=1752804701212
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x9c6013e2, L:/*************:8091 - R:/*************:13909],client version:1.7.1
2025-07-18 10:11:42 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752804702211
timestamp=1752804702211
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x8d4fb410, L:/*************:8091 - R:/*************:13912],client version:1.7.1
2025-07-18 10:11:44 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752804704478
timestamp=1752804704478
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xd7533a63, L:/*************:8091 - R:/*************:13919],client version:1.7.1
2025-07-18 10:11:46 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xc38ddd22, L:/*************:8091 - R:/*************:13939],client version:1.7.1
2025-07-18 10:11:46 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13909 to server channel inactive.
2025-07-18 10:11:46 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9c6013e2, L:/*************:8091 ! R:/*************:13909]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13909', channel=[id: 0x9c6013e2, L:/*************:8091 ! R:/*************:13909], resourceSets=null}
2025-07-18 10:11:48 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xcb936794, L:/*************:8091 - R:/*************:13952],client version:1.7.1
2025-07-18 10:12:22 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752804742619
timestamp=1752804742619
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x05eb0f8c, L:/*************:8091 - R:/*************:14070],client version:1.7.1
2025-07-18 10:13:22 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x9e662e35, L:/*************:8091 - R:/*************:14138],client version:1.7.1
2025-07-18 10:50:28 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13939 to server channel inactive.
2025-07-18 10:50:28 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13912 to server channel inactive.
2025-07-18 10:50:28 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8d4fb410, L:/*************:8091 ! R:/*************:13912]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13912', channel=[id: 0x8d4fb410, L:/*************:8091 ! R:/*************:13912], resourceSets=null}
2025-07-18 10:50:28 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc38ddd22, L:/*************:8091 ! R:/*************:13939]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13939', channel=[id: 0xc38ddd22, L:/*************:8091 ! R:/*************:13939], resourceSets=[]}
2025-07-18 10:50:46 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752807046212
timestamp=1752807046212
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x02229a8f, L:/*************:8091 - R:/*************:7517],client version:1.7.1
2025-07-18 10:50:49 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x4273519d, L:/*************:8091 - R:/*************:7522],client version:1.7.1
2025-07-18 11:13:26 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7522 to server channel inactive.
2025-07-18 11:13:26 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13726 to server channel inactive.
2025-07-18 11:13:26 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4273519d, L:/*************:8091 ! R:/*************:7522]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:7522', channel=[id: 0x4273519d, L:/*************:8091 ! R:/*************:7522], resourceSets=[]}
2025-07-18 11:13:26 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7517 to server channel inactive.
2025-07-18 11:13:26 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14070 to server channel inactive.
2025-07-18 11:13:26 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4dc370c9, L:/*************:8091 ! R:/*************:13726]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13726', channel=[id: 0x4dc370c9, L:/*************:8091 ! R:/*************:13726], resourceSets=[]}
2025-07-18 11:13:26 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x02229a8f, L:/*************:8091 ! R:/*************:7517]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:7517', channel=[id: 0x02229a8f, L:/*************:8091 ! R:/*************:7517], resourceSets=null}
2025-07-18 11:13:26 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13919 to server channel inactive.
2025-07-18 11:13:26 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd7533a63, L:/*************:8091 ! R:/*************:13919]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13919', channel=[id: 0xd7533a63, L:/*************:8091 ! R:/*************:13919], resourceSets=null}
2025-07-18 11:13:26 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x05eb0f8c, L:/*************:8091 ! R:/*************:14070]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:14070', channel=[id: 0x05eb0f8c, L:/*************:8091 ! R:/*************:14070], resourceSets=null}
2025-07-18 11:13:26 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14138 to server channel inactive.
2025-07-18 11:13:26 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13683 to server channel inactive.
2025-07-18 11:13:26 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9e662e35, L:/*************:8091 ! R:/*************:14138]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:14138', channel=[id: 0x9e662e35, L:/*************:8091 ! R:/*************:14138], resourceSets=null}
2025-07-18 11:13:26 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1ef17624, L:/*************:8091 ! R:/*************:13683]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13683', channel=[id: 0x1ef17624, L:/*************:8091 ! R:/*************:13683], resourceSets=null}
2025-07-18 11:13:26 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13952 to server channel inactive.
2025-07-18 11:13:26 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcb936794, L:/*************:8091 ! R:/*************:13952]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:13952', channel=[id: 0xcb936794, L:/*************:8091 ! R:/*************:13952], resourceSets=[]}
2025-07-18 11:13:56 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752808436842
timestamp=1752808436842
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xf1167837, L:/*************:8091 - R:/*************:6438],client version:1.7.1
2025-07-18 11:13:57 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x87117eea, L:/*************:8091 - R:/*************:6514],client version:1.7.1
2025-07-18 11:14:01 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 11:14:02 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752808442567
timestamp=1752808442567
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xbd3f47a6, L:/*************:8091 - R:/*************:7133],client version:1.7.1
2025-07-18 11:14:02 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x0c581e67, L:/*************:8091 - R:/*************:7198],client version:1.7.1
2025-07-18 11:14:04 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752808444369
timestamp=1752808444369
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x95287aaa, L:/*************:8091 - R:/*************:7261],client version:1.7.1
2025-07-18 11:14:04 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x32092bf0, L:/*************:8091 - R:/*************:7263],client version:1.7.1
2025-07-18 11:14:06 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752808446047
timestamp=1752808446047
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xf60de851, L:/*************:8091 - R:/*************:7327],client version:1.7.1
2025-07-18 11:14:06 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x6ee1df2c, L:/*************:8091 - R:/*************:7407],client version:1.7.1
2025-07-18 11:40:00 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 11:40:00 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 11:40:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 11:40:00 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 11:40:03 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 17740 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 11:40:03 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 11:40:04 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 11:40:04 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 11:40:04 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 11:40:04 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 11:40:04 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 11:40:04 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1547 ms
2025-07-18 11:40:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 11:40:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 11:40:05 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 11:40:06 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6af29394, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@70f8d217, org.springframework.security.web.context.SecurityContextPersistenceFilter@5bddd141, org.springframework.security.web.header.HeaderWriterFilter@2a5ed225, org.springframework.security.web.authentication.logout.LogoutFilter@166d576b, io.seata.console.filter.JwtAuthenticationTokenFilter@528c4353, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@475eb4fd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@447630c4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1a06b95, org.springframework.security.web.session.SessionManagementFilter@5c194652, org.springframework.security.web.access.ExceptionTranslationFilter@10e4ce98, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@509e4902]
2025-07-18 11:40:06 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 11:40:06 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 11:40:06 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 6.313 seconds (JVM running for 7.726)
2025-07-18 11:40:06 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 11:40:12 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 11:40:13 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 11:40:18 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 11:40:18 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 11:40:22 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 11:44:08 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 11:44:08 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 11:44:08 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 11:44:08 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 11:44:09 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 11:44:09 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 11:44:09 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 11:44:09 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 11:44:09 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 20224 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 11:44:09 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 11:44:10 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 11:44:10 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 11:44:10 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 11:44:10 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 11:44:10 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 11:44:10 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1111 ms
2025-07-18 11:44:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 11:44:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 11:44:10 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 11:44:11 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@65f96d58, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@48a61e38, org.springframework.security.web.context.SecurityContextPersistenceFilter@572db5ee, org.springframework.security.web.header.HeaderWriterFilter@abe7d36, org.springframework.security.web.authentication.logout.LogoutFilter@2dd4a7a9, io.seata.console.filter.JwtAuthenticationTokenFilter@52a3eef8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@31ab75a5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@69a3944, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@37d8562f, org.springframework.security.web.session.SessionManagementFilter@384b3fe7, org.springframework.security.web.access.ExceptionTranslationFilter@7bbe532b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@69ae7632]
2025-07-18 11:44:11 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 11:44:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 11:44:11 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.563 seconds (JVM running for 4.488)
2025-07-18 11:44:11 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 11:44:11 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 11:44:11 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 11:44:11 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 11:44:11 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 11:44:11 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 11:44:11 [main] INFO  io.seata.server.ServerRunner - seata server started in 546 millSeconds
2025-07-18 11:44:37 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752810277512
timestamp=1752810277512
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xd475c08b, L:/*************:8091 - R:/*************:5096],client version:1.7.1
2025-07-18 11:45:37 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x50ec87cf, L:/*************:8091 - R:/*************:5149],client version:1.7.1
2025-07-18 12:37:28 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5096 to server channel inactive.
2025-07-18 12:37:28 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd475c08b, L:/*************:8091 ! R:/*************:5096]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:5096', channel=[id: 0xd475c08b, L:/*************:8091 ! R:/*************:5096], resourceSets=null}
2025-07-18 12:37:28 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5149 to server channel inactive.
2025-07-18 12:37:28 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x50ec87cf, L:/*************:8091 ! R:/*************:5149]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:5149', channel=[id: 0x50ec87cf, L:/*************:8091 ! R:/*************:5149], resourceSets=null}
2025-07-18 12:37:35 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752813455051
timestamp=1752813455051
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x91267337, L:/*************:8091 - R:/*************:5846],client version:1.7.1
2025-07-18 12:37:35 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x28aac3a6, L:/*************:8091 - R:/*************:5847],client version:1.7.1
2025-07-18 12:56:27 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5846 to server channel inactive.
2025-07-18 12:56:27 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x91267337, L:/*************:8091 ! R:/*************:5846]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:5846', channel=[id: 0x91267337, L:/*************:8091 ! R:/*************:5846], resourceSets=null}
2025-07-18 12:56:28 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5847 to server channel inactive.
2025-07-18 12:56:28 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x28aac3a6, L:/*************:8091 ! R:/*************:5847]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:5847', channel=[id: 0x28aac3a6, L:/*************:8091 ! R:/*************:5847], resourceSets=null}
2025-07-18 12:56:32 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752814592445
timestamp=1752814592445
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x78a2d68a, L:/*************:8091 - R:/*************:6497],client version:1.7.1
2025-07-18 12:56:32 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x302018b9, L:/*************:8091 - R:/*************:6498],client version:1.7.1
2025-07-18 13:33:42 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 13:34:31 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 13:34:31 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 13:34:31 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 13:34:31 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 13:34:32 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 13:34:32 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 13:34:32 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 13:34:32 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 13:34:32 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 28188 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 13:34:32 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 13:34:33 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 13:34:33 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 13:34:33 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 13:34:33 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 13:34:33 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 13:34:33 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1153 ms
2025-07-18 13:34:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 13:34:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 13:34:34 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 13:34:34 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5a5c2889, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7668f8fd, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c205259, org.springframework.security.web.header.HeaderWriterFilter@3b11deb6, org.springframework.security.web.authentication.logout.LogoutFilter@572db5ee, io.seata.console.filter.JwtAuthenticationTokenFilter@75566f4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@20923380, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1640a6b5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6785786d, org.springframework.security.web.session.SessionManagementFilter@f0d01c9, org.springframework.security.web.access.ExceptionTranslationFilter@4678ec43, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@31a80c88]
2025-07-18 13:34:34 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 13:34:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 13:34:34 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.61 seconds (JVM running for 4.645)
2025-07-18 13:34:34 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 13:34:34 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 13:34:35 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 13:34:35 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 13:34:35 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 13:34:35 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 13:34:35 [main] INFO  io.seata.server.ServerRunner - seata server started in 594 millSeconds
2025-07-18 13:35:14 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752816914795
timestamp=1752816914795
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x42e62b42, L:/*************:8091 - R:/*************:6649],client version:1.7.1
2025-07-18 13:35:38 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752816938853
timestamp=1752816938853
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x042644be, L:/*************:8091 - R:/*************:6709],client version:1.7.1
2025-07-18 13:35:41 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xf2db242d, L:/*************:8091 - R:/*************:6714],client version:1.7.1
2025-07-18 13:36:15 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xa930e50c, L:/*************:8091 - R:/*************:6768],client version:1.7.1
2025-07-18 13:42:26 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752817346154
timestamp=1752817346154
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x4be4fc14, L:/*************:8091 - R:/*************:8527],client version:1.7.1
2025-07-18 13:42:28 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xf4fdf0e1, L:/*************:8091 - R:/*************:8533],client version:1.7.1
2025-07-18 13:43:27 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6649 to server channel inactive.
2025-07-18 13:43:27 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6768 to server channel inactive.
2025-07-18 13:43:27 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x42e62b42, L:/*************:8091 ! R:/*************:6649]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:6649', channel=[id: 0x42e62b42, L:/*************:8091 ! R:/*************:6649], resourceSets=null}
2025-07-18 13:43:27 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa930e50c, L:/*************:8091 ! R:/*************:6768]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:6768', channel=[id: 0xa930e50c, L:/*************:8091 ! R:/*************:6768], resourceSets=null}
2025-07-18 13:43:30 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8527 to server channel inactive.
2025-07-18 13:43:30 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4be4fc14, L:/*************:8091 ! R:/*************:8527]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:8527', channel=[id: 0x4be4fc14, L:/*************:8091 ! R:/*************:8527], resourceSets=null}
2025-07-18 13:43:30 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8533 to server channel inactive.
2025-07-18 13:43:30 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf4fdf0e1, L:/*************:8091 ! R:/*************:8533]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:8533', channel=[id: 0xf4fdf0e1, L:/*************:8091 ! R:/*************:8533], resourceSets=[]}
2025-07-18 13:43:36 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6709 to server channel inactive.
2025-07-18 13:43:36 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x042644be, L:/*************:8091 ! R:/*************:6709]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:6709', channel=[id: 0x042644be, L:/*************:8091 ! R:/*************:6709], resourceSets=null}
2025-07-18 13:43:36 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6714 to server channel inactive.
2025-07-18 13:43:36 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf2db242d, L:/*************:8091 ! R:/*************:6714]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:6714', channel=[id: 0xf2db242d, L:/*************:8091 ! R:/*************:6714], resourceSets=[]}
2025-07-18 13:43:37 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 13:59:06 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 13:59:06 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 13:59:06 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 13:59:06 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 13:59:08 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 13:59:08 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 13:59:08 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 13:59:08 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 13:59:08 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 18240 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 13:59:08 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 13:59:09 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 13:59:09 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 13:59:09 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 13:59:09 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 13:59:09 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 13:59:09 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1220 ms
2025-07-18 13:59:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 13:59:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 13:59:10 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 13:59:10 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@75566f4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6785786d, org.springframework.security.web.context.SecurityContextPersistenceFilter@20923380, org.springframework.security.web.header.HeaderWriterFilter@5c13534a, org.springframework.security.web.authentication.logout.LogoutFilter@31ab75a5, io.seata.console.filter.JwtAuthenticationTokenFilter@5c261c74, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6520625f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@47fce2c4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d38a81d, org.springframework.security.web.session.SessionManagementFilter@205b73d8, org.springframework.security.web.access.ExceptionTranslationFilter@3b11deb6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5a5c2889]
2025-07-18 13:59:10 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 13:59:10 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 13:59:10 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.941 seconds (JVM running for 5.057)
2025-07-18 13:59:10 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 13:59:10 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 13:59:10 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 13:59:15 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 13:59:15 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 13:59:15 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 13:59:15 [main] INFO  io.seata.server.ServerRunner - seata server started in 5142 millSeconds
2025-07-18 14:01:02 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752818461867
timestamp=1752818461867
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x627b5c06, L:/*************:8091 - R:/*************:11289],client version:1.7.1
2025-07-18 14:02:02 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xdfba4af0, L:/*************:8091 - R:/*************:11396],client version:1.7.1
2025-07-18 14:07:55 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752818875600
timestamp=1752818875600
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x274196c9, L:/*************:8091 - R:/*************:14934],client version:1.7.1
2025-07-18 14:07:59 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14934 to server channel inactive.
2025-07-18 14:07:59 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x274196c9, L:/*************:8091 ! R:/*************:14934]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:14934', channel=[id: 0x274196c9, L:/*************:8091 ! R:/*************:14934], resourceSets=null}
2025-07-18 14:11:10 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 14:11:17 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11289 to server channel inactive.
2025-07-18 14:11:17 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11396 to server channel inactive.
2025-07-18 14:11:17 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x627b5c06, L:/*************:8091 ! R:/*************:11289]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11289', channel=[id: 0x627b5c06, L:/*************:8091 ! R:/*************:11289], resourceSets=null}
2025-07-18 14:11:17 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdfba4af0, L:/*************:8091 ! R:/*************:11396]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11396', channel=[id: 0xdfba4af0, L:/*************:8091 ! R:/*************:11396], resourceSets=null}
2025-07-18 14:11:20 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 14:11:20 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 14:11:20 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 14:11:20 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 14:11:21 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 14:11:21 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 14:11:21 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 14:11:21 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 14:11:21 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 26196 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 14:11:21 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 14:11:22 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 14:11:22 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 14:11:22 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 14:11:22 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 14:11:22 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 14:11:22 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1233 ms
2025-07-18 14:11:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 14:11:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 14:11:23 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 14:11:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@65f96d58, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@48a61e38, org.springframework.security.web.context.SecurityContextPersistenceFilter@572db5ee, org.springframework.security.web.header.HeaderWriterFilter@abe7d36, org.springframework.security.web.authentication.logout.LogoutFilter@2dd4a7a9, io.seata.console.filter.JwtAuthenticationTokenFilter@52a3eef8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@31ab75a5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@69a3944, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@37d8562f, org.springframework.security.web.session.SessionManagementFilter@384b3fe7, org.springframework.security.web.access.ExceptionTranslationFilter@7bbe532b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@69ae7632]
2025-07-18 14:11:23 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 14:11:23 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 14:11:23 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.738 seconds (JVM running for 4.912)
2025-07-18 14:11:23 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 14:11:28 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 14:11:29 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 14:11:31 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752819091671
timestamp=1752819091671
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xd1470da0, L:/*************:8091 - R:/*************:3105],client version:1.7.1
2025-07-18 14:11:32 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xf558a264, L:/*************:8091 - R:/*************:3108],client version:1.7.1
2025-07-18 14:11:33 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 14:11:33 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 14:11:33 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 14:11:33 [main] INFO  io.seata.server.ServerRunner - seata server started in 9956 millSeconds
2025-07-18 14:17:35 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3105 to server channel inactive.
2025-07-18 14:17:35 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd1470da0, L:/*************:8091 ! R:/*************:3105]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:3105', channel=[id: 0xd1470da0, L:/*************:8091 ! R:/*************:3105], resourceSets=null}
2025-07-18 14:17:35 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3108 to server channel inactive.
2025-07-18 14:17:35 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf558a264, L:/*************:8091 ! R:/*************:3108]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:3108', channel=[id: 0xf558a264, L:/*************:8091 ! R:/*************:3108], resourceSets=null}
2025-07-18 14:17:41 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752819461670
timestamp=1752819461670
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xa43cd65f, L:/*************:8091 - R:/*************:2639],client version:1.7.1
2025-07-18 14:17:42 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x6effbffa, L:/*************:8091 - R:/*************:2642],client version:1.7.1
2025-07-18 14:20:04 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 14:20:07 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2639 to server channel inactive.
2025-07-18 14:20:07 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2642 to server channel inactive.
2025-07-18 14:20:07 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6effbffa, L:/*************:8091 ! R:/*************:2642]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2642', channel=[id: 0x6effbffa, L:/*************:8091 ! R:/*************:2642], resourceSets=null}
2025-07-18 14:20:07 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa43cd65f, L:/*************:8091 ! R:/*************:2639]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2639', channel=[id: 0xa43cd65f, L:/*************:8091 ! R:/*************:2639], resourceSets=null}
2025-07-18 14:20:09 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 14:20:09 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 14:20:10 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 14:20:10 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 14:20:11 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 14:20:11 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 14:20:11 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 14:20:11 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 14:20:11 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 28076 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 14:20:11 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 14:20:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 14:20:12 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 14:20:12 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 14:20:12 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 14:20:12 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 14:20:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1198 ms
2025-07-18 14:20:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 14:20:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 14:20:12 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 14:20:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4b808427, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@167f9043, org.springframework.security.web.context.SecurityContextPersistenceFilter@2d97344c, org.springframework.security.web.header.HeaderWriterFilter@57fae911, org.springframework.security.web.authentication.logout.LogoutFilter@763ddfc3, io.seata.console.filter.JwtAuthenticationTokenFilter@6771fc29, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@23f539df, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@513bec8c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1f370472, org.springframework.security.web.session.SessionManagementFilter@118879d2, org.springframework.security.web.access.ExceptionTranslationFilter@8de4206, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@64f2b1b4]
2025-07-18 14:20:13 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 14:20:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 14:20:13 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.774 seconds (JVM running for 5.065)
2025-07-18 14:20:13 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 14:20:13 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 14:20:13 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 14:20:13 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 14:20:13 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 14:20:13 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x3c461118, L:/*************:8091 - R:/*************:4625],client version:1.7.1
2025-07-18 14:20:13 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752819611682
timestamp=1752819611682
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x37f0b30c, L:/*************:8091 - R:/*************:4624],client version:1.7.1
2025-07-18 14:20:13 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 14:20:13 [main] INFO  io.seata.server.ServerRunner - seata server started in 565 millSeconds
2025-07-18 14:20:52 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752819652395
timestamp=1752819652395
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x523cc14d, L:/*************:8091 - R:/*************:4692],client version:1.7.1
2025-07-18 14:20:55 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xf86b731b, L:/*************:8091 - R:/*************:4696],client version:1.7.1
2025-07-18 14:21:08 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752819668456
timestamp=1752819668456
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xdbee3f30, L:/*************:8091 - R:/*************:4787],client version:1.7.1
2025-07-18 14:21:13 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xe8cb4631, L:/*************:8091 - R:/*************:4795],client version:1.7.1
2025-07-18 14:21:14 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752819674205
timestamp=1752819674205
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xe7a7e6eb, L:/*************:8091 - R:/*************:4806],client version:1.7.1
2025-07-18 14:21:17 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x7fe53398, L:/*************:8091 - R:/*************:4823],client version:1.7.1
2025-07-18 14:21:18 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4806 to server channel inactive.
2025-07-18 14:21:18 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4823 to server channel inactive.
2025-07-18 14:21:18 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe7a7e6eb, L:/*************:8091 ! R:/*************:4806]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4806', channel=[id: 0xe7a7e6eb, L:/*************:8091 ! R:/*************:4806], resourceSets=null}
2025-07-18 14:21:18 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7fe53398, L:/*************:8091 ! R:/*************:4823]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4823', channel=[id: 0x7fe53398, L:/*************:8091 ! R:/*************:4823], resourceSets=[]}
2025-07-18 14:43:32 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752821011938
timestamp=1752821011938
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x8e76811a, L:/*************:8091 - R:/*************:11446],client version:1.7.1
2025-07-18 14:43:35 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x992801ac, L:/*************:8091 - R:/*************:11463],client version:1.7.1
2025-07-18 15:02:22 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11446 to server channel inactive.
2025-07-18 15:02:22 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11463 to server channel inactive.
2025-07-18 15:02:22 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8e76811a, L:/*************:8091 ! R:/*************:11446]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11446', channel=[id: 0x8e76811a, L:/*************:8091 ! R:/*************:11446], resourceSets=null}
2025-07-18 15:02:22 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x992801ac, L:/*************:8091 ! R:/*************:11463]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11463', channel=[id: 0x992801ac, L:/*************:8091 ! R:/*************:11463], resourceSets=[]}
2025-07-18 15:02:26 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4787 to server channel inactive.
2025-07-18 15:02:26 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdbee3f30, L:/*************:8091 ! R:/*************:4787]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:4787', channel=[id: 0xdbee3f30, L:/*************:8091 ! R:/*************:4787], resourceSets=null}
2025-07-18 15:02:26 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4795 to server channel inactive.
2025-07-18 15:02:26 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe8cb4631, L:/*************:8091 ! R:/*************:4795]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:4795', channel=[id: 0xe8cb4631, L:/*************:8091 ! R:/*************:4795], resourceSets=[]}
2025-07-18 15:02:34 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752822154525
timestamp=1752822154525
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xcef98490, L:/*************:8091 - R:/*************:3885],client version:1.7.1
2025-07-18 15:02:38 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752822158255
timestamp=1752822158255
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x8f1d3bcf, L:/*************:8091 - R:/*************:3911],client version:1.7.1
2025-07-18 15:02:38 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x4541583f, L:/*************:8091 - R:/*************:3915],client version:1.7.1
2025-07-18 15:02:42 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xa220c863, L:/*************:8091 - R:/*************:3965],client version:1.7.1
2025-07-18 15:57:22 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 15:59:33 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 15:59:33 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 15:59:33 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 15:59:33 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 15:59:34 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 15:59:34 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 15:59:34 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 15:59:34 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 15:59:34 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 20372 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 15:59:34 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 15:59:36 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 15:59:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 15:59:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 15:59:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 15:59:36 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 15:59:36 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1491 ms
2025-07-18 15:59:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 15:59:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 15:59:37 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 15:59:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@75566f4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6785786d, org.springframework.security.web.context.SecurityContextPersistenceFilter@1c65ec63, org.springframework.security.web.header.HeaderWriterFilter@1d686622, org.springframework.security.web.authentication.logout.LogoutFilter@2a7b5925, io.seata.console.filter.JwtAuthenticationTokenFilter@5c261c74, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@10f192d8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5bc40f5d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d38a81d, org.springframework.security.web.session.SessionManagementFilter@2643ed03, org.springframework.security.web.access.ExceptionTranslationFilter@54ec8ab3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7ef3aa21]
2025-07-18 15:59:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 15:59:37 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 15:59:37 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.948 seconds (JVM running for 6.069)
2025-07-18 15:59:38 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 15:59:38 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 15:59:38 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 15:59:38 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 15:59:38 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 15:59:38 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 15:59:38 [main] INFO  io.seata.server.ServerRunner - seata server started in 949 millSeconds
2025-07-18 15:59:46 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752825586071
timestamp=1752825586071
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x4f7ad80b, L:/*************:8091 - R:/*************:11287],client version:1.7.1
2025-07-18 15:59:50 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752825590479
timestamp=1752825590479
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x43ce48ad, L:/*************:8091 - R:/*************:11319],client version:1.7.1
2025-07-18 15:59:56 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xaf6d0755, L:/*************:8091 - R:/*************:11361],client version:1.7.1
2025-07-18 16:00:01 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752825601163
timestamp=1752825601163
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xd695aed9, L:/*************:8091 - R:/*************:11401],client version:1.7.1
2025-07-18 16:00:03 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825603157
timestamp=1752825603157
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x4cb3c8fa, L:/*************:8091 - R:/*************:11414],client version:1.7.1
2025-07-18 16:00:06 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x148ef4f6, L:/*************:8091 - R:/*************:11434],client version:1.7.1
2025-07-18 16:00:07 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xd566eda2, L:/*************:8091 - R:/*************:11439],client version:1.7.1
2025-07-18 16:00:46 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x228cbdf3, L:/*************:8091 - R:/*************:11543],client version:1.7.1
2025-07-18 17:02:09 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11414 to server channel inactive.
2025-07-18 17:02:09 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11439 to server channel inactive.
2025-07-18 17:02:09 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4cb3c8fa, L:/*************:8091 ! R:/*************:11414]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11414', channel=[id: 0x4cb3c8fa, L:/*************:8091 ! R:/*************:11414], resourceSets=null}
2025-07-18 17:02:09 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd566eda2, L:/*************:8091 ! R:/*************:11439]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:11439', channel=[id: 0xd566eda2, L:/*************:8091 ! R:/*************:11439], resourceSets=[]}
2025-07-18 17:02:25 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752829344803
timestamp=1752829344803
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa9a2878b, L:/*************:8091 - R:/*************:3511],client version:1.7.1
2025-07-18 17:02:28 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xb5cb63ef, L:/*************:8091 - R:/*************:3663],client version:1.7.1
2025-07-18 17:02:51 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11401 to server channel inactive.
2025-07-18 17:02:51 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd695aed9, L:/*************:8091 ! R:/*************:11401]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11401', channel=[id: 0xd695aed9, L:/*************:8091 ! R:/*************:11401], resourceSets=null}
2025-07-18 17:02:51 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11434 to server channel inactive.
2025-07-18 17:02:51 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x148ef4f6, L:/*************:8091 ! R:/*************:11434]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:11434', channel=[id: 0x148ef4f6, L:/*************:8091 ! R:/*************:11434], resourceSets=[]}
2025-07-18 17:03:11 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752829391548
timestamp=1752829391548
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x6a0ea662, L:/*************:8091 - R:/*************:4920],client version:1.7.1
2025-07-18 17:03:15 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xcceebfdf, L:/*************:8091 - R:/*************:5552],client version:1.7.1
2025-07-18 18:25:38 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x228cbdf3, L:/*************:8091 - R:/*************:11543] read idle.
2025-07-18 18:25:38 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x4f7ad80b, L:/*************:8091 - R:/*************:11287] read idle.
2025-07-18 18:25:38 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11287 to server channel inactive.
2025-07-18 18:25:38 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4f7ad80b, L:/*************:8091 - R:/*************:11287]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11287', channel=[id: 0x4f7ad80b, L:/*************:8091 - R:/*************:11287], resourceSets=null}
2025-07-18 18:25:38 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11543 to server channel inactive.
2025-07-18 18:25:38 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x228cbdf3, L:/*************:8091 - R:/*************:11543]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11543', channel=[id: 0x228cbdf3, L:/*************:8091 - R:/*************:11543], resourceSets=null}
2025-07-18 18:25:38 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x4f7ad80b, L:/*************:8091 - R:/*************:11287]
2025-07-18 18:25:38 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x228cbdf3, L:/*************:8091 - R:/*************:11543]
2025-07-18 18:25:38 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11287 to server channel inactive.
2025-07-18 18:25:38 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4f7ad80b, L:/*************:8091 ! R:/*************:11287]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11287', channel=[id: 0x4f7ad80b, L:/*************:8091 ! R:/*************:11287], resourceSets=null}
2025-07-18 18:25:38 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11543 to server channel inactive.
2025-07-18 18:25:38 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x228cbdf3, L:/*************:8091 ! R:/*************:11543]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:11543', channel=[id: 0x228cbdf3, L:/*************:8091 ! R:/*************:11543], resourceSets=null}
2025-07-18 18:25:38 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xb5cb63ef, L:/*************:8091 - R:/*************:3663] read idle.
2025-07-18 18:25:38 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3663 to server channel inactive.
2025-07-18 18:25:38 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb5cb63ef, L:/*************:8091 - R:/*************:3663]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:3663', channel=[id: 0xb5cb63ef, L:/*************:8091 - R:/*************:3663], resourceSets=[]}
2025-07-18 18:25:38 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xb5cb63ef, L:/*************:8091 - R:/*************:3663]
2025-07-18 18:25:38 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3663 to server channel inactive.
2025-07-18 18:25:38 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb5cb63ef, L:/*************:8091 ! R:/*************:3663]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:3663', channel=[id: 0xb5cb63ef, L:/*************:8091 ! R:/*************:3663], resourceSets=[]}
2025-07-18 18:25:38 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x2cebac18, L:/*************:8091 - R:/*************:1948],client version:1.7.1
2025-07-18 18:25:40 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3511 to server channel inactive.
2025-07-18 18:25:40 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa9a2878b, L:/*************:8091 ! R:/*************:3511]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:3511', channel=[id: 0xa9a2878b, L:/*************:8091 ! R:/*************:3511], resourceSets=null}
2025-07-18 18:25:40 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:5552 to server channel inactive.
2025-07-18 18:25:40 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcceebfdf, L:/*************:8091 ! R:/*************:5552]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:5552', channel=[id: 0xcceebfdf, L:/*************:8091 ! R:/*************:5552], resourceSets=[]}
2025-07-18 18:25:41 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11319 to server channel inactive.
2025-07-18 18:25:41 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x43ce48ad, L:/*************:8091 ! R:/*************:11319]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11319', channel=[id: 0x43ce48ad, L:/*************:8091 ! R:/*************:11319], resourceSets=null}
2025-07-18 18:25:41 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752834341495
timestamp=1752834341495
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xa12d7765, L:/*************:8091 - R:/*************:2279],client version:1.7.1
2025-07-18 18:25:41 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x6a0ea662, L:/*************:8091 - R:/*************:4920] read idle.
2025-07-18 18:25:41 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4920 to server channel inactive.
2025-07-18 18:25:41 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6a0ea662, L:/*************:8091 - R:/*************:4920]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:4920', channel=[id: 0x6a0ea662, L:/*************:8091 - R:/*************:4920], resourceSets=null}
2025-07-18 18:25:41 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x6a0ea662, L:/*************:8091 - R:/*************:4920]
2025-07-18 18:25:41 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4920 to server channel inactive.
2025-07-18 18:25:41 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6a0ea662, L:/*************:8091 ! R:/*************:4920]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:4920', channel=[id: 0x6a0ea662, L:/*************:8091 ! R:/*************:4920], resourceSets=null}
2025-07-18 18:25:42 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:11361 to server channel inactive.
2025-07-18 18:25:42 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xaf6d0755, L:/*************:8091 ! R:/*************:11361]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:11361', channel=[id: 0xaf6d0755, L:/*************:8091 ! R:/*************:11361], resourceSets=[]}
2025-07-18 18:25:42 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x79007bdc, L:/*************:8091 - R:/*************:7858],client version:1.7.1
2025-07-18 18:25:42 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752834342741
timestamp=1752834342741
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xf8e5da40, L:/*************:8091 - R:/*************:7860],client version:1.7.1
2025-07-18 18:25:47 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752834347157
timestamp=1752834347157
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xf7b167f8, L:/*************:8091 - R:/*************:8308],client version:1.7.1
2025-07-18 18:25:47 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x93b3df21, L:/*************:8091 - R:/*************:8309],client version:1.7.1
2025-07-18 18:25:48 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752834348260
timestamp=1752834348260
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x446c4914, L:/*************:8091 - R:/*************:8311],client version:1.7.1
2025-07-18 18:25:48 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x6c6ac3f4, L:/*************:8091 - R:/*************:8322],client version:1.7.1
2025-07-18 19:07:33 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 19:07:36 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8311 to server channel inactive.
2025-07-18 19:07:36 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8309 to server channel inactive.
2025-07-18 19:07:36 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1948 to server channel inactive.
2025-07-18 19:07:36 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8308 to server channel inactive.
2025-07-18 19:07:36 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:8322 to server channel inactive.
2025-07-18 19:07:36 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7860 to server channel inactive.
2025-07-18 19:07:36 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:7858 to server channel inactive.
2025-07-18 19:07:36 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2279 to server channel inactive.
2025-07-18 19:07:36 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf8e5da40, L:/*************:8091 ! R:/*************:7860]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:7860', channel=[id: 0xf8e5da40, L:/*************:8091 ! R:/*************:7860], resourceSets=null}
2025-07-18 19:07:36 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x446c4914, L:/*************:8091 ! R:/*************:8311]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:8311', channel=[id: 0x446c4914, L:/*************:8091 ! R:/*************:8311], resourceSets=null}
2025-07-18 19:07:36 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6c6ac3f4, L:/*************:8091 ! R:/*************:8322]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:8322', channel=[id: 0x6c6ac3f4, L:/*************:8091 ! R:/*************:8322], resourceSets=[]}
2025-07-18 19:07:36 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x79007bdc, L:/*************:8091 ! R:/*************:7858]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:7858', channel=[id: 0x79007bdc, L:/*************:8091 ! R:/*************:7858], resourceSets=[]}
2025-07-18 19:07:36 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x93b3df21, L:/*************:8091 ! R:/*************:8309]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:8309', channel=[id: 0x93b3df21, L:/*************:8091 ! R:/*************:8309], resourceSets=[]}
2025-07-18 19:07:36 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2cebac18, L:/*************:8091 ! R:/*************:1948]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:1948', channel=[id: 0x2cebac18, L:/*************:8091 ! R:/*************:1948], resourceSets=null}
2025-07-18 19:07:36 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf7b167f8, L:/*************:8091 ! R:/*************:8308]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:8308', channel=[id: 0xf7b167f8, L:/*************:8091 ! R:/*************:8308], resourceSets=null}
2025-07-18 19:07:36 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa12d7765, L:/*************:8091 ! R:/*************:2279]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2279', channel=[id: 0xa12d7765, L:/*************:8091 ! R:/*************:2279], resourceSets=null}
2025-07-18 19:07:41 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 19:07:41 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 19:07:41 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 19:07:41 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 19:07:42 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 19:07:42 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 19:07:42 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 19:07:42 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 19:07:42 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 31668 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 19:07:42 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 19:07:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 19:07:43 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 19:07:43 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 19:07:43 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 19:07:43 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 19:07:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1406 ms
2025-07-18 19:07:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 19:07:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 19:07:44 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 19:07:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4ee19768, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@8113ea7, org.springframework.security.web.context.SecurityContextPersistenceFilter@34f32575, org.springframework.security.web.header.HeaderWriterFilter@8a7cd7c, org.springframework.security.web.authentication.logout.LogoutFilter@42db3ff, io.seata.console.filter.JwtAuthenticationTokenFilter@1c879f07, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7c013560, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4b808427, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@78e4fa1, org.springframework.security.web.session.SessionManagementFilter@29693b1d, org.springframework.security.web.access.ExceptionTranslationFilter@7a689979, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5923210]
2025-07-18 19:07:44 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 19:07:44 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 19:07:44 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.278 seconds (JVM running for 5.897)
2025-07-18 19:07:45 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 19:07:45 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 19:07:45 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 19:07:45 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 19:07:45 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 19:07:45 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 19:07:45 [main] INFO  io.seata.server.ServerRunner - seata server started in 571 millSeconds
2025-07-18 19:07:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752836867134
timestamp=1752836867134
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xe2c1b588, L:/*************:8091 - R:/*************:2712],client version:1.7.1
2025-07-18 19:07:47 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xb09a0b4b, L:/*************:8091 - R:/*************:2713],client version:1.7.1
2025-07-18 19:07:48 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752836868219
timestamp=1752836868219
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x3f94741d, L:/*************:8091 - R:/*************:2715],client version:1.7.1
2025-07-18 19:07:48 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x55123ed8, L:/*************:8091 - R:/*************:2716],client version:1.7.1
2025-07-18 19:07:51 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752836871464
timestamp=1752836871464
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xc4101215, L:/*************:8091 - R:/*************:2719],client version:1.7.1
2025-07-18 19:07:51 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x98f4e345, L:/*************:8091 - R:/*************:2720],client version:1.7.1
2025-07-18 19:07:52 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752836872708
timestamp=1752836872708
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x473083dc, L:/*************:8091 - R:/*************:2721],client version:1.7.1
2025-07-18 19:07:53 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xa2c7331d, L:/*************:8091 - R:/*************:2723],client version:1.7.1
2025-07-18 19:35:36 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2715 to server channel inactive.
2025-07-18 19:35:36 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2716 to server channel inactive.
2025-07-18 19:35:36 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3f94741d, L:/*************:8091 ! R:/*************:2715]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2715', channel=[id: 0x3f94741d, L:/*************:8091 ! R:/*************:2715], resourceSets=null}
2025-07-18 19:35:36 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x55123ed8, L:/*************:8091 ! R:/*************:2716]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2716', channel=[id: 0x55123ed8, L:/*************:8091 ! R:/*************:2716], resourceSets=[]}
2025-07-18 19:35:52 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752838551826
timestamp=1752838551826
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x17e86cbd, L:/*************:8091 - R:/*************:7460],client version:1.7.1
2025-07-18 19:35:55 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x5a9eadc6, L:/*************:8091 - R:/*************:7474],client version:1.7.1
2025-07-18 19:49:12 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 19:57:22 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 19:57:22 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 19:57:22 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 19:57:22 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 19:57:23 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 19:57:23 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 19:57:23 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 19:57:23 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 19:57:23 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 25824 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 19:57:23 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 19:57:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 19:57:25 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 19:57:25 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 19:57:25 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 19:57:25 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 19:57:25 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2045 ms
2025-07-18 19:57:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 19:57:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 19:57:26 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 19:57:26 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7668f8fd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@75566f4, org.springframework.security.web.context.SecurityContextPersistenceFilter@5bc40f5d, org.springframework.security.web.header.HeaderWriterFilter@5d08a65c, org.springframework.security.web.authentication.logout.LogoutFilter@36f80ceb, io.seata.console.filter.JwtAuthenticationTokenFilter@6785786d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b2c41f9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@70940cb8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5c261c74, org.springframework.security.web.session.SessionManagementFilter@3c38e2bf, org.springframework.security.web.access.ExceptionTranslationFilter@694f0655, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7602c65d]
2025-07-18 19:57:26 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 19:57:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 19:57:26 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 5.611 seconds (JVM running for 6.961)
2025-07-18 19:57:27 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 19:57:27 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 19:57:27 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 19:57:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 19:57:27 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 19:57:27 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 19:57:27 [main] INFO  io.seata.server.ServerRunner - seata server started in 834 millSeconds
2025-07-18 19:57:28 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752839848035
timestamp=1752839848035
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x2a433cef, L:/*************:8091 - R:/*************:11678],client version:1.7.1
2025-07-18 19:57:28 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x07d81bd3, L:/*************:8091 - R:/*************:11681],client version:1.7.1
2025-07-18 19:57:31 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xcd1bfc53, L:/*************:8091 - R:/*************:11697],client version:1.7.1
2025-07-18 19:57:32 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839852667
timestamp=1752839852667
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x424e2f55, L:/*************:8091 - R:/*************:11718],client version:1.7.1
2025-07-18 19:57:36 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xd94dad92, L:/*************:8091 - R:/*************:11743],client version:1.7.1
2025-07-18 19:58:19 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752839899814
timestamp=1752839899814
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x059cc2ab, L:/*************:8091 - R:/*************:11841],client version:1.7.1
2025-07-18 19:58:20 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x57b333f8, L:/*************:8091 - R:/*************:11842],client version:1.7.1
2025-07-18 19:58:24 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752839904007
timestamp=1752839904007
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x5b542216, L:/*************:8091 - R:/*************:11844],client version:1.7.1
2025-07-18 20:16:41 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-18 20:32:17 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 20:32:17 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 20:32:17 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 20:32:17 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 20:32:18 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-18 20:32:18 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-18 20:32:18 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-18 20:32:18 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-18 20:32:18 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 32804 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 20:32:18 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-18 20:32:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-18 20:32:20 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-18 20:32:20 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-18 20:32:20 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-18 20:32:20 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-18 20:32:20 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1226 ms
2025-07-18 20:32:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-18 20:32:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-18 20:32:20 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-18 20:32:20 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1f27f354, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6dc7b7f, org.springframework.security.web.context.SecurityContextPersistenceFilter@eb09112, org.springframework.security.web.header.HeaderWriterFilter@6c167296, org.springframework.security.web.authentication.logout.LogoutFilter@6e4bccc, io.seata.console.filter.JwtAuthenticationTokenFilter@4425b6ed, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2b06c388, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@70d16858, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@702432cc, org.springframework.security.web.session.SessionManagementFilter@1256925b, org.springframework.security.web.access.ExceptionTranslationFilter@5afa0b1a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@36ce9eaf]
2025-07-18 20:32:20 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-18 20:32:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-18 20:32:20 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 3.609 seconds (JVM running for 4.705)
2025-07-18 20:32:21 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-18 20:32:21 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-18 20:32:21 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-18 20:32:21 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-18 20:32:21 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-18 20:32:21 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-18 20:32:21 [main] INFO  io.seata.server.ServerRunner - seata server started in 583 millSeconds
2025-07-18 20:32:25 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841945066
timestamp=1752841945066
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x5f3dfb04, L:/*************:8091 - R:/*************:4095],client version:1.7.1
2025-07-18 20:32:25 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x9937bb76, L:/*************:8091 - R:/*************:4096],client version:1.7.1
2025-07-18 20:33:50 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
