2025-07-17 08:45:56 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-17 08:45:56 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-17 08:45:56 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-17 08:45:56 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-17 08:45:58 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-17 08:45:58 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-17 08:45:58 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-17 08:45:58 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-17 08:45:58 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 25972 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-17 08:45:58 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-17 08:46:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-17 08:46:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-17 08:46:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-17 08:46:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 08:46:00 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-17 08:46:00 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2190 ms
2025-07-17 08:46:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-17 08:46:00 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-17 08:46:01 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-17 08:46:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@498503cb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@380e3122, org.springframework.security.web.context.SecurityContextPersistenceFilter@47fce2c4, org.springframework.security.web.header.HeaderWriterFilter@5c13534a, org.springframework.security.web.authentication.logout.LogoutFilter@3e3bf77b, io.seata.console.filter.JwtAuthenticationTokenFilter@576323ff, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2b76ecd5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3e0a112f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1fc386f8, org.springframework.security.web.session.SessionManagementFilter@205b73d8, org.springframework.security.web.access.ExceptionTranslationFilter@3b11deb6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d7c4fcb]
2025-07-17 08:46:01 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-17 08:46:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-17 08:46:01 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 6.154 seconds (JVM running for 7.934)
2025-07-17 08:46:02 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-17 08:46:02 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-17 08:46:02 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-17 08:46:02 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-17 08:46:02 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-17 08:46:02 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-17 08:46:02 [main] INFO  io.seata.server.ServerRunner - seata server started in 877 millSeconds
2025-07-17 08:46:03 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752713163319
timestamp=1752713163319
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x21cd4819, L:/*************:8091 - R:/*************:4121],client version:1.7.1
2025-07-17 08:46:07 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x88322191, L:/*************:8091 - R:/*************:4218],client version:1.7.1
2025-07-17 08:46:11 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752713171436
timestamp=1752713171436
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0xc409e283, L:/*************:8091 - R:/*************:4244],client version:1.7.1
2025-07-17 08:46:13 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752713173001
timestamp=1752713173001
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x2b2d513a, L:/*************:8091 - R:/*************:4274],client version:1.7.1
2025-07-17 08:46:14 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x809f747a, L:/*************:8091 - R:/*************:4298],client version:1.7.1
2025-07-17 08:46:16 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xcbae177b, L:/*************:8091 - R:/*************:4318],client version:1.7.1
2025-07-17 08:46:32 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752713192278
timestamp=1752713192278
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x8ccfee9a, L:/*************:8091 - R:/*************:4493],client version:1.7.1
2025-07-17 08:46:32 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x15bf0b3a, L:/*************:8091 - R:/*************:4517],client version:1.7.1
2025-07-17 11:18:20 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4121 to server channel inactive.
2025-07-17 11:18:20 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4218 to server channel inactive.
2025-07-17 11:18:20 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x21cd4819, L:/*************:8091 ! R:/*************:4121]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4121', channel=[id: 0x21cd4819, L:/*************:8091 ! R:/*************:4121], resourceSets=null}
2025-07-17 11:18:20 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x88322191, L:/*************:8091 ! R:/*************:4218]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:4218', channel=[id: 0x88322191, L:/*************:8091 ! R:/*************:4218], resourceSets=[]}
2025-07-17 11:18:35 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752722315176
timestamp=1752722315176
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xe376e5e2, L:/*************:8091 - R:/*************:14174],client version:1.7.1
2025-07-17 11:18:38 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xe626daec, L:/*************:8091 - R:/*************:14179],client version:1.7.1
2025-07-17 11:26:25 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xe626daec, L:/*************:8091 - R:/*************:14179] read idle.
2025-07-17 11:26:25 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14179 to server channel inactive.
2025-07-17 11:26:25 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe626daec, L:/*************:8091 - R:/*************:14179]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14179', channel=[id: 0xe626daec, L:/*************:8091 - R:/*************:14179], resourceSets=[]}
2025-07-17 11:26:25 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xe626daec, L:/*************:8091 - R:/*************:14179]
2025-07-17 11:26:25 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14179 to server channel inactive.
2025-07-17 11:26:25 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe626daec, L:/*************:8091 ! R:/*************:14179]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14179', channel=[id: 0xe626daec, L:/*************:8091 ! R:/*************:14179], resourceSets=[]}
2025-07-17 11:26:31 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xe376e5e2, L:/*************:8091 - R:/*************:14174] read idle.
2025-07-17 11:26:31 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14174 to server channel inactive.
2025-07-17 11:26:31 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe376e5e2, L:/*************:8091 - R:/*************:14174]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14174', channel=[id: 0xe376e5e2, L:/*************:8091 - R:/*************:14174], resourceSets=null}
2025-07-17 11:26:31 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xe376e5e2, L:/*************:8091 - R:/*************:14174]
2025-07-17 11:26:31 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:14174 to server channel inactive.
2025-07-17 11:26:31 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe376e5e2, L:/*************:8091 ! R:/*************:14174]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:14174', channel=[id: 0xe376e5e2, L:/*************:8091 ! R:/*************:14174], resourceSets=null}
2025-07-17 11:26:44 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752722804964
timestamp=1752722804964
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x17557aa9, L:/*************:8091 - R:/*************:1038],client version:1.7.1
2025-07-17 11:26:45 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x2d3983d5, L:/*************:8091 - R:/*************:1043],client version:1.7.1
2025-07-17 11:27:09 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x17557aa9, L:/*************:8091 - R:/*************:1038] read idle.
2025-07-17 11:27:09 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1038 to server channel inactive.
2025-07-17 11:27:09 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x17557aa9, L:/*************:8091 - R:/*************:1038]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1038', channel=[id: 0x17557aa9, L:/*************:8091 - R:/*************:1038], resourceSets=null}
2025-07-17 11:27:09 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x17557aa9, L:/*************:8091 - R:/*************:1038]
2025-07-17 11:27:09 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1038 to server channel inactive.
2025-07-17 11:27:09 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x17557aa9, L:/*************:8091 ! R:/*************:1038]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1038', channel=[id: 0x17557aa9, L:/*************:8091 ! R:/*************:1038], resourceSets=null}
2025-07-17 11:27:10 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x2d3983d5, L:/*************:8091 - R:/*************:1043] read idle.
2025-07-17 11:27:10 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1043 to server channel inactive.
2025-07-17 11:27:10 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2d3983d5, L:/*************:8091 - R:/*************:1043]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1043', channel=[id: 0x2d3983d5, L:/*************:8091 - R:/*************:1043], resourceSets=[]}
2025-07-17 11:27:10 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x2d3983d5, L:/*************:8091 - R:/*************:1043]
2025-07-17 11:27:10 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1043 to server channel inactive.
2025-07-17 11:27:10 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2d3983d5, L:/*************:8091 ! R:/*************:1043]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1043', channel=[id: 0x2d3983d5, L:/*************:8091 ! R:/*************:1043], resourceSets=[]}
2025-07-17 11:27:15 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752722835449
timestamp=1752722835449
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x04cfc984, L:/*************:8091 - R:/*************:1088],client version:1.7.1
2025-07-17 11:27:15 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x9a9fa002, L:/*************:8091 - R:/*************:1089],client version:1.7.1
2025-07-17 12:24:51 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4298 to server channel inactive.
2025-07-17 12:24:51 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x809f747a, L:/*************:8091 ! R:/*************:4298]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:4298', channel=[id: 0x809f747a, L:/*************:8091 ! R:/*************:4298], resourceSets=[]}
2025-07-17 12:24:52 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4318 to server channel inactive.
2025-07-17 12:24:52 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcbae177b, L:/*************:8091 ! R:/*************:4318]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:4318', channel=[id: 0xcbae177b, L:/*************:8091 ! R:/*************:4318], resourceSets=[]}
2025-07-17 12:24:52 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4244 to server channel inactive.
2025-07-17 12:24:52 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc409e283, L:/*************:8091 ! R:/*************:4244]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:4244', channel=[id: 0xc409e283, L:/*************:8091 ! R:/*************:4244], resourceSets=null}
2025-07-17 12:24:52 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4493 to server channel inactive.
2025-07-17 12:24:52 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8ccfee9a, L:/*************:8091 ! R:/*************:4493]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:4493', channel=[id: 0x8ccfee9a, L:/*************:8091 ! R:/*************:4493], resourceSets=null}
2025-07-17 12:24:52 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x9a9fa002, L:/*************:8091 - R:/*************:1089] read idle.
2025-07-17 12:24:52 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1089 to server channel inactive.
2025-07-17 12:24:52 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9a9fa002, L:/*************:8091 - R:/*************:1089]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1089', channel=[id: 0x9a9fa002, L:/*************:8091 - R:/*************:1089], resourceSets=[]}
2025-07-17 12:24:52 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x9a9fa002, L:/*************:8091 - R:/*************:1089]
2025-07-17 12:24:52 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1089 to server channel inactive.
2025-07-17 12:24:52 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9a9fa002, L:/*************:8091 ! R:/*************:1089]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1089', channel=[id: 0x9a9fa002, L:/*************:8091 ! R:/*************:1089], resourceSets=[]}
2025-07-17 12:24:53 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x04cfc984, L:/*************:8091 - R:/*************:1088] read idle.
2025-07-17 12:24:53 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1088 to server channel inactive.
2025-07-17 12:24:53 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x04cfc984, L:/*************:8091 - R:/*************:1088]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1088', channel=[id: 0x04cfc984, L:/*************:8091 - R:/*************:1088], resourceSets=null}
2025-07-17 12:24:53 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x04cfc984, L:/*************:8091 - R:/*************:1088]
2025-07-17 12:24:53 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:1088 to server channel inactive.
2025-07-17 12:24:53 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x04cfc984, L:/*************:8091 ! R:/*************:1088]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:1088', channel=[id: 0x04cfc984, L:/*************:8091 ! R:/*************:1088], resourceSets=null}
2025-07-17 12:24:53 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4517 to server channel inactive.
2025-07-17 12:24:53 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x15bf0b3a, L:/*************:8091 ! R:/*************:4517]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:4517', channel=[id: 0x15bf0b3a, L:/*************:8091 ! R:/*************:4517], resourceSets=null}
2025-07-17 12:24:54 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x2b2d513a, L:/*************:8091 - R:/*************:4274] read idle.
2025-07-17 12:24:54 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4274 to server channel inactive.
2025-07-17 12:24:54 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2b2d513a, L:/*************:8091 - R:/*************:4274]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:4274', channel=[id: 0x2b2d513a, L:/*************:8091 - R:/*************:4274], resourceSets=null}
2025-07-17 12:24:54 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x2b2d513a, L:/*************:8091 - R:/*************:4274]
2025-07-17 12:24:54 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:4274 to server channel inactive.
2025-07-17 12:24:54 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2b2d513a, L:/*************:8091 ! R:/*************:4274]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:4274', channel=[id: 0x2b2d513a, L:/*************:8091 ! R:/*************:4274], resourceSets=null}
2025-07-17 12:24:58 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752726298963
timestamp=1752726298963
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x37c9d88f, L:/*************:8091 - R:/*************:13764],client version:1.7.1
2025-07-17 12:24:59 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xd2cacc11, L:/*************:8091 - R:/*************:13768],client version:1.7.1
2025-07-17 12:24:59 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752726299922
timestamp=1752726299922
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0xc2cc3010, L:/*************:8091 - R:/*************:13770],client version:1.7.1
2025-07-17 12:25:00 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xdcdee496, L:/*************:8091 - R:/*************:13772],client version:1.7.1
2025-07-17 12:25:00 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752726300539
timestamp=1752726300539
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x072af1d5, L:/*************:8091 - R:/*************:13774],client version:1.7.1
2025-07-17 12:25:00 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0x2e830f85, L:/*************:8091 - R:/*************:13777],client version:1.7.1
2025-07-17 12:25:02 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752726302256
timestamp=1752726302256
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x548f0ef0, L:/*************:8091 - R:/*************:6937],client version:1.7.1
2025-07-17 12:25:02 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xea74ec5a, L:/*************:8091 - R:/*************:6939],client version:1.7.1
2025-07-17 13:28:32 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13777 to server channel inactive.
2025-07-17 13:28:32 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2e830f85, L:/*************:8091 ! R:/*************:13777]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13777', channel=[id: 0x2e830f85, L:/*************:8091 ! R:/*************:13777], resourceSets=[]}
2025-07-17 13:28:33 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6937 to server channel inactive.
2025-07-17 13:28:33 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x548f0ef0, L:/*************:8091 ! R:/*************:6937]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6937', channel=[id: 0x548f0ef0, L:/*************:8091 ! R:/*************:6937], resourceSets=null}
2025-07-17 13:28:33 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13774 to server channel inactive.
2025-07-17 13:28:33 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x072af1d5, L:/*************:8091 ! R:/*************:13774]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:13774', channel=[id: 0x072af1d5, L:/*************:8091 ! R:/*************:13774], resourceSets=null}
2025-07-17 13:28:33 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6939 to server channel inactive.
2025-07-17 13:28:33 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xea74ec5a, L:/*************:8091 ! R:/*************:6939]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6939', channel=[id: 0xea74ec5a, L:/*************:8091 ! R:/*************:6939], resourceSets=[]}
2025-07-17 13:28:35 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13764 to server channel inactive.
2025-07-17 13:28:35 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x37c9d88f, L:/*************:8091 ! R:/*************:13764]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13764', channel=[id: 0x37c9d88f, L:/*************:8091 ! R:/*************:13764], resourceSets=null}
2025-07-17 13:28:35 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xd2cacc11, L:/*************:8091 - R:/*************:13768] read idle.
2025-07-17 13:28:35 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13768 to server channel inactive.
2025-07-17 13:28:35 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd2cacc11, L:/*************:8091 - R:/*************:13768]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13768', channel=[id: 0xd2cacc11, L:/*************:8091 - R:/*************:13768], resourceSets=[]}
2025-07-17 13:28:35 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xd2cacc11, L:/*************:8091 - R:/*************:13768]
2025-07-17 13:28:35 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13768 to server channel inactive.
2025-07-17 13:28:35 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd2cacc11, L:/*************:8091 ! R:/*************:13768]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:13768', channel=[id: 0xd2cacc11, L:/*************:8091 ! R:/*************:13768], resourceSets=[]}
2025-07-17 13:28:35 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13770 to server channel inactive.
2025-07-17 13:28:35 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc2cc3010, L:/*************:8091 ! R:/*************:13770]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13770', channel=[id: 0xc2cc3010, L:/*************:8091 ! R:/*************:13770], resourceSets=null}
2025-07-17 13:28:36 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752730116144
timestamp=1752730116144
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x6cf642e3, L:/*************:8091 - R:/*************:12710],client version:1.7.1
2025-07-17 13:28:36 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:13772 to server channel inactive.
2025-07-17 13:28:36 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdcdee496, L:/*************:8091 ! R:/*************:13772]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:13772', channel=[id: 0xdcdee496, L:/*************:8091 ! R:/*************:13772], resourceSets=null}
2025-07-17 13:28:36 [ServerHandlerThread_1_12_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x64a49e62, L:/*************:8091 - R:/*************:12712],client version:1.7.1
2025-07-17 13:28:37 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752730117082
timestamp=1752730117082
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x3ca0f41b, L:/*************:8091 - R:/*************:12715],client version:1.7.1
2025-07-17 13:28:37 [ServerHandlerThread_1_13_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x5a599891, L:/*************:8091 - R:/*************:12716],client version:1.7.1
2025-07-17 13:28:37 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752730117696
timestamp=1752730117696
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x9d394b76, L:/*************:8091 - R:/*************:12717],client version:1.7.1
2025-07-17 13:28:38 [ServerHandlerThread_1_14_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xb90e765c, L:/*************:8091 - R:/*************:12719],client version:1.7.1
2025-07-17 13:28:39 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752730119422
timestamp=1752730119422
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xe8892ce5, L:/*************:8091 - R:/*************:12727],client version:1.7.1
2025-07-17 13:28:40 [ServerHandlerThread_1_15_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x8e0c3139, L:/*************:8091 - R:/*************:12741],client version:1.7.1
2025-07-17 14:10:13 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
2025-07-17 14:31:26 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-17 14:31:26 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-17 14:31:26 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-17 14:31:26 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-17 14:31:27 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-17 14:31:27 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-17 14:31:27 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-17 14:31:27 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-17 14:31:27 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 1444 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-17 14:31:27 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-17 14:31:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-17 14:31:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-17 14:31:28 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-17 14:31:28 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-17 14:31:29 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-17 14:31:29 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1536 ms
2025-07-17 14:31:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-17 14:31:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-17 14:31:29 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-17 14:31:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@702432cc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5039c2cf, org.springframework.security.web.context.SecurityContextPersistenceFilter@4c9bed65, org.springframework.security.web.header.HeaderWriterFilter@170f0fd6, org.springframework.security.web.authentication.logout.LogoutFilter@736a8aea, io.seata.console.filter.JwtAuthenticationTokenFilter@355b4c34, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7e35d743, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@590f806a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5ca006ac, org.springframework.security.web.session.SessionManagementFilter@eb09112, org.springframework.security.web.access.ExceptionTranslationFilter@506e382b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6dc7b7f]
2025-07-17 14:31:30 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-17 14:31:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-17 14:31:30 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.438 seconds (JVM running for 5.661)
2025-07-17 14:31:30 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-17 14:31:30 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-17 14:31:30 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-17 14:31:30 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-17 14:31:30 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-17 14:31:30 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-17 14:31:30 [main] INFO  io.seata.server.ServerRunner - seata server started in 820 millSeconds
2025-07-17 14:31:31 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752733891175
timestamp=1752733891175
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x09590707, L:/*************:8091 - R:/*************:2504],client version:1.7.1
2025-07-17 14:31:35 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x998b6363, L:/*************:8091 - R:/*************:2532],client version:1.7.1
2025-07-17 14:31:38 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752733898732
timestamp=1752733898732
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x52a06436, L:/*************:8091 - R:/*************:2567],client version:1.7.1
2025-07-17 14:31:40 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752733900485
timestamp=1752733900485
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0xefa2b773, L:/*************:8091 - R:/*************:2575],client version:1.7.1
2025-07-17 14:31:41 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x31f902a3, L:/*************:8091 - R:/*************:2590],client version:1.7.1
2025-07-17 14:31:43 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xb481aafd, L:/*************:8091 - R:/*************:2604],client version:1.7.1
2025-07-17 14:32:30 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752733950068
timestamp=1752733950068
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x3305e5e3, L:/*************:8091 - R:/*************:2711],client version:1.7.1
2025-07-17 14:32:30 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xc44ee090, L:/*************:8091 - R:/*************:2712],client version:1.7.1
2025-07-17 14:39:19 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2567 to server channel inactive.
2025-07-17 14:39:19 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2590 to server channel inactive.
2025-07-17 14:39:19 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x52a06436, L:/*************:8091 ! R:/*************:2567]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2567', channel=[id: 0x52a06436, L:/*************:8091 ! R:/*************:2567], resourceSets=null}
2025-07-17 14:39:19 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x31f902a3, L:/*************:8091 ! R:/*************:2590]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:2590', channel=[id: 0x31f902a3, L:/*************:8091 ! R:/*************:2590], resourceSets=[]}
2025-07-17 14:39:33 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752734372800
timestamp=1752734372800
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0x6b95bbc7, L:/*************:8091 - R:/*************:3465],client version:1.7.1
2025-07-17 14:39:36 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x554f3dd8, L:/*************:8091 - R:/*************:3475],client version:1.7.1
2025-07-17 15:30:04 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3475 to server channel inactive.
2025-07-17 15:30:04 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:3465 to server channel inactive.
2025-07-17 15:30:04 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6b95bbc7, L:/*************:8091 ! R:/*************:3465]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:3465', channel=[id: 0x6b95bbc7, L:/*************:8091 ! R:/*************:3465], resourceSets=null}
2025-07-17 15:30:04 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x554f3dd8, L:/*************:8091 ! R:/*************:3475]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:3475', channel=[id: 0x554f3dd8, L:/*************:8091 ! R:/*************:3475], resourceSets=[]}
2025-07-17 15:30:16 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752737415886
timestamp=1752737415886
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xfe923879, L:/*************:8091 - R:/*************:6492],client version:1.7.1
2025-07-17 15:30:20 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x294e2597, L:/*************:8091 - R:/*************:6502],client version:1.7.1
2025-07-17 18:28:46 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2604 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb481aafd, L:/*************:8091 ! R:/*************:2604]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:2604', channel=[id: 0xb481aafd, L:/*************:8091 ! R:/*************:2604], resourceSets=[]}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2532 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x998b6363, L:/*************:8091 ! R:/*************:2532]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2532', channel=[id: 0x998b6363, L:/*************:8091 ! R:/*************:2532], resourceSets=[]}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xc44ee090, L:/*************:8091 - R:/*************:2712] read idle.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2712 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc44ee090, L:/*************:8091 - R:/*************:2712]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2712', channel=[id: 0xc44ee090, L:/*************:8091 - R:/*************:2712], resourceSets=null}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xc44ee090, L:/*************:8091 - R:/*************:2712]
2025-07-17 18:28:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2712 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc44ee090, L:/*************:8091 ! R:/*************:2712]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2712', channel=[id: 0xc44ee090, L:/*************:8091 ! R:/*************:2712], resourceSets=null}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xefa2b773, L:/*************:8091 - R:/*************:2575] read idle.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2575 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xefa2b773, L:/*************:8091 - R:/*************:2575]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:2575', channel=[id: 0xefa2b773, L:/*************:8091 - R:/*************:2575], resourceSets=null}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xefa2b773, L:/*************:8091 - R:/*************:2575]
2025-07-17 18:28:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2575 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xefa2b773, L:/*************:8091 ! R:/*************:2575]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:*************:2575', channel=[id: 0xefa2b773, L:/*************:8091 ! R:/*************:2575], resourceSets=null}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x09590707, L:/*************:8091 - R:/*************:2504] read idle.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2504 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x09590707, L:/*************:8091 - R:/*************:2504]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2504', channel=[id: 0x09590707, L:/*************:8091 - R:/*************:2504], resourceSets=null}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x09590707, L:/*************:8091 - R:/*************:2504]
2025-07-17 18:28:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2504 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x09590707, L:/*************:8091 ! R:/*************:2504]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:*************:2504', channel=[id: 0x09590707, L:/*************:8091 ! R:/*************:2504], resourceSets=null}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x294e2597, L:/*************:8091 - R:/*************:6502] read idle.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6502 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x3305e5e3, L:/*************:8091 - R:/*************:2711] read idle.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2711 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x294e2597, L:/*************:8091 - R:/*************:6502]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6502', channel=[id: 0x294e2597, L:/*************:8091 - R:/*************:6502], resourceSets=[]}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3305e5e3, L:/*************:8091 - R:/*************:2711]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2711', channel=[id: 0x3305e5e3, L:/*************:8091 - R:/*************:2711], resourceSets=null}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x294e2597, L:/*************:8091 - R:/*************:6502]
2025-07-17 18:28:47 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x3305e5e3, L:/*************:8091 - R:/*************:2711]
2025-07-17 18:28:47 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6502 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:2711 to server channel inactive.
2025-07-17 18:28:47 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x294e2597, L:/*************:8091 ! R:/*************:6502]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6502', channel=[id: 0x294e2597, L:/*************:8091 ! R:/*************:6502], resourceSets=[]}
2025-07-17 18:28:47 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3305e5e3, L:/*************:8091 ! R:/*************:2711]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:*************:2711', channel=[id: 0x3305e5e3, L:/*************:8091 ! R:/*************:2711], resourceSets=null}
2025-07-17 18:28:48 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - *************:6492 to server channel inactive.
2025-07-17 18:28:48 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfe923879, L:/*************:8091 ! R:/*************:6492]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:*************:6492', channel=[id: 0xfe923879, L:/*************:8091 ! R:/*************:6492], resourceSets=null}
2025-07-17 18:28:48 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752748128650
timestamp=1752748128650
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'},channel:[id: 0xcef356c2, L:/*************:8091 - R:/*************:10375],client version:1.7.1
2025-07-17 18:28:49 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x99d40f47, L:/*************:8091 - R:/*************:10502],client version:1.7.1
2025-07-17 18:28:53 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,*************,1752748133036
timestamp=1752748133036
authVersion=V4
vgroup=yumeng-auth-group
ip=*************
'},channel:[id: 0x400b8bbf, L:/*************:8091 - R:/*************:10785],client version:1.7.1
2025-07-17 18:28:53 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0xe50b5154, L:/*************:8091 - R:/*************:10790],client version:1.7.1
2025-07-17 18:28:53 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752748133296
timestamp=1752748133296
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'},channel:[id: 0x1fe77229, L:/*************:8091 - R:/*************:10791],client version:1.7.1
2025-07-17 18:28:53 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xb07c3e05, L:/*************:8091 - R:/*************:10797],client version:1.7.1
2025-07-17 18:28:53 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,*************,1752748133948
timestamp=1752748133948
authVersion=V4
vgroup=yumeng-system-group
ip=*************
'},channel:[id: 0x3ea7618c, L:/*************:8091 - R:/*************:10806],client version:1.7.1
2025-07-17 18:28:54 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x84b324d2, L:/*************:8091 - R:/*************:10819],client version:1.7.1
2025-07-17 18:30:45 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
