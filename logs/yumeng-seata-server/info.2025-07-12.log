2025-07-12 08:32:35 [main] INFO  io.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-12 08:32:35 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-12 08:32:35 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-12 08:32:35 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-12 08:32:36 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to get current node abilities...
2025-07-12 08:32:36 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-12 08:32:36 [main] INFO  c.a.n.c.a.AbstractAbilityControlManager - Initialize current abilities finish...
2025-07-12 08:32:36 [main] INFO  c.a.n.c.a.d.NacosAbilityManagerHolder - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-12 08:32:36 [main] INFO  i.s.server.SeataServerApplication - Starting SeataServerApplication using Java 17.0.10 on LAPTOP-9O0OI624 with PID 16872 (D:\project\yumeng\ship-Integrated-management-api\yumeng-visual\yumeng-seata-server\target\classes started by qingyi in D:\project\yumeng)
2025-07-12 08:32:36 [main] INFO  i.s.server.SeataServerApplication - The following 1 profile is active: "dev"
2025-07-12 08:32:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 7091 (http)
2025-07-12 08:32:38 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7091"]
2025-07-12 08:32:38 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-12 08:32:38 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-12 08:32:38 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-12 08:32:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1643 ms
2025-07-12 08:32:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-12 08:32:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-12 08:32:39 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.css']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.js']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.html']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.map']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.svg']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.png']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.jpeg']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/**/*.ico']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will not secure Ant [pattern='/api/v1/auth/login']
2025-07-12 08:32:39 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2b9370cc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1a56f608, org.springframework.security.web.context.SecurityContextPersistenceFilter@63ccb1b2, org.springframework.security.web.header.HeaderWriterFilter@2f5a092e, org.springframework.security.web.authentication.logout.LogoutFilter@67216c33, io.seata.console.filter.JwtAuthenticationTokenFilter@2809e38a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1b868ef0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c9b6332, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@b9c9bf5, org.springframework.security.web.session.SessionManagementFilter@7da1ef46, org.springframework.security.web.access.ExceptionTranslationFilter@6520625f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1574f323]
2025-07-12 08:32:39 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7091"]
2025-07-12 08:32:39 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 7091 (http) with context path ''
2025-07-12 08:32:39 [main] INFO  i.s.server.SeataServerApplication - Started SeataServerApplication in 4.672 seconds (JVM running for 5.709)
2025-07-12 08:32:39 [main] INFO  i.seata.server.session.SessionHolder - use session store mode: db
2025-07-12 08:32:40 [main] INFO  i.s.server.lock.LockerManagerFactory - use lock store mode: db
2025-07-12 08:32:40 [main] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Server started, service listen port: 8091
2025-07-12 08:32:40 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-12 08:32:40 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-12 08:32:40 [main] INFO  io.seata.server.ServerRunner - 
 you can visit seata console UI on http://127.0.0.1:7091. 
 log path: ./logs/seata.
2025-07-12 08:32:40 [main] INFO  io.seata.server.ServerRunner - seata server started in 941 millSeconds
2025-07-12 08:32:41 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752280361462
timestamp=1752280361462
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'},channel:[id: 0xff3ec15d, L:/************:8091 - R:/************:2170],client version:1.7.1
2025-07-12 08:32:41 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x41eaa07b, L:/************:8091 - R:/************:2172],client version:1.7.1
2025-07-12 08:32:48 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752280368042
timestamp=1752280368042
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xe2a81759, L:/************:8091 - R:/************:2186],client version:1.7.1
2025-07-12 08:32:48 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x28e4ad67, L:/************:8091 - R:/************:2187],client version:1.7.1
2025-07-12 08:32:51 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752280371025
timestamp=1752280371025
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'},channel:[id: 0x4e508e18, L:/************:8091 - R:/************:2191],client version:1.7.1
2025-07-12 08:32:51 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752280371603
timestamp=1752280371603
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0x70d5f675, L:/************:8091 - R:/************:2192],client version:1.7.1
2025-07-12 08:32:51 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xec4e9ab2, L:/************:8091 - R:/************:2193],client version:1.7.1
2025-07-12 08:32:53 [ServerHandlerThread_1_4_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xe92f60e1, L:/************:8091 - R:/************:2197],client version:1.7.1
2025-07-12 08:40:41 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752280841804
timestamp=1752280841804
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x55acbec3, L:/************:8091 - R:/************:2711],client version:1.7.1
2025-07-12 08:40:44 [ServerHandlerThread_1_5_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xad513a94, L:/************:8091 - R:/************:2717],client version:1.7.1
2025-07-12 08:40:58 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2717 to server channel inactive.
2025-07-12 08:40:58 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2711 to server channel inactive.
2025-07-12 08:40:58 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x55acbec3, L:/************:8091 ! R:/************:2711]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2711', channel=[id: 0x55acbec3, L:/************:8091 ! R:/************:2711], resourceSets=null}
2025-07-12 08:40:58 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xad513a94, L:/************:8091 ! R:/************:2717]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2717', channel=[id: 0xad513a94, L:/************:8091 ! R:/************:2717], resourceSets=[]}
2025-07-12 08:48:09 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752281289198
timestamp=1752281289198
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xb47ee88d, L:/************:8091 - R:/************:3054],client version:1.7.1
2025-07-12 08:48:11 [ServerHandlerThread_1_6_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xede48734, L:/************:8091 - R:/************:3059],client version:1.7.1
2025-07-12 08:48:24 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:3054 to server channel inactive.
2025-07-12 08:48:24 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb47ee88d, L:/************:8091 ! R:/************:3054]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:3054', channel=[id: 0xb47ee88d, L:/************:8091 ! R:/************:3054], resourceSets=null}
2025-07-12 08:48:24 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:3059 to server channel inactive.
2025-07-12 08:48:24 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xede48734, L:/************:8091 ! R:/************:3059]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:3059', channel=[id: 0xede48734, L:/************:8091 ! R:/************:3059], resourceSets=[]}
2025-07-12 08:53:30 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752281610256
timestamp=1752281610256
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xa77f8499, L:/************:8091 - R:/************:3888],client version:1.7.1
2025-07-12 08:53:32 [ServerHandlerThread_1_7_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x038f77e3, L:/************:8091 - R:/************:3893],client version:1.7.1
2025-07-12 08:53:46 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:3888 to server channel inactive.
2025-07-12 08:53:46 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa77f8499, L:/************:8091 ! R:/************:3888]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:3888', channel=[id: 0xa77f8499, L:/************:8091 ! R:/************:3888], resourceSets=null}
2025-07-12 08:53:46 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:3893 to server channel inactive.
2025-07-12 08:53:46 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x038f77e3, L:/************:8091 ! R:/************:3893]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:3893', channel=[id: 0x038f77e3, L:/************:8091 ! R:/************:3893], resourceSets=[]}
2025-07-12 08:56:12 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752281772001
timestamp=1752281772001
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xf1baba6a, L:/************:8091 - R:/************:4052],client version:1.7.1
2025-07-12 08:56:14 [ServerHandlerThread_1_8_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xdd4044e0, L:/************:8091 - R:/************:4058],client version:1.7.1
2025-07-12 08:56:28 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4052 to server channel inactive.
2025-07-12 08:56:28 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf1baba6a, L:/************:8091 ! R:/************:4052]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:4052', channel=[id: 0xf1baba6a, L:/************:8091 ! R:/************:4052], resourceSets=null}
2025-07-12 08:56:28 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4058 to server channel inactive.
2025-07-12 08:56:28 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xdd4044e0, L:/************:8091 ! R:/************:4058]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:4058', channel=[id: 0xdd4044e0, L:/************:8091 ! R:/************:4058], resourceSets=[]}
2025-07-12 08:57:15 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752281835271
timestamp=1752281835271
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x06debab0, L:/************:8091 - R:/************:4123],client version:1.7.1
2025-07-12 08:57:17 [ServerHandlerThread_1_9_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x9177ef51, L:/************:8091 - R:/************:4128],client version:1.7.1
2025-07-12 08:57:40 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x06debab0, L:/************:8091 - R:/************:4123] read idle.
2025-07-12 08:57:40 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4123 to server channel inactive.
2025-07-12 08:57:40 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x06debab0, L:/************:8091 - R:/************:4123]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:4123', channel=[id: 0x06debab0, L:/************:8091 - R:/************:4123], resourceSets=null}
2025-07-12 08:57:40 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x06debab0, L:/************:8091 - R:/************:4123]
2025-07-12 08:57:40 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4123 to server channel inactive.
2025-07-12 08:57:40 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x06debab0, L:/************:8091 ! R:/************:4123]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:4123', channel=[id: 0x06debab0, L:/************:8091 ! R:/************:4123], resourceSets=null}
2025-07-12 08:57:42 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x9177ef51, L:/************:8091 - R:/************:4128] read idle.
2025-07-12 08:57:42 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4128 to server channel inactive.
2025-07-12 08:57:42 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9177ef51, L:/************:8091 - R:/************:4128]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:4128', channel=[id: 0x9177ef51, L:/************:8091 - R:/************:4128], resourceSets=[]}
2025-07-12 08:57:42 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x9177ef51, L:/************:8091 - R:/************:4128]
2025-07-12 08:57:42 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4128 to server channel inactive.
2025-07-12 08:57:42 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9177ef51, L:/************:8091 ! R:/************:4128]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:4128', channel=[id: 0x9177ef51, L:/************:8091 ! R:/************:4128], resourceSets=[]}
2025-07-12 09:02:37 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752282157666
timestamp=1752282157666
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xb9b10ac3, L:/************:8091 - R:/************:4482],client version:1.7.1
2025-07-12 09:02:40 [ServerHandlerThread_1_10_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x0dcf0707, L:/************:8091 - R:/************:4488],client version:1.7.1
2025-07-12 09:02:54 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4482 to server channel inactive.
2025-07-12 09:02:54 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb9b10ac3, L:/************:8091 ! R:/************:4482]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:4482', channel=[id: 0xb9b10ac3, L:/************:8091 ! R:/************:4482], resourceSets=null}
2025-07-12 09:02:54 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:4488 to server channel inactive.
2025-07-12 09:02:54 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0dcf0707, L:/************:8091 ! R:/************:4488]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:4488', channel=[id: 0x0dcf0707, L:/************:8091 ! R:/************:4488], resourceSets=[]}
2025-07-12 09:52:51 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752285171006
timestamp=1752285171006
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x0eaa07ef, L:/************:8091 - R:/************:9169],client version:1.7.1
2025-07-12 09:52:54 [ServerHandlerThread_1_11_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x85f7d7c3, L:/************:8091 - R:/************:9176],client version:1.7.1
2025-07-12 09:53:08 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9169 to server channel inactive.
2025-07-12 09:53:08 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0eaa07ef, L:/************:8091 ! R:/************:9169]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9169', channel=[id: 0x0eaa07ef, L:/************:8091 ! R:/************:9169], resourceSets=null}
2025-07-12 09:53:08 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9176 to server channel inactive.
2025-07-12 09:53:08 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x85f7d7c3, L:/************:8091 ! R:/************:9176]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9176', channel=[id: 0x85f7d7c3, L:/************:8091 ! R:/************:9176], resourceSets=[]}
2025-07-12 09:55:18 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752285318737
timestamp=1752285318737
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x1b999da9, L:/************:8091 - R:/************:9340],client version:1.7.1
2025-07-12 09:55:21 [ServerHandlerThread_1_12_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x57630927, L:/************:8091 - R:/************:9346],client version:1.7.1
2025-07-12 09:55:35 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9340 to server channel inactive.
2025-07-12 09:55:35 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x1b999da9, L:/************:8091 ! R:/************:9340]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9340', channel=[id: 0x1b999da9, L:/************:8091 ! R:/************:9340], resourceSets=null}
2025-07-12 09:55:35 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9346 to server channel inactive.
2025-07-12 09:55:35 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x57630927, L:/************:8091 ! R:/************:9346]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9346', channel=[id: 0x57630927, L:/************:8091 ! R:/************:9346], resourceSets=[]}
2025-07-12 09:57:01 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752285421196
timestamp=1752285421196
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xcc8aa6c6, L:/************:8091 - R:/************:9430],client version:1.7.1
2025-07-12 09:57:03 [ServerHandlerThread_1_13_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x64ebecd7, L:/************:8091 - R:/************:9434],client version:1.7.1
2025-07-12 09:57:18 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9430 to server channel inactive.
2025-07-12 09:57:18 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcc8aa6c6, L:/************:8091 ! R:/************:9430]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9430', channel=[id: 0xcc8aa6c6, L:/************:8091 ! R:/************:9430], resourceSets=null}
2025-07-12 09:57:18 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9434 to server channel inactive.
2025-07-12 09:57:18 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x64ebecd7, L:/************:8091 ! R:/************:9434]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9434', channel=[id: 0x64ebecd7, L:/************:8091 ! R:/************:9434], resourceSets=[]}
2025-07-12 09:58:21 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752285501642
timestamp=1752285501642
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x20afc1b9, L:/************:8091 - R:/************:9516],client version:1.7.1
2025-07-12 09:58:24 [ServerHandlerThread_1_14_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xd716370c, L:/************:8091 - R:/************:9520],client version:1.7.1
2025-07-12 09:58:39 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9516 to server channel inactive.
2025-07-12 09:58:39 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x20afc1b9, L:/************:8091 ! R:/************:9516]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9516', channel=[id: 0x20afc1b9, L:/************:8091 ! R:/************:9516], resourceSets=null}
2025-07-12 09:58:39 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9520 to server channel inactive.
2025-07-12 09:58:39 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd716370c, L:/************:8091 ! R:/************:9520]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9520', channel=[id: 0xd716370c, L:/************:8091 ! R:/************:9520], resourceSets=[]}
2025-07-12 10:02:27 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752285747484
timestamp=1752285747484
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xd1482df1, L:/************:8091 - R:/************:10030],client version:1.7.1
2025-07-12 10:02:30 [ServerHandlerThread_1_15_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x8d466ed6, L:/************:8091 - R:/************:10034],client version:1.7.1
2025-07-12 10:02:44 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10030 to server channel inactive.
2025-07-12 10:02:44 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd1482df1, L:/************:8091 ! R:/************:10030]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10030', channel=[id: 0xd1482df1, L:/************:8091 ! R:/************:10030], resourceSets=null}
2025-07-12 10:02:44 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10034 to server channel inactive.
2025-07-12 10:02:44 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8d466ed6, L:/************:8091 ! R:/************:10034]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10034', channel=[id: 0x8d466ed6, L:/************:8091 ! R:/************:10034], resourceSets=[]}
2025-07-12 10:03:32 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752285812239
timestamp=1752285812239
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xf6711dff, L:/************:8091 - R:/************:10141],client version:1.7.1
2025-07-12 10:03:34 [ServerHandlerThread_1_16_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x80d7e965, L:/************:8091 - R:/************:10146],client version:1.7.1
2025-07-12 10:03:49 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10141 to server channel inactive.
2025-07-12 10:03:49 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf6711dff, L:/************:8091 ! R:/************:10141]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10141', channel=[id: 0xf6711dff, L:/************:8091 ! R:/************:10141], resourceSets=null}
2025-07-12 10:03:49 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10146 to server channel inactive.
2025-07-12 10:03:49 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x80d7e965, L:/************:8091 ! R:/************:10146]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10146', channel=[id: 0x80d7e965, L:/************:8091 ! R:/************:10146], resourceSets=[]}
2025-07-12 10:07:15 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752286035459
timestamp=1752286035459
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x46501f3b, L:/************:8091 - R:/************:10435],client version:1.7.1
2025-07-12 10:07:18 [ServerHandlerThread_1_17_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x9617c490, L:/************:8091 - R:/************:10441],client version:1.7.1
2025-07-12 10:07:32 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10435 to server channel inactive.
2025-07-12 10:07:32 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x46501f3b, L:/************:8091 ! R:/************:10435]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10435', channel=[id: 0x46501f3b, L:/************:8091 ! R:/************:10435], resourceSets=null}
2025-07-12 10:07:32 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10441 to server channel inactive.
2025-07-12 10:07:32 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9617c490, L:/************:8091 ! R:/************:10441]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10441', channel=[id: 0x9617c490, L:/************:8091 ! R:/************:10441], resourceSets=[]}
2025-07-12 10:11:06 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2186 to server channel inactive.
2025-07-12 10:11:06 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe2a81759, L:/************:8091 ! R:/************:2186]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2186', channel=[id: 0xe2a81759, L:/************:8091 ! R:/************:2186], resourceSets=null}
2025-07-12 10:11:06 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2187 to server channel inactive.
2025-07-12 10:11:06 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x28e4ad67, L:/************:8091 ! R:/************:2187]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2187', channel=[id: 0x28e4ad67, L:/************:8091 ! R:/************:2187], resourceSets=[]}
2025-07-12 10:11:16 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752286276095
timestamp=1752286276095
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x4558c4a1, L:/************:8091 - R:/************:10885],client version:1.7.1
2025-07-12 10:11:21 [ServerHandlerThread_1_18_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xd37c790f, L:/************:8091 - R:/************:10902],client version:1.7.1
2025-07-12 10:11:50 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752286310700
timestamp=1752286310700
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x7f5a165c, L:/************:8091 - R:/************:10963],client version:1.7.1
2025-07-12 10:11:53 [ServerHandlerThread_1_19_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xb80d8a11, L:/************:8091 - R:/************:10967],client version:1.7.1
2025-07-12 10:12:07 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10963 to server channel inactive.
2025-07-12 10:12:07 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7f5a165c, L:/************:8091 ! R:/************:10963]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10963', channel=[id: 0x7f5a165c, L:/************:8091 ! R:/************:10963], resourceSets=null}
2025-07-12 10:12:07 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10967 to server channel inactive.
2025-07-12 10:12:07 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb80d8a11, L:/************:8091 ! R:/************:10967]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10967', channel=[id: 0xb80d8a11, L:/************:8091 ! R:/************:10967], resourceSets=[]}
2025-07-12 10:14:54 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752286493981
timestamp=1752286493981
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x5c2e4346, L:/************:8091 - R:/************:11140],client version:1.7.1
2025-07-12 10:14:56 [ServerHandlerThread_1_20_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x7e416676, L:/************:8091 - R:/************:11150],client version:1.7.1
2025-07-12 10:15:14 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11140 to server channel inactive.
2025-07-12 10:15:14 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5c2e4346, L:/************:8091 ! R:/************:11140]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11140', channel=[id: 0x5c2e4346, L:/************:8091 ! R:/************:11140], resourceSets=null}
2025-07-12 10:15:14 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11150 to server channel inactive.
2025-07-12 10:15:14 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7e416676, L:/************:8091 ! R:/************:11150]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11150', channel=[id: 0x7e416676, L:/************:8091 ! R:/************:11150], resourceSets=[]}
2025-07-12 10:17:53 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752286673433
timestamp=1752286673433
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xf8b102d5, L:/************:8091 - R:/************:11359],client version:1.7.1
2025-07-12 10:17:56 [ServerHandlerThread_1_21_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x0b1f16d8, L:/************:8091 - R:/************:11368],client version:1.7.1
2025-07-12 10:18:10 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11359 to server channel inactive.
2025-07-12 10:18:10 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf8b102d5, L:/************:8091 ! R:/************:11359]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11359', channel=[id: 0xf8b102d5, L:/************:8091 ! R:/************:11359], resourceSets=null}
2025-07-12 10:18:10 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11368 to server channel inactive.
2025-07-12 10:18:10 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0b1f16d8, L:/************:8091 ! R:/************:11368]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11368', channel=[id: 0x0b1f16d8, L:/************:8091 ! R:/************:11368], resourceSets=[]}
2025-07-12 10:18:45 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10885 to server channel inactive.
2025-07-12 10:18:45 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4558c4a1, L:/************:8091 ! R:/************:10885]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10885', channel=[id: 0x4558c4a1, L:/************:8091 ! R:/************:10885], resourceSets=null}
2025-07-12 10:18:45 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10902 to server channel inactive.
2025-07-12 10:18:45 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd37c790f, L:/************:8091 ! R:/************:10902]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10902', channel=[id: 0xd37c790f, L:/************:8091 ! R:/************:10902], resourceSets=[]}
2025-07-12 10:18:55 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752286735313
timestamp=1752286735313
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xf521d7ee, L:/************:8091 - R:/************:11469],client version:1.7.1
2025-07-12 10:18:58 [ServerHandlerThread_1_22_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x7ae85d9f, L:/************:8091 - R:/************:11474],client version:1.7.1
2025-07-12 10:19:38 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752286778157
timestamp=1752286778157
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x2b989f35, L:/************:8091 - R:/************:11546],client version:1.7.1
2025-07-12 10:19:40 [ServerHandlerThread_1_23_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x353522a6, L:/************:8091 - R:/************:11550],client version:1.7.1
2025-07-12 10:19:55 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11546 to server channel inactive.
2025-07-12 10:19:55 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2b989f35, L:/************:8091 ! R:/************:11546]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11546', channel=[id: 0x2b989f35, L:/************:8091 ! R:/************:11546], resourceSets=null}
2025-07-12 10:19:55 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11550 to server channel inactive.
2025-07-12 10:19:55 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x353522a6, L:/************:8091 ! R:/************:11550]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11550', channel=[id: 0x353522a6, L:/************:8091 ! R:/************:11550], resourceSets=[]}
2025-07-12 10:22:59 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11469 to server channel inactive.
2025-07-12 10:22:59 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf521d7ee, L:/************:8091 ! R:/************:11469]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11469', channel=[id: 0xf521d7ee, L:/************:8091 ! R:/************:11469], resourceSets=null}
2025-07-12 10:22:59 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11474 to server channel inactive.
2025-07-12 10:22:59 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7ae85d9f, L:/************:8091 ! R:/************:11474]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11474', channel=[id: 0x7ae85d9f, L:/************:8091 ! R:/************:11474], resourceSets=[]}
2025-07-12 10:23:09 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752286989057
timestamp=1752286989057
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xc2f6f744, L:/************:8091 - R:/************:11767],client version:1.7.1
2025-07-12 10:23:11 [ServerHandlerThread_1_24_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x0f03d780, L:/************:8091 - R:/************:11775],client version:1.7.1
2025-07-12 10:23:46 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752287026675
timestamp=1752287026675
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xcf57776e, L:/************:8091 - R:/************:11887],client version:1.7.1
2025-07-12 10:23:49 [ServerHandlerThread_1_25_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xf20198f3, L:/************:8091 - R:/************:11894],client version:1.7.1
2025-07-12 10:24:04 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11887 to server channel inactive.
2025-07-12 10:24:04 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcf57776e, L:/************:8091 ! R:/************:11887]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11887', channel=[id: 0xcf57776e, L:/************:8091 ! R:/************:11887], resourceSets=null}
2025-07-12 10:24:04 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11894 to server channel inactive.
2025-07-12 10:24:04 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xf20198f3, L:/************:8091 ! R:/************:11894]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11894', channel=[id: 0xf20198f3, L:/************:8091 ! R:/************:11894], resourceSets=[]}
2025-07-12 10:27:58 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752287278151
timestamp=1752287278151
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x8c07166a, L:/************:8091 - R:/************:12526],client version:1.7.1
2025-07-12 10:28:01 [ServerHandlerThread_1_26_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x54518275, L:/************:8091 - R:/************:12540],client version:1.7.1
2025-07-12 10:28:17 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12526 to server channel inactive.
2025-07-12 10:28:17 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8c07166a, L:/************:8091 ! R:/************:12526]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:12526', channel=[id: 0x8c07166a, L:/************:8091 ! R:/************:12526], resourceSets=null}
2025-07-12 10:28:17 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12540 to server channel inactive.
2025-07-12 10:28:17 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x54518275, L:/************:8091 ! R:/************:12540]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:12540', channel=[id: 0x54518275, L:/************:8091 ! R:/************:12540], resourceSets=[]}
2025-07-12 10:28:45 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11767 to server channel inactive.
2025-07-12 10:28:45 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc2f6f744, L:/************:8091 ! R:/************:11767]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11767', channel=[id: 0xc2f6f744, L:/************:8091 ! R:/************:11767], resourceSets=null}
2025-07-12 10:28:45 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11775 to server channel inactive.
2025-07-12 10:28:45 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0f03d780, L:/************:8091 ! R:/************:11775]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11775', channel=[id: 0x0f03d780, L:/************:8091 ! R:/************:11775], resourceSets=[]}
2025-07-12 10:28:58 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752287337878
timestamp=1752287337878
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x7fbd0cc9, L:/************:8091 - R:/************:12665],client version:1.7.1
2025-07-12 10:29:00 [ServerHandlerThread_1_27_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xfe8733f7, L:/************:8091 - R:/************:12672],client version:1.7.1
2025-07-12 10:29:40 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752287380176
timestamp=1752287380176
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xfa5c97a8, L:/************:8091 - R:/************:12763],client version:1.7.1
2025-07-12 10:29:42 [ServerHandlerThread_1_28_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xb26249c7, L:/************:8091 - R:/************:12768],client version:1.7.1
2025-07-12 10:29:58 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12763 to server channel inactive.
2025-07-12 10:29:58 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfa5c97a8, L:/************:8091 ! R:/************:12763]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:12763', channel=[id: 0xfa5c97a8, L:/************:8091 ! R:/************:12763], resourceSets=null}
2025-07-12 10:29:58 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12768 to server channel inactive.
2025-07-12 10:29:58 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xb26249c7, L:/************:8091 ! R:/************:12768]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:12768', channel=[id: 0xb26249c7, L:/************:8091 ! R:/************:12768], resourceSets=[]}
2025-07-12 10:49:12 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12672 to server channel inactive.
2025-07-12 10:49:12 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:12665 to server channel inactive.
2025-07-12 10:49:12 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x7fbd0cc9, L:/************:8091 ! R:/************:12665]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:12665', channel=[id: 0x7fbd0cc9, L:/************:8091 ! R:/************:12665], resourceSets=null}
2025-07-12 10:49:12 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfe8733f7, L:/************:8091 ! R:/************:12672]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:12672', channel=[id: 0xfe8733f7, L:/************:8091 ! R:/************:12672], resourceSets=[]}
2025-07-12 10:49:24 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752288564795
timestamp=1752288564795
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x145a2e3b, L:/************:8091 - R:/************:14332],client version:1.7.1
2025-07-12 10:49:27 [ServerHandlerThread_1_29_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x378afb2f, L:/************:8091 - R:/************:14337],client version:1.7.1
2025-07-12 13:27:43 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xe92f60e1, L:/************:8091 - R:/************:2197] read idle.
2025-07-12 13:27:43 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x145a2e3b, L:/************:8091 - R:/************:14332] read idle.
2025-07-12 13:27:43 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:14332 to server channel inactive.
2025-07-12 13:27:43 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x145a2e3b, L:/************:8091 - R:/************:14332]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:14332', channel=[id: 0x145a2e3b, L:/************:8091 - R:/************:14332], resourceSets=null}
2025-07-12 13:27:43 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x145a2e3b, L:/************:8091 - R:/************:14332]
2025-07-12 13:27:43 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:14332 to server channel inactive.
2025-07-12 13:27:43 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x145a2e3b, L:/************:8091 ! R:/************:14332]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:14332', channel=[id: 0x145a2e3b, L:/************:8091 ! R:/************:14332], resourceSets=null}
2025-07-12 13:27:43 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2197 to server channel inactive.
2025-07-12 13:27:43 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe92f60e1, L:/************:8091 - R:/************:2197]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:2197', channel=[id: 0xe92f60e1, L:/************:8091 - R:/************:2197], resourceSets=[]}
2025-07-12 13:27:43 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xe92f60e1, L:/************:8091 - R:/************:2197]
2025-07-12 13:27:43 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2197 to server channel inactive.
2025-07-12 13:27:43 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe92f60e1, L:/************:8091 ! R:/************:2197]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:2197', channel=[id: 0xe92f60e1, L:/************:8091 ! R:/************:2197], resourceSets=[]}
2025-07-12 13:27:43 [ServerHandlerThread_1_30_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xbf337fef, L:/************:8091 - R:/************:10177],client version:1.7.1
2025-07-12 13:27:44 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752298064789
timestamp=1752298064789
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x210bf753, L:/************:8091 - R:/************:10182],client version:1.7.1
2025-07-12 13:27:45 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x378afb2f, L:/************:8091 - R:/************:14337] read idle.
2025-07-12 13:27:45 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:14337 to server channel inactive.
2025-07-12 13:27:45 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x378afb2f, L:/************:8091 - R:/************:14337]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:14337', channel=[id: 0x378afb2f, L:/************:8091 - R:/************:14337], resourceSets=[]}
2025-07-12 13:27:45 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x378afb2f, L:/************:8091 - R:/************:14337]
2025-07-12 13:27:45 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:14337 to server channel inactive.
2025-07-12 13:27:45 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x378afb2f, L:/************:8091 ! R:/************:14337]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:14337', channel=[id: 0x378afb2f, L:/************:8091 ! R:/************:14337], resourceSets=[]}
2025-07-12 13:27:45 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2170 to server channel inactive.
2025-07-12 13:27:45 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xff3ec15d, L:/************:8091 ! R:/************:2170]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:2170', channel=[id: 0xff3ec15d, L:/************:8091 ! R:/************:2170], resourceSets=null}
2025-07-12 13:27:45 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2172 to server channel inactive.
2025-07-12 13:27:45 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x41eaa07b, L:/************:8091 ! R:/************:2172]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:2172', channel=[id: 0x41eaa07b, L:/************:8091 ! R:/************:2172], resourceSets=null}
2025-07-12 13:27:45 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2191 to server channel inactive.
2025-07-12 13:27:45 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4e508e18, L:/************:8091 ! R:/************:2191]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:2191', channel=[id: 0x4e508e18, L:/************:8091 ! R:/************:2191], resourceSets=null}
2025-07-12 13:27:45 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2192 to server channel inactive.
2025-07-12 13:27:45 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x70d5f675, L:/************:8091 ! R:/************:2192]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:2192', channel=[id: 0x70d5f675, L:/************:8091 ! R:/************:2192], resourceSets=null}
2025-07-12 13:27:45 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2193 to server channel inactive.
2025-07-12 13:27:45 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xec4e9ab2, L:/************:8091 ! R:/************:2193]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:2193', channel=[id: 0xec4e9ab2, L:/************:8091 ! R:/************:2193], resourceSets=[]}
2025-07-12 13:27:47 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752298067969
timestamp=1752298067969
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'},channel:[id: 0xda40332e, L:/************:8091 - R:/************:2080],client version:1.7.1
2025-07-12 13:27:48 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752298068515
timestamp=1752298068515
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'},channel:[id: 0xd1d3087b, L:/************:8091 - R:/************:2340],client version:1.7.1
2025-07-12 13:27:48 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752298068664
timestamp=1752298068664
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0x12d0a438, L:/************:8091 - R:/************:2342],client version:1.7.1
2025-07-12 13:27:48 [ServerHandlerThread_1_31_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x0622759c, L:/************:8091 - R:/************:2344],client version:1.7.1
2025-07-12 13:27:48 [ServerHandlerThread_1_32_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x8f14ed2d, L:/************:8091 - R:/************:2350],client version:1.7.1
2025-07-12 13:27:51 [ServerHandlerThread_1_33_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x6034d7a3, L:/************:8091 - R:/************:1605],client version:1.7.1
2025-07-12 14:17:08 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10182 to server channel inactive.
2025-07-12 14:17:08 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:1605 to server channel inactive.
2025-07-12 14:17:08 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x210bf753, L:/************:8091 ! R:/************:10182]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10182', channel=[id: 0x210bf753, L:/************:8091 ! R:/************:10182], resourceSets=null}
2025-07-12 14:17:08 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x6034d7a3, L:/************:8091 ! R:/************:1605]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:1605', channel=[id: 0x6034d7a3, L:/************:8091 ! R:/************:1605], resourceSets=[]}
2025-07-12 14:17:23 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752301043348
timestamp=1752301043348
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x602db815, L:/************:8091 - R:/************:11648],client version:1.7.1
2025-07-12 14:17:26 [ServerHandlerThread_1_34_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xe2230c45, L:/************:8091 - R:/************:11664],client version:1.7.1
2025-07-12 15:13:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11664 to server channel inactive.
2025-07-12 15:13:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11648 to server channel inactive.
2025-07-12 15:13:47 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x602db815, L:/************:8091 ! R:/************:11648]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11648', channel=[id: 0x602db815, L:/************:8091 ! R:/************:11648], resourceSets=null}
2025-07-12 15:13:47 [NettyServerNIOWorker_1_4_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe2230c45, L:/************:8091 ! R:/************:11664]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11664', channel=[id: 0xe2230c45, L:/************:8091 ! R:/************:11664], resourceSets=[]}
2025-07-12 15:14:00 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752304440367
timestamp=1752304440367
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xac86b9e4, L:/************:8091 - R:/************:9312],client version:1.7.1
2025-07-12 15:14:03 [ServerHandlerThread_1_35_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xcdb06b0a, L:/************:8091 - R:/************:9319],client version:1.7.1
2025-07-12 15:38:19 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9312 to server channel inactive.
2025-07-12 15:38:19 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9319 to server channel inactive.
2025-07-12 15:38:19 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xac86b9e4, L:/************:8091 ! R:/************:9312]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9312', channel=[id: 0xac86b9e4, L:/************:8091 ! R:/************:9312], resourceSets=null}
2025-07-12 15:38:19 [NettyServerNIOWorker_1_6_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xcdb06b0a, L:/************:8091 ! R:/************:9319]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9319', channel=[id: 0xcdb06b0a, L:/************:8091 ! R:/************:9319], resourceSets=[]}
2025-07-12 15:38:30 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752305910650
timestamp=1752305910650
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x4a75f072, L:/************:8091 - R:/************:13921],client version:1.7.1
2025-07-12 15:38:34 [ServerHandlerThread_1_36_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xaf695e50, L:/************:8091 - R:/************:13932],client version:1.7.1
2025-07-12 16:05:14 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:13921 to server channel inactive.
2025-07-12 16:05:14 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:13932 to server channel inactive.
2025-07-12 16:05:14 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4a75f072, L:/************:8091 ! R:/************:13921]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:13921', channel=[id: 0x4a75f072, L:/************:8091 ! R:/************:13921], resourceSets=null}
2025-07-12 16:05:14 [NettyServerNIOWorker_1_8_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xaf695e50, L:/************:8091 ! R:/************:13932]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:13932', channel=[id: 0xaf695e50, L:/************:8091 ! R:/************:13932], resourceSets=[]}
2025-07-12 16:05:27 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752307527294
timestamp=1752307527294
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x96c87c38, L:/************:8091 - R:/************:7141],client version:1.7.1
2025-07-12 16:05:30 [ServerHandlerThread_1_37_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xd793fbb5, L:/************:8091 - R:/************:7147],client version:1.7.1
2025-07-12 16:15:11 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7141 to server channel inactive.
2025-07-12 16:15:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:7147 to server channel inactive.
2025-07-12 16:15:11 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x96c87c38, L:/************:8091 ! R:/************:7141]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:7141', channel=[id: 0x96c87c38, L:/************:8091 ! R:/************:7141], resourceSets=null}
2025-07-12 16:15:11 [NettyServerNIOWorker_1_10_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd793fbb5, L:/************:8091 ! R:/************:7147]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:7147', channel=[id: 0xd793fbb5, L:/************:8091 ! R:/************:7147], resourceSets=[]}
2025-07-12 16:15:25 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752308125155
timestamp=1752308125155
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xa751d3b9, L:/************:8091 - R:/************:9392],client version:1.7.1
2025-07-12 16:15:29 [ServerHandlerThread_1_38_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x3b474a05, L:/************:8091 - R:/************:9413],client version:1.7.1
2025-07-12 16:21:38 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9392 to server channel inactive.
2025-07-12 16:21:38 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9413 to server channel inactive.
2025-07-12 16:21:38 [NettyServerNIOWorker_1_11_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xa751d3b9, L:/************:8091 ! R:/************:9392]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9392', channel=[id: 0xa751d3b9, L:/************:8091 ! R:/************:9392], resourceSets=null}
2025-07-12 16:21:38 [NettyServerNIOWorker_1_12_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x3b474a05, L:/************:8091 ! R:/************:9413]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:9413', channel=[id: 0x3b474a05, L:/************:8091 ! R:/************:9413], resourceSets=[]}
2025-07-12 16:21:49 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752308509426
timestamp=1752308509426
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xe8979230, L:/************:8091 - R:/************:11008],client version:1.7.1
2025-07-12 16:21:52 [ServerHandlerThread_1_39_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xd8b09435, L:/************:8091 - R:/************:11028],client version:1.7.1
2025-07-12 16:23:07 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xd8b09435, L:/************:8091 - R:/************:11028] read idle.
2025-07-12 16:23:07 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11028 to server channel inactive.
2025-07-12 16:23:07 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd8b09435, L:/************:8091 - R:/************:11028]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11028', channel=[id: 0xd8b09435, L:/************:8091 - R:/************:11028], resourceSets=[]}
2025-07-12 16:23:07 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xd8b09435, L:/************:8091 - R:/************:11028]
2025-07-12 16:23:07 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11028 to server channel inactive.
2025-07-12 16:23:07 [NettyServerNIOWorker_1_14_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd8b09435, L:/************:8091 ! R:/************:11028]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11028', channel=[id: 0xd8b09435, L:/************:8091 ! R:/************:11028], resourceSets=[]}
2025-07-12 16:23:14 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xe8979230, L:/************:8091 - R:/************:11008] read idle.
2025-07-12 16:23:14 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11008 to server channel inactive.
2025-07-12 16:23:14 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe8979230, L:/************:8091 - R:/************:11008]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11008', channel=[id: 0xe8979230, L:/************:8091 - R:/************:11008], resourceSets=null}
2025-07-12 16:23:14 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xe8979230, L:/************:8091 - R:/************:11008]
2025-07-12 16:23:14 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11008 to server channel inactive.
2025-07-12 16:23:14 [NettyServerNIOWorker_1_13_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe8979230, L:/************:8091 ! R:/************:11008]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11008', channel=[id: 0xe8979230, L:/************:8091 ! R:/************:11008], resourceSets=null}
2025-07-12 16:24:56 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752308696426
timestamp=1752308696426
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xd19185e8, L:/************:8091 - R:/************:11427],client version:1.7.1
2025-07-12 16:24:56 [ServerHandlerThread_1_40_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x460d0eec, L:/************:8091 - R:/************:11428],client version:1.7.1
2025-07-12 16:39:37 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11427 to server channel inactive.
2025-07-12 16:39:37 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:11428 to server channel inactive.
2025-07-12 16:39:37 [NettyServerNIOWorker_1_15_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd19185e8, L:/************:8091 ! R:/************:11427]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11427', channel=[id: 0xd19185e8, L:/************:8091 ! R:/************:11427], resourceSets=null}
2025-07-12 16:39:37 [NettyServerNIOWorker_1_16_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x460d0eec, L:/************:8091 ! R:/************:11428]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:11428', channel=[id: 0x460d0eec, L:/************:8091 ! R:/************:11428], resourceSets=[]}
2025-07-12 16:39:48 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752309588014
timestamp=1752309588014
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xc31d7865, L:/************:8091 - R:/************:14796],client version:1.7.1
2025-07-12 16:39:52 [ServerHandlerThread_1_41_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x8506d3f6, L:/************:8091 - R:/************:14811],client version:1.7.1
2025-07-12 16:51:28 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:14796 to server channel inactive.
2025-07-12 16:51:28 [NettyServerNIOWorker_1_17_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc31d7865, L:/************:8091 ! R:/************:14796]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:14796', channel=[id: 0xc31d7865, L:/************:8091 ! R:/************:14796], resourceSets=null}
2025-07-12 16:51:28 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:14811 to server channel inactive.
2025-07-12 16:51:28 [NettyServerNIOWorker_1_18_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8506d3f6, L:/************:8091 ! R:/************:14811]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:14811', channel=[id: 0x8506d3f6, L:/************:8091 ! R:/************:14811], resourceSets=[]}
2025-07-12 16:51:43 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752310302829
timestamp=1752310302829
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x0288faa0, L:/************:8091 - R:/************:2119],client version:1.7.1
2025-07-12 16:51:46 [ServerHandlerThread_1_42_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xe2a0ca74, L:/************:8091 - R:/************:2131],client version:1.7.1
2025-07-12 16:57:12 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2119 to server channel inactive.
2025-07-12 16:57:12 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2131 to server channel inactive.
2025-07-12 16:57:12 [NettyServerNIOWorker_1_19_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0288faa0, L:/************:8091 ! R:/************:2119]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2119', channel=[id: 0x0288faa0, L:/************:8091 ! R:/************:2119], resourceSets=null}
2025-07-12 16:57:12 [NettyServerNIOWorker_1_20_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe2a0ca74, L:/************:8091 ! R:/************:2131]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2131', channel=[id: 0xe2a0ca74, L:/************:8091 ! R:/************:2131], resourceSets=[]}
2025-07-12 16:57:22 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752310642661
timestamp=1752310642661
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x5aade4b5, L:/************:8091 - R:/************:2679],client version:1.7.1
2025-07-12 16:57:26 [ServerHandlerThread_1_43_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x8ca83980, L:/************:8091 - R:/************:2686],client version:1.7.1
2025-07-12 16:58:13 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2679 to server channel inactive.
2025-07-12 16:58:13 [NettyServerNIOWorker_1_21_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5aade4b5, L:/************:8091 ! R:/************:2679]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2679', channel=[id: 0x5aade4b5, L:/************:8091 ! R:/************:2679], resourceSets=null}
2025-07-12 16:58:13 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2686 to server channel inactive.
2025-07-12 16:58:13 [NettyServerNIOWorker_1_22_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8ca83980, L:/************:8091 ! R:/************:2686]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2686', channel=[id: 0x8ca83980, L:/************:8091 ! R:/************:2686], resourceSets=[]}
2025-07-12 16:58:48 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752310728204
timestamp=1752310728204
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x2d9fc091, L:/************:8091 - R:/************:2878],client version:1.7.1
2025-07-12 16:58:51 [ServerHandlerThread_1_44_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0xfed0a039, L:/************:8091 - R:/************:2897],client version:1.7.1
2025-07-12 17:05:22 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2878 to server channel inactive.
2025-07-12 17:05:22 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2897 to server channel inactive.
2025-07-12 17:05:22 [NettyServerNIOWorker_1_23_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2d9fc091, L:/************:8091 ! R:/************:2878]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2878', channel=[id: 0x2d9fc091, L:/************:8091 ! R:/************:2878], resourceSets=null}
2025-07-12 17:05:22 [NettyServerNIOWorker_1_24_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xfed0a039, L:/************:8091 ! R:/************:2897]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:2897', channel=[id: 0xfed0a039, L:/************:8091 ! R:/************:2897], resourceSets=[]}
2025-07-12 17:05:36 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752311136000
timestamp=1752311136000
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x9ae8f4e1, L:/************:8091 - R:/************:3852],client version:1.7.1
2025-07-12 17:05:39 [ServerHandlerThread_1_45_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x49d55366, L:/************:8091 - R:/************:3857],client version:1.7.1
2025-07-12 18:41:12 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2340 to server channel inactive.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xd1d3087b, L:/************:8091 ! R:/************:2340]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:2340', channel=[id: 0xd1d3087b, L:/************:8091 ! R:/************:2340], resourceSets=null}
2025-07-12 18:41:12 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2080 to server channel inactive.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2342 to server channel inactive.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2344 to server channel inactive.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0622759c, L:/************:8091 ! R:/************:2344]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:2344', channel=[id: 0x0622759c, L:/************:8091 ! R:/************:2344], resourceSets=null}
2025-07-12 18:41:12 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xda40332e, L:/************:8091 ! R:/************:2080]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:2080', channel=[id: 0xda40332e, L:/************:8091 ! R:/************:2080], resourceSets=null}
2025-07-12 18:41:12 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x12d0a438, L:/************:8091 ! R:/************:2342]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:2342', channel=[id: 0x12d0a438, L:/************:8091 ! R:/************:2342], resourceSets=null}
2025-07-12 18:41:12 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x9ae8f4e1, L:/************:8091 - R:/************:3852] read idle.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:3852 to server channel inactive.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9ae8f4e1, L:/************:8091 - R:/************:3852]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:3852', channel=[id: 0x9ae8f4e1, L:/************:8091 - R:/************:3852], resourceSets=null}
2025-07-12 18:41:12 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x9ae8f4e1, L:/************:8091 - R:/************:3852]
2025-07-12 18:41:12 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:3852 to server channel inactive.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_25_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x9ae8f4e1, L:/************:8091 ! R:/************:3852]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:3852', channel=[id: 0x9ae8f4e1, L:/************:8091 ! R:/************:3852], resourceSets=null}
2025-07-12 18:41:12 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:2350 to server channel inactive.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0xbf337fef, L:/************:8091 - R:/************:10177] read idle.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10177 to server channel inactive.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbf337fef, L:/************:8091 - R:/************:10177]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:10177', channel=[id: 0xbf337fef, L:/************:8091 - R:/************:10177], resourceSets=[]}
2025-07-12 18:41:12 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x8f14ed2d, L:/************:8091 ! R:/************:2350]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:2350', channel=[id: 0x8f14ed2d, L:/************:8091 ! R:/************:2350], resourceSets=[]}
2025-07-12 18:41:12 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0xbf337fef, L:/************:8091 - R:/************:10177]
2025-07-12 18:41:12 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10177 to server channel inactive.
2025-07-12 18:41:12 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xbf337fef, L:/************:8091 ! R:/************:10177]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:10177', channel=[id: 0xbf337fef, L:/************:8091 ! R:/************:10177], resourceSets=[]}
2025-07-12 18:41:12 [ServerHandlerThread_1_46_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0x80278c7a, L:/************:8091 - R:/************:9108],client version:1.7.1
2025-07-12 18:41:14 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752316874312
timestamp=1752316874312
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'},channel:[id: 0x4b26edeb, L:/************:8091 - R:/************:10016],client version:1.7.1
2025-07-12 18:41:14 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752316874661
timestamp=1752316874661
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'},channel:[id: 0xc507fcc8, L:/************:8091 - R:/************:10024],client version:1.7.1
2025-07-12 18:41:15 [ServerHandlerThread_1_47_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xe2456c11, L:/************:8091 - R:/************:10027],client version:1.7.1
2025-07-12 18:41:15 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752316875365
timestamp=1752316875365
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0x2d490c16, L:/************:8091 - R:/************:10036],client version:1.7.1
2025-07-12 18:41:15 [ServerHandlerThread_1_48_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x5b05565a, L:/************:8091 - R:/************:10037],client version:1.7.1
2025-07-12 18:41:15 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:3857 to server channel inactive.
2025-07-12 18:41:15 [NettyServerNIOWorker_1_26_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x49d55366, L:/************:8091 ! R:/************:3857]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:3857', channel=[id: 0x49d55366, L:/************:8091 ! R:/************:3857], resourceSets=[]}
2025-07-12 18:41:22 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752316882452
timestamp=1752316882452
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0x0f3182c0, L:/************:8091 - R:/************:10194],client version:1.7.1
2025-07-12 18:41:22 [ServerHandlerThread_1_49_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x815a78db, L:/************:8091 - R:/************:10198],client version:1.7.1
2025-07-12 19:03:11 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10016 to server channel inactive.
2025-07-12 19:03:11 [NettyServerNIOWorker_1_28_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x4b26edeb, L:/************:8091 ! R:/************:10016]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:10016', channel=[id: 0x4b26edeb, L:/************:8091 ! R:/************:10016], resourceSets=null}
2025-07-12 19:03:11 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10024 to server channel inactive.
2025-07-12 19:03:11 [NettyServerNIOWorker_1_29_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xc507fcc8, L:/************:8091 ! R:/************:10024]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:10024', channel=[id: 0xc507fcc8, L:/************:8091 ! R:/************:10024], resourceSets=null}
2025-07-12 19:03:12 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10027 to server channel inactive.
2025-07-12 19:03:12 [NettyServerNIOWorker_1_30_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0xe2456c11, L:/************:8091 ! R:/************:10027]context:RpcContext{applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', clientId='yumeng-resource:************:10027', channel=[id: 0xe2456c11, L:/************:8091 ! R:/************:10027], resourceSets=[]}
2025-07-12 19:03:12 [NettyServerNIOWorker_1_3_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='ak=null
digest=yumeng-auth-group,************,1752318192257
timestamp=1752318192257
authVersion=V4
vgroup=yumeng-auth-group
ip=************
'},channel:[id: 0xac24462f, L:/************:8091 - R:/************:4203],client version:1.7.1
2025-07-12 19:03:12 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10036 to server channel inactive.
2025-07-12 19:03:12 [NettyServerNIOWorker_1_31_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x2d490c16, L:/************:8091 ! R:/************:10036]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:10036', channel=[id: 0x2d490c16, L:/************:8091 ! R:/************:10036], resourceSets=null}
2025-07-12 19:03:12 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10037 to server channel inactive.
2025-07-12 19:03:12 [NettyServerNIOWorker_1_32_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x5b05565a, L:/************:8091 ! R:/************:10037]context:RpcContext{applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', clientId='yumeng-auth:************:10037', channel=[id: 0x5b05565a, L:/************:8091 ! R:/************:10037], resourceSets=null}
2025-07-12 19:03:13 [ServerHandlerThread_1_50_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='null', version='1.7.1', applicationId='yumeng-auth', transactionServiceGroup='yumeng-auth-group', extraData='null'},channel:[id: 0x32626bd6, L:/************:8091 - R:/************:4236],client version:1.7.1
2025-07-12 19:03:14 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10194 to server channel inactive.
2025-07-12 19:03:14 [NettyServerNIOWorker_1_1_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x0f3182c0, L:/************:8091 ! R:/************:10194]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10194', channel=[id: 0x0f3182c0, L:/************:8091 ! R:/************:10194], resourceSets=null}
2025-07-12 19:03:14 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:9108 to server channel inactive.
2025-07-12 19:03:14 [NettyServerNIOWorker_1_27_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x80278c7a, L:/************:8091 ! R:/************:9108]context:RpcContext{applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', clientId='yumeng-salary-settlement:************:9108', channel=[id: 0x80278c7a, L:/************:8091 ! R:/************:9108], resourceSets=[]}
2025-07-12 19:03:15 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - channel:[id: 0x815a78db, L:/************:8091 - R:/************:10198] read idle.
2025-07-12 19:03:15 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10198 to server channel inactive.
2025-07-12 19:03:15 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x815a78db, L:/************:8091 - R:/************:10198]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10198', channel=[id: 0x815a78db, L:/************:8091 - R:/************:10198], resourceSets=[]}
2025-07-12 19:03:15 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - closeChannelHandlerContext channel:[id: 0x815a78db, L:/************:8091 - R:/************:10198]
2025-07-12 19:03:15 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - ************:10198 to server channel inactive.
2025-07-12 19:03:15 [NettyServerNIOWorker_1_2_32] INFO  i.s.c.r.n.AbstractNettyRemotingServer - remove channel:[id: 0x815a78db, L:/************:8091 ! R:/************:10198]context:RpcContext{applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', clientId='yumeng-system:************:10198', channel=[id: 0x815a78db, L:/************:8091 ! R:/************:10198], resourceSets=[]}
2025-07-12 19:03:19 [NettyServerNIOWorker_1_5_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='ak=null
digest=yumeng-system-group,************,1752318199428
timestamp=1752318199428
authVersion=V4
vgroup=yumeng-system-group
ip=************
'},channel:[id: 0xe2e99951, L:/************:8091 - R:/************:4338],client version:1.7.1
2025-07-12 19:03:19 [ServerHandlerThread_1_1_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-system', transactionServiceGroup='yumeng-system-group', extraData='null'},channel:[id: 0x8eef476c, L:/************:8091 - R:/************:4343],client version:1.7.1
2025-07-12 19:03:21 [NettyServerNIOWorker_1_7_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752318201636
timestamp=1752318201636
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'},channel:[id: 0xfd4d880a, L:/************:8091 - R:/************:4351],client version:1.7.1
2025-07-12 19:03:22 [ServerHandlerThread_1_2_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'},channel:[id: 0xbdcc4eff, L:/************:8091 - R:/************:4817],client version:1.7.1
2025-07-12 19:03:22 [NettyServerNIOWorker_1_9_32] INFO  i.s.c.r.p.server.RegTmProcessor - TM register success,message:RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1752318202341
timestamp=1752318202341
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'},channel:[id: 0x5b5a00ab, L:/************:8091 - R:/************:4822],client version:1.7.1
2025-07-12 19:03:22 [ServerHandlerThread_1_3_500] INFO  i.s.c.r.p.server.RegRmProcessor - RM register success,message:RegisterRMRequest{resourceIds='jdbc:mysql://***********:3307/yumeng-salary', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'},channel:[id: 0xee7c65a7, L:/************:8091 - R:/************:4824],client version:1.7.1
2025-07-12 19:03:53 [SpringApplicationShutdownHook] INFO  i.s.c.rpc.netty.NettyServerBootstrap - Shutting server down, the listen port: 8091
