2025-07-29 09:20:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 09:20:30 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 30708 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 09:20:30 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-29 09:20:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-29 09:20:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 09:20:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 09:20:35 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 09:20:35 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 09:20:35 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 09:20:35 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 09:20:35 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 09:20:35 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 09:20:35 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 09:20:35 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753752035904
timestamp=1753752035904
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 09:20:36 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5a765a66, L:/***************:13847 - R:/***************:8091]
2025-07-29 09:20:36 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 54 ms, version:1.7.1,role:TMROLE,channel:[id: 0x5a765a66, L:/***************:13847 - R:/***************:8091]
2025-07-29 09:20:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-29 09:20:36 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 09:20:36 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 09:20:36 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 09:20:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-29 09:20:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 09:20:37 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 09:20:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 09:20:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 09:20:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 09:20:39 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2d8a45a
2025-07-29 09:20:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 09:20:39 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 09:20:39 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 09:20:39 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 09:20:39 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xc2df9b25, L:/***************:13889 - R:/***************:8091]
2025-07-29 09:20:39 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 10 ms, version:1.7.1,role:RMROLE,channel:[id: 0xc2df9b25, L:/***************:13889 - R:/***************:8091]
2025-07-29 09:20:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 09:20:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 09:20:40 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 09:20:42 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 09:20:42 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 09:20:43 [redisson-netty-2-7] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 09:20:43 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 09:20:45 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 09:20:45 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 09:20:45 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 09:20:45 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 09:20:46 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource ***************:9204 register finished
2025-07-29 09:20:51 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 24.514 seconds (process running for 25.767)
2025-07-29 09:20:51 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-29 09:20:51 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-29 09:20:51 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 09:20:51 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 09:20:51 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-29 09:20:52 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 09:58:37 [DubboServerHandler-***************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-29 09:58:38 [DubboServerHandler-***************:20880-thread-5] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-29 09:58:39 [DubboServerHandler-***************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[1839ms]
2025-07-29 10:14:04 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5a765a66, L:/***************:13847 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xc2df9b25, L:/***************:13889 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5a765a66, L:/***************:13847 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xc2df9b25, L:/***************:13889 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc2df9b25, L:/***************:13889 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5a765a66, L:/***************:13847 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5a765a66, L:/***************:13847 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc2df9b25, L:/***************:13889 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc2df9b25, L:/***************:13889 ! R:/***************:8091]) will closed
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a765a66, L:/***************:13847 ! R:/***************:8091]) will closed
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a765a66, L:/***************:13847 ! R:/***************:8091]) will closed
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc2df9b25, L:/***************:13889 ! R:/***************:8091]) will closed
2025-07-29 10:14:15 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 10:14:15 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753755255580
timestamp=1753755255580
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 10:14:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 10:14:16 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 10:14:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 10:14:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe5a08384, L:null ! R:/***************:8091]) will closed
2025-07-29 10:14:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeb5faf76, L:null ! R:/***************:8091]) will closed
2025-07-29 10:14:25 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 10:14:25 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753755265580
timestamp=1753755265580
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 10:14:25 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7ee69b17, L:/***************:9168 - R:/***************:8091]
2025-07-29 10:14:25 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:TMROLE,channel:[id: 0x7ee69b17, L:/***************:9168 - R:/***************:8091]
2025-07-29 10:14:26 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 10:14:26 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 10:14:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 10:14:26 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe1607c16, L:/***************:9170 - R:/***************:8091]
2025-07-29 10:14:26 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe1607c16, L:/***************:9170 - R:/***************:8091]
2025-07-29 11:34:33 [DubboServerHandler-***************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 11:34:34 [DubboServerHandler-***************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[299ms]
2025-07-29 11:34:34 [DubboServerHandler-***************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 11:34:34 [DubboServerHandler-***************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[26ms]
2025-07-29 11:34:35 [DubboServerHandler-***************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 11:34:35 [DubboServerHandler-***************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[29ms]
2025-07-29 11:34:35 [DubboServerHandler-***************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 11:34:35 [DubboServerHandler-***************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[26ms]
2025-07-29 11:34:36 [DubboServerHandler-***************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 11:34:36 [DubboServerHandler-***************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[26ms]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7ee69b17, L:/***************:9168 - R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe1607c16, L:/***************:9170 - R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x7ee69b17, L:/***************:9168 - R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xe1607c16, L:/***************:9170 - R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7ee69b17, L:/***************:9168 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7ee69b17, L:/***************:9168 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7ee69b17, L:/***************:9168 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7ee69b17, L:/***************:9168 ! R:/***************:8091]) will closed
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7ee69b17, L:/***************:9168 ! R:/***************:8091]) will closed
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe1607c16, L:/***************:9170 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe1607c16, L:/***************:9170 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe1607c16, L:/***************:9170 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe1607c16, L:/***************:9170 ! R:/***************:8091]) will closed
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe1607c16, L:/***************:9170 ! R:/***************:8091]) will closed
2025-07-29 13:28:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:28:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753766922414
timestamp=1753766922414
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:28:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0cb9ab65, L:null ! R:/***************:8091]) will closed
2025-07-29 13:28:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:28:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:28:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe1607c16, L:/***************:9170 ! R:/***************:8091]
2025-07-29 13:28:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe1607c16, L:/***************:9170 ! R:/***************:8091]
2025-07-29 13:28:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:28:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfacc4af2, L:null ! R:/***************:8091]) will closed
2025-07-29 13:28:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:28:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753766932413
timestamp=1753766932413
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:28:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:28:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:28:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:29:02 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa9900f57, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753766942415
timestamp=1753766942415
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:29:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:29:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:29:02 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe967a931, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x901f0bc2, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753766952416
timestamp=1753766952416
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:29:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb327eacc, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:29:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:29:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x670f6732, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:22 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:22 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753766962419
timestamp=1753766962419
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:29:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:29:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:29:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb989d824, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf9ed264e, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753766972420
timestamp=1753766972420
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:29:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:29:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:29:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5d71eb78, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xec321102, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753766982423
timestamp=1753766982423
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:29:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x89eec6a0, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:29:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:29:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe0c949ef, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753766992424
timestamp=1753766992424
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:29:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf41e6038, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:29:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:30:02 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2dc4d734, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767002428
timestamp=1753767002428
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:30:02 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2735f7c7, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:30:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:30:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0bad5d62, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767012429
timestamp=1753767012429
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:30:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:30:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:30:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x443ea70e, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x46ca583f, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:22 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:22 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767022431
timestamp=1753767022431
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:30:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfea9d150, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:30:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:30:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4ac8a9a2, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767032432
timestamp=1753767032432
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:30:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48b0d9f9, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:30:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:30:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767042433
timestamp=1753767042433
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:30:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac70d6fe, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6c57f14b, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:30:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:30:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x22e8398d, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767052435
timestamp=1753767052435
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:30:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2735a5c, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:30:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:31:02 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc9ff9d2d, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767062436
timestamp=1753767062436
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:31:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:31:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:31:02 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x020a25d2, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x988b8aed, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767072440
timestamp=1753767072440
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:31:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:31:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:31:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0acbea19, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:19 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-29 13:31:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd6983f25, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:22 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:22 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767082443
timestamp=1753767082443
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:31:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd3e07558, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:31:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:31:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6e64406b, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:31:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1753767092446
timestamp=1753767092446
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-29 13:31:32 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x07a7e01a, L:/*************:13390 - R:/*************:8091]
2025-07-29 13:31:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:TMROLE,channel:[id: 0x07a7e01a, L:/*************:13390 - R:/*************:8091]
2025-07-29 13:31:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdf73661f, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:31:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:31:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:31:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa08e9a5a, L:/*************:13392 - R:/*************:8091]
2025-07-29 13:31:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0xa08e9a5a, L:/*************:13392 - R:/*************:8091]
2025-07-29 13:32:25 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 13:32:25 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 13:32:25 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 13:32:25 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 13:32:25 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 13:32:26 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 13:32:26 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 13:32:26 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 13:32:26 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 13:32:26 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 13:32:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x07a7e01a, L:/*************:13390 ! R:/*************:8091]
2025-07-29 13:32:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x07a7e01a, L:/*************:13390 ! R:/*************:8091]
2025-07-29 13:32:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x07a7e01a, L:/*************:13390 ! R:/*************:8091]
2025-07-29 13:32:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x07a7e01a, L:/*************:13390 ! R:/*************:8091]
2025-07-29 13:32:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x07a7e01a, L:/*************:13390 ! R:/*************:8091]) will closed
2025-07-29 13:32:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x07a7e01a, L:/*************:13390 ! R:/*************:8091]) will closed
2025-07-29 13:32:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa08e9a5a, L:/*************:13392 ! R:/*************:8091]
2025-07-29 13:32:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa08e9a5a, L:/*************:13392 ! R:/*************:8091]
2025-07-29 13:32:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa08e9a5a, L:/*************:13392 ! R:/*************:8091]
2025-07-29 13:32:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa08e9a5a, L:/*************:13392 ! R:/*************:8091]
2025-07-29 13:32:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa08e9a5a, L:/*************:13392 ! R:/*************:8091]) will closed
2025-07-29 13:32:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa08e9a5a, L:/*************:13392 ! R:/*************:8091]) will closed
2025-07-29 13:32:32 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 13:32:32 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 37056 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 13:32:32 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-29 13:32:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-29 13:32:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 13:32:32 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 13:32:36 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 13:32:36 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 13:32:36 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 13:32:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 13:32:36 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 13:32:36 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 13:32:37 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:32:37 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753767157157
timestamp=1753767157157
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 13:32:37 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xcbd5e57e, L:/*************:13531 - R:/*************:8091]
2025-07-29 13:32:37 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 52 ms, version:1.7.1,role:TMROLE,channel:[id: 0xcbd5e57e, L:/*************:13531 - R:/*************:8091]
2025-07-29 13:32:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-29 13:32:37 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 13:32:37 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 13:32:37 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 13:32:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-29 13:32:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 13:32:38 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 13:32:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 13:32:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 13:32:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 13:32:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3413effc
2025-07-29 13:32:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 13:32:40 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:32:40 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:32:40 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:32:40 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf248b9c5, L:/*************:13545 - R:/*************:8091]
2025-07-29 13:32:40 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xf248b9c5, L:/*************:13545 - R:/*************:8091]
2025-07-29 13:32:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 13:32:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 13:32:41 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 13:32:42 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 13:32:42 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 13:32:43 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 13:32:43 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 13:32:45 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 13:32:45 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 13:32:45 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 13:32:45 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 13:32:45 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-29 13:32:48 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 18.198 seconds (process running for 19.734)
2025-07-29 13:32:48 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-29 13:32:48 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-29 13:32:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 13:32:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 13:32:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-29 13:32:49 [RMI TCP Connection(6)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 13:37:07 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 13:37:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xcbd5e57e, L:/*************:13531 ! R:/*************:8091]
2025-07-29 13:37:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xcbd5e57e, L:/*************:13531 ! R:/*************:8091]
2025-07-29 13:37:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xcbd5e57e, L:/*************:13531 ! R:/*************:8091]
2025-07-29 13:37:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xcbd5e57e, L:/*************:13531 ! R:/*************:8091]
2025-07-29 13:37:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcbd5e57e, L:/*************:13531 ! R:/*************:8091]) will closed
2025-07-29 13:37:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcbd5e57e, L:/*************:13531 ! R:/*************:8091]) will closed
2025-07-29 13:37:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf248b9c5, L:/*************:13545 ! R:/*************:8091]
2025-07-29 13:37:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf248b9c5, L:/*************:13545 ! R:/*************:8091]
2025-07-29 13:37:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf248b9c5, L:/*************:13545 ! R:/*************:8091]
2025-07-29 13:37:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf248b9c5, L:/*************:13545 ! R:/*************:8091]
2025-07-29 13:37:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf248b9c5, L:/*************:13545 ! R:/*************:8091]) will closed
2025-07-29 13:37:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf248b9c5, L:/*************:13545 ! R:/*************:8091]) will closed
2025-07-29 13:37:11 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 13:37:11 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 33408 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 13:37:11 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-29 13:37:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-29 13:37:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 13:37:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 13:37:15 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 13:37:15 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 13:37:15 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 13:37:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 13:37:16 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 13:37:16 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 13:37:16 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:37:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753767436275
timestamp=1753767436275
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 13:37:16 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x56ddade3, L:/*************:14371 - R:/*************:8091]
2025-07-29 13:37:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 68 ms, version:1.7.1,role:TMROLE,channel:[id: 0x56ddade3, L:/*************:14371 - R:/*************:8091]
2025-07-29 13:37:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-29 13:37:16 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 13:37:16 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 13:37:16 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 13:37:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-29 13:37:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 13:37:17 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 13:37:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 13:37:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 13:37:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 13:37:19 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@40c7cea8
2025-07-29 13:37:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 13:37:19 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:37:19 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:37:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:37:19 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x0b199fa0, L:/*************:14386 - R:/*************:8091]
2025-07-29 13:37:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x0b199fa0, L:/*************:14386 - R:/*************:8091]
2025-07-29 13:37:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 13:37:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 13:37:20 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 13:37:22 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 13:37:22 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 13:37:22 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 13:37:22 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 13:37:24 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 13:37:24 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 13:37:24 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 13:37:24 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 13:37:24 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-29 13:37:28 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 19.158 seconds (process running for 20.164)
2025-07-29 13:37:28 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-29 13:37:29 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-29 13:37:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 13:37:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 13:37:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-29 13:37:29 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 13:39:52 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:39:54 [DubboServerHandler-*************:20881-thread-5] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-29 13:39:54 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1083ms]
2025-07-29 13:39:54 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:39:54 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-29 13:39:54 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:39:54 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-29 13:39:55 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:39:55 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-29 13:39:56 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:39:56 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[112ms]
2025-07-29 13:40:09 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:09 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-29 13:40:10 [DubboServerHandler-*************:20881-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:10 [DubboServerHandler-*************:20881-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-29 13:40:10 [DubboServerHandler-*************:20881-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:10 [DubboServerHandler-*************:20881-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-29 13:40:10 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:10 [DubboServerHandler-*************:20881-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 13:40:11 [DubboServerHandler-*************:20881-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:11 [DubboServerHandler-*************:20881-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 13:40:12 [DubboServerHandler-*************:20881-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:12 [DubboServerHandler-*************:20881-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-29 13:40:12 [DubboServerHandler-*************:20881-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:12 [DubboServerHandler-*************:20881-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-29 13:40:13 [DubboServerHandler-*************:20881-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:13 [DubboServerHandler-*************:20881-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-29 13:40:13 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:13 [DubboServerHandler-*************:20881-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-29 13:40:14 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:14 [DubboServerHandler-*************:20881-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-29 13:40:15 [DubboServerHandler-*************:20881-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:40:15 [DubboServerHandler-*************:20881-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-29 13:43:14 [DubboServerHandler-*************:20881-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:14 [DubboServerHandler-*************:20881-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[59ms]
2025-07-29 13:43:15 [DubboServerHandler-*************:20881-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:15 [DubboServerHandler-*************:20881-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-29 13:43:15 [DubboServerHandler-*************:20881-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:15 [DubboServerHandler-*************:20881-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-29 13:43:15 [DubboServerHandler-*************:20881-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:15 [DubboServerHandler-*************:20881-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-29 13:43:16 [DubboServerHandler-*************:20881-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:16 [DubboServerHandler-*************:20881-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-29 13:43:18 [DubboServerHandler-*************:20881-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:18 [DubboServerHandler-*************:20881-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 13:43:19 [DubboServerHandler-*************:20881-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:19 [DubboServerHandler-*************:20881-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 13:43:19 [DubboServerHandler-*************:20881-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:19 [DubboServerHandler-*************:20881-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-29 13:43:20 [DubboServerHandler-*************:20881-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:20 [DubboServerHandler-*************:20881-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 13:43:21 [DubboServerHandler-*************:20881-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:21 [DubboServerHandler-*************:20881-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 13:43:28 [DubboServerHandler-*************:20881-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:28 [DubboServerHandler-*************:20881-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 13:43:28 [DubboServerHandler-*************:20881-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:28 [DubboServerHandler-*************:20881-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-29 13:43:29 [DubboServerHandler-*************:20881-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:29 [DubboServerHandler-*************:20881-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-29 13:43:29 [DubboServerHandler-*************:20881-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:29 [DubboServerHandler-*************:20881-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-29 13:43:30 [DubboServerHandler-*************:20881-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:30 [DubboServerHandler-*************:20881-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 13:43:31 [DubboServerHandler-*************:20881-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 13:43:31 [DubboServerHandler-*************:20881-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x56ddade3, L:/*************:14371 - R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x56ddade3, L:/*************:14371 - R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x0b199fa0, L:/*************:14386 - R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x56ddade3, L:/*************:14371 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x56ddade3, L:/*************:14371 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x56ddade3, L:/*************:14371 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x56ddade3, L:/*************:14371 ! R:/*************:8091]) will closed
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x56ddade3, L:/*************:14371 ! R:/*************:8091]) will closed
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x0b199fa0, L:/*************:14386 - R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x0b199fa0, L:/*************:14386 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x0b199fa0, L:/*************:14386 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x0b199fa0, L:/*************:14386 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0b199fa0, L:/*************:14386 ! R:/*************:8091]) will closed
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0b199fa0, L:/*************:14386 ! R:/*************:8091]) will closed
2025-07-29 13:48:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:48:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753768086008
timestamp=1753768086008
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 13:48:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x52b08e09, L:null ! R:/*************:8091]) will closed
2025-07-29 13:48:06 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:48:06 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:48:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x0b199fa0, L:/*************:14386 ! R:/*************:8091]
2025-07-29 13:48:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x0b199fa0, L:/*************:14386 ! R:/*************:8091]
2025-07-29 13:48:06 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:48:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf67eb548, L:null ! R:/*************:8091]) will closed
2025-07-29 13:48:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:48:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753768096009
timestamp=1753768096009
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 13:48:16 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x86986fff, L:/*************:3494 - R:/*************:8091]
2025-07-29 13:48:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:TMROLE,channel:[id: 0x86986fff, L:/*************:3494 - R:/*************:8091]
2025-07-29 13:48:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:48:16 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:48:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:48:16 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa0f9a9b1, L:/*************:3498 - R:/*************:8091]
2025-07-29 13:48:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:RMROLE,channel:[id: 0xa0f9a9b1, L:/*************:3498 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x86986fff, L:/*************:3494 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x86986fff, L:/*************:3494 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x86986fff, L:/*************:3494 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x86986fff, L:/*************:3494 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x86986fff, L:/*************:3494 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x86986fff, L:/*************:3494 ! R:/*************:8091]) will closed
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x86986fff, L:/*************:3494 ! R:/*************:8091]) will closed
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa0f9a9b1, L:/*************:3498 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xa0f9a9b1, L:/*************:3498 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa0f9a9b1, L:/*************:3498 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa0f9a9b1, L:/*************:3498 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa0f9a9b1, L:/*************:3498 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa0f9a9b1, L:/*************:3498 ! R:/*************:8091]) will closed
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa0f9a9b1, L:/*************:3498 ! R:/*************:8091]) will closed
2025-07-29 13:53:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:53:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753768396009
timestamp=1753768396009
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 13:53:16 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2aa38ce8, L:/*************:4283 - R:/*************:8091]
2025-07-29 13:53:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:TMROLE,channel:[id: 0x2aa38ce8, L:/*************:4283 - R:/*************:8091]
2025-07-29 13:53:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:53:16 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 13:53:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa0f9a9b1, L:/*************:3498 ! R:/*************:8091]
2025-07-29 13:53:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa0f9a9b1, L:/*************:3498 ! R:/*************:8091]
2025-07-29 13:53:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 13:53:16 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe25af958, L:/*************:4287 - R:/*************:8091]
2025-07-29 13:53:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe25af958, L:/*************:4287 - R:/*************:8091]
2025-07-29 14:12:33 [DubboServerHandler-*************:20881-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:33 [DubboServerHandler-*************:20881-thread-61] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[103ms]
2025-07-29 14:12:34 [DubboServerHandler-*************:20881-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:34 [DubboServerHandler-*************:20881-thread-62] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:12:34 [DubboServerHandler-*************:20881-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:34 [DubboServerHandler-*************:20881-thread-63] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:12:35 [DubboServerHandler-*************:20881-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:35 [DubboServerHandler-*************:20881-thread-64] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:12:36 [DubboServerHandler-*************:20881-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:36 [DubboServerHandler-*************:20881-thread-65] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:12:39 [DubboServerHandler-*************:20881-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:39 [DubboServerHandler-*************:20881-thread-66] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[18ms]
2025-07-29 14:12:39 [DubboServerHandler-*************:20881-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:40 [DubboServerHandler-*************:20881-thread-67] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[33ms]
2025-07-29 14:12:40 [DubboServerHandler-*************:20881-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:40 [DubboServerHandler-*************:20881-thread-68] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:12:41 [DubboServerHandler-*************:20881-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:41 [DubboServerHandler-*************:20881-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:12:42 [DubboServerHandler-*************:20881-thread-70] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:42 [DubboServerHandler-*************:20881-thread-70] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-29 14:12:42 [DubboServerHandler-*************:20881-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:42 [DubboServerHandler-*************:20881-thread-71] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:12:48 [DubboServerHandler-*************:20881-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:12:48 [DubboServerHandler-*************:20881-thread-72] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-29 14:13:28 [DubboServerHandler-*************:20881-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:13:28 [DubboServerHandler-*************:20881-thread-73] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[47ms]
2025-07-29 14:13:43 [DubboServerHandler-*************:20881-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:13:43 [DubboServerHandler-*************:20881-thread-74] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:13:43 [DubboServerHandler-*************:20881-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:13:43 [DubboServerHandler-*************:20881-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:13:44 [DubboServerHandler-*************:20881-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:13:44 [DubboServerHandler-*************:20881-thread-76] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:13:45 [DubboServerHandler-*************:20881-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:13:45 [DubboServerHandler-*************:20881-thread-77] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:13:45 [DubboServerHandler-*************:20881-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:13:45 [DubboServerHandler-*************:20881-thread-78] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-29 14:13:46 [DubboServerHandler-*************:20881-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:13:46 [DubboServerHandler-*************:20881-thread-79] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-29 14:14:08 [DubboServerHandler-*************:20881-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:08 [DubboServerHandler-*************:20881-thread-80] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[45ms]
2025-07-29 14:14:20 [DubboServerHandler-*************:20881-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:20 [DubboServerHandler-*************:20881-thread-81] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-29 14:14:20 [DubboServerHandler-*************:20881-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:20 [DubboServerHandler-*************:20881-thread-82] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-29 14:14:20 [DubboServerHandler-*************:20881-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:20 [DubboServerHandler-*************:20881-thread-83] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:14:20 [DubboServerHandler-*************:20881-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:21 [DubboServerHandler-*************:20881-thread-84] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[18ms]
2025-07-29 14:14:21 [DubboServerHandler-*************:20881-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:21 [DubboServerHandler-*************:20881-thread-85] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-29 14:14:21 [DubboServerHandler-*************:20881-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:21 [DubboServerHandler-*************:20881-thread-86] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[18ms]
2025-07-29 14:14:31 [DubboServerHandler-*************:20881-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:31 [DubboServerHandler-*************:20881-thread-87] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[25ms]
2025-07-29 14:14:31 [DubboServerHandler-*************:20881-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:31 [DubboServerHandler-*************:20881-thread-88] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:14:32 [DubboServerHandler-*************:20881-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:32 [DubboServerHandler-*************:20881-thread-89] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:14:32 [DubboServerHandler-*************:20881-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:32 [DubboServerHandler-*************:20881-thread-90] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:14:33 [DubboServerHandler-*************:20881-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:33 [DubboServerHandler-*************:20881-thread-91] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:14:37 [DubboServerHandler-*************:20881-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:37 [DubboServerHandler-*************:20881-thread-92] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[17ms]
2025-07-29 14:14:37 [DubboServerHandler-*************:20881-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:37 [DubboServerHandler-*************:20881-thread-93] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[18ms]
2025-07-29 14:14:38 [DubboServerHandler-*************:20881-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:38 [DubboServerHandler-*************:20881-thread-94] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:14:38 [DubboServerHandler-*************:20881-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:38 [DubboServerHandler-*************:20881-thread-95] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[37ms]
2025-07-29 14:14:39 [DubboServerHandler-*************:20881-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:39 [DubboServerHandler-*************:20881-thread-96] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:14:40 [DubboServerHandler-*************:20881-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:40 [DubboServerHandler-*************:20881-thread-97] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[18ms]
2025-07-29 14:14:43 [DubboServerHandler-*************:20881-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:14:43 [DubboServerHandler-*************:20881-thread-98] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 14:21:33 [DubboServerHandler-*************:20881-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:21:33 [DubboServerHandler-*************:20881-thread-99] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[45ms]
2025-07-29 14:21:37 [DubboServerHandler-*************:20881-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:21:37 [DubboServerHandler-*************:20881-thread-100] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-29 14:21:38 [DubboServerHandler-*************:20881-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:21:38 [DubboServerHandler-*************:20881-thread-101] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:21:38 [DubboServerHandler-*************:20881-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:21:38 [DubboServerHandler-*************:20881-thread-102] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[18ms]
2025-07-29 14:21:39 [DubboServerHandler-*************:20881-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:21:39 [DubboServerHandler-*************:20881-thread-103] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[18ms]
2025-07-29 14:21:40 [DubboServerHandler-*************:20881-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:21:40 [DubboServerHandler-*************:20881-thread-104] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-29 14:22:03 [DubboServerHandler-*************:20881-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:22:03 [DubboServerHandler-*************:20881-thread-105] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 14:22:22 [DubboServerHandler-*************:20881-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:22:22 [DubboServerHandler-*************:20881-thread-106] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[37ms]
2025-07-29 14:22:46 [DubboServerHandler-*************:20881-thread-107] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:22:46 [DubboServerHandler-*************:20881-thread-107] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-29 14:23:13 [DubboServerHandler-*************:20881-thread-108] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:13 [DubboServerHandler-*************:20881-thread-108] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[39ms]
2025-07-29 14:23:14 [DubboServerHandler-*************:20881-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:14 [DubboServerHandler-*************:20881-thread-109] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:23:14 [DubboServerHandler-*************:20881-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:14 [DubboServerHandler-*************:20881-thread-110] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:23:14 [DubboServerHandler-*************:20881-thread-111] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:14 [DubboServerHandler-*************:20881-thread-111] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:23:14 [DubboServerHandler-*************:20881-thread-112] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-112] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-113] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-114] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[18ms]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-115] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-116] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:15 [DubboServerHandler-*************:20881-thread-117] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-29 14:23:16 [DubboServerHandler-*************:20881-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:16 [DubboServerHandler-*************:20881-thread-118] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-29 14:23:16 [DubboServerHandler-*************:20881-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:16 [DubboServerHandler-*************:20881-thread-119] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 14:23:17 [DubboServerHandler-*************:20881-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:17 [DubboServerHandler-*************:20881-thread-120] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-29 14:23:17 [DubboServerHandler-*************:20881-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:17 [DubboServerHandler-*************:20881-thread-121] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-29 14:23:18 [DubboServerHandler-*************:20881-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:18 [DubboServerHandler-*************:20881-thread-122] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 14:23:19 [DubboServerHandler-*************:20881-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-29 14:23:19 [DubboServerHandler-*************:20881-thread-123] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-29 14:34:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-29 14:34:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 14:34:58 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 14:34:59 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2aa38ce8, L:/*************:4283 - R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe25af958, L:/*************:4287 - R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x2aa38ce8, L:/*************:4283 - R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xe25af958, L:/*************:4287 - R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2aa38ce8, L:/*************:4283 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe25af958, L:/*************:4287 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe25af958, L:/*************:4287 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2aa38ce8, L:/*************:4283 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe25af958, L:/*************:4287 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2aa38ce8, L:/*************:4283 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2aa38ce8, L:/*************:4283 ! R:/*************:8091]) will closed
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe25af958, L:/*************:4287 ! R:/*************:8091]) will closed
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2aa38ce8, L:/*************:4283 ! R:/*************:8091]) will closed
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe25af958, L:/*************:4287 ! R:/*************:8091]) will closed
2025-07-29 21:49:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753796976853
timestamp=1753796976853
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 21:49:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 21:49:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe25af958, L:/*************:4287 ! R:/*************:8091]
2025-07-29 21:49:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe25af958, L:/*************:4287 ! R:/*************:8091]
2025-07-29 21:49:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 21:49:38 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb1d55bfe, L:null ! R:/*************:8091]) will closed
2025-07-29 21:49:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48c03fd4, L:null ! R:/*************:8091]) will closed
2025-07-29 21:49:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753796986858
timestamp=1753796986858
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 21:49:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 21:49:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 21:49:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8c09eb3b, L:null ! R:/*************:8091]) will closed
2025-07-29 21:49:49 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x95940372, L:null ! R:/*************:8091]) will closed
2025-07-29 21:49:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753796996864
timestamp=1753796996864
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 21:49:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 21:49:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 21:49:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x300c988a, L:null ! R:/*************:8091]) will closed
2025-07-29 21:49:59 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xecaf4725, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753797006852
timestamp=1753797006852
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 21:50:07 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:07 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 21:50:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 21:50:08 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67b266da, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa18260ce, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753797016852
timestamp=1753797016852
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 21:50:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:17 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 21:50:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 21:50:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2e0e98b4, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa1a54572, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753797026852
timestamp=1753797026852
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-29 21:50:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-29 21:50:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-29 21:50:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0b8f0ca6, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0426723b, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:32 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 21:50:32 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 21:50:32 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 21:50:32 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 21:50:33 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 21:50:33 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 21:50:33 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 21:50:33 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 21:50:33 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 21:50:33 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
