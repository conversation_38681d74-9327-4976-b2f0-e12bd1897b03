2025-07-16 08:33:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 08:33:44 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 25408 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 08:33:44 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-16 08:33:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-16 08:33:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-16 08:33:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 08:33:50 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 08:33:50 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 08:33:50 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 08:33:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 08:33:50 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 08:33:50 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 08:33:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 08:33:50 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 08:33:50 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 08:33:50 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 08:33:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 08:33:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 08:33:52 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 08:33:53 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-16 08:33:53 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-16 08:33:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-16 08:33:54 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@734fcb45
2025-07-16 08:33:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-16 08:33:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-16 08:33:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-16 08:33:56 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 08:33:57 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 08:33:58 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 08:33:58 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-16 08:33:58 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-16 08:34:01 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 08:34:01 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 08:34:01 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 08:34:01 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 08:34:02 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource ************:9204 register finished
2025-07-16 08:34:05 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 24.007 seconds (process running for 25.344)
2025-07-16 08:34:05 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-16 08:34:05 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-16 08:34:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-16 08:34:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 08:34:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-16 08:34:06 [RMI TCP Connection(2)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 08:34:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 08:34:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752626090673
timestamp=1752626090673
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 08:34:50 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x09001ba3, L:/************:2311 - R:/************:8091]
2025-07-16 08:34:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 27 ms, version:1.7.1,role:TMROLE,channel:[id: 0x09001ba3, L:/************:2311 - R:/************:8091]
2025-07-16 08:34:50 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 08:34:50 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 08:34:50 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 08:34:50 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xafbf7026, L:/************:2312 - R:/************:8091]
2025-07-16 08:34:50 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0xafbf7026, L:/************:2312 - R:/************:8091]
2025-07-16 09:08:59 [DubboServerHandler-************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-16 09:09:00 [DubboServerHandler-************:20881-thread-5] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-16 09:09:00 [DubboServerHandler-************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1220ms]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xafbf7026, L:/************:2312 - R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x09001ba3, L:/************:2311 - R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x09001ba3, L:/************:2311 - R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xafbf7026, L:/************:2312 - R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xafbf7026, L:/************:2312 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xafbf7026, L:/************:2312 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x09001ba3, L:/************:2311 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x09001ba3, L:/************:2311 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xafbf7026, L:/************:2312 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x09001ba3, L:/************:2311 ! R:/************:8091]
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xafbf7026, L:/************:2312 ! R:/************:8091]) will closed
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x09001ba3, L:/************:2311 ! R:/************:8091]) will closed
2025-07-16 09:14:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xafbf7026, L:/************:2312 ! R:/************:8091]) will closed
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x09001ba3, L:/************:2311 ! R:/************:8091]) will closed
2025-07-16 09:14:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:14:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628490798
timestamp=1752628490798
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:14:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb612dbc2, L:null ! R:/************:8091]) will closed
2025-07-16 09:14:50 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:14:50 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:14:50 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xafbf7026, L:/************:2312 ! R:/************:8091]
2025-07-16 09:14:50 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xafbf7026, L:/************:2312 ! R:/************:8091]
2025-07-16 09:14:50 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:14:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x09d70f58, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628500811
timestamp=1752628500811
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:15:00 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:00 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:15:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:15:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfefad874, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628510813
timestamp=1752628510813
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:15:10 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:10 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:15:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x60e2f034, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:10 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:15:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3887e3cb, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628520815
timestamp=1752628520815
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:15:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5403dcac, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:15:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:15:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfc2eafbc, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628530818
timestamp=1752628530818
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:15:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4330c331, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:15:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:15:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628540820
timestamp=1752628540820
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:15:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0cb83361, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:15:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:15:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5f3ffeaa, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628550821
timestamp=1752628550821
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:15:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfc3809fd, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a680fcf, L:null ! R:/************:8091]) will closed
2025-07-16 09:15:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:15:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:15:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:16:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628560823
timestamp=1752628560823
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:16:00 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbd8dddde, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x05f1e64c, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:16:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:16:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x741f4ee1, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628570826
timestamp=1752628570826
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:16:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:16:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:16:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1e48884d, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x967eefbf, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628580828
timestamp=1752628580828
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:16:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x49b347fb, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:16:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:16:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628590830
timestamp=1752628590830
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:16:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x15bc7af8, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:16:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:16:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0dd40140, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0ab5f1e5, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628600839
timestamp=1752628600839
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:16:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:16:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:16:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6865e4d1, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xef2b4b19, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628610842
timestamp=1752628610842
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:16:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9813d1d6, L:null ! R:/************:8091]) will closed
2025-07-16 09:16:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:16:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:16:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:17:00 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1a05e08d, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628620844
timestamp=1752628620844
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:17:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x47186bb7, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:17:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:17:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9616e225, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628630846
timestamp=1752628630846
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:17:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9b712106, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:17:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:17:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2d93826c, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628640848
timestamp=1752628640848
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:17:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf6d427fe, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:17:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:17:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe6f4eafe, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628650851
timestamp=1752628650851
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:17:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:17:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:17:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9697c1d4, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6a34dd7d, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628660857
timestamp=1752628660857
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:17:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5147c30b, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:17:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:17:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc570b6ab, L:null ! R:/************:8091]) will closed
2025-07-16 09:17:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628670859
timestamp=1752628670859
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:17:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:17:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:17:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:17:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd9210c1b, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:00 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd37e7b82, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628680861
timestamp=1752628680861
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:18:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:18:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:18:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf9620357, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbe13549f, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628690863
timestamp=1752628690863
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:18:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88b18c11, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:18:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:18:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x862333dc, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628700865
timestamp=1752628700865
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:18:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x330927be, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:18:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:18:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x378c244e, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628710867
timestamp=1752628710867
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:18:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0b49d884, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:18:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:18:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628720869
timestamp=1752628720869
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:18:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa577a3e6, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:18:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:18:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdae09a36, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x301f435a, L:null ! R:/************:8091]) will closed
2025-07-16 09:18:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628730872
timestamp=1752628730872
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:18:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:18:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:18:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:18:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x13c00922, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628740873
timestamp=1752628740873
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:19:00 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0ac27c95, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5f770bdd, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:19:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:19:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8257b23d, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628750875
timestamp=1752628750875
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:19:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd4c9ea07, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:19:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:19:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8e60e84c, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628760877
timestamp=1752628760877
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:19:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7fa3166, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:19:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:19:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628770879
timestamp=1752628770879
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:19:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7311d0f4, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:19:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:19:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x90e157cb, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628780881
timestamp=1752628780881
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:19:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6d5f60f4, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:19:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:19:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x23f85fed, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628790882
timestamp=1752628790882
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:19:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2afbe9d6, L:null ! R:/************:8091]) will closed
2025-07-16 09:19:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:19:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:19:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:19:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x327816ff, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628800884
timestamp=1752628800884
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:20:00 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x89fc7580, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaa187271, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:20:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:20:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x655fae4f, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628810887
timestamp=1752628810887
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:20:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x994aeabd, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:20:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:20:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628820888
timestamp=1752628820888
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:20:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xee48cba6, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:20:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:20:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6baab244, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628830890
timestamp=1752628830890
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:20:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9862d417, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc409d28d, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:20:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:20:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x16c132db, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628840892
timestamp=1752628840892
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:20:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x01fdf82a, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:20:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:20:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc67e4748, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628850895
timestamp=1752628850895
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:20:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa033880f, L:null ! R:/************:8091]) will closed
2025-07-16 09:20:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:20:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:20:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:21:00 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd6464997, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628860897
timestamp=1752628860897
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:21:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:21:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:21:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2bd8619, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2b576a81, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628870900
timestamp=1752628870900
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:21:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd71ef151, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:21:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:21:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1482a67c, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628880902
timestamp=1752628880902
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:21:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa2df42fe, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:21:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:21:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628890903
timestamp=1752628890903
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:21:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x58cdeb18, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x40f0f2cb, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:21:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:21:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf91e8332, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628900905
timestamp=1752628900905
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:21:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5f13f9f1, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:21:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:21:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbec09b58, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628910908
timestamp=1752628910908
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:21:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa05f6d48, L:null ! R:/************:8091]) will closed
2025-07-16 09:21:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:21:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:21:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:22:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628920909
timestamp=1752628920909
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:22:00 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf5fe9265, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1da27215, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:22:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:22:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628930912
timestamp=1752628930912
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:22:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaf7d9ed8, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:22:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:22:11 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5fd40702, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628940914
timestamp=1752628940914
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:22:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4cae4fd1, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:22:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:22:21 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x71f80762, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd2d60ebf, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628950916
timestamp=1752628950916
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:22:31 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc5bc7b5c, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:22:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:22:33 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-16 09:22:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628960918
timestamp=1752628960918
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:22:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5e06bd0c, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5e81c044, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-16 09:22:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:22:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:22:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xade624af, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:22:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752628970920
timestamp=1752628970920
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:22:50 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xfb058c88, L:/***************:7187 - R:/***************:8091]
2025-07-16 09:22:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0xfb058c88, L:/***************:7187 - R:/***************:8091]
2025-07-16 09:22:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:22:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:22:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:22:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaa513901, L:null ! R:/************:8091]) will closed
2025-07-16 09:22:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa0ac4fae, L:/***************:7189 - R:/***************:8091]
2025-07-16 09:22:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0xa0ac4fae, L:/***************:7189 - R:/***************:8091]
2025-07-16 09:25:08 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa0ac4fae, L:/***************:7189 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xfb058c88, L:/***************:7187 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa0ac4fae, L:/***************:7189 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa0ac4fae, L:/***************:7189 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa0ac4fae, L:/***************:7189 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xfb058c88, L:/***************:7187 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa0ac4fae, L:/***************:7189 ! R:/***************:8091]) will closed
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfb058c88, L:/***************:7187 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa0ac4fae, L:/***************:7189 ! R:/***************:8091]) will closed
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfb058c88, L:/***************:7187 ! R:/***************:8091]
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfb058c88, L:/***************:7187 ! R:/***************:8091]) will closed
2025-07-16 09:25:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfb058c88, L:/***************:7187 ! R:/***************:8091]) will closed
2025-07-16 09:25:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752629110834
timestamp=1752629110834
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:25:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:25:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:25:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0da6052c, L:null ! R:/***************:8091]) will closed
2025-07-16 09:25:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcacf1328, L:null ! R:/***************:8091]) will closed
2025-07-16 09:25:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752629120842
timestamp=1752629120842
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:25:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:25:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:25:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa4f1544f, L:null ! R:/***************:8091]) will closed
2025-07-16 09:25:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a3ba3b7, L:null ! R:/***************:8091]) will closed
2025-07-16 09:25:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1752629130838
timestamp=1752629130838
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-16 09:25:30 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5848d5f4, L:/***************:7473 - R:/***************:8091]
2025-07-16 09:25:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x5848d5f4, L:/***************:7473 - R:/***************:8091]
2025-07-16 09:25:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:25:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:25:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:25:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x1f1af36b, L:/***************:7476 - R:/***************:8091]
2025-07-16 09:25:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0x1f1af36b, L:/***************:7476 - R:/***************:8091]
2025-07-16 09:45:29 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-16 09:45:29 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-16 09:45:29 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-16 09:45:29 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-16 09:45:29 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-16 09:45:29 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-16 09:45:29 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-16 09:45:30 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-16 09:45:30 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-16 09:45:30 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-16 09:45:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5848d5f4, L:/***************:7473 ! R:/***************:8091]
2025-07-16 09:45:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5848d5f4, L:/***************:7473 ! R:/***************:8091]
2025-07-16 09:45:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5848d5f4, L:/***************:7473 ! R:/***************:8091]
2025-07-16 09:45:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5848d5f4, L:/***************:7473 ! R:/***************:8091]
2025-07-16 09:45:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5848d5f4, L:/***************:7473 ! R:/***************:8091]) will closed
2025-07-16 09:45:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5848d5f4, L:/***************:7473 ! R:/***************:8091]) will closed
2025-07-16 09:45:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x1f1af36b, L:/***************:7476 ! R:/***************:8091]
2025-07-16 09:45:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x1f1af36b, L:/***************:7476 ! R:/***************:8091]
2025-07-16 09:45:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x1f1af36b, L:/***************:7476 ! R:/***************:8091]
2025-07-16 09:45:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x1f1af36b, L:/***************:7476 ! R:/***************:8091]
2025-07-16 09:45:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1f1af36b, L:/***************:7476 ! R:/***************:8091]) will closed
2025-07-16 09:45:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1f1af36b, L:/***************:7476 ! R:/***************:8091]) will closed
2025-07-16 09:45:36 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 09:45:36 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 29852 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 09:45:36 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-16 09:45:36 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-16 09:45:36 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-16 09:45:36 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 09:45:40 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 09:45:40 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 09:45:40 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 09:45:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 09:45:40 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 09:45:40 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 09:45:40 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:45:40 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1752630340513
timestamp=1752630340513
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-16 09:45:40 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x22eb393d, L:/***************:12077 - R:/***************:8091]
2025-07-16 09:45:40 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 50 ms, version:1.7.1,role:TMROLE,channel:[id: 0x22eb393d, L:/***************:12077 - R:/***************:8091]
2025-07-16 09:45:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 09:45:40 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 09:45:40 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 09:45:40 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 09:45:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 09:45:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 09:45:41 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 09:45:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-16 09:45:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-16 09:45:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-16 09:45:52 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f3f8115
2025-07-16 09:45:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-16 09:45:52 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 09:45:52 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 09:45:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 09:45:52 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xbb62683b, L:/***************:12101 - R:/***************:8091]
2025-07-16 09:45:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0xbb62683b, L:/***************:12101 - R:/***************:8091]
2025-07-16 09:45:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-16 09:45:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-16 09:45:53 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 09:45:54 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 09:45:54 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 09:45:54 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-16 09:45:55 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-16 09:45:57 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 09:45:57 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 09:45:57 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 09:45:57 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 09:45:57 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource ***************:9204 register finished
2025-07-16 09:46:00 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 26.115 seconds (process running for 27.58)
2025-07-16 09:46:00 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-16 09:46:01 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-16 09:46:01 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-16 09:46:01 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 09:46:01 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-16 09:46:01 [RMI TCP Connection(2)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:47:02 [redisson-3-2] INFO  c.y.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录达世滨航运服务平台
2025-07-16 10:04:22 [redisson-3-3] INFO  c.y.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录达世滨航运服务平台
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xbb62683b, L:/***************:12101 - R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x22eb393d, L:/***************:12077 - R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x22eb393d, L:/***************:12077 - R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xbb62683b, L:/***************:12101 - R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xbb62683b, L:/***************:12101 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x22eb393d, L:/***************:12077 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xbb62683b, L:/***************:12101 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x22eb393d, L:/***************:12077 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xbb62683b, L:/***************:12101 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x22eb393d, L:/***************:12077 ! R:/***************:8091]
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbb62683b, L:/***************:12101 ! R:/***************:8091]) will closed
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x22eb393d, L:/***************:12077 ! R:/***************:8091]) will closed
2025-07-16 10:09:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbb62683b, L:/***************:12101 ! R:/***************:8091]) will closed
2025-07-16 10:09:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x22eb393d, L:/***************:12077 ! R:/***************:8091]) will closed
2025-07-16 10:09:40 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:09:40 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 10:09:40 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xbb62683b, L:/***************:12101 ! R:/***************:8091]
2025-07-16 10:09:40 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xbb62683b, L:/***************:12101 ! R:/***************:8091]
2025-07-16 10:09:40 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 10:09:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xde8acafb, L:null ! R:/***************:8091]) will closed
2025-07-16 10:09:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:09:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1752631790387
timestamp=1752631790387
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-16 10:09:50 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:09:50 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 10:09:50 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 10:10:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1752631800388
timestamp=1752631800388
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-16 10:10:00 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3ec2c4db, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:00 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:00 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 10:10:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 10:10:00 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfba1f199, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:06 [redisson-3-1] INFO  c.y.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录达世滨航运服务平台
2025-07-16 10:10:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1752631810389
timestamp=1752631810389
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-16 10:10:10 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x19629785, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc2633059, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:10 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:10 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 10:10:10 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 10:10:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7253a7d3, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,***************,1752631820392
timestamp=1752631820392
authVersion=V4
vgroup=yumeng-resource-group
ip=***************
'} >
2025-07-16 10:10:20 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-16 10:10:20 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 10:10:20 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 10:10:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x99ce6764, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:28 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-16 10:10:28 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-16 10:10:28 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-16 10:10:29 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-16 10:10:29 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-16 10:10:29 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-16 10:10:29 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-16 10:10:29 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-16 10:10:29 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-16 10:10:29 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-16 10:10:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xba05f791, L:null ! R:/***************:8091]) will closed
2025-07-16 10:10:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x949ae8d9, L:null ! R:/***************:8091]) will closed
2025-07-16 10:49:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 10:49:24 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 25452 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 10:49:24 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-16 10:49:24 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-16 10:49:24 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-16 10:49:24 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 10:49:28 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 10:49:28 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 10:49:28 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 10:49:28 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 10:49:28 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 10:49:28 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 10:49:28 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 10:49:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752634168977
timestamp=1752634168977
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 10:49:29 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x229ace4f, L:/*************:10294 - R:/*************:8091]
2025-07-16 10:49:29 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 59 ms, version:1.7.1,role:TMROLE,channel:[id: 0x229ace4f, L:/*************:10294 - R:/*************:8091]
2025-07-16 10:49:29 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 10:49:29 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 10:49:29 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 10:49:29 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 10:49:29 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 10:49:29 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 10:49:30 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 10:49:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-16 10:49:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-16 10:49:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-16 10:49:32 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@618cfd1
2025-07-16 10:49:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-16 10:49:32 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 10:49:32 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 10:49:32 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 10:49:32 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x97a00dfd, L:/*************:10308 - R:/*************:8091]
2025-07-16 10:49:32 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x97a00dfd, L:/*************:10308 - R:/*************:8091]
2025-07-16 10:49:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-16 10:49:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-16 10:49:33 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 10:49:35 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 10:49:35 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 10:49:35 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-16 10:49:35 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-16 10:49:37 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 10:49:37 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 10:49:38 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 10:49:38 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 10:49:38 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-16 10:49:41 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 19.135 seconds (process running for 20.222)
2025-07-16 10:49:41 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-16 10:49:41 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-16 10:49:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-16 10:49:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 10:49:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-16 10:49:41 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 11:18:00 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-16 11:18:01 [DubboServerHandler-*************:20881-thread-7] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-16 11:18:01 [DubboServerHandler-*************:20881-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1258ms]
2025-07-16 11:18:01 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-16 11:18:01 [DubboServerHandler-*************:20881-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[26ms]
2025-07-16 11:18:02 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-16 11:18:02 [DubboServerHandler-*************:20881-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[28ms]
2025-07-16 11:18:14 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-16 11:18:14 [DubboServerHandler-*************:20881-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x229ace4f, L:/*************:10294 - R:/*************:8091] read idle.
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x229ace4f, L:/*************:10294 - R:/*************:8091]
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x229ace4f, L:/*************:10294 - R:/*************:8091]) will closed
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]) will closed
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]) will closed
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]) will closed
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]
2025-07-16 12:26:11 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]) will closed
2025-07-16 12:26:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x229ace4f, L:/*************:10294 ! R:/*************:8091]) will closed
2025-07-16 12:26:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 12:26:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752639972203
timestamp=1752639972203
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 12:26:12 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xfeb905b6, L:/*************:2323 - R:/*************:8091]
2025-07-16 12:26:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 22 ms, version:1.7.1,role:TMROLE,channel:[id: 0xfeb905b6, L:/*************:2323 - R:/*************:8091]
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x97a00dfd, L:/*************:10308 - R:/*************:8091] read idle.
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x97a00dfd, L:/*************:10308 - R:/*************:8091]
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x97a00dfd, L:/*************:10308 - R:/*************:8091]) will closed
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]) will closed
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]) will closed
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]) will closed
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]) will closed
2025-07-16 12:26:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x97a00dfd, L:/*************:10308 ! R:/*************:8091]) will closed
2025-07-16 12:26:16 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 12:26:16 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 12:26:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 12:26:16 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe983d7d0, L:/*************:5648 - R:/*************:8091]
2025-07-16 12:26:16 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe983d7d0, L:/*************:5648 - R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xfeb905b6, L:/*************:2323 - R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe983d7d0, L:/*************:5648 - R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xfeb905b6, L:/*************:2323 - R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xe983d7d0, L:/*************:5648 - R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xfeb905b6, L:/*************:2323 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe983d7d0, L:/*************:5648 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfeb905b6, L:/*************:2323 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe983d7d0, L:/*************:5648 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe983d7d0, L:/*************:5648 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfeb905b6, L:/*************:2323 ! R:/*************:8091]
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe983d7d0, L:/*************:5648 ! R:/*************:8091]) will closed
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfeb905b6, L:/*************:2323 ! R:/*************:8091]) will closed
2025-07-16 12:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe983d7d0, L:/*************:5648 ! R:/*************:8091]) will closed
2025-07-16 12:27:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfeb905b6, L:/*************:2323 ! R:/*************:8091]) will closed
2025-07-16 13:26:36 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 13:26:36 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 35412 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 13:26:36 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-16 13:26:36 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-16 13:26:36 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-16 13:26:36 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 13:26:41 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 13:26:41 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 13:26:41 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 13:26:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 13:26:41 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 13:26:41 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 13:26:41 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 13:26:42 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752643602057
timestamp=1752643602057
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 13:26:42 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xaf380301, L:/*************:9057 - R:/*************:8091]
2025-07-16 13:26:42 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 79 ms, version:1.7.1,role:TMROLE,channel:[id: 0xaf380301, L:/*************:9057 - R:/*************:8091]
2025-07-16 13:26:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 13:26:42 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 13:26:42 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 13:26:42 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 13:26:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 13:26:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 13:26:43 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 13:26:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-16 13:26:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-16 13:26:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-16 13:26:45 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-07-16 13:26:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-16 13:26:45 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 13:26:45 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 13:26:45 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 13:26:45 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2a833e9d, L:/*************:9078 - R:/*************:8091]
2025-07-16 13:26:45 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 11 ms, version:1.7.1,role:RMROLE,channel:[id: 0x2a833e9d, L:/*************:9078 - R:/*************:8091]
2025-07-16 13:26:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-16 13:26:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-16 13:26:46 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 13:26:47 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 13:26:47 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 13:26:47 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-16 13:26:47 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-16 13:26:49 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 13:26:49 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 13:26:49 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 13:26:50 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 13:26:50 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-16 13:26:52 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 18.497 seconds (process running for 19.665)
2025-07-16 13:26:52 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-16 13:26:53 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-16 13:26:53 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-16 13:26:53 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 13:26:53 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-16 13:26:53 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 13:38:47 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-16 13:38:49 [DubboServerHandler-*************:20881-thread-5] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-16 13:38:49 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1697ms]
2025-07-16 13:54:15 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-16 13:54:15 [DubboServerHandler-*************:20881-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[86ms]
2025-07-16 14:36:32 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2a833e9d, L:/*************:9078 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2a833e9d, L:/*************:9078 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xaf380301, L:/*************:9057 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2a833e9d, L:/*************:9078 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xaf380301, L:/*************:9057 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xaf380301, L:/*************:9057 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xaf380301, L:/*************:9057 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2a833e9d, L:/*************:9078 ! R:/*************:8091]
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaf380301, L:/*************:9057 ! R:/*************:8091]) will closed
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2a833e9d, L:/*************:9078 ! R:/*************:8091]) will closed
2025-07-16 14:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaf380301, L:/*************:9057 ! R:/*************:8091]) will closed
2025-07-16 14:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2a833e9d, L:/*************:9078 ! R:/*************:8091]) will closed
2025-07-16 14:36:41 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:36:41 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752647801823
timestamp=1752647801823
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 14:36:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:36:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 14:36:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 14:36:42 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x05ac657b, L:/*************:3007 - R:/*************:8091]
2025-07-16 14:36:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5933062e, L:/*************:3013 - R:/*************:8091]
2025-07-16 14:36:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 50 ms, version:1.7.1,role:TMROLE,channel:[id: 0x05ac657b, L:/*************:3007 - R:/*************:8091]
2025-07-16 14:36:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 59 ms, version:1.7.1,role:RMROLE,channel:[id: 0x5933062e, L:/*************:3013 - R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5933062e, L:/*************:3013 - R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5933062e, L:/*************:3013 - R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5933062e, L:/*************:3013 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5933062e, L:/*************:3013 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5933062e, L:/*************:3013 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5933062e, L:/*************:3013 ! R:/*************:8091]) will closed
2025-07-16 14:50:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5933062e, L:/*************:3013 ! R:/*************:8091]) will closed
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x05ac657b, L:/*************:3007 - R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x05ac657b, L:/*************:3007 - R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x05ac657b, L:/*************:3007 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x05ac657b, L:/*************:3007 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x05ac657b, L:/*************:3007 ! R:/*************:8091]
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x05ac657b, L:/*************:3007 ! R:/*************:8091]) will closed
2025-07-16 14:50:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x05ac657b, L:/*************:3007 ! R:/*************:8091]) will closed
2025-07-16 14:50:51 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:50:51 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752648651823
timestamp=1752648651823
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 14:50:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:50:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 14:50:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5933062e, L:/*************:3013 ! R:/*************:8091]
2025-07-16 14:50:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5933062e, L:/*************:3013 ! R:/*************:8091]
2025-07-16 14:50:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 14:50:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x714558d8, L:null ! R:/*************:8091]) will closed
2025-07-16 14:50:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xacca2c21, L:null ! R:/*************:8091]) will closed
2025-07-16 14:57:30 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-16 14:57:30 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 17268 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-16 14:57:30 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-16 14:57:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-16 14:57:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-16 14:57:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-16 14:57:35 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-16 14:57:36 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-16 14:57:36 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-16 14:57:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-16 14:57:36 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 14:57:36 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-16 14:57:36 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:57:36 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752649056577
timestamp=1752649056577
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 14:57:36 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4c11f45b, L:/*************:6360 - R:/*************:8091]
2025-07-16 14:57:36 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 71 ms, version:1.7.1,role:TMROLE,channel:[id: 0x4c11f45b, L:/*************:6360 - R:/*************:8091]
2025-07-16 14:57:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 14:57:36 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-16 14:57:36 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-16 14:57:36 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-16 14:57:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-16 14:57:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-16 14:57:38 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 14:57:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-16 14:57:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-16 14:57:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-16 14:57:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5e6d2502
2025-07-16 14:57:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-16 14:57:40 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 14:57:40 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 14:57:40 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 14:57:40 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x48aef81a, L:/*************:6390 - R:/*************:8091]
2025-07-16 14:57:40 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x48aef81a, L:/*************:6390 - R:/*************:8091]
2025-07-16 14:57:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-16 14:57:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-16 14:57:41 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-16 14:57:43 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-16 14:57:43 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-16 14:57:43 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-16 14:57:43 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-16 14:57:46 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-16 14:57:46 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 14:57:46 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 14:57:46 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 14:57:46 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-16 14:57:47 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 14:57:49 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 22.097 seconds (process running for 23.261)
2025-07-16 14:57:49 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-16 14:57:50 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-16 14:57:50 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-16 14:57:50 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-16 14:57:50 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-16 14:59:47 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-16 14:59:48 [DubboServerHandler-*************:20881-thread-5] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-16 14:59:48 [DubboServerHandler-*************:20881-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1069ms]
2025-07-16 15:01:34 [redisson-3-2] INFO  c.y.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1943143574256308226] message=欢迎登录达世滨航运服务平台
2025-07-16 15:05:22 [redisson-3-3] INFO  c.y.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1943143574256308226] message=欢迎登录达世滨航运服务平台
2025-07-16 15:07:45 [redisson-3-4] INFO  c.y.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录达世滨航运服务平台
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x48aef81a, L:/*************:6390 - R:/*************:8091] read idle.
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x48aef81a, L:/*************:6390 - R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48aef81a, L:/*************:6390 - R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x48aef81a, L:/*************:6390 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x4c11f45b, L:/*************:6360 - R:/*************:8091] read idle.
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4c11f45b, L:/*************:6360 - R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c11f45b, L:/*************:6360 - R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4c11f45b, L:/*************:6360 ! R:/*************:8091]) will closed
2025-07-16 19:11:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:11:34 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 19:11:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 19:11:34 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe9d49bf2, L:/*************:12321 - R:/*************:8091]
2025-07-16 19:11:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 52 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe9d49bf2, L:/*************:12321 - R:/*************:8091]
2025-07-16 19:11:43 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:11:43 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752664303549
timestamp=1752664303549
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 19:11:43 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4a27a51c, L:/*************:6148 - R:/*************:8091]
2025-07-16 19:11:43 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 27 ms, version:1.7.1,role:TMROLE,channel:[id: 0x4a27a51c, L:/*************:6148 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe9d49bf2, L:/*************:12321 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4a27a51c, L:/*************:6148 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x4a27a51c, L:/*************:6148 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xe9d49bf2, L:/*************:12321 - R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe9d49bf2, L:/*************:12321 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4a27a51c, L:/*************:6148 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe9d49bf2, L:/*************:12321 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4a27a51c, L:/*************:6148 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe9d49bf2, L:/*************:12321 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe9d49bf2, L:/*************:12321 ! R:/*************:8091]) will closed
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4a27a51c, L:/*************:6148 ! R:/*************:8091]
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4a27a51c, L:/*************:6148 ! R:/*************:8091]) will closed
2025-07-16 19:14:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe9d49bf2, L:/*************:12321 ! R:/*************:8091]) will closed
2025-07-16 19:14:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4a27a51c, L:/*************:6148 ! R:/*************:8091]) will closed
2025-07-16 19:14:43 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:14:43 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752664483548
timestamp=1752664483548
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 19:14:43 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:14:43 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 19:14:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe9d49bf2, L:/*************:12321 ! R:/*************:8091]
2025-07-16 19:14:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe9d49bf2, L:/*************:12321 ! R:/*************:8091]
2025-07-16 19:14:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 19:14:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe0550a11, L:null ! R:/*************:8091]) will closed
2025-07-16 19:14:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7a8f2ff, L:null ! R:/*************:8091]) will closed
2025-07-16 19:14:53 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:14:53 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752664493538
timestamp=1752664493538
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 19:14:53 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:14:53 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 19:14:53 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 19:14:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x81d3d348, L:null ! R:/*************:8091]) will closed
2025-07-16 19:14:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x551e2f05, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:03 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:03 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752664503537
timestamp=1752664503537
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 19:15:03 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:03 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 19:15:03 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 19:15:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x804cdbef, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcc428c66, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:13 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:13 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752664513538
timestamp=1752664513538
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 19:15:13 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:13 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 19:15:13 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 19:15:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa5f51f29, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x01ab8f19, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:23 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:23 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752664523538
timestamp=1752664523538
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 19:15:23 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:23 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 19:15:23 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 19:15:25 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd9ab3e7b, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8ce90fbb, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:33 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:33 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752664533544
timestamp=1752664533544
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 19:15:33 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:33 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 19:15:33 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 19:15:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfbe5aef4, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x070238f7, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:43 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:43 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752664543545
timestamp=1752664543545
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-16 19:15:43 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-16 19:15:43 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-16 19:15:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-16 19:15:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc76b7226, L:null ! R:/*************:8091]) will closed
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-16 19:15:45 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-16 19:15:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf19851e3, L:null ! R:/*************:8091]) will closed
