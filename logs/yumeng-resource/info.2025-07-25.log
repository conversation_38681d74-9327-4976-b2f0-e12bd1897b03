2025-07-25 08:34:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 08:34:00 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 28332 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 08:34:00 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-25 08:34:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:06 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 08:34:06 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 08:34:06 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 08:34:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 08:34:06 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 08:34:06 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 08:34:07 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 08:34:07 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753403647068
timestamp=1753403647068
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 08:34:07 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xcb600370, L:/*************:2029 - R:/*************:8091]
2025-07-25 08:34:07 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 76 ms, version:1.7.1,role:TMROLE,channel:[id: 0xcb600370, L:/*************:2029 - R:/*************:8091]
2025-07-25 08:34:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 08:34:07 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 08:34:07 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 08:34:07 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 08:34:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 08:34:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 08:34:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 08:34:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 08:34:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 08:34:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 08:34:12 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@9b5ff2e
2025-07-25 08:34:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 08:34:13 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 08:34:13 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 08:34:13 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 08:34:13 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x413b028b, L:/*************:2073 - R:/*************:8091]
2025-07-25 08:34:13 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 10 ms, version:1.7.1,role:RMROLE,channel:[id: 0x413b028b, L:/*************:2073 - R:/*************:8091]
2025-07-25 08:34:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 08:34:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 08:34:14 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 08:34:15 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 08:34:15 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 08:34:15 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 08:34:16 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 08:34:18 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 08:34:18 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 08:34:18 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 08:34:18 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 08:34:18 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-25 08:34:21 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 23.361 seconds (process running for 24.561)
2025-07-25 08:34:21 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-25 08:34:21 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-25 08:34:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 08:34:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 08:34:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-25 08:34:22 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x413b028b, L:/*************:2073 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xcb600370, L:/*************:2029 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xcb600370, L:/*************:2029 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x413b028b, L:/*************:2073 - R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xcb600370, L:/*************:2029 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x413b028b, L:/*************:2073 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x413b028b, L:/*************:2073 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xcb600370, L:/*************:2029 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x413b028b, L:/*************:2073 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xcb600370, L:/*************:2029 ! R:/*************:8091]
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x413b028b, L:/*************:2073 ! R:/*************:8091]) will closed
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcb600370, L:/*************:2029 ! R:/*************:8091]) will closed
2025-07-25 10:44:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x413b028b, L:/*************:2073 ! R:/*************:8091]) will closed
2025-07-25 10:44:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcb600370, L:/*************:2029 ! R:/*************:8091]) will closed
2025-07-25 10:44:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753411467278
timestamp=1753411467278
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 10:44:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 10:44:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x413b028b, L:/*************:2073 ! R:/*************:8091]
2025-07-25 10:44:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x413b028b, L:/*************:2073 ! R:/*************:8091]
2025-07-25 10:44:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 10:44:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3f854b53, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x68de6a2e, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753411477283
timestamp=1753411477283
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 10:44:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 10:44:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 10:44:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x165afd2e, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd9103453, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753411487279
timestamp=1753411487279
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 10:44:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 10:44:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 10:44:49 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xece62fd2, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:49 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd4064442, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753411497289
timestamp=1753411497289
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 10:44:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 10:44:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 10:44:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 10:44:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf42496c4, L:null ! R:/*************:8091]) will closed
2025-07-25 10:44:59 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7cf54947, L:null ! R:/*************:8091]) will closed
2025-07-25 11:23:59 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 11:24:00 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 30640 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 11:24:00 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-25 11:24:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-25 11:24:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 11:24:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 11:24:04 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 11:24:04 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 11:24:04 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 11:24:05 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 11:24:05 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 11:24:05 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 11:24:05 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 11:24:05 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753413845321
timestamp=1753413845321
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 11:24:05 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x286ceec2, L:/*************:10006 - R:/*************:8091]
2025-07-25 11:24:05 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 55 ms, version:1.7.1,role:TMROLE,channel:[id: 0x286ceec2, L:/*************:10006 - R:/*************:8091]
2025-07-25 11:24:05 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 11:24:05 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 11:24:05 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 11:24:05 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 11:24:05 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 11:24:05 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 11:24:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 11:24:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 11:24:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 11:24:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 11:24:08 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1069baea
2025-07-25 11:24:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 11:24:08 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 11:24:08 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 11:24:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 11:24:08 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xfde86639, L:/*************:10032 - R:/*************:8091]
2025-07-25 11:24:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xfde86639, L:/*************:10032 - R:/*************:8091]
2025-07-25 11:24:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 11:24:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 11:24:09 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 11:24:10 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 11:24:10 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 11:24:10 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 11:24:11 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 11:24:13 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 11:24:13 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 11:24:13 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 11:24:13 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 11:24:13 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-25 11:24:16 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 18.555 seconds (process running for 19.813)
2025-07-25 11:24:16 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-25 11:24:16 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-25 11:24:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 11:24:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 11:24:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-25 11:24:16 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xfde86639, L:/*************:10032 - R:/*************:8091] read idle.
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfde86639, L:/*************:10032 - R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfde86639, L:/*************:10032 - R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]) will closed
2025-07-25 13:27:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfde86639, L:/*************:10032 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x286ceec2, L:/*************:10006 - R:/*************:8091] read idle.
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x286ceec2, L:/*************:10006 - R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x286ceec2, L:/*************:10006 - R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]) will closed
2025-07-25 13:27:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x286ceec2, L:/*************:10006 ! R:/*************:8091]) will closed
2025-07-25 13:27:31 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:27:31 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753421251986
timestamp=1753421251986
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 13:27:31 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe32c71ec, L:/*************:11674 - R:/*************:8091]
2025-07-25 13:27:31 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:TMROLE,channel:[id: 0xe32c71ec, L:/*************:11674 - R:/*************:8091]
2025-07-25 13:27:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:27:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 13:27:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 13:27:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xbc963dd1, L:/*************:11676 - R:/*************:8091]
2025-07-25 13:27:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0xbc963dd1, L:/*************:11676 - R:/*************:8091]
2025-07-25 13:29:17 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xbc963dd1, L:/*************:11676 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe32c71ec, L:/*************:11674 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xbc963dd1, L:/*************:11676 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xbc963dd1, L:/*************:11676 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xbc963dd1, L:/*************:11676 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbc963dd1, L:/*************:11676 ! R:/*************:8091]) will closed
2025-07-25 13:29:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbc963dd1, L:/*************:11676 ! R:/*************:8091]) will closed
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe32c71ec, L:/*************:11674 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe32c71ec, L:/*************:11674 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe32c71ec, L:/*************:11674 ! R:/*************:8091]
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe32c71ec, L:/*************:11674 ! R:/*************:8091]) will closed
2025-07-25 13:29:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe32c71ec, L:/*************:11674 ! R:/*************:8091]) will closed
2025-07-25 13:29:21 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:21 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753421361990
timestamp=1753421361990
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 13:29:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 13:29:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 13:29:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9ee54e7b, L:null ! R:/*************:8091]) will closed
2025-07-25 13:29:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x03fde34e, L:null ! R:/*************:8091]) will closed
2025-07-25 13:29:31 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:31 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753421371986
timestamp=1753421371986
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 13:29:31 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x45844624, L:/*************:12040 - R:/*************:8091]
2025-07-25 13:29:31 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:TMROLE,channel:[id: 0x45844624, L:/*************:12040 - R:/*************:8091]
2025-07-25 13:29:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 13:29:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 13:29:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 13:29:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7e4b275a, L:/*************:12041 - R:/*************:8091]
2025-07-25 13:29:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0x7e4b275a, L:/*************:12041 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7e4b275a, L:/*************:12041 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x45844624, L:/*************:12040 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x7e4b275a, L:/*************:12041 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x45844624, L:/*************:12040 - R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7e4b275a, L:/*************:12041 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x45844624, L:/*************:12040 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x45844624, L:/*************:12040 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7e4b275a, L:/*************:12041 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x45844624, L:/*************:12040 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7e4b275a, L:/*************:12041 ! R:/*************:8091]
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x45844624, L:/*************:12040 ! R:/*************:8091]) will closed
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e4b275a, L:/*************:12041 ! R:/*************:8091]) will closed
2025-07-25 14:55:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e4b275a, L:/*************:12041 ! R:/*************:8091]) will closed
2025-07-25 14:55:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x45844624, L:/*************:12040 ! R:/*************:8091]) will closed
2025-07-25 14:55:21 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:21 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753426521986
timestamp=1753426521986
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 14:55:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 14:55:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7e4b275a, L:/*************:12041 ! R:/*************:8091]
2025-07-25 14:55:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7e4b275a, L:/*************:12041 ! R:/*************:8091]
2025-07-25 14:55:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 14:55:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa8c49a25, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2fac6fc5, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:31 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:31 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753426531986
timestamp=1753426531986
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 14:55:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 14:55:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 14:55:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x670ccec4, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x735f9806, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:41 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:41 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753426541993
timestamp=1753426541993
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 14:55:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 14:55:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 14:55:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xed36f8e6, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0220c77f, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:51 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:51 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753426551986
timestamp=1753426551986
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 14:55:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:55:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 14:55:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 14:55:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x42e34d46, L:null ! R:/*************:8091]) will closed
2025-07-25 14:55:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x864dea90, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:01 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:01 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753426561995
timestamp=1753426561995
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 14:56:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 14:56:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 14:56:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5c452971, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xecde7afd, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:11 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:11 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753426571986
timestamp=1753426571986
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 14:56:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 14:56:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 14:56:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x50737fe4, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0f31e096, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:21 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:21 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753426581987
timestamp=1753426581987
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 14:56:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 14:56:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 14:56:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 14:56:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2efcc041, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 14:56:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8c21c2f2, L:null ! R:/*************:8091]) will closed
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-25 14:56:24 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-25 15:12:47 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 15:12:47 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 37592 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 15:12:47 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-25 15:12:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-25 15:12:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 15:12:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 15:12:53 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 15:12:53 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 15:12:53 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 15:12:53 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 15:12:53 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 15:12:53 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 15:12:54 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 15:12:54 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753427574056
timestamp=1753427574056
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 15:12:54 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x0242237e, L:/*************:12152 - R:/*************:8091]
2025-07-25 15:12:54 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 140 ms, version:1.7.1,role:TMROLE,channel:[id: 0x0242237e, L:/*************:12152 - R:/*************:8091]
2025-07-25 15:12:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 15:12:54 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 15:12:54 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 15:12:54 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 15:12:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 15:12:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 15:12:55 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 15:12:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 15:12:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 15:12:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 15:12:57 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3669d7ac
2025-07-25 15:12:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 15:12:58 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 15:12:58 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 15:12:58 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 15:12:58 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x74f173a7, L:/*************:12165 - R:/*************:8091]
2025-07-25 15:12:58 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 13 ms, version:1.7.1,role:RMROLE,channel:[id: 0x74f173a7, L:/*************:12165 - R:/*************:8091]
2025-07-25 15:12:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 15:12:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 15:12:59 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 15:13:01 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 15:13:01 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 15:13:01 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 15:13:02 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 15:13:06 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 15:13:06 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 15:13:06 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 15:13:06 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 15:13:06 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-25 15:13:09 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 24.56 seconds (process running for 25.816)
2025-07-25 15:13:09 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-25 15:13:09 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-25 15:13:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 15:13:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 15:13:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-25 15:13:10 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 16:42:18 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:19 [DubboServerHandler-*************:20880-thread-9] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-25 16:42:19 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1609ms]
2025-07-25 16:42:20 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:20 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[27ms]
2025-07-25 16:42:20 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:20 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[35ms]
2025-07-25 16:42:21 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:21 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[20ms]
2025-07-25 16:42:22 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:42:22 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-25 16:44:30 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:44:30 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[105ms]
2025-07-25 16:44:30 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:44:30 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-25 16:52:03 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:03 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[48ms]
2025-07-25 16:52:03 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:03 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-25 16:52:04 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:04 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[26ms]
2025-07-25 16:52:04 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:04 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-25 16:52:05 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:05 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-25 16:52:05 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:05 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-25 16:52:06 [DubboServerHandler-*************:20880-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:06 [DubboServerHandler-*************:20880-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[61ms]
2025-07-25 16:52:09 [DubboServerHandler-*************:20880-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:09 [DubboServerHandler-*************:20880-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-25 16:52:15 [DubboServerHandler-*************:20880-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:52:15 [DubboServerHandler-*************:20880-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[61ms]
2025-07-25 16:53:01 [DubboServerHandler-*************:20880-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 16:53:01 [DubboServerHandler-*************:20880-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[44ms]
2025-07-25 17:47:49 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 17:47:49 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 17:47:49 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 17:47:49 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 17:47:49 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 17:47:49 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-25 17:47:49 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-25 17:47:50 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-25 17:47:50 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-25 17:47:50 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-25 17:47:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x0242237e, L:/*************:12152 ! R:/*************:8091]
2025-07-25 17:47:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x0242237e, L:/*************:12152 ! R:/*************:8091]
2025-07-25 17:47:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x74f173a7, L:/*************:12165 ! R:/*************:8091]
2025-07-25 17:47:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x74f173a7, L:/*************:12165 ! R:/*************:8091]
2025-07-25 17:47:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x74f173a7, L:/*************:12165 ! R:/*************:8091]
2025-07-25 17:47:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x0242237e, L:/*************:12152 ! R:/*************:8091]
2025-07-25 17:47:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x0242237e, L:/*************:12152 ! R:/*************:8091]
2025-07-25 17:47:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x74f173a7, L:/*************:12165 ! R:/*************:8091]
2025-07-25 17:47:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0242237e, L:/*************:12152 ! R:/*************:8091]) will closed
2025-07-25 17:47:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x74f173a7, L:/*************:12165 ! R:/*************:8091]) will closed
2025-07-25 17:47:50 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0242237e, L:/*************:12152 ! R:/*************:8091]) will closed
2025-07-25 17:47:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x74f173a7, L:/*************:12165 ! R:/*************:8091]) will closed
2025-07-25 17:47:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 17:47:55 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 27720 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 17:47:55 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-25 17:47:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:47:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:47:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 17:47:59 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 17:47:59 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 17:47:59 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 17:47:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 17:47:59 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 17:47:59 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 17:47:59 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 17:47:59 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753436879632
timestamp=1753436879632
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 17:47:59 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xead3928e, L:/*************:7333 - R:/*************:8091]
2025-07-25 17:47:59 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 48 ms, version:1.7.1,role:TMROLE,channel:[id: 0xead3928e, L:/*************:7333 - R:/*************:8091]
2025-07-25 17:47:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 17:47:59 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 17:47:59 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 17:47:59 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 17:47:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 17:47:59 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 17:48:00 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 17:48:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 17:48:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 17:48:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 17:48:02 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-07-25 17:48:02 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 17:48:02 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 17:48:02 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 17:48:02 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 17:48:02 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x84cbfccc, L:/*************:7342 - R:/*************:8091]
2025-07-25 17:48:02 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x84cbfccc, L:/*************:7342 - R:/*************:8091]
2025-07-25 17:48:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 17:48:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 17:48:03 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 17:48:04 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 17:48:04 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 17:48:04 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 17:48:04 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 17:48:06 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 17:48:06 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 17:48:06 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 17:48:06 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 17:48:06 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-25 17:48:09 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 15.439 seconds (process running for 16.624)
2025-07-25 17:48:09 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-25 17:48:09 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-25 17:48:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 17:48:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 17:48:09 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-25 17:48:10 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 17:49:47 [DubboServerHandler-*************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:52:33 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:52:53 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:52:53 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:52:53 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 17:52:54 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-25 17:53:01 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-25 17:53:13 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-25 17:53:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xead3928e, L:/*************:7333 ! R:/*************:8091]
2025-07-25 17:53:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xead3928e, L:/*************:7333 ! R:/*************:8091]
2025-07-25 17:53:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xead3928e, L:/*************:7333 ! R:/*************:8091]
2025-07-25 17:53:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xead3928e, L:/*************:7333 ! R:/*************:8091]
2025-07-25 17:53:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xead3928e, L:/*************:7333 ! R:/*************:8091]) will closed
2025-07-25 17:53:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xead3928e, L:/*************:7333 ! R:/*************:8091]) will closed
2025-07-25 17:53:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x84cbfccc, L:/*************:7342 ! R:/*************:8091]
2025-07-25 17:53:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x84cbfccc, L:/*************:7342 ! R:/*************:8091]
2025-07-25 17:53:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x84cbfccc, L:/*************:7342 ! R:/*************:8091]
2025-07-25 17:53:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x84cbfccc, L:/*************:7342 ! R:/*************:8091]
2025-07-25 17:53:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x84cbfccc, L:/*************:7342 ! R:/*************:8091]) will closed
2025-07-25 17:53:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x84cbfccc, L:/*************:7342 ! R:/*************:8091]) will closed
2025-07-25 17:53:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 17:53:18 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 35904 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 17:53:18 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-25 17:53:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:53:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:53:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 17:53:21 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 17:53:21 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 17:53:21 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 17:53:21 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 17:53:21 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 17:53:21 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 17:53:21 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 17:53:21 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753437201969
timestamp=1753437201969
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 17:53:22 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2392906d, L:/*************:9293 - R:/*************:8091]
2025-07-25 17:53:22 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 52 ms, version:1.7.1,role:TMROLE,channel:[id: 0x2392906d, L:/*************:9293 - R:/*************:8091]
2025-07-25 17:53:22 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 17:53:22 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 17:53:22 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 17:53:22 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 17:53:22 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 17:53:22 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 17:53:23 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 17:53:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 17:53:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 17:53:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 17:53:24 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@dcaa0e8
2025-07-25 17:53:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 17:53:24 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 17:53:24 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 17:53:24 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 17:53:24 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xb38ac7bb, L:/*************:9299 - R:/*************:8091]
2025-07-25 17:53:24 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0xb38ac7bb, L:/*************:9299 - R:/*************:8091]
2025-07-25 17:53:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 17:53:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 17:53:25 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 17:53:26 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 17:53:26 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 17:53:26 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 17:53:26 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 17:53:28 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 17:53:28 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 17:53:28 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 17:53:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 17:53:28 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-25 17:53:31 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 15.121 seconds (process running for 16.307)
2025-07-25 17:53:31 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-25 17:53:31 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-25 17:53:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 17:53:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 17:53:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-25 17:53:31 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 17:54:02 [DubboServerHandler-*************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:54:42 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:56:07 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:56:29 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:56:29 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:56:29 [nacos.client.config.listener.task-0] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 17:56:45 [DubboServerHandler-*************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:56:54 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 17:56:54 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 17:56:54 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 17:56:54 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 17:56:54 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 17:56:54 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-25 17:56:54 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-25 17:56:55 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-25 17:56:55 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-25 17:56:55 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-25 17:56:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2392906d, L:/*************:9293 ! R:/*************:8091]
2025-07-25 17:56:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2392906d, L:/*************:9293 ! R:/*************:8091]
2025-07-25 17:56:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2392906d, L:/*************:9293 ! R:/*************:8091]
2025-07-25 17:56:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2392906d, L:/*************:9293 ! R:/*************:8091]
2025-07-25 17:56:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2392906d, L:/*************:9293 ! R:/*************:8091]) will closed
2025-07-25 17:56:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2392906d, L:/*************:9293 ! R:/*************:8091]) will closed
2025-07-25 17:56:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xb38ac7bb, L:/*************:9299 ! R:/*************:8091]
2025-07-25 17:56:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xb38ac7bb, L:/*************:9299 ! R:/*************:8091]
2025-07-25 17:56:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb38ac7bb, L:/*************:9299 ! R:/*************:8091]
2025-07-25 17:56:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb38ac7bb, L:/*************:9299 ! R:/*************:8091]
2025-07-25 17:56:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb38ac7bb, L:/*************:9299 ! R:/*************:8091]) will closed
2025-07-25 17:56:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb38ac7bb, L:/*************:9299 ! R:/*************:8091]) will closed
2025-07-25 17:56:59 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 17:56:59 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 34236 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 17:56:59 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-25 17:56:59 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:56:59 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 17:56:59 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 17:57:02 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 17:57:02 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 17:57:02 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 17:57:02 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 17:57:02 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 17:57:02 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 17:57:03 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 17:57:03 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753437423066
timestamp=1753437423066
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 17:57:03 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x65fb0bf9, L:/*************:11017 - R:/*************:8091]
2025-07-25 17:57:03 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 48 ms, version:1.7.1,role:TMROLE,channel:[id: 0x65fb0bf9, L:/*************:11017 - R:/*************:8091]
2025-07-25 17:57:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 17:57:03 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 17:57:03 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 17:57:03 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 17:57:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-25 17:57:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 17:57:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 17:57:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 17:57:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 17:57:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 17:57:05 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4c66b3d9
2025-07-25 17:57:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 17:57:05 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 17:57:05 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 17:57:05 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 17:57:05 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7e4a7060, L:/*************:11029 - R:/*************:8091]
2025-07-25 17:57:05 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0x7e4a7060, L:/*************:11029 - R:/*************:8091]
2025-07-25 17:57:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 17:57:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 17:57:06 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 17:57:07 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 17:57:07 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 17:57:07 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 17:57:07 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 17:57:09 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 17:57:09 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 17:57:09 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 17:57:09 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 17:57:09 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-25 17:57:12 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 15.147 seconds (process running for 16.227)
2025-07-25 17:57:12 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-25 17:57:12 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-25 17:57:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 17:57:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 17:57:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-25 17:57:13 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 17:57:17 [DubboServerHandler-*************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-25 17:57:18 [DubboServerHandler-*************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send],SpendTime=[1296ms]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x7e4a7060, L:/*************:11029 - R:/*************:8091] read idle.
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7e4a7060, L:/*************:11029 - R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e4a7060, L:/*************:11029 - R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]) will closed
2025-07-25 19:13:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e4a7060, L:/*************:11029 ! R:/*************:8091]) will closed
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x65fb0bf9, L:/*************:11017 - R:/*************:8091] read idle.
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x65fb0bf9, L:/*************:11017 - R:/*************:8091]
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65fb0bf9, L:/*************:11017 - R:/*************:8091]) will closed
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]) will closed
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]) will closed
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]) will closed
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]) will closed
2025-07-25 19:13:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65fb0bf9, L:/*************:11017 ! R:/*************:8091]) will closed
2025-07-25 19:13:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 19:13:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753442000066
timestamp=1753442000066
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 19:13:20 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x30a67760, L:/*************:11489 - R:/*************:8091]
2025-07-25 19:13:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x30a67760, L:/*************:11489 - R:/*************:8091]
2025-07-25 19:13:20 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 19:13:20 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 19:13:20 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 19:13:20 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x3f445c52, L:/*************:11784 - R:/*************:8091]
2025-07-25 19:13:20 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0x3f445c52, L:/*************:11784 - R:/*************:8091]
2025-07-25 20:20:17 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 20:20:19 [DubboServerHandler-*************:20880-thread-14] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-25 20:20:19 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[2282ms]
2025-07-25 20:20:19 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-25 20:20:19 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x3f445c52, L:/*************:11784 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x30a67760, L:/*************:11489 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x3f445c52, L:/*************:11784 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x30a67760, L:/*************:11489 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x3f445c52, L:/*************:11784 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x30a67760, L:/*************:11489 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3f445c52, L:/*************:11784 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x30a67760, L:/*************:11489 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x30a67760, L:/*************:11489 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3f445c52, L:/*************:11784 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x30a67760, L:/*************:11489 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3f445c52, L:/*************:11784 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x30a67760, L:/*************:11489 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3f445c52, L:/*************:11784 ! R:/*************:8091]) will closed
2025-07-25 20:41:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753447300065
timestamp=1753447300065
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 20:41:40 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:40 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 20:41:40 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3f445c52, L:/*************:11784 ! R:/*************:8091]
2025-07-25 20:41:40 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3f445c52, L:/*************:11784 ! R:/*************:8091]
2025-07-25 20:41:40 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 20:41:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf8f39be7, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x85ef2cf8, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:50 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:50 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753447310065
timestamp=1753447310065
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 20:41:50 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:50 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 20:41:50 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 20:41:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x24249cce, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9fd5c409, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753447320066
timestamp=1753447320066
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 20:42:00 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:00 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 20:42:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 20:42:02 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xec37debf, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:02 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x14865bac, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753447330065
timestamp=1753447330065
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 20:42:10 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:10 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 20:42:10 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 20:42:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8ee2b987, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfa26b9e8, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753447340065
timestamp=1753447340065
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 20:42:20 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:20 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 20:42:20 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 20:42:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7f3c524f, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xce4d4035, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:30 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:30 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753447350065
timestamp=1753447350065
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 20:42:30 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:30 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 20:42:30 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 20:42:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1f783021, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8256fc82, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:40 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:40 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753447360066
timestamp=1753447360066
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-25 20:42:40 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:40 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-25 20:42:40 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-25 20:42:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6cb7398a, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x58f98917, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:42 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 20:42:42 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 20:42:42 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 20:42:42 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 20:42:43 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 20:42:43 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-25 20:42:43 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-25 20:42:43 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-25 20:42:43 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-25 20:42:43 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
