2025-07-18 09:47:11 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 09:47:11 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 31956 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 09:47:11 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-18 09:47:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-18 09:47:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 09:47:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 09:47:16 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 09:47:16 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 09:47:16 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 09:47:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 09:47:16 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 09:47:16 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 09:47:16 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 09:47:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752803236858
timestamp=1752803236858
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 09:47:17 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xae94ceb6, L:/*************:13683 - R:/*************:8091]
2025-07-18 09:47:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 89 ms, version:1.7.1,role:TMROLE,channel:[id: 0xae94ceb6, L:/*************:13683 - R:/*************:8091]
2025-07-18 09:47:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 09:47:17 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 09:47:17 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 09:47:17 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 09:47:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 09:47:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 09:47:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 09:47:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 09:47:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 09:47:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 09:47:19 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@79b07ccd
2025-07-18 09:47:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 09:47:19 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 09:47:19 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 09:47:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 09:47:19 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xce566ff4, L:/*************:13726 - R:/*************:8091]
2025-07-18 09:47:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 9 ms, version:1.7.1,role:RMROLE,channel:[id: 0xce566ff4, L:/*************:13726 - R:/*************:8091]
2025-07-18 09:47:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 09:47:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 09:47:20 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 09:47:24 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 09:47:24 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 09:47:25 [redisson-netty-2-7] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 09:47:25 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 09:47:27 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 09:47:27 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 09:47:27 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 09:47:28 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 09:47:28 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-18 09:47:32 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 24.545 seconds (process running for 25.982)
2025-07-18 09:47:32 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-18 09:47:32 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-18 09:47:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 09:47:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 09:47:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-18 09:47:32 [RMI TCP Connection(4)-**********] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 09:50:04 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-18 10:19:12 [DubboServerHandler-*************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 10:19:13 [DubboServerHandler-*************:20880-thread-5] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-18 10:19:13 [DubboServerHandler-*************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1165ms]
2025-07-18 10:19:13 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 10:19:13 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-18 10:42:26 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xae94ceb6, L:/*************:13683 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xae94ceb6, L:/*************:13683 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xce566ff4, L:/*************:13726 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xce566ff4, L:/*************:13726 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xce566ff4, L:/*************:13726 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xae94ceb6, L:/*************:13683 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xae94ceb6, L:/*************:13683 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xae94ceb6, L:/*************:13683 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xce566ff4, L:/*************:13726 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xce566ff4, L:/*************:13726 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xae94ceb6, L:/*************:13683 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xce566ff4, L:/*************:13726 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xae94ceb6, L:/*************:13683 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xce566ff4, L:/*************:13726 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:13:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752808406831
timestamp=1752808406831
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 11:13:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:13:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 11:13:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xce566ff4, L:/*************:13726 ! R:/*************:8091]
2025-07-18 11:13:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xce566ff4, L:/*************:13726 ! R:/*************:8091]
2025-07-18 11:13:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 11:13:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xee892f00, L:/**********:2955 ! R:/*************:8091]
2025-07-18 11:13:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x70e0dc81, L:/**********:2951 ! R:/*************:8091]
2025-07-18 11:13:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x70e0dc81, L:/**********:2951 ! R:/*************:8091]
2025-07-18 11:13:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x70e0dc81, L:/**********:2951 ! R:/*************:8091]
2025-07-18 11:13:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x70e0dc81, L:/**********:2951 ! R:/*************:8091]) will closed
2025-07-18 11:13:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x70e0dc81, L:/**********:2951 ! R:/*************:8091]) will closed
2025-07-18 11:13:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:13:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752808436842
timestamp=1752808436842
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 11:13:56 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x09ccdce5, L:/*************:6438 - R:/*************:8091]
2025-07-18 11:13:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 11 ms, version:1.7.1,role:TMROLE,channel:[id: 0x09ccdce5, L:/*************:6438 - R:/*************:8091]
2025-07-18 11:13:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xee892f00, L:/**********:2955 ! R:/*************:8091]
2025-07-18 11:13:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xee892f00, L:/**********:2955 ! R:/*************:8091]
2025-07-18 11:13:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xee892f00, L:/**********:2955 ! R:/*************:8091]) will closed
2025-07-18 11:13:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xee892f00, L:/**********:2955 ! R:/*************:8091]) will closed
2025-07-18 11:13:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:13:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 11:13:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 11:13:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2561e21a, L:/*************:6514 - R:/*************:8091]
2025-07-18 11:13:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x2561e21a, L:/*************:6514 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2561e21a, L:/*************:6514 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x2561e21a, L:/*************:6514 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2561e21a, L:/*************:6514 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2561e21a, L:/*************:6514 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2561e21a, L:/*************:6514 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2561e21a, L:/*************:6514 ! R:/*************:8091]) will closed
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2561e21a, L:/*************:6514 ! R:/*************:8091]) will closed
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x09ccdce5, L:/*************:6438 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x09ccdce5, L:/*************:6438 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x09ccdce5, L:/*************:6438 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x09ccdce5, L:/*************:6438 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x09ccdce5, L:/*************:6438 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x09ccdce5, L:/*************:6438 ! R:/*************:8091]) will closed
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x09ccdce5, L:/*************:6438 ! R:/*************:8091]) will closed
2025-07-18 11:40:01 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 11:40:01 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 5328 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 11:40:01 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-18 11:40:01 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-18 11:40:01 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 11:40:01 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 11:40:07 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 11:40:07 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 11:40:07 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 11:40:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 11:40:08 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 11:40:08 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 11:40:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 11:40:09 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 11:40:09 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 11:40:09 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 11:40:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 11:40:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 11:40:10 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 13:35:35 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 13:35:35 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 22312 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 13:35:35 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-18 13:35:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-18 13:35:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 13:35:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 13:35:38 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 13:35:38 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 13:35:38 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 13:35:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 13:35:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 13:35:38 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 13:35:38 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 13:35:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752816938853
timestamp=1752816938853
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 13:35:38 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x863fc88d, L:/*************:6709 - R:/*************:8091]
2025-07-18 13:35:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 42 ms, version:1.7.1,role:TMROLE,channel:[id: 0x863fc88d, L:/*************:6709 - R:/*************:8091]
2025-07-18 13:35:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 13:35:38 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 13:35:38 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 13:35:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 13:35:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 13:35:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 13:35:39 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 13:35:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 13:35:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 13:35:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 13:35:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@506d7831
2025-07-18 13:35:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 13:35:41 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 13:35:41 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 13:35:41 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 13:35:41 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x14be039c, L:/*************:6714 - R:/*************:8091]
2025-07-18 13:35:41 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x14be039c, L:/*************:6714 - R:/*************:8091]
2025-07-18 13:35:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 13:35:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 13:35:41 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 13:35:42 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 13:35:42 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 13:35:43 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 13:35:43 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 13:35:44 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 13:35:44 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 13:35:44 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 13:35:45 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 13:35:45 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-18 13:35:47 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 14.224 seconds (process running for 15.231)
2025-07-18 13:35:47 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-18 13:35:47 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-18 13:35:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 13:35:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 13:35:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-18 13:35:48 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 13:37:35 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 13:43:36 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 13:43:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x863fc88d, L:/*************:6709 ! R:/*************:8091]
2025-07-18 13:43:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x863fc88d, L:/*************:6709 ! R:/*************:8091]
2025-07-18 13:43:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x863fc88d, L:/*************:6709 ! R:/*************:8091]
2025-07-18 13:43:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x863fc88d, L:/*************:6709 ! R:/*************:8091]
2025-07-18 13:43:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x863fc88d, L:/*************:6709 ! R:/*************:8091]) will closed
2025-07-18 13:43:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x863fc88d, L:/*************:6709 ! R:/*************:8091]) will closed
2025-07-18 13:43:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x14be039c, L:/*************:6714 ! R:/*************:8091]
2025-07-18 13:43:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x14be039c, L:/*************:6714 ! R:/*************:8091]
2025-07-18 13:43:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x14be039c, L:/*************:6714 ! R:/*************:8091]
2025-07-18 13:43:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x14be039c, L:/*************:6714 ! R:/*************:8091]
2025-07-18 13:43:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x14be039c, L:/*************:6714 ! R:/*************:8091]) will closed
2025-07-18 13:43:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x14be039c, L:/*************:6714 ! R:/*************:8091]) will closed
2025-07-18 14:07:50 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 14:07:50 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 21896 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 14:07:50 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-18 14:07:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-18 14:07:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 14:07:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 14:07:55 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 14:07:55 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 14:07:55 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 14:07:55 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 14:07:55 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:07:55 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 14:07:55 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 14:07:55 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752818875600
timestamp=1752818875600
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 14:07:55 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x91987b60, L:/*************:14934 - R:/*************:8091]
2025-07-18 14:07:55 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 54 ms, version:1.7.1,role:TMROLE,channel:[id: 0x91987b60, L:/*************:14934 - R:/*************:8091]
2025-07-18 14:07:55 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 14:07:55 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 14:07:55 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 14:07:55 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:07:55 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 14:07:55 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 14:07:56 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 14:07:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 14:07:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 14:07:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 14:07:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x91987b60, L:/*************:14934 ! R:/*************:8091]
2025-07-18 14:07:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x91987b60, L:/*************:14934 ! R:/*************:8091]
2025-07-18 14:07:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x91987b60, L:/*************:14934 ! R:/*************:8091]
2025-07-18 14:07:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x91987b60, L:/*************:14934 ! R:/*************:8091]
2025-07-18 14:07:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x91987b60, L:/*************:14934 ! R:/*************:8091]) will closed
2025-07-18 14:07:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x91987b60, L:/*************:14934 ! R:/*************:8091]) will closed
2025-07-18 14:20:48 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 14:20:48 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 18360 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 14:20:48 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-18 14:20:48 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-18 14:20:48 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 14:20:48 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 14:20:51 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 14:20:52 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 14:20:52 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 14:20:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 14:20:52 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:20:52 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 14:20:52 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 14:20:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752819652395
timestamp=1752819652395
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 14:20:52 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x0828ee60, L:/*************:4692 - R:/*************:8091]
2025-07-18 14:20:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 48 ms, version:1.7.1,role:TMROLE,channel:[id: 0x0828ee60, L:/*************:4692 - R:/*************:8091]
2025-07-18 14:20:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 14:20:52 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 14:20:52 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 14:20:52 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:20:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 14:20:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 14:20:53 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 14:20:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 14:20:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 14:20:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 14:20:54 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@39d666e0
2025-07-18 14:20:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 14:20:55 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 14:20:55 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 14:20:55 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 14:20:55 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd02e7566, L:/*************:4696 - R:/*************:8091]
2025-07-18 14:20:55 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xd02e7566, L:/*************:4696 - R:/*************:8091]
2025-07-18 14:20:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 14:20:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 14:20:56 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 14:20:57 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 14:20:57 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 14:20:57 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 14:20:58 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 14:21:00 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 14:21:00 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 14:21:00 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 14:21:00 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 14:21:00 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-18 14:21:03 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 17.58 seconds (process running for 18.787)
2025-07-18 14:21:04 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-18 14:21:04 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-18 14:21:04 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 14:21:04 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 14:21:04 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-18 14:21:04 [RMI TCP Connection(5)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x0828ee60, L:/*************:4692 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd02e7566, L:/*************:4696 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xd02e7566, L:/*************:4696 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x0828ee60, L:/*************:4692 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x0828ee60, L:/*************:4692 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd02e7566, L:/*************:4696 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x0828ee60, L:/*************:4692 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd02e7566, L:/*************:4696 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x0828ee60, L:/*************:4692 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd02e7566, L:/*************:4696 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd02e7566, L:/*************:4696 ! R:/*************:8091]) will closed
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0828ee60, L:/*************:4692 ! R:/*************:8091]) will closed
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd02e7566, L:/*************:4696 ! R:/*************:8091]) will closed
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0828ee60, L:/*************:4692 ! R:/*************:8091]) will closed
2025-07-18 15:57:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752825452209
timestamp=1752825452209
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 15:57:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:57:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd02e7566, L:/*************:4696 ! R:/*************:8091]
2025-07-18 15:57:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd02e7566, L:/*************:4696 ! R:/*************:8091]
2025-07-18 15:57:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 15:57:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1b2d753a, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x293739e5, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752825462210
timestamp=1752825462210
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 15:57:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:57:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 15:57:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x94ade141, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2dd46bcc, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752825472210
timestamp=1752825472210
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 15:57:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:57:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 15:57:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3ef4414a, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7e780f4, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752825482210
timestamp=1752825482210
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 15:58:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:58:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 15:58:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbcefd3cf, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcfc6063d, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752825492209
timestamp=1752825492209
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 15:58:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:58:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 15:58:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3c52ff99, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0cd4167b, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:22 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:22 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752825502210
timestamp=1752825502210
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 15:58:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:58:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 15:58:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9c47485a, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x67fae94e, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:25 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 15:58:25 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 15:58:25 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 15:58:25 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 15:58:26 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 15:58:26 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 15:58:26 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 15:58:26 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 15:58:26 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 15:58:26 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 15:59:43 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 15:59:44 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 10704 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 15:59:44 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-18 15:59:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-18 15:59:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 15:59:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 15:59:49 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 15:59:49 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 15:59:49 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 15:59:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 15:59:50 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 15:59:50 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 15:59:50 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:59:50 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752825590479
timestamp=1752825590479
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 15:59:50 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x55700849, L:/*************:11319 - R:/*************:8091]
2025-07-18 15:59:50 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 62 ms, version:1.7.1,role:TMROLE,channel:[id: 0x55700849, L:/*************:11319 - R:/*************:8091]
2025-07-18 15:59:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 15:59:50 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 15:59:50 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 15:59:50 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 15:59:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 15:59:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 15:59:51 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 15:59:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 15:59:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 15:59:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 15:59:55 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@11f633f
2025-07-18 15:59:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 15:59:56 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:59:56 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 15:59:56 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 15:59:56 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xdf5a92a3, L:/*************:11361 - R:/*************:8091]
2025-07-18 15:59:56 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 12 ms, version:1.7.1,role:RMROLE,channel:[id: 0xdf5a92a3, L:/*************:11361 - R:/*************:8091]
2025-07-18 15:59:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 15:59:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 15:59:57 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 15:59:59 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 15:59:59 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 15:59:59 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 15:59:59 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 16:00:04 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 16:00:04 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 16:00:04 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 16:00:05 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 16:00:05 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-18 16:00:08 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 26.384 seconds (process running for 27.69)
2025-07-18 16:00:08 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-18 16:00:08 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-18 16:00:08 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 16:00:08 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 16:00:08 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-18 16:00:08 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 17:08:14 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 17:08:15 [DubboServerHandler-*************:20880-thread-9] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-18 17:08:15 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[1825ms]
2025-07-18 17:21:20 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:21:20 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[250ms]
2025-07-18 17:21:27 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 17:21:28 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[649ms]
2025-07-18 17:22:20 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 17:22:20 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[424ms]
2025-07-18 17:28:18 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 17:28:18 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[416ms]
2025-07-18 17:36:40 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:36:40 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[48ms]
2025-07-18 17:37:04 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:37:04 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-18 17:37:08 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:37:08 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-18 17:37:28 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 17:37:28 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[41ms]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x55700849, L:/*************:11319 - R:/*************:8091] read idle.
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x55700849, L:/*************:11319 - R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x55700849, L:/*************:11319 - R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x55700849, L:/*************:11319 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x55700849, L:/*************:11319 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x55700849, L:/*************:11319 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x55700849, L:/*************:11319 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x55700849, L:/*************:11319 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x55700849, L:/*************:11319 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x55700849, L:/*************:11319 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x55700849, L:/*************:11319 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x55700849, L:/*************:11319 ! R:/*************:8091]
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x55700849, L:/*************:11319 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x55700849, L:/*************:11319 ! R:/*************:8091]) will closed
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xdf5a92a3, L:/*************:11361 - R:/*************:8091] read idle.
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xdf5a92a3, L:/*************:11361 - R:/*************:8091]
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdf5a92a3, L:/*************:11361 - R:/*************:8091]) will closed
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]) will closed
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]) will closed
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]) will closed
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]) will closed
2025-07-18 18:25:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdf5a92a3, L:/*************:11361 ! R:/*************:8091]) will closed
2025-07-18 18:25:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 18:25:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752834347157
timestamp=1752834347157
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 18:25:47 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf6b5200e, L:/*************:8308 - R:/*************:8091]
2025-07-18 18:25:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:TMROLE,channel:[id: 0xf6b5200e, L:/*************:8308 - R:/*************:8091]
2025-07-18 18:25:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 18:25:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 18:25:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 18:25:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe8a71fec, L:/*************:8309 - R:/*************:8091]
2025-07-18 18:25:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe8a71fec, L:/*************:8309 - R:/*************:8091]
2025-07-18 18:49:51 [DubboServerHandler-*************:20880-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 18:49:52 [DubboServerHandler-*************:20880-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[91ms]
2025-07-18 18:49:55 [DubboServerHandler-*************:20880-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 18:49:55 [DubboServerHandler-*************:20880-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-18 18:50:07 [DubboServerHandler-*************:20880-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 18:50:08 [DubboServerHandler-*************:20880-thread-28] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[514ms]
2025-07-18 18:50:10 [DubboServerHandler-*************:20880-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 18:50:11 [DubboServerHandler-*************:20880-thread-29] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[363ms]
2025-07-18 19:07:34 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe8a71fec, L:/*************:8309 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf6b5200e, L:/*************:8308 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf6b5200e, L:/*************:8308 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe8a71fec, L:/*************:8309 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf6b5200e, L:/*************:8308 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe8a71fec, L:/*************:8309 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf6b5200e, L:/*************:8308 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe8a71fec, L:/*************:8309 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe8a71fec, L:/*************:8309 ! R:/*************:8091]) will closed
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf6b5200e, L:/*************:8308 ! R:/*************:8091]) will closed
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe8a71fec, L:/*************:8309 ! R:/*************:8091]) will closed
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf6b5200e, L:/*************:8308 ! R:/*************:8091]) will closed
2025-07-18 19:07:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752836857140
timestamp=1752836857140
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:07:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:07:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 19:07:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0a5b9651, L:null ! R:/*************:8091]) will closed
2025-07-18 19:07:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x96d84b4a, L:null ! R:/*************:8091]) will closed
2025-07-18 19:07:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752836867134
timestamp=1752836867134
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:07:47 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x17b5b16e, L:/*************:2712 - R:/*************:8091]
2025-07-18 19:07:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 62 ms, version:1.7.1,role:TMROLE,channel:[id: 0x17b5b16e, L:/*************:2712 - R:/*************:8091]
2025-07-18 19:07:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:07:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 19:07:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd994ab47, L:/*************:2713 - R:/*************:8091]
2025-07-18 19:07:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0xd994ab47, L:/*************:2713 - R:/*************:8091]
2025-07-18 19:12:10 [DubboServerHandler-*************:20880-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 19:12:10 [DubboServerHandler-*************:20880-thread-30] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[458ms]
2025-07-18 19:12:21 [DubboServerHandler-*************:20880-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 19:12:22 [DubboServerHandler-*************:20880-thread-31] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[407ms]
2025-07-18 19:20:16 [DubboServerHandler-*************:20880-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:20:16 [DubboServerHandler-*************:20880-thread-32] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[216ms]
2025-07-18 19:20:41 [DubboServerHandler-*************:20880-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:20:41 [DubboServerHandler-*************:20880-thread-33] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-18 19:22:13 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:22:13 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[40ms]
2025-07-18 19:23:07 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:23:07 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[42ms]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[76ms]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[28ms]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[26ms]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[4ms]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[21ms]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[4ms]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[4ms]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:24:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 19:25:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:25:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[114ms]
2025-07-18 19:25:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:25:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[24ms]
2025-07-18 19:25:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:25:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[27ms]
2025-07-18 19:25:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 19:25:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-18 19:25:12 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:25:12 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[44ms]
2025-07-18 19:25:28 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:25:28 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[0ms]
2025-07-18 19:25:35 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:25:35 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[2ms]
2025-07-18 19:26:18 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:26:18 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[43ms]
2025-07-18 19:26:33 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:26:33 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[8ms]
2025-07-18 19:30:05 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:05 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[41ms]
2025-07-18 19:30:07 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:07 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-18 19:30:08 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:08 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-18 19:30:08 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:08 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-18 19:30:17 [DubboServerHandler-*************:20880-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:17 [DubboServerHandler-*************:20880-thread-45] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-18 19:30:17 [DubboServerHandler-*************:20880-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:30:17 [DubboServerHandler-*************:20880-thread-46] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-18 19:35:17 [DubboServerHandler-*************:20880-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:35:17 [DubboServerHandler-*************:20880-thread-47] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[43ms]
2025-07-18 19:38:33 [DubboServerHandler-*************:20880-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-18 19:38:33 [DubboServerHandler-*************:20880-thread-50] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[53ms]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x17b5b16e, L:/*************:2712 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd994ab47, L:/*************:2713 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xd994ab47, L:/*************:2713 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x17b5b16e, L:/*************:2712 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd994ab47, L:/*************:2713 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x17b5b16e, L:/*************:2712 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd994ab47, L:/*************:2713 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x17b5b16e, L:/*************:2712 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd994ab47, L:/*************:2713 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x17b5b16e, L:/*************:2712 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd994ab47, L:/*************:2713 ! R:/*************:8091]) will closed
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x17b5b16e, L:/*************:2712 ! R:/*************:8091]) will closed
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd994ab47, L:/*************:2713 ! R:/*************:8091]) will closed
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x17b5b16e, L:/*************:2712 ! R:/*************:8091]) will closed
2025-07-18 19:49:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752839356923
timestamp=1752839356923
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:49:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:17 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:49:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd994ab47, L:/*************:2713 ! R:/*************:8091]
2025-07-18 19:49:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd994ab47, L:/*************:2713 ! R:/*************:8091]
2025-07-18 19:49:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 19:49:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x42861bbb, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc02f9141, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:26 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:26 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752839366919
timestamp=1752839366919
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:49:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:49:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 19:49:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x79803694, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x17527fd2, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:36 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:36 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752839376918
timestamp=1752839376918
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:49:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:49:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 19:49:38 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3f1970bf, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeeb37ee8, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:46 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:46 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752839386921
timestamp=1752839386921
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:49:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:49:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 19:49:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc69dcaa6, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:49 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x420436a8, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:56 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:56 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752839396922
timestamp=1752839396922
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:49:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:49:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 19:49:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x799f690f, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:59 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa26e326b, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:06 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:06 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752839406917
timestamp=1752839406917
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:50:07 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:07 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:50:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 19:50:08 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1a16be82, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe5d56fe5, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:16 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 19:50:16 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 19:50:16 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 19:50:16 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 19:50:16 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:16 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,*************
timestamp=*************
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:50:17 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 19:50:17 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 19:50:17 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 19:50:17 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 19:50:17 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 19:50:17 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 19:50:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x818f9ff3, L:null ! R:/*************:8091]) will closed
2025-07-18 19:57:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 19:57:21 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 13896 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 19:57:21 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-18 19:57:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-18 19:57:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 19:57:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 19:57:27 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 19:57:27 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 19:57:27 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 19:57:27 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 19:57:27 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 19:57:27 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 19:57:27 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:57:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752839848035
timestamp=1752839848035
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 19:57:28 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf7b77be9, L:/*************:11678 - R:/*************:8091]
2025-07-18 19:57:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 163 ms, version:1.7.1,role:TMROLE,channel:[id: 0xf7b77be9, L:/*************:11678 - R:/*************:8091]
2025-07-18 19:57:28 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 19:57:28 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 19:57:28 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 19:57:28 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 19:57:28 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-18 19:57:28 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 19:57:29 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 19:57:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 19:57:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 19:57:30 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 19:57:31 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@153f6a67
2025-07-18 19:57:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 19:57:31 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:57:31 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 19:57:31 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 19:57:31 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x743078a1, L:/*************:11697 - R:/*************:8091]
2025-07-18 19:57:31 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x743078a1, L:/*************:11697 - R:/*************:8091]
2025-07-18 19:57:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 19:57:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 19:57:32 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 19:57:33 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 19:57:33 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 19:57:33 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 19:57:34 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 19:57:36 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 19:57:36 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 19:57:36 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 19:57:36 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 19:57:36 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-18 19:57:39 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 20.905 seconds (process running for 22.146)
2025-07-18 19:57:39 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-18 19:57:39 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-18 19:57:39 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 19:57:39 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 19:57:39 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-18 19:57:39 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 20:06:40 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 20:06:41 [DubboServerHandler-*************:20880-thread-6] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-18 20:06:42 [DubboServerHandler-*************:20880-thread-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[1915ms]
2025-07-18 20:06:43 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload]
2025-07-18 20:06:43 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[upload],SpendTime=[267ms]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf7b77be9, L:/*************:11678 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x743078a1, L:/*************:11697 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xf7b77be9, L:/*************:11678 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x743078a1, L:/*************:11697 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x743078a1, L:/*************:11697 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf7b77be9, L:/*************:11678 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf7b77be9, L:/*************:11678 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x743078a1, L:/*************:11697 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x743078a1, L:/*************:11697 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf7b77be9, L:/*************:11678 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x743078a1, L:/*************:11697 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7b77be9, L:/*************:11678 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x743078a1, L:/*************:11697 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7b77be9, L:/*************:11678 ! R:/*************:8091]) will closed
2025-07-18 20:16:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752841007681
timestamp=1752841007681
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 20:16:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:16:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x743078a1, L:/*************:11697 ! R:/*************:8091]
2025-07-18 20:16:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x743078a1, L:/*************:11697 ! R:/*************:8091]
2025-07-18 20:16:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 20:16:49 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb87b5b7b, L:null ! R:/*************:8091]) will closed
2025-07-18 20:16:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf25c28a2, L:null ! R:/*************:8091]) will closed
2025-07-18 20:16:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752841017680
timestamp=1752841017680
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 20:16:58 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:58 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:16:58 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 20:16:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf52afda0, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:00 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x95256848, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:07 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:07 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752841027680
timestamp=1752841027680
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 20:17:08 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:08 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:08 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 20:17:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfa033477, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x210337b0, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752841037692
timestamp=1752841037692
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 20:17:18 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:18 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:18 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 20:17:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd9a0f1af, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf6290973, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752841047679
timestamp=1752841047679
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 20:17:28 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:28 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:28 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 20:17:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x236deb82, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x841258e2, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1752841057679
timestamp=1752841057679
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-18 20:17:38 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:38 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-18 20:17:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-18 20:17:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e8aafcf, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfbf1ab6a, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:45 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 20:17:45 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 20:17:45 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 20:17:45 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 20:17:45 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 20:17:45 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 20:17:46 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 20:17:46 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 20:17:46 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 20:17:46 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
