2025-07-24 08:31:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 08:31:55 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 23088 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-24 08:31:55 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-24 08:31:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-24 08:31:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-24 08:31:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-24 08:32:00 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-24 08:32:00 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-24 08:32:00 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-24 08:32:00 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-24 08:32:00 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-24 08:32:00 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-24 08:32:00 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-24 08:32:00 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-24 08:32:00 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-24 08:32:00 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-24 08:32:00 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-24 08:32:00 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-24 08:32:02 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-24 08:32:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-24 08:32:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-24 08:32:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-24 08:32:04 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@48dbf9d
2025-07-24 08:32:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-24 08:32:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-24 08:32:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-24 08:32:05 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-24 08:32:07 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-24 08:32:07 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-24 08:32:07 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-24 08:32:08 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-24 08:32:10 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-24 08:32:10 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-24 08:32:10 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-24 08:32:11 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-24 08:32:11 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-24 08:32:14 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 21.637 seconds (process running for 22.701)
2025-07-24 08:32:14 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-24 08:32:14 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-24 08:32:14 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-24 08:32:14 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-24 08:32:14 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-24 08:32:14 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 08:33:00 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 08:33:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753317180476
timestamp=1753317180476
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 08:33:00 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x88281c9a, L:/*************:2123 - R:/*************:8091]
2025-07-24 08:33:00 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 24 ms, version:1.7.1,role:TMROLE,channel:[id: 0x88281c9a, L:/*************:2123 - R:/*************:8091]
2025-07-24 08:33:00 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 08:33:00 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 08:33:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 08:33:00 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xac7c2e28, L:/*************:2127 - R:/*************:8091]
2025-07-24 08:33:00 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0xac7c2e28, L:/*************:2127 - R:/*************:8091]
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x88281c9a, L:/*************:2123 - R:/*************:8091] read idle.
2025-07-24 13:27:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xac7c2e28, L:/*************:2127 - R:/*************:8091] read idle.
2025-07-24 13:27:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xac7c2e28, L:/*************:2127 - R:/*************:8091]
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x88281c9a, L:/*************:2123 - R:/*************:8091]
2025-07-24 13:27:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac7c2e28, L:/*************:2127 - R:/*************:8091]) will closed
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88281c9a, L:/*************:2123 - R:/*************:8091]) will closed
2025-07-24 13:27:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]) will closed
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]) will closed
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]) will closed
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]) will closed
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]) will closed
2025-07-24 13:27:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88281c9a, L:/*************:2123 ! R:/*************:8091]) will closed
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]) will closed
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]) will closed
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]) will closed
2025-07-24 13:27:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac7c2e28, L:/*************:2127 ! R:/*************:8091]) will closed
2025-07-24 13:27:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 13:27:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753334857453
timestamp=1753334857453
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 13:27:37 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x906ef34d, L:/*************:12066 - R:/*************:8091]
2025-07-24 13:27:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 111 ms, version:1.7.1,role:TMROLE,channel:[id: 0x906ef34d, L:/*************:12066 - R:/*************:8091]
2025-07-24 13:27:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 13:27:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 13:27:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 13:27:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa3eaa972, L:/*************:12408 - R:/*************:8091]
2025-07-24 13:27:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0xa3eaa972, L:/*************:12408 - R:/*************:8091]
2025-07-24 14:08:19 [DubboServerHandler-*************:20880-thread-69] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-24 14:10:13 [DubboServerHandler-*************:20880-thread-70] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-24 14:10:23 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-24 14:10:25 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x906ef34d, L:/*************:12066 ! R:/*************:8091]
2025-07-24 14:10:25 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x906ef34d, L:/*************:12066 ! R:/*************:8091]
2025-07-24 14:10:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa3eaa972, L:/*************:12408 ! R:/*************:8091]
2025-07-24 14:10:25 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x906ef34d, L:/*************:12066 ! R:/*************:8091]
2025-07-24 14:10:25 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x906ef34d, L:/*************:12066 ! R:/*************:8091]
2025-07-24 14:10:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa3eaa972, L:/*************:12408 ! R:/*************:8091]
2025-07-24 14:10:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa3eaa972, L:/*************:12408 ! R:/*************:8091]
2025-07-24 14:10:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa3eaa972, L:/*************:12408 ! R:/*************:8091]
2025-07-24 14:10:25 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x906ef34d, L:/*************:12066 ! R:/*************:8091]) will closed
2025-07-24 14:10:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa3eaa972, L:/*************:12408 ! R:/*************:8091]) will closed
2025-07-24 14:10:25 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x906ef34d, L:/*************:12066 ! R:/*************:8091]) will closed
2025-07-24 14:10:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa3eaa972, L:/*************:12408 ! R:/*************:8091]) will closed
2025-07-24 14:10:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 14:10:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753337427344
timestamp=1753337427344
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 14:10:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 14:10:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 14:10:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 14:10:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x943c5df2, L:null ! R:/*************:8091]) will closed
2025-07-24 14:10:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x907ecd48, L:null ! R:/*************:8091]) will closed
2025-07-24 14:10:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 14:10:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753337437333
timestamp=1753337437333
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 14:10:37 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x26df67c1, L:/*************:3517 - R:/*************:8091]
2025-07-24 14:10:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x26df67c1, L:/*************:3517 - R:/*************:8091]
2025-07-24 14:10:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 14:10:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 14:10:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 14:10:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x525a76f5, L:/*************:3518 - R:/*************:8091]
2025-07-24 14:10:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0x525a76f5, L:/*************:3518 - R:/*************:8091]
2025-07-24 14:15:39 [DubboServerHandler-*************:20880-thread-75] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-24 14:16:17 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-24 14:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x26df67c1, L:/*************:3517 ! R:/*************:8091]
2025-07-24 14:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x26df67c1, L:/*************:3517 ! R:/*************:8091]
2025-07-24 14:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x26df67c1, L:/*************:3517 ! R:/*************:8091]
2025-07-24 14:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x26df67c1, L:/*************:3517 ! R:/*************:8091]
2025-07-24 14:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x26df67c1, L:/*************:3517 ! R:/*************:8091]) will closed
2025-07-24 14:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x26df67c1, L:/*************:3517 ! R:/*************:8091]) will closed
2025-07-24 14:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x525a76f5, L:/*************:3518 ! R:/*************:8091]
2025-07-24 14:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x525a76f5, L:/*************:3518 ! R:/*************:8091]
2025-07-24 14:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x525a76f5, L:/*************:3518 ! R:/*************:8091]
2025-07-24 14:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x525a76f5, L:/*************:3518 ! R:/*************:8091]
2025-07-24 14:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x525a76f5, L:/*************:3518 ! R:/*************:8091]) will closed
2025-07-24 14:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x525a76f5, L:/*************:3518 ! R:/*************:8091]) will closed
2025-07-24 14:16:22 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 14:16:22 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 33164 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-24 14:16:22 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-24 14:16:23 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-24 14:16:23 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-24 14:16:23 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-24 14:16:26 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-24 14:16:26 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-24 14:16:26 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-24 14:16:26 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-24 14:16:26 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-24 14:16:26 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-24 14:16:26 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 14:16:26 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753337786943
timestamp=1753337786943
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 14:16:27 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7042cdd8, L:/*************:4328 - R:/*************:8091]
2025-07-24 14:16:27 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 53 ms, version:1.7.1,role:TMROLE,channel:[id: 0x7042cdd8, L:/*************:4328 - R:/*************:8091]
2025-07-24 14:16:27 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-24 14:16:27 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-24 14:16:27 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-24 14:16:27 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-24 14:16:27 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-24 14:16:27 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-24 14:16:28 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-24 14:16:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-24 14:16:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-24 14:16:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-24 14:16:29 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@dcaa0e8
2025-07-24 14:16:29 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-24 14:16:29 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 14:16:29 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 14:16:29 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 14:16:29 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x93391685, L:/*************:4336 - R:/*************:8091]
2025-07-24 14:16:29 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x93391685, L:/*************:4336 - R:/*************:8091]
2025-07-24 14:16:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-24 14:16:29 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-24 14:16:30 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-24 14:16:31 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-24 14:16:31 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-24 14:16:31 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-24 14:16:32 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-24 14:16:34 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-24 14:16:34 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-24 14:16:34 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-24 14:16:34 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-24 14:16:34 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-24 14:16:37 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 16.334 seconds (process running for 17.844)
2025-07-24 14:16:37 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-24 14:16:37 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-24 14:16:37 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-24 14:16:37 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-24 14:16:37 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-24 14:16:37 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 14:17:18 [DubboServerHandler-*************:20880-thread-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-24 14:49:16 [XNIO-1 task-2] INFO  c.ym.common.oss.factory.OssFactory - 创建OSS实例 key => aliyun
2025-07-24 14:49:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-24 14:49:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[53ms]
2025-07-24 14:49:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:49:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[69ms]
2025-07-24 14:51:03 [DubboServerHandler-*************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 14:51:03 [DubboServerHandler-*************:20880-thread-8] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[56ms]
2025-07-24 14:51:09 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 14:51:09 [DubboServerHandler-*************:20880-thread-9] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-24 14:51:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:51:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[50ms]
2025-07-24 14:51:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-24 14:51:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[22ms]
2025-07-24 14:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-24 14:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-24 14:51:19 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 14:51:19 [DubboServerHandler-*************:20880-thread-10] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[25ms]
2025-07-24 14:51:22 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 14:51:22 [DubboServerHandler-*************:20880-thread-11] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-24 14:51:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:51:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[24ms]
2025-07-24 14:51:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:51:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-24 14:51:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-24 14:51:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[22ms]
2025-07-24 14:51:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:51:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[4ms]
2025-07-24 14:51:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:51:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-24 14:51:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:51:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-24 14:52:00 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 14:52:00 [DubboServerHandler-*************:20880-thread-12] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[49ms]
2025-07-24 14:52:03 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 14:52:03 [DubboServerHandler-*************:20880-thread-13] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[1ms]
2025-07-24 14:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-24 14:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-24 14:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:52:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-24 14:53:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-24 14:53:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[27ms]
2025-07-24 14:53:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:53:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-24 14:53:37 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 14:53:37 [DubboServerHandler-*************:20880-thread-14] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[72ms]
2025-07-24 14:53:37 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 14:53:37 [DubboServerHandler-*************:20880-thread-15] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-24 14:54:28 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 14:54:28 [DubboServerHandler-*************:20880-thread-16] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[134ms]
2025-07-24 14:54:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:54:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[52ms]
2025-07-24 14:54:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:54:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-24 14:54:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-24 14:54:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[19ms]
2025-07-24 15:02:06 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:06 [DubboServerHandler-*************:20880-thread-17] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[51ms]
2025-07-24 15:02:06 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:06 [DubboServerHandler-*************:20880-thread-18] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[33ms]
2025-07-24 15:02:07 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:07 [DubboServerHandler-*************:20880-thread-19] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[33ms]
2025-07-24 15:02:07 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:07 [DubboServerHandler-*************:20880-thread-20] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-24 15:02:08 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:08 [DubboServerHandler-*************:20880-thread-21] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[31ms]
2025-07-24 15:02:09 [DubboServerHandler-*************:20880-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:09 [DubboServerHandler-*************:20880-thread-22] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[25ms]
2025-07-24 15:02:09 [DubboServerHandler-*************:20880-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:09 [DubboServerHandler-*************:20880-thread-23] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[30ms]
2025-07-24 15:02:10 [DubboServerHandler-*************:20880-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:10 [DubboServerHandler-*************:20880-thread-24] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-24 15:02:10 [DubboServerHandler-*************:20880-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:10 [DubboServerHandler-*************:20880-thread-25] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-24 15:02:11 [DubboServerHandler-*************:20880-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:11 [DubboServerHandler-*************:20880-thread-26] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[30ms]
2025-07-24 15:02:12 [DubboServerHandler-*************:20880-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:02:12 [DubboServerHandler-*************:20880-thread-27] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[19ms]
2025-07-24 15:34:01 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:01 [DubboServerHandler-*************:20880-thread-34] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[95ms]
2025-07-24 15:34:02 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:02 [DubboServerHandler-*************:20880-thread-35] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[31ms]
2025-07-24 15:34:02 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:02 [DubboServerHandler-*************:20880-thread-36] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-24 15:34:03 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:03 [DubboServerHandler-*************:20880-thread-37] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[24ms]
2025-07-24 15:34:04 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:04 [DubboServerHandler-*************:20880-thread-38] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[28ms]
2025-07-24 15:34:04 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:04 [DubboServerHandler-*************:20880-thread-39] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-24 15:34:05 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:05 [DubboServerHandler-*************:20880-thread-40] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-24 15:34:06 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:06 [DubboServerHandler-*************:20880-thread-41] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[22ms]
2025-07-24 15:34:06 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:07 [DubboServerHandler-*************:20880-thread-42] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[55ms]
2025-07-24 15:34:07 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:07 [DubboServerHandler-*************:20880-thread-43] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[21ms]
2025-07-24 15:34:08 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds]
2025-07-24 15:34:08 [DubboServerHandler-*************:20880-thread-44] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteFileService],MethodName=[selectUrlByIds],SpendTime=[23ms]
2025-07-24 16:40:38 [NettyServerWorker-6-8] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed31:0:0:0:147]:13348 -> /[2408:8215:a11:ed31:0:0:0:147]:20880 is disconnected., dubbo version: 3.2.14, current host: *************
2025-07-24 16:41:06 [NettyServerWorker-6-9] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed31:0:0:0:147]:7279 -> /[2408:8215:a11:ed31:0:0:0:147]:20880 is established., dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:13 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x8abb6449, L:/[2408:8215:a11:ed31:0:0:0:147]:4492 - R:/[2408:8215:a11:ed31:0:0:0:147]:20881]], dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:13 [NettyServerWorker-6-5] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed31:0:0:0:147]:4483 -> /[2408:8215:a11:ed31:0:0:0:147]:20880 is disconnected., dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:13 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x8abb6449, L:/[2408:8215:a11:ed31:0:0:0:147]:4492 - R:/[2408:8215:a11:ed31:0:0:0:147]:20881]], dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos-grpc-client-executor-localhost-1686] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Receive server push request, request = NotifySubscriberRequest, requestId = 132
2025-07-24 16:42:14 [nacos-grpc-client-executor-localhost-1686] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DUBBO_GROUP@@yumeng-system -> [{"instanceId":"*************#20881#null#yumeng-system","ip":"*************","port":20881,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DUBBO_GROUP@@yumeng-system","metadata":{"dubbo.endpoints":"[{\"port\":20881,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"100a2ea5f0576bf126adbefd4e91c5da","dubbo.metadata.storage-type":"remote","ipv6":"2408:8215:a11:ed31:0:0:0:147","timestamp":"1753337830171"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-24 16:42:14 [nacos-grpc-client-executor-localhost-1686] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DUBBO_GROUP@@yumeng-system -> []
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Received instance notification, serviceName: yumeng-system, instances: 0, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos-grpc-client-executor-localhost-1686] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Ack server push request, request = NotifySubscriberRequest, requestId = 132
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteUserService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteUserService. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteLogService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteLogService. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteDictService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteDictService. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteDeptService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteDeptService. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteDataScopeService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteDataScopeService. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteClientService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x8abb6449, L:/[2408:8215:a11:ed31:0:0:0:147]:4492 - R:/[2408:8215:a11:ed31:0:0:0:147]:20881], dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [NettyClientWorker-7-3] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /[2408:8215:a11:ed31:0:0:0:147]:4492 -> /[2408:8215:a11:ed31:0:0:0:147]:20881 is disconnected., dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:14 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteClientService. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.14, current host: *************
2025-07-24 16:42:58 [NettyServerWorker-6-10] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed31:0:0:0:147]:7484 -> /[2408:8215:a11:ed31:0:0:0:147]:20880 is established., dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos-grpc-client-executor-localhost-1695] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Receive server push request, request = NotifySubscriberRequest, requestId = 143
2025-07-24 16:43:00 [nacos-grpc-client-executor-localhost-1695] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DUBBO_GROUP@@yumeng-system -> [{"instanceId":"*************#20881#null#yumeng-system","ip":"*************","port":20881,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DUBBO_GROUP@@yumeng-system","metadata":{"dubbo.endpoints":"[{\"port\":20881,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"634802969e01091e0d9dcd911815deb0","dubbo.metadata.storage-type":"remote","ipv6":"2408:8215:a11:ed31:0:0:0:147","timestamp":"1753346577741"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-24 16:43:00 [nacos-grpc-client-executor-localhost-1695] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DUBBO_GROUP@@yumeng-system -> [{"instanceId":"*************#20881#null#yumeng-system","ip":"*************","port":20881,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DUBBO_GROUP@@yumeng-system","metadata":{"dubbo.endpoints":"[{\"port\":20881,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"634802969e01091e0d9dcd911815deb0","dubbo.metadata.storage-type":"remote","ipv6":"2408:8215:a11:ed31:0:0:0:147","timestamp":"1753346577741"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Received instance notification, serviceName: yumeng-system, instances: 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos-grpc-client-executor-localhost-1695] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Ack server push request, request = NotifySubscriberRequest, requestId = 143
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] 1 unique working revisions: 634802969e01091e0d9dcd911815deb0 , dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteUserService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [NettyClientWorker-7-4] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /[2408:8215:a11:ed31:0:0:0:147]:7495 -> /[2408:8215:a11:ed31:0:0:0:147]:20881 is established., dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.transport.AbstractClient -  [DUBBO] Successfully connect to server /[2408:8215:a11:ed31:0:0:0:147]:20881 from NettyClient ************* using dubbo version 3.2.14, channel is NettyChannel [channel=[id: 0xc91bbf55, L:/[2408:8215:a11:ed31:0:0:0:147]:7495 - R:/[2408:8215:a11:ed31:0:0:0:147]:20881]], dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.transport.AbstractClient -  [DUBBO] Start NettyClient /************* connect to the server /[2408:8215:a11:ed31:0:0:0:147]:20881, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteUserService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20881, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteLogService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteLogService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20881, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteDictService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteDictService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20881, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteDeptService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteDeptService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20881, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteDataScopeService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteDataScopeService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20881, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteClientService:null with urls 1, dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: *************
2025-07-24 16:43:00 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteClientService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20881, dubbo version: 3.2.14, current host: *************
2025-07-24 17:00:27 [DubboServerHandler-*************:20880-thread-49] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-24 17:10:16 [DubboClientHandler-thread-3] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xc91bbf55, L:/[2408:8215:a11:ed31:0:0:0:147]:7495 - R:/[2408:8215:a11:ed31:0:0:0:147]:20881]], dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [SpringContextShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](yumeng-resource), dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [DubboClientHandler-thread-3] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x01fec79d, L:/[2408:8215:a11:ed31:0:0:0:147]:4378 - R:/[2408:8215:a11:ed31:0:0:0:147]:20880]], dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [SpringContextShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [yumeng-resource], dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [GRPC-UNSUBSCRIBE] service:yumeng-resource, group:DUBBO_GROUP, cluster: 
2025-07-24 17:10:16 [NettyServerWorker-6-10] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed31:0:0:0:147]:7484 -> /[2408:8215:a11:ed31:0:0:0:147]:20880 is disconnected., dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [SpringContextShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x01fec79d, L:/[2408:8215:a11:ed31:0:0:0:147]:4378 - R:/[2408:8215:a11:ed31:0:0:0:147]:20880], dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [NettyClientWorker-7-2] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /[2408:8215:a11:ed31:0:0:0:147]:4378 -> /[2408:8215:a11:ed31:0:0:0:147]:20880 is disconnected., dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [NettyServerWorker-6-3] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed31:0:0:0:147]:4378 -> /[2408:8215:a11:ed31:0:0:0:147]:20880 is disconnected., dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [292a23ce-fe99-4c4c-97f0-fc6aff9198da_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [adc36b24-215f-439c-a075-29b8c772b97b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9f978492-1f85-46aa-b363-ea52a5751da6] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [90ca63c4-7bfa-401b-b180-eee24119ef6d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f62f6a18-94ca-419a-8745-117af61e1176_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4694bca3-7036-4248-9495-93210b6b5e95] Server healthy check fail, currentConnection = 1753337786871_127.0.0.1_4327
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4694bca3-7036-4248-9495-93210b6b5e95] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [292a23ce-fe99-4c4c-97f0-fc6aff9198da_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [adc36b24-215f-439c-a075-29b8c772b97b] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f62f6a18-94ca-419a-8745-117af61e1176_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [90ca63c4-7bfa-401b-b180-eee24119ef6d_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9f978492-1f85-46aa-b363-ea52a5751da6] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4694bca3-7036-4248-9495-93210b6b5e95] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [292a23ce-fe99-4c4c-97f0-fc6aff9198da_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [Dubbo-framework-shared-scheduler-thread-7] INFO  o.a.d.r.c.AbstractServiceDiscovery -  [DUBBO] Metadata of instance changed, updating instance with revision 0., dubbo version: 3.2.14, current host: *************
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [adc36b24-215f-439c-a075-29b8c772b97b] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [90ca63c4-7bfa-401b-b180-eee24119ef6d_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f62f6a18-94ca-419a-8745-117af61e1176_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [Dubbo-framework-shared-scheduler-thread-7] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='*************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed31:0:0:0:147, timestamp=1753337795312}}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9f978492-1f85-46aa-b363-ea52a5751da6] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4694bca3-7036-4248-9495-93210b6b5e95] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:16 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [292a23ce-fe99-4c4c-97f0-fc6aff9198da_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [adc36b24-215f-439c-a075-29b8c772b97b] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [90ca63c4-7bfa-401b-b180-eee24119ef6d_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f62f6a18-94ca-419a-8745-117af61e1176_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9f978492-1f85-46aa-b363-ea52a5751da6] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4694bca3-7036-4248-9495-93210b6b5e95] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [Dubbo-framework-shared-scheduler-thread-7] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='*************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed31:0:0:0:147, timestamp=1753337795312}}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [292a23ce-fe99-4c4c-97f0-fc6aff9198da_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [adc36b24-215f-439c-a075-29b8c772b97b] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [90ca63c4-7bfa-401b-b180-eee24119ef6d_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f62f6a18-94ca-419a-8745-117af61e1176_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9f978492-1f85-46aa-b363-ea52a5751da6] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4694bca3-7036-4248-9495-93210b6b5e95] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:17 [Dubbo-framework-shared-scheduler-thread-7] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='*************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed31:0:0:0:147, timestamp=1753337795312}}
2025-07-24 17:10:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x93391685, L:/*************:4336 - R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7042cdd8, L:/*************:4328 - R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x7042cdd8, L:/*************:4328 - R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x93391685, L:/*************:4336 - R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7042cdd8, L:/*************:4328 ! R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x93391685, L:/*************:4336 ! R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x93391685, L:/*************:4336 ! R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7042cdd8, L:/*************:4328 ! R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7042cdd8, L:/*************:4328 ! R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x93391685, L:/*************:4336 ! R:/*************:8091]
2025-07-24 17:10:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93391685, L:/*************:4336 ! R:/*************:8091]) will closed
2025-07-24 17:10:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7042cdd8, L:/*************:4328 ! R:/*************:8091]) will closed
2025-07-24 17:10:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7042cdd8, L:/*************:4328 ! R:/*************:8091]) will closed
2025-07-24 17:10:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93391685, L:/*************:4336 ! R:/*************:8091]) will closed
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [292a23ce-fe99-4c4c-97f0-fc6aff9198da_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [adc36b24-215f-439c-a075-29b8c772b97b] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [90ca63c4-7bfa-401b-b180-eee24119ef6d_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9f978492-1f85-46aa-b363-ea52a5751da6] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f62f6a18-94ca-419a-8745-117af61e1176_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [Dubbo-framework-shared-scheduler-thread-7] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='*************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed31:0:0:0:147, timestamp=1753337795312}}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4694bca3-7036-4248-9495-93210b6b5e95] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [Dubbo-framework-shared-scheduler-thread-7] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='*************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed31:0:0:0:147, timestamp=1753337795312}}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [292a23ce-fe99-4c4c-97f0-fc6aff9198da_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [adc36b24-215f-439c-a075-29b8c772b97b] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9f978492-1f85-46aa-b363-ea52a5751da6] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f62f6a18-94ca-419a-8745-117af61e1176_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [90ca63c4-7bfa-401b-b180-eee24119ef6d_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:18 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4694bca3-7036-4248-9495-93210b6b5e95] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:18 [Dubbo-framework-shared-scheduler-thread-7] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='*************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed31:0:0:0:147, timestamp=1753337795312}}
2025-07-24 17:10:19 [Dubbo-framework-shared-scheduler-thread-7] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='*************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed31:0:0:0:147, timestamp=1753337795312}}
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [292a23ce-fe99-4c4c-97f0-fc6aff9198da_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8570a933-d1b1-4ddb-ba6d-82d597580618] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [adc36b24-215f-439c-a075-29b8c772b97b] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9f978492-1f85-46aa-b363-ea52a5751da6] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f62f6a18-94ca-419a-8745-117af61e1176_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [90ca63c4-7bfa-401b-b180-eee24119ef6d_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-24 17:10:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4694bca3-7036-4248-9495-93210b6b5e95] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-24 17:10:19 [Dubbo-framework-shared-scheduler-thread-7] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='*************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed31:0:0:0:147, timestamp=1753337795312}}
2025-07-24 17:18:07 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 17:18:07 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 41100 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-24 17:18:07 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-24 17:18:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-24 17:18:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-24 17:18:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-24 17:18:11 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-24 17:18:11 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-24 17:18:11 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-24 17:18:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-24 17:18:12 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-24 17:18:12 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-24 17:18:12 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 17:18:12 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753348692397
timestamp=1753348692397
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 17:18:12 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7ee13535, L:/*************:11269 - R:/*************:8091]
2025-07-24 17:18:12 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 113 ms, version:1.7.1,role:TMROLE,channel:[id: 0x7ee13535, L:/*************:11269 - R:/*************:8091]
2025-07-24 17:18:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-24 17:18:12 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-24 17:18:12 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-24 17:18:12 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-24 17:18:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-24 17:18:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-24 17:18:13 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-24 17:18:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-24 17:18:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-24 17:18:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-24 17:18:15 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@74ed1b3d
2025-07-24 17:18:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-24 17:18:15 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 17:18:15 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 17:18:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 17:18:15 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5079c49c, L:/*************:11286 - R:/*************:8091]
2025-07-24 17:18:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 13 ms, version:1.7.1,role:RMROLE,channel:[id: 0x5079c49c, L:/*************:11286 - R:/*************:8091]
2025-07-24 17:18:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-24 17:18:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-24 17:18:16 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-24 17:18:18 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-24 17:18:18 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-24 17:18:18 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-24 17:18:18 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-24 17:18:21 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-24 17:18:21 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-24 17:18:21 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-24 17:18:21 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-24 17:18:21 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-24 17:18:24 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 18.982 seconds (process running for 20.007)
2025-07-24 17:18:24 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-24 17:18:24 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-24 17:18:24 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-24 17:18:24 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-24 17:18:24 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-24 17:18:25 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 17:20:27 [DubboServerHandler-*************:20880-thread-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMailService],MethodName=[send]
2025-07-24 18:30:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7ee13535, L:/*************:11269 - R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5079c49c, L:/*************:11286 - R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5079c49c, L:/*************:11286 - R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x7ee13535, L:/*************:11269 - R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7ee13535, L:/*************:11269 ! R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5079c49c, L:/*************:11286 ! R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7ee13535, L:/*************:11269 ! R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5079c49c, L:/*************:11286 ! R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5079c49c, L:/*************:11286 ! R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7ee13535, L:/*************:11269 ! R:/*************:8091]
2025-07-24 18:30:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7ee13535, L:/*************:11269 ! R:/*************:8091]) will closed
2025-07-24 18:30:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5079c49c, L:/*************:11286 ! R:/*************:8091]) will closed
2025-07-24 18:30:28 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7ee13535, L:/*************:11269 ! R:/*************:8091]) will closed
2025-07-24 18:30:28 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5079c49c, L:/*************:11286 ! R:/*************:8091]) will closed
2025-07-24 18:30:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:30:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753353032195
timestamp=1753353032195
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 18:30:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:30:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 18:30:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5079c49c, L:/*************:11286 ! R:/*************:8091]
2025-07-24 18:30:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5079c49c, L:/*************:11286 ! R:/*************:8091]
2025-07-24 18:30:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 18:30:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x50a62eb5, L:null ! R:/*************:8091]) will closed
2025-07-24 18:30:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1f16a1ce, L:null ! R:/*************:8091]) will closed
2025-07-24 18:30:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:30:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753353042183
timestamp=1753353042183
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 18:30:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:30:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 18:30:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 18:30:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x80645754, L:null ! R:/*************:8091]) will closed
2025-07-24 18:30:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7b6e132, L:null ! R:/*************:8091]) will closed
2025-07-24 18:30:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:30:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753353052195
timestamp=1753353052195
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 18:30:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:30:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 18:30:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 18:30:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2706bd09, L:null ! R:/*************:8091]) will closed
2025-07-24 18:30:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8c9b0023, L:null ! R:/*************:8091]) will closed
2025-07-24 18:31:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:31:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753353062183
timestamp=1753353062183
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 18:31:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:31:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 18:31:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 18:31:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x32e51291, L:null ! R:/*************:8091]) will closed
2025-07-24 18:31:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6d3a2519, L:null ! R:/*************:8091]) will closed
2025-07-24 18:31:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:31:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753353072183
timestamp=1753353072183
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 18:31:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:31:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 18:31:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 18:31:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xca91de54, L:null ! R:/*************:8091]) will closed
2025-07-24 18:31:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x307041a9, L:null ! R:/*************:8091]) will closed
2025-07-24 18:31:22 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:31:22 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753353082182
timestamp=1753353082182
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 18:31:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:31:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 18:31:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 18:31:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfbc9cb80, L:null ! R:/*************:8091]) will closed
2025-07-24 18:31:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc6e82b4b, L:null ! R:/*************:8091]) will closed
2025-07-24 18:31:31 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-24 18:31:32 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-24 18:31:32 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-24 18:31:32 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-24 18:31:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:31:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,*************
timestamp=*************
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 18:31:32 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-24 18:31:32 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-24 18:31:32 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-24 18:31:32 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-24 18:31:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 18:31:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 18:31:32 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-24 18:31:32 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-24 18:31:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 18:31:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd034a9f0, L:null ! R:/*************:8091]) will closed
2025-07-24 18:31:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe88dd4fa, L:null ! R:/*************:8091]) will closed
2025-07-24 19:06:06 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-24 19:06:06 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 28532 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-24 19:06:06 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-24 19:06:06 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-24 19:06:06 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-24 19:06:06 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-24 19:06:12 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-24 19:06:12 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-24 19:06:12 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-24 19:06:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-24 19:06:12 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-24 19:06:12 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-24 19:06:13 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 19:06:13 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753355173227
timestamp=1753355173227
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 19:06:13 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x1868dc41, L:/*************:5825 - R:/*************:8091]
2025-07-24 19:06:13 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 145 ms, version:1.7.1,role:TMROLE,channel:[id: 0x1868dc41, L:/*************:5825 - R:/*************:8091]
2025-07-24 19:06:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-24 19:06:13 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-24 19:06:13 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-24 19:06:13 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-24 19:06:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-24 19:06:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-24 19:06:14 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-24 19:06:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-24 19:06:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-24 19:06:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-24 19:06:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@28908c04
2025-07-24 19:06:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-24 19:06:16 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 19:06:16 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 19:06:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 19:06:16 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x9ff3f59c, L:/*************:5847 - R:/*************:8091]
2025-07-24 19:06:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 10 ms, version:1.7.1,role:RMROLE,channel:[id: 0x9ff3f59c, L:/*************:5847 - R:/*************:8091]
2025-07-24 19:06:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-24 19:06:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-24 19:06:18 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-24 19:06:19 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-24 19:06:19 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-24 19:06:20 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-24 19:06:20 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-24 19:06:22 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-24 19:06:22 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-24 19:06:22 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-24 19:06:22 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-24 19:06:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource *************:9204 register finished
2025-07-24 19:06:25 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 21.857 seconds (process running for 23.026)
2025-07-24 19:06:26 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-24 19:06:26 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-24 19:06:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-24 19:06:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-24 19:06:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-24 19:06:26 [RMI TCP Connection(2)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-24 20:35:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x1868dc41, L:/*************:5825 - R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x9ff3f59c, L:/*************:5847 - R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x9ff3f59c, L:/*************:5847 - R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x1868dc41, L:/*************:5825 - R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x1868dc41, L:/*************:5825 ! R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x9ff3f59c, L:/*************:5847 ! R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x9ff3f59c, L:/*************:5847 ! R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x1868dc41, L:/*************:5825 ! R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x1868dc41, L:/*************:5825 ! R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x9ff3f59c, L:/*************:5847 ! R:/*************:8091]
2025-07-24 20:35:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9ff3f59c, L:/*************:5847 ! R:/*************:8091]) will closed
2025-07-24 20:35:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1868dc41, L:/*************:5825 ! R:/*************:8091]) will closed
2025-07-24 20:35:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9ff3f59c, L:/*************:5847 ! R:/*************:8091]) will closed
2025-07-24 20:35:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1868dc41, L:/*************:5825 ! R:/*************:8091]) will closed
2025-07-24 20:35:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:35:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753360542997
timestamp=1753360542997
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 20:35:43 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:35:43 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 20:35:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x9ff3f59c, L:/*************:5847 ! R:/*************:8091]
2025-07-24 20:35:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x9ff3f59c, L:/*************:5847 ! R:/*************:8091]
2025-07-24 20:35:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 20:35:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x38316e29, L:null ! R:/*************:8091]) will closed
2025-07-24 20:35:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb75fded0, L:null ! R:/*************:8091]) will closed
2025-07-24 20:35:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:35:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753360552985
timestamp=1753360552985
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 20:35:53 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:35:53 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 20:35:53 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 20:35:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1de8d905, L:null ! R:/*************:8091]) will closed
2025-07-24 20:35:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5ab034be, L:null ! R:/*************:8091]) will closed
2025-07-24 20:36:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:36:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753360562998
timestamp=1753360562998
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 20:36:03 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:36:03 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 20:36:03 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 20:36:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcb6d6347, L:null ! R:/*************:8091]) will closed
2025-07-24 20:36:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa90c3546, L:null ! R:/*************:8091]) will closed
2025-07-24 20:36:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:36:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753360572985
timestamp=1753360572985
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 20:36:13 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:36:13 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 20:36:13 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 20:36:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbf522ba8, L:null ! R:/*************:8091]) will closed
2025-07-24 20:36:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x797876ef, L:null ! R:/*************:8091]) will closed
2025-07-24 20:36:22 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:36:22 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753360582994
timestamp=1753360582994
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 20:36:23 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:36:23 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 20:36:23 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 20:36:25 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf2ddeeb2, L:null ! R:/*************:8091]) will closed
2025-07-24 20:36:25 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd3e345d5, L:null ! R:/*************:8091]) will closed
2025-07-24 20:36:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:36:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,*************,1753360592985
timestamp=1753360592985
authVersion=V4
vgroup=yumeng-resource-group
ip=*************
'} >
2025-07-24 20:36:33 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-24 20:36:33 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-24 20:36:33 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-24 20:36:35 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5848827d, L:null ! R:/*************:8091]) will closed
2025-07-24 20:36:35 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1c311273, L:null ! R:/*************:8091]) will closed
2025-07-24 20:36:38 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-24 20:36:38 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-24 20:36:38 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-24 20:36:39 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-24 20:36:39 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-24 20:36:39 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-24 20:36:39 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-24 20:36:39 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-24 20:36:39 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-24 20:36:39 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
