2025-07-22 08:34:47 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 08:34:47 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 15200 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 08:34:47 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-22 08:34:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-22 08:34:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 08:34:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 08:34:52 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 08:34:52 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 08:34:52 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 08:34:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 08:34:52 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 08:34:52 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 08:34:52 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 08:34:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1753144492990
timestamp=1753144492990
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-22 08:34:53 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x758e3186, L:/************:2193 - R:/************:8091]
2025-07-22 08:34:53 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 69 ms, version:1.7.1,role:TMROLE,channel:[id: 0x758e3186, L:/************:2193 - R:/************:8091]
2025-07-22 08:34:53 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-22 08:34:53 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 08:34:53 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 08:34:53 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 08:34:53 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-22 08:34:53 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 08:34:54 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 08:34:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 08:34:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 08:34:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 08:34:56 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-07-22 08:34:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 08:34:56 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 08:34:56 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-22 08:34:56 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-22 08:34:56 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x07a8000e, L:/************:2198 - R:/************:8091]
2025-07-22 08:34:56 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 14 ms, version:1.7.1,role:RMROLE,channel:[id: 0x07a8000e, L:/************:2198 - R:/************:8091]
2025-07-22 08:34:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 08:34:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 08:34:58 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 08:34:59 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 08:34:59 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 08:35:00 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 08:35:00 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 08:35:04 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 08:35:04 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 08:35:04 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 08:35:04 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 08:35:04 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource ************:9204 register finished
2025-07-22 08:35:11 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 26.273 seconds (process running for 27.408)
2025-07-22 08:35:11 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-22 08:35:12 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-22 08:35:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 08:35:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 08:35:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-22 08:35:12 [RMI TCP Connection(5)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 08:35:40 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='******************************************', saveDays=7, branchType=AT}
2025-07-22 08:38:00 [NettyServerWorker-6-3] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2586 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is established., dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:00 [NettyServerWorker-6-4] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2587 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is established., dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos-grpc-client-executor-localhost-47] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Receive server push request, request = NotifySubscriberRequest, requestId = 25
2025-07-22 08:38:02 [nacos-grpc-client-executor-localhost-47] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DUBBO_GROUP@@yumeng-system -> [{"instanceId":"************#20881#null#yumeng-system","ip":"************","port":20881,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DUBBO_GROUP@@yumeng-system","metadata":{"dubbo.endpoints":"[{\"port\":20881,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"100a2ea5f0576bf126adbefd4e91c5da","dubbo.metadata.storage-type":"remote","ipv6":"2408:8215:a11:ed30:603e:f8b2:cae1:741a","timestamp":"1753144679728"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-22 08:38:02 [nacos-grpc-client-executor-localhost-47] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DUBBO_GROUP@@yumeng-system -> [{"instanceId":"************#20881#null#yumeng-system","ip":"************","port":20881,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DUBBO_GROUP@@yumeng-system","metadata":{"dubbo.endpoints":"[{\"port\":20881,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"100a2ea5f0576bf126adbefd4e91c5da","dubbo.metadata.storage-type":"remote","ipv6":"2408:8215:a11:ed30:603e:f8b2:cae1:741a","timestamp":"1753144679728"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Received instance notification, serviceName: yumeng-system, instances: 1, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos-grpc-client-executor-localhost-47] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Ack server push request, request = NotifySubscriberRequest, requestId = 25
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] 1 unique working revisions: 100a2ea5f0576bf126adbefd4e91c5da , dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteUserService:null with urls 1, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [NettyClientWorker-7-2] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2598 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881 is established., dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.transport.AbstractClient -  [DUBBO] Successfully connect to server /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881 from NettyClient ************ using dubbo version 3.2.14, channel is NettyChannel [channel=[id: 0x1c520cf7, L:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2598 - R:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881]], dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.transport.AbstractClient -  [DUBBO] Start NettyClient /************ connect to the server /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-dev-localhost_8848] [subscribe] yumeng-system.MESHAPPRULE+dubbo+dev
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  c.a.n.client.config.impl.CacheData - [fixed-dev-localhost_8848] [add-listener] ok, tenant=dev, dataId=yumeng-system.MESHAPPRULE, group=dubbo, cnt=1
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-dev-localhost_8848] [subscribe] yumeng-system.tag-router+dubbo+dev
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  c.a.n.client.config.impl.CacheData - [fixed-dev-localhost_8848] [add-listener] ok, tenant=dev, dataId=yumeng-system.tag-router, group=dubbo, cnt=1
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-dev-localhost_8848] [subscribe] yumeng-system.condition-router+dubbo+dev
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  c.a.n.client.config.impl.CacheData - [fixed-dev-localhost_8848] [add-listener] ok, tenant=dev, dataId=yumeng-system.condition-router, group=dubbo, cnt=1
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-dev-localhost_8848] [subscribe] yumeng-system.script-router+dubbo+dev
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  c.a.n.client.config.impl.CacheData - [fixed-dev-localhost_8848] [add-listener] ok, tenant=dev, dataId=yumeng-system.script-router, group=dubbo, cnt=1
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteUserService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20881, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteLogService:null with urls 1, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteLogService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20881, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteDictService:null with urls 1, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteDictService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20881, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteDeptService:null with urls 1, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteDeptService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20881, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteDataScopeService:null with urls 1, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:02 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:03 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteDataScopeService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20881, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:03 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify service com.ym.system.api.RemoteClientService:null with urls 1, dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:03 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: localhost:8848, subscribed key: [yumeng-system])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.14, current host: ************
2025-07-22 08:38:03 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: com.ym.system.api.RemoteClientService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20881, dubbo version: 3.2.14, current host: ************
2025-07-22 09:07:42 [NettyServerWorker-6-4] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2587 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 09:08:15 [NettyServerWorker-6-5] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:4519 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is established., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [NettyServerWorker-6-5] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:4519 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 10:33:43 [NettyServerWorker-6-6] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:13598 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is established., dubbo version: 3.2.14, current host: ************
2025-07-22 10:33:46 [NettyServerWorker-6-6] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:13598 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 10:41:33 [NettyServerWorker-6-7] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:14109 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is established., dubbo version: 3.2.14, current host: ************
2025-07-22 10:41:38 [NettyServerWorker-6-7] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:14109 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Server healthy check fail, currentConnection = 1753144507545_127.0.0.1_2234
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Server healthy check fail, currentConnection = 1753144509277_127.0.0.1_2244
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x758e3186, L:/************:2193 - R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x07a8000e, L:/************:2198 - R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x07a8000e, L:/************:2198 - R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x758e3186, L:/************:2193 - R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x758e3186, L:/************:2193 ! R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x07a8000e, L:/************:2198 ! R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x758e3186, L:/************:2193 ! R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x07a8000e, L:/************:2198 ! R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x758e3186, L:/************:2193 ! R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x07a8000e, L:/************:2198 ! R:/************:8091]
2025-07-22 11:03:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x758e3186, L:/************:2193 ! R:/************:8091]) will closed
2025-07-22 11:03:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x07a8000e, L:/************:2198 ! R:/************:8091]) will closed
2025-07-22 11:03:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x07a8000e, L:/************:2198 ! R:/************:8091]) will closed
2025-07-22 11:03:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x758e3186, L:/************:2193 ! R:/************:8091]) will closed
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [NettyServerWorker-6-2] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2254 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:42 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:42 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Waiting for modules(Dubbo Application[1.1](yumeng-resource)) managed by Spring to be shutdown., dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:42 [SpringContextShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:42 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo wait for application(Dubbo Application[1.1](yumeng-resource)) managed by Spring to be shutdown failed, time usage: 18ms, dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:42 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](yumeng-resource), dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:42 [DubboClientHandler-thread-3] INFO  o.a.d.r.e.s.h.HeaderExchangeHandler -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x8a858e1d, L:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2252 - R:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880]], dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [Dubbo-framework-shared-scheduler-thread-2] INFO  o.a.d.r.c.AbstractServiceDiscovery -  [DUBBO] Metadata of instance changed, updating instance with revision 0., dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:43 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 11:03:43 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1753153423340
timestamp=1753153423340
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-22 11:03:43 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:43 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 11:03:43 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-22 11:03:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x07a8000e, L:/************:2198 ! R:/************:8091]
2025-07-22 11:03:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x07a8000e, L:/************:2198 ! R:/************:8091]
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:44 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:45 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:45 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:45 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa7a7d478, L:null ! R:/************:8091]) will closed
2025-07-22 11:03:45 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa1965ab5, L:null ! R:/************:8091]) will closed
2025-07-22 11:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:45 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:03:46 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:46 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [Dubbo-framework-shared-scheduler-thread-2] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-resource with instance: Instance{instanceId='null', ip='************', port=20880, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-resource', metadata={dubbo.endpoints=[{"port":20880,"protocol":"dubbo"}], dubbo.metadata.revision=b00c8c7356a944fcb5134cdfd75f8633, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753144509015}}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:03:49 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:49 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:49 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:49 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [SpringContextShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x8a858e1d, L:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2252 - R:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880], dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:51 [NettyClientWorker-7-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2252 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:51 [NettyServerWorker-6-1] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:2252 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:03:52 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:52 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:52 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:52 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 11:03:53 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1753153433332
timestamp=1753153433332
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 11:03:53 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-22 11:03:53 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-22 11:03:54 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:54 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:54 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:54 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:54 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:03:55 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf13241bc, L:null ! R:/************:8091]) will closed
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88d14d63, L:null ! R:/************:8091]) will closed
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:56 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:56 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:03:58 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:58 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:00 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:00 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:00 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:02 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:02 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 11:04:03 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1753153443332
timestamp=1753153443332
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 11:04:03 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-22 11:04:03 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-22 11:04:03 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:04:04 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:04 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x954d6ab0, L:null ! R:/************:8091]) will closed
2025-07-22 11:04:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x052d1aee, L:null ! R:/************:8091]) will closed
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:06 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:04:06 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:09 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:09 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:09 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:11 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:11 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:12 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 11:04:13 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,************,1753153453332
timestamp=1753153453332
authVersion=V4
vgroup=yumeng-resource-group
ip=************
'} >
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 11:04:13 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-22 11:04:13 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-22 11:04:14 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:14 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x45c5b792, L:null ! R:/************:8091]) will closed
2025-07-22 11:04:15 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:15 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x47d5c9c0, L:null ! R:/************:8091]) will closed
2025-07-22 11:04:16 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DUBBO_GROUP@@yumeng-resource
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9779703c-fb58-493c-99f2-b67ac48a3702] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [65a69992-bdce-427b-916f-046a0407239f_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [9dac795f-939d-49cc-9577-140d1935b099] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [41da61bf-adf8-4857-9e87-362f19e4bab3_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [ecba5d97-8f64-47bb-a147-1a8656036abc] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [e055bea4-6fcd-489f-96d4-e1785f95a00c_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:18 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:18 [com.alibaba.nacos.client.remote.worker.0] INFO  c.alibaba.nacos.common.remote.client - [271281ce-0c2f-4836-af88-ab01364dcfa8] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:09:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 11:09:58 [main] INFO  c.ym.resource.YmResourceApplication - Starting YmResourceApplication using Java 17.0.10 with PID 2788 (D:\project\yumeng\ship-Integrated-management-api\yumeng-modules\yumeng-resource\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 11:09:58 [main] INFO  c.ym.resource.YmResourceApplication - The following 1 profile is active: "dev"
2025-07-22 11:09:58 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-resource.yml, group=DEFAULT_GROUP] success
2025-07-22 11:09:58 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 11:09:58 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 11:10:04 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 11:10:04 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 11:10:04 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 11:10:04 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 11:10:04 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 11:10:04 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 11:10:04 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 11:10:04 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,**************,1753153804764
timestamp=1753153804764
authVersion=V4
vgroup=yumeng-resource-group
ip=**************
'} >
2025-07-22 11:10:04 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x574d7674, L:/**************:1638 - R:/**************:8091]
2025-07-22 11:10:04 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 61 ms, version:1.7.1,role:TMROLE,channel:[id: 0x574d7674, L:/**************:1638 - R:/**************:8091]
2025-07-22 11:10:04 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-22 11:10:04 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 11:10:04 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 11:10:04 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 11:10:04 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-resource] txServiceGroup[yumeng-resource-group]
2025-07-22 11:10:04 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 11:10:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 11:10:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 11:10:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 11:10:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 11:10:08 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5e1fc897
2025-07-22 11:10:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 11:10:08 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 11:10:08 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-22 11:10:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-22 11:10:08 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x3ccbc127, L:/**************:1661 - R:/**************:8091]
2025-07-22 11:10:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 20 ms, version:1.7.1,role:RMROLE,channel:[id: 0x3ccbc127, L:/**************:1661 - R:/**************:8091]
2025-07-22 11:10:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 11:10:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 11:10:09 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 11:10:10 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 11:10:10 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 11:10:11 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 11:10:11 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 11:10:13 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 11:10:13 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 11:10:13 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 11:10:13 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 11:10:13 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-resource **************:9204 register finished
2025-07-22 11:10:16 [main] INFO  c.ym.resource.YmResourceApplication - Started YmResourceApplication in 20.036 seconds (process running for 21.099)
2025-07-22 11:10:16 [main] INFO  c.y.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-22 11:10:16 [main] INFO  c.y.r.r.ResourceApplicationRunner - 初始化OSS配置成功
2025-07-22 11:10:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 11:10:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 11:10:16 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-resource.yml, group=DEFAULT_GROUP
2025-07-22 11:10:16 [RMI TCP Connection(1)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 11:11:55 [redisson-3-2] INFO  c.y.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录达世滨航运服务平台
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x3ccbc127, L:/**************:1661 - R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x574d7674, L:/**************:1638 - R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x3ccbc127, L:/**************:1661 - R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x574d7674, L:/**************:1638 - R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x574d7674, L:/**************:1638 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x3ccbc127, L:/**************:1661 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x574d7674, L:/**************:1638 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3ccbc127, L:/**************:1661 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3ccbc127, L:/**************:1661 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x574d7674, L:/**************:1638 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x574d7674, L:/**************:1638 ! R:/**************:8091]) will closed
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3ccbc127, L:/**************:1661 ! R:/**************:8091]) will closed
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3ccbc127, L:/**************:1661 ! R:/**************:8091]) will closed
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x574d7674, L:/**************:1638 ! R:/**************:8091]) will closed
2025-07-22 20:19:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 20:19:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='ak=null
digest=yumeng-resource-group,**************,1753186764539
timestamp=1753186764539
authVersion=V4
vgroup=yumeng-resource-group
ip=**************
'} >
2025-07-22 20:19:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 20:19:24 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :******************************************
2025-07-22 20:19:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3ccbc127, L:/**************:1661 ! R:/**************:8091]
2025-07-22 20:19:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3ccbc127, L:/**************:1661 ! R:/**************:8091]
2025-07-22 20:19:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='******************************************', version='1.7.1', applicationId='yumeng-resource', transactionServiceGroup='yumeng-resource-group', extraData='null'} >
2025-07-22 20:19:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6699547c, L:null ! R:/**************:8091]) will closed
