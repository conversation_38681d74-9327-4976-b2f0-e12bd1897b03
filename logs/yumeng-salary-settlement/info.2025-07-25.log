2025-07-25 08:34:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 08:34:05 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 27136 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 08:34:05 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-25 08:34:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:05 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 08:34:11 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 08:34:11 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 08:34:11 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 08:34:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 08:34:11 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 08:34:11 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 08:34:11 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 08:34:11 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753403651804
timestamp=1753403651804
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 08:34:11 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x27eca32e, L:/*************:2067 - R:/*************:8091]
2025-07-25 08:34:11 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 55 ms, version:1.7.1,role:TMROLE,channel:[id: 0x27eca32e, L:/*************:2067 - R:/*************:8091]
2025-07-25 08:34:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-25 08:34:11 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 08:34:11 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 08:34:12 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 08:34:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-25 08:34:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 08:34:13 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 08:34:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 08:34:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 08:34:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 08:34:14 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6465f4a
2025-07-25 08:34:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 08:34:15 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 08:34:15 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 08:34:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 08:34:15 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xcc0a3741, L:/*************:2101 - R:/*************:8091]
2025-07-25 08:34:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0xcc0a3741, L:/*************:2101 - R:/*************:8091]
2025-07-25 08:34:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 08:34:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 08:34:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 08:34:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 08:34:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 08:34:17 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 08:34:18 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 08:34:18 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 08:34:18 [redisson-netty-2-7] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 08:34:19 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 08:34:20 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 08:34:20 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 08:34:20 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 08:34:21 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 08:34:21 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-25 08:34:24 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 22.75 seconds (process running for 23.882)
2025-07-25 08:34:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 08:34:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 08:34:25 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-25 08:34:27 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 08:34:27 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 08:34:27 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 08:34:27 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 08:34:27 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-25 08:34:27 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-25 08:34:27 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-25 08:34:27 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-25 08:34:27 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-25 08:34:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x27eca32e, L:/*************:2067 ! R:/*************:8091]
2025-07-25 08:34:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x27eca32e, L:/*************:2067 ! R:/*************:8091]
2025-07-25 08:34:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x27eca32e, L:/*************:2067 ! R:/*************:8091]
2025-07-25 08:34:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x27eca32e, L:/*************:2067 ! R:/*************:8091]
2025-07-25 08:34:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x27eca32e, L:/*************:2067 ! R:/*************:8091]) will closed
2025-07-25 08:34:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x27eca32e, L:/*************:2067 ! R:/*************:8091]) will closed
2025-07-25 08:34:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xcc0a3741, L:/*************:2101 ! R:/*************:8091]
2025-07-25 08:34:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xcc0a3741, L:/*************:2101 ! R:/*************:8091]
2025-07-25 08:34:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xcc0a3741, L:/*************:2101 ! R:/*************:8091]
2025-07-25 08:34:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xcc0a3741, L:/*************:2101 ! R:/*************:8091]
2025-07-25 08:34:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcc0a3741, L:/*************:2101 ! R:/*************:8091]) will closed
2025-07-25 08:34:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcc0a3741, L:/*************:2101 ! R:/*************:8091]) will closed
2025-07-25 20:23:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-25 20:23:03 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 39636 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-25 20:23:03 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-25 20:23:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-25 20:23:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-25 20:23:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-25 20:23:07 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-25 20:23:07 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-25 20:23:07 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-25 20:23:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-25 20:23:07 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 20:23:07 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-25 20:23:08 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:23:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753446188167
timestamp=1753446188167
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:23:08 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x94cdeebf, L:/*************:4819 - R:/*************:8091]
2025-07-25 20:23:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 65 ms, version:1.7.1,role:TMROLE,channel:[id: 0x94cdeebf, L:/*************:4819 - R:/*************:8091]
2025-07-25 20:23:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-25 20:23:08 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-25 20:23:08 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-25 20:23:08 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-25 20:23:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-25 20:23:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-25 20:23:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-25 20:23:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-25 20:23:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-25 20:23:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-25 20:23:11 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-07-25 20:23:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-25 20:23:11 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:23:11 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:23:11 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:23:11 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x59a5bc2b, L:/*************:4830 - R:/*************:8091]
2025-07-25 20:23:11 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x59a5bc2b, L:/*************:4830 - R:/*************:8091]
2025-07-25 20:23:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-25 20:23:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-25 20:23:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 20:23:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 20:23:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-25 20:23:13 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-25 20:23:14 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-25 20:23:14 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-25 20:23:14 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-25 20:23:14 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-25 20:23:16 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-25 20:23:16 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-25 20:23:16 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-25 20:23:16 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-25 20:23:16 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-25 20:23:20 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 19.909 seconds (process running for 21.558)
2025-07-25 20:23:20 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-25 20:23:20 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-25 20:23:20 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-25 20:23:20 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[70ms]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-25 20:32:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x94cdeebf, L:/*************:4819 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x59a5bc2b, L:/*************:4830 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x59a5bc2b, L:/*************:4830 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x94cdeebf, L:/*************:4819 - R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x59a5bc2b, L:/*************:4830 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x94cdeebf, L:/*************:4819 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x94cdeebf, L:/*************:4819 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x59a5bc2b, L:/*************:4830 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x59a5bc2b, L:/*************:4830 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x94cdeebf, L:/*************:4819 ! R:/*************:8091]
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x94cdeebf, L:/*************:4819 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x59a5bc2b, L:/*************:4830 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x59a5bc2b, L:/*************:4830 ! R:/*************:8091]) will closed
2025-07-25 20:41:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x94cdeebf, L:/*************:4819 ! R:/*************:8091]) will closed
2025-07-25 20:41:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447307955
timestamp=1753447307955
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:41:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:41:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x59a5bc2b, L:/*************:4830 ! R:/*************:8091]
2025-07-25 20:41:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x59a5bc2b, L:/*************:4830 ! R:/*************:8091]
2025-07-25 20:41:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:41:49 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x36c38fbd, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9eca751f, L:null ! R:/*************:8091]) will closed
2025-07-25 20:41:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447317955
timestamp=1753447317955
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:41:58 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:41:58 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:41:58 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:41:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9559e8ff, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:00 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3e1142e6, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:07 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:07 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447327957
timestamp=1753447327957
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:42:08 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:08 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:42:08 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:42:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe09b47a5, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2d562be9, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447337955
timestamp=1753447337955
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:42:18 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:18 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:42:18 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:42:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa5cd7126, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd91db74d, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447347955
timestamp=1753447347955
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:42:28 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:28 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:42:28 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:42:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3f8dd1f7, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7acee09d, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447357963
timestamp=1753447357963
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:42:38 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:38 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:42:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:42:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x31f0e485, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5923e04d, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447367955
timestamp=1753447367955
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:42:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:42:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:42:49 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x25b44eb5, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa3495bea, L:null ! R:/*************:8091]) will closed
2025-07-25 20:42:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447377963
timestamp=1753447377963
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:42:58 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:42:58 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:42:58 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:42:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x79e516e2, L:null ! R:/*************:8091]) will closed
2025-07-25 20:43:00 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xed5069c9, L:null ! R:/*************:8091]) will closed
2025-07-25 20:43:07 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:43:07 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447387955
timestamp=1753447387955
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:43:08 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:43:08 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:43:08 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:43:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe3cbb15f, L:null ! R:/*************:8091]) will closed
2025-07-25 20:43:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x091d98d6, L:null ! R:/*************:8091]) will closed
2025-07-25 20:43:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:43:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753447397955
timestamp=1753447397955
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-25 20:43:18 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-25 20:43:18 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-25 20:43:18 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-25 20:43:19 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-25 20:43:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe688dcb6, L:null ! R:/*************:8091]) will closed
2025-07-25 20:43:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x41a6ab11, L:null ! R:/*************:8091]) will closed
