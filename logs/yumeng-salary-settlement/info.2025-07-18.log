2025-07-18 10:07:10 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 10:07:10 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 21668 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 10:07:10 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 10:07:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 10:07:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 10:07:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 10:07:16 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 10:07:16 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 10:07:16 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 10:07:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 10:07:17 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:07:17 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 10:07:17 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:07:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752804437633
timestamp=1752804437633
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 10:07:18 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd6b6ddd8, L:/*************:11489 - R:/*************:8091]
2025-07-18 10:07:18 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 105 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd6b6ddd8, L:/*************:11489 - R:/*************:8091]
2025-07-18 10:07:18 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 10:07:18 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 10:07:18 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 10:07:18 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:07:18 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 10:07:18 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 10:07:19 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 10:07:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 10:07:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 10:07:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 10:10:11 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 10:10:11 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 27932 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 10:10:11 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 10:10:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 10:10:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 10:10:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 10:10:16 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 10:10:16 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 10:10:16 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 10:10:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 10:10:16 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:10:16 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 10:10:16 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:10:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752804617039
timestamp=1752804617039
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 10:10:17 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xc8fa7331, L:/*************:13401 - R:/*************:8091]
2025-07-18 10:10:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 54 ms, version:1.7.1,role:TMROLE,channel:[id: 0xc8fa7331, L:/*************:13401 - R:/*************:8091]
2025-07-18 10:10:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 10:10:17 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 10:10:17 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 10:10:17 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:10:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 10:10:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 10:10:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 10:10:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 10:10:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 10:10:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 10:10:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xc8fa7331, L:/*************:13401 ! R:/*************:8091]
2025-07-18 10:10:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xc8fa7331, L:/*************:13401 ! R:/*************:8091]
2025-07-18 10:10:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc8fa7331, L:/*************:13401 ! R:/*************:8091]
2025-07-18 10:10:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc8fa7331, L:/*************:13401 ! R:/*************:8091]
2025-07-18 10:10:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc8fa7331, L:/*************:13401 ! R:/*************:8091]) will closed
2025-07-18 10:10:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc8fa7331, L:/*************:13401 ! R:/*************:8091]) will closed
2025-07-18 10:11:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 10:11:39 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 33104 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 10:11:39 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 10:11:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 10:11:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 10:11:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 10:11:43 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 10:11:43 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 10:11:43 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 10:11:44 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 10:11:44 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:11:44 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 10:11:44 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:11:44 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752804704478
timestamp=1752804704478
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 10:11:44 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xdd253318, L:/*************:13919 - R:/*************:8091]
2025-07-18 10:11:44 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 115 ms, version:1.7.1,role:TMROLE,channel:[id: 0xdd253318, L:/*************:13919 - R:/*************:8091]
2025-07-18 10:11:44 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 10:11:44 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 10:11:44 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 10:11:44 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 10:11:44 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 10:11:44 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 10:11:46 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 10:11:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 10:11:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 10:11:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 10:11:48 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5cb7eb6c
2025-07-18 10:11:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 10:11:48 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 10:11:48 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 10:11:48 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 10:11:48 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x85c94938, L:/*************:13952 - R:/*************:8091]
2025-07-18 10:11:48 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x85c94938, L:/*************:13952 - R:/*************:8091]
2025-07-18 10:11:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 10:11:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 10:11:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 10:11:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 10:11:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 10:11:50 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 10:11:51 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 10:11:51 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 10:11:51 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 10:11:52 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 10:11:54 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 10:11:54 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 10:11:54 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 10:11:54 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 10:11:54 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-18 10:11:58 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 21.506 seconds (process running for 22.79)
2025-07-18 10:11:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 10:11:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 10:11:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-18 10:11:58 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[87ms]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[58ms]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:26:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:26:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[84ms]
2025-07-18 10:26:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:26:44 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:26:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[81ms]
2025-07-18 10:26:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-18 10:26:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:26:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[44ms]
2025-07-18 10:26:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[130ms]
2025-07-18 10:26:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[26ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[86ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:26:44 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[20ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:26:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:26:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 10:26:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-18 10:26:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:26:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:26:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:26:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[128ms]
2025-07-18 10:26:47 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-18 10:27:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[26ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[57ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[6ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:27:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:27:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-18 10:27:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-18 10:27:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:27:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:27:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[137ms]
2025-07-18 10:27:07 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-18 10:27:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:27:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[44ms]
2025-07-18 10:27:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-18 10:27:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:27:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:27:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:27:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[130ms]
2025-07-18 10:27:23 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-18 10:28:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:28:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-18 10:28:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[91ms]
2025-07-18 10:28:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-18 10:28:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:28:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-18 10:31:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[56ms]
2025-07-18 10:31:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-18 10:31:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-18 10:31:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:31:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[54ms]
2025-07-18 10:31:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-18 10:31:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:31:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-18 10:31:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:31:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-18 10:31:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[84ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[104ms]
2025-07-18 10:31:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-18 10:31:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:31:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:31:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[85ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[25ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:31:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-18 10:31:52 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[26ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-18 10:31:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-18 10:31:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-18 10:31:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-18 10:31:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-18 10:31:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-18 10:31:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-18 10:31:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[132ms]
2025-07-18 10:31:55 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-18 10:42:26 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x85c94938, L:/*************:13952 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x85c94938, L:/*************:13952 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x85c94938, L:/*************:13952 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x85c94938, L:/*************:13952 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x85c94938, L:/*************:13952 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x85c94938, L:/*************:13952 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x85c94938, L:/*************:13952 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xdd253318, L:/*************:13919 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xdd253318, L:/*************:13919 - R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xdd253318, L:/*************:13919 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xdd253318, L:/*************:13919 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xdd253318, L:/*************:13919 ! R:/*************:8091]
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdd253318, L:/*************:13919 ! R:/*************:8091]) will closed
2025-07-18 11:13:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdd253318, L:/*************:13919 ! R:/*************:8091]) will closed
2025-07-18 11:13:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:13:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752808414362
timestamp=1752808414362
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 11:13:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:13:34 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 11:13:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x85c94938, L:/*************:13952 ! R:/*************:8091]
2025-07-18 11:13:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x85c94938, L:/*************:13952 ! R:/*************:8091]
2025-07-18 11:13:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 11:13:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x0484b59a, L:/**********:8734 ! R:/*************:8091]
2025-07-18 11:13:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xcf664128, L:/**********:8732 ! R:/*************:8091]
2025-07-18 11:14:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xcf664128, L:/**********:8732 ! R:/*************:8091]
2025-07-18 11:14:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xcf664128, L:/**********:8732 ! R:/*************:8091]
2025-07-18 11:14:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcf664128, L:/**********:8732 ! R:/*************:8091]) will closed
2025-07-18 11:14:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcf664128, L:/**********:8732 ! R:/*************:8091]) will closed
2025-07-18 11:14:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:14:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752808444369
timestamp=1752808444369
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 11:14:04 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x88d594af, L:/*************:7261 - R:/*************:8091]
2025-07-18 11:14:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:TMROLE,channel:[id: 0x88d594af, L:/*************:7261 - R:/*************:8091]
2025-07-18 11:14:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x0484b59a, L:/**********:8734 ! R:/*************:8091]
2025-07-18 11:14:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x0484b59a, L:/**********:8734 ! R:/*************:8091]
2025-07-18 11:14:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0484b59a, L:/**********:8734 ! R:/*************:8091]) will closed
2025-07-18 11:14:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0484b59a, L:/**********:8734 ! R:/*************:8091]) will closed
2025-07-18 11:14:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 11:14:04 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 11:14:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 11:14:04 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xc86e7b34, L:/*************:7263 - R:/*************:8091]
2025-07-18 11:14:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0xc86e7b34, L:/*************:7263 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x88d594af, L:/*************:7261 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xc86e7b34, L:/*************:7263 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x88d594af, L:/*************:7261 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xc86e7b34, L:/*************:7263 - R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x88d594af, L:/*************:7261 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x88d594af, L:/*************:7261 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x88d594af, L:/*************:7261 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88d594af, L:/*************:7261 ! R:/*************:8091]) will closed
2025-07-18 11:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88d594af, L:/*************:7261 ! R:/*************:8091]) will closed
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xc86e7b34, L:/*************:7263 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc86e7b34, L:/*************:7263 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc86e7b34, L:/*************:7263 ! R:/*************:8091]
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc86e7b34, L:/*************:7263 ! R:/*************:8091]) will closed
2025-07-18 11:14:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc86e7b34, L:/*************:7263 ! R:/*************:8091]) will closed
2025-07-18 14:21:08 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 14:21:09 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 26816 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 14:21:09 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 14:21:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 14:21:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 14:21:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 14:21:13 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 14:21:13 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 14:21:13 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 14:21:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 14:21:13 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:21:13 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 14:21:14 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 14:21:14 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752819674205
timestamp=1752819674205
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 14:21:14 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x81158d67, L:/*************:4806 - R:/*************:8091]
2025-07-18 14:21:14 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 130 ms, version:1.7.1,role:TMROLE,channel:[id: 0x81158d67, L:/*************:4806 - R:/*************:8091]
2025-07-18 14:21:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 14:21:14 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 14:21:14 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 14:21:14 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:21:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 14:21:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 14:21:15 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 14:21:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 14:21:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 14:21:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 14:21:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6ab80ea
2025-07-18 14:21:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 14:21:17 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 14:21:17 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 14:21:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 14:21:17 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x87a13533, L:/*************:4823 - R:/*************:8091]
2025-07-18 14:21:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x87a13533, L:/*************:4823 - R:/*************:8091]
2025-07-18 14:21:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 14:21:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 14:21:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 14:21:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 14:21:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 14:21:18 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 14:21:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 14:21:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x87a13533, L:/*************:4823 ! R:/*************:8091]
2025-07-18 14:21:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x87a13533, L:/*************:4823 ! R:/*************:8091]
2025-07-18 14:21:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x87a13533, L:/*************:4823 ! R:/*************:8091]
2025-07-18 14:21:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x87a13533, L:/*************:4823 ! R:/*************:8091]
2025-07-18 14:21:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x87a13533, L:/*************:4823 ! R:/*************:8091]) will closed
2025-07-18 14:21:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x87a13533, L:/*************:4823 ! R:/*************:8091]) will closed
2025-07-18 14:43:26 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 14:43:26 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 21812 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 14:43:26 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 14:43:27 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 14:43:27 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 14:43:27 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 14:43:31 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 14:43:31 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 14:43:31 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 14:43:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 14:43:31 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:43:31 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 14:43:31 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 14:43:31 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752821011938
timestamp=1752821011938
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 14:43:32 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x77b367e0, L:/*************:11446 - R:/*************:8091]
2025-07-18 14:43:32 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 98 ms, version:1.7.1,role:TMROLE,channel:[id: 0x77b367e0, L:/*************:11446 - R:/*************:8091]
2025-07-18 14:43:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 14:43:32 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 14:43:32 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 14:43:32 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 14:43:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 14:43:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 14:43:33 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 14:43:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 14:43:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 14:43:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 14:43:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@52ac4878
2025-07-18 14:43:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 14:43:35 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 14:43:35 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 14:43:35 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 14:43:35 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5c9d527a, L:/*************:11463 - R:/*************:8091]
2025-07-18 14:43:35 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x5c9d527a, L:/*************:11463 - R:/*************:8091]
2025-07-18 14:43:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 14:43:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 14:43:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 14:43:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 14:43:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 14:43:37 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 14:43:38 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 14:43:38 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 14:43:38 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 14:43:39 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 14:43:41 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 14:43:41 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 14:43:41 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 14:43:41 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 14:43:41 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-18 14:43:45 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 20.979 seconds (process running for 22.296)
2025-07-18 14:43:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 14:43:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 14:43:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-18 14:43:45 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 15:02:22 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 15:02:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x77b367e0, L:/*************:11446 ! R:/*************:8091]
2025-07-18 15:02:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x77b367e0, L:/*************:11446 ! R:/*************:8091]
2025-07-18 15:02:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x77b367e0, L:/*************:11446 ! R:/*************:8091]
2025-07-18 15:02:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x77b367e0, L:/*************:11446 ! R:/*************:8091]
2025-07-18 15:02:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x77b367e0, L:/*************:11446 ! R:/*************:8091]) will closed
2025-07-18 15:02:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x77b367e0, L:/*************:11446 ! R:/*************:8091]) will closed
2025-07-18 15:02:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5c9d527a, L:/*************:11463 ! R:/*************:8091]
2025-07-18 15:02:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5c9d527a, L:/*************:11463 ! R:/*************:8091]
2025-07-18 15:02:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5c9d527a, L:/*************:11463 ! R:/*************:8091]
2025-07-18 15:02:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5c9d527a, L:/*************:11463 ! R:/*************:8091]
2025-07-18 15:02:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5c9d527a, L:/*************:11463 ! R:/*************:8091]) will closed
2025-07-18 15:02:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5c9d527a, L:/*************:11463 ! R:/*************:8091]) will closed
2025-07-18 15:02:27 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 15:02:28 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 14448 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 15:02:28 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 15:02:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 15:02:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 15:02:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 15:02:33 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 15:02:34 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 15:02:34 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 15:02:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 15:02:34 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 15:02:34 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 15:02:34 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:02:34 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752822154525
timestamp=1752822154525
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:02:34 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x810b99cb, L:/*************:3885 - R:/*************:8091]
2025-07-18 15:02:34 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 112 ms, version:1.7.1,role:TMROLE,channel:[id: 0x810b99cb, L:/*************:3885 - R:/*************:8091]
2025-07-18 15:02:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 15:02:34 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 15:02:34 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 15:02:34 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 15:02:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 15:02:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 15:02:36 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 15:02:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 15:02:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 15:02:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 15:02:38 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@650f4d76
2025-07-18 15:02:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 15:02:38 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:02:38 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:02:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:02:38 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x21d60df9, L:/*************:3915 - R:/*************:8091]
2025-07-18 15:02:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x21d60df9, L:/*************:3915 - R:/*************:8091]
2025-07-18 15:02:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 15:02:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 15:02:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 15:02:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 15:02:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 15:02:41 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 15:02:42 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 15:02:42 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 15:02:42 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 15:02:42 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 15:02:45 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 15:02:45 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 15:02:45 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 15:02:45 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 15:02:45 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-18 15:02:49 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 23.899 seconds (process running for 25.158)
2025-07-18 15:02:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 15:02:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 15:02:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-18 15:02:49 [RMI TCP Connection(5)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x810b99cb, L:/*************:3885 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x21d60df9, L:/*************:3915 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x21d60df9, L:/*************:3915 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x810b99cb, L:/*************:3885 - R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x810b99cb, L:/*************:3885 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x810b99cb, L:/*************:3885 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x810b99cb, L:/*************:3885 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x810b99cb, L:/*************:3885 ! R:/*************:8091]) will closed
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x21d60df9, L:/*************:3915 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x810b99cb, L:/*************:3885 ! R:/*************:8091]) will closed
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x21d60df9, L:/*************:3915 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x21d60df9, L:/*************:3915 ! R:/*************:8091]
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x21d60df9, L:/*************:3915 ! R:/*************:8091]) will closed
2025-07-18 15:57:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x21d60df9, L:/*************:3915 ! R:/*************:8091]) will closed
2025-07-18 15:57:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825444286
timestamp=1752825444286
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:57:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:24 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:57:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x21d60df9, L:/*************:3915 ! R:/*************:8091]
2025-07-18 15:57:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x21d60df9, L:/*************:3915 ! R:/*************:8091]
2025-07-18 15:57:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:57:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcb966406, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9bbac089, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825454298
timestamp=1752825454298
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:57:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:34 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:57:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:57:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8a0ce4b0, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x633df645, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825464286
timestamp=1752825464286
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:57:44 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:44 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:57:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:57:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7b2cbb3c, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x99bd4105, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825474286
timestamp=1752825474286
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:57:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:57:54 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:57:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:57:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x762ac49e, L:null ! R:/*************:8091]) will closed
2025-07-18 15:57:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc36a13d5, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:04 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:04 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825484288
timestamp=1752825484288
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:58:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:04 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:58:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:58:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe5d551b0, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x40093633, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825494286
timestamp=1752825494286
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:58:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:14 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:58:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:58:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xed1e6cac, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x80f76075, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:24 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:24 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825504286
timestamp=1752825504286
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:58:24 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:24 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:58:24 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:58:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbd567706, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7bc84b10, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:34 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:34 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825514287
timestamp=1752825514287
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:58:34 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:34 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:58:34 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:58:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfcc2c607, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x22f3ac63, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825524286
timestamp=1752825524286
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:58:44 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:44 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:58:44 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:58:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbce42c91, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x402d34db, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:54 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:54 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825534286
timestamp=1752825534286
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 15:58:54 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 15:58:54 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 15:58:54 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 15:58:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7688483d, L:null ! R:/*************:8091]) will closed
2025-07-18 15:58:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0072dec0, L:null ! R:/*************:8091]) will closed
2025-07-18 15:59:02 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 15:59:02 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 15:59:02 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 15:59:02 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 15:59:03 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 15:59:03 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 15:59:03 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 15:59:03 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 15:59:03 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 15:59:03 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 15:59:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 15:59:53 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 31060 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 15:59:53 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 15:59:53 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 15:59:53 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 15:59:53 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 16:00:01 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 16:00:02 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 16:00:02 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 16:00:02 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 16:00:02 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 16:00:02 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 16:00:02 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 16:00:03 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752825603157
timestamp=1752825603157
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 16:00:03 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xb82a48c1, L:/*************:11414 - R:/*************:8091]
2025-07-18 16:00:03 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 146 ms, version:1.7.1,role:TMROLE,channel:[id: 0xb82a48c1, L:/*************:11414 - R:/*************:8091]
2025-07-18 16:00:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 16:00:03 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 16:00:03 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 16:00:03 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 16:00:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 16:00:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 16:00:05 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 16:00:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 16:00:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 16:00:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 16:00:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@427937c1
2025-07-18 16:00:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 16:00:07 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 16:00:07 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 16:00:07 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 16:00:07 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5b5296b6, L:/*************:11439 - R:/*************:8091]
2025-07-18 16:00:07 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x5b5296b6, L:/*************:11439 - R:/*************:8091]
2025-07-18 16:00:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 16:00:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 16:00:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 16:00:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 16:00:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 16:00:09 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 16:00:09 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 16:00:09 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 16:00:10 [redisson-netty-2-7] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 16:00:10 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 16:00:12 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 16:00:12 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 16:00:12 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 16:00:12 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 16:00:12 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-18 16:00:18 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 28.21 seconds (process running for 29.317)
2025-07-18 16:00:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 16:00:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 16:00:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-18 16:00:18 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 16:02:38 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*******************************************', saveDays=7, branchType=AT}
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 17:02:09 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 17:02:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xb82a48c1, L:/*************:11414 ! R:/*************:8091]
2025-07-18 17:02:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xb82a48c1, L:/*************:11414 ! R:/*************:8091]
2025-07-18 17:02:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb82a48c1, L:/*************:11414 ! R:/*************:8091]
2025-07-18 17:02:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb82a48c1, L:/*************:11414 ! R:/*************:8091]
2025-07-18 17:02:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb82a48c1, L:/*************:11414 ! R:/*************:8091]) will closed
2025-07-18 17:02:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb82a48c1, L:/*************:11414 ! R:/*************:8091]) will closed
2025-07-18 17:02:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5b5296b6, L:/*************:11439 ! R:/*************:8091]
2025-07-18 17:02:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5b5296b6, L:/*************:11439 ! R:/*************:8091]
2025-07-18 17:02:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5b5296b6, L:/*************:11439 ! R:/*************:8091]
2025-07-18 17:02:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5b5296b6, L:/*************:11439 ! R:/*************:8091]
2025-07-18 17:02:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5b5296b6, L:/*************:11439 ! R:/*************:8091]) will closed
2025-07-18 17:02:09 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5b5296b6, L:/*************:11439 ! R:/*************:8091]) will closed
2025-07-18 17:02:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 17:02:19 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 32584 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 17:02:19 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 17:02:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 17:02:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 17:02:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 17:02:24 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 17:02:24 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 17:02:24 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 17:02:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 17:02:24 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 17:02:24 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 17:02:24 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 17:02:24 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752829344803
timestamp=1752829344803
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 17:02:25 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4f48e207, L:/*************:3511 - R:/*************:8091]
2025-07-18 17:02:25 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 116 ms, version:1.7.1,role:TMROLE,channel:[id: 0x4f48e207, L:/*************:3511 - R:/*************:8091]
2025-07-18 17:02:25 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 17:02:25 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 17:02:25 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 17:02:25 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 17:02:25 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 17:02:25 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 17:02:26 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 17:02:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 17:02:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 17:02:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 17:02:28 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@72d9bd09
2025-07-18 17:02:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 17:02:28 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 17:02:28 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 17:02:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 17:02:28 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7cc71a34, L:/*************:3663 - R:/*************:8091]
2025-07-18 17:02:28 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x7cc71a34, L:/*************:3663 - R:/*************:8091]
2025-07-18 17:02:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 17:02:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 17:02:29 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 17:02:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 17:02:30 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 17:02:30 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 17:02:31 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 17:02:31 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 17:02:31 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 17:02:32 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 17:02:34 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 17:02:34 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 17:02:34 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 17:02:34 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 17:02:34 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-18 17:02:38 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 21.049 seconds (process running for 22.384)
2025-07-18 17:02:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 17:02:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 17:02:38 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-18 17:02:38 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x7cc71a34, L:/*************:3663 - R:/*************:8091] read idle.
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7cc71a34, L:/*************:3663 - R:/*************:8091]
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7cc71a34, L:/*************:3663 - R:/*************:8091]) will closed
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]) will closed
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]) will closed
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]) will closed
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]) will closed
2025-07-18 18:25:38 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7cc71a34, L:/*************:3663 ! R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x4f48e207, L:/*************:3511 - R:/*************:8091] read idle.
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4f48e207, L:/*************:3511 - R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4f48e207, L:/*************:3511 - R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]) will closed
2025-07-18 18:25:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4f48e207, L:/*************:3511 ! R:/*************:8091]) will closed
2025-07-18 18:25:41 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 18:25:41 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752834341495
timestamp=1752834341495
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 18:25:41 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x80471d65, L:/*************:2279 - R:/*************:8091]
2025-07-18 18:25:41 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 29 ms, version:1.7.1,role:TMROLE,channel:[id: 0x80471d65, L:/*************:2279 - R:/*************:8091]
2025-07-18 18:25:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 18:25:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 18:25:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 18:25:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xb385829e, L:/*************:7858 - R:/*************:8091]
2025-07-18 18:25:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xb385829e, L:/*************:7858 - R:/*************:8091]
2025-07-18 19:07:34 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xb385829e, L:/*************:7858 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x80471d65, L:/*************:2279 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x80471d65, L:/*************:2279 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xb385829e, L:/*************:7858 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb385829e, L:/*************:7858 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x80471d65, L:/*************:2279 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x80471d65, L:/*************:2279 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb385829e, L:/*************:7858 ! R:/*************:8091]
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb385829e, L:/*************:7858 ! R:/*************:8091]) will closed
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x80471d65, L:/*************:2279 ! R:/*************:8091]) will closed
2025-07-18 19:07:36 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb385829e, L:/*************:7858 ! R:/*************:8091]) will closed
2025-07-18 19:07:36 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x80471d65, L:/*************:2279 ! R:/*************:8091]) will closed
2025-07-18 19:07:41 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:41 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752836861465
timestamp=1752836861465
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:07:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:07:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:07:43 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4e19bb89, L:null ! R:/*************:8091]) will closed
2025-07-18 19:07:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6b9cc710, L:null ! R:/*************:8091]) will closed
2025-07-18 19:07:51 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:51 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752836871464
timestamp=1752836871464
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:07:51 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x9b23bfc7, L:/*************:2719 - R:/*************:8091]
2025-07-18 19:07:51 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x9b23bfc7, L:/*************:2719 - R:/*************:8091]
2025-07-18 19:07:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:07:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:07:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:07:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x95d94d0e, L:/*************:2720 - R:/*************:8091]
2025-07-18 19:07:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x95d94d0e, L:/*************:2720 - R:/*************:8091]
2025-07-18 19:10:45 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*******************************************', saveDays=7, branchType=AT}
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x95d94d0e, L:/*************:2720 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x9b23bfc7, L:/*************:2719 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x9b23bfc7, L:/*************:2719 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x95d94d0e, L:/*************:2720 - R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x9b23bfc7, L:/*************:2719 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x95d94d0e, L:/*************:2720 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x9b23bfc7, L:/*************:2719 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x95d94d0e, L:/*************:2720 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x9b23bfc7, L:/*************:2719 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x95d94d0e, L:/*************:2720 ! R:/*************:8091]
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x95d94d0e, L:/*************:2720 ! R:/*************:8091]) will closed
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9b23bfc7, L:/*************:2719 ! R:/*************:8091]) will closed
2025-07-18 19:49:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9b23bfc7, L:/*************:2719 ! R:/*************:8091]) will closed
2025-07-18 19:49:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x95d94d0e, L:/*************:2720 ! R:/*************:8091]) will closed
2025-07-18 19:49:21 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:21 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839361264
timestamp=1752839361264
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:49:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:49:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x95d94d0e, L:/*************:2720 ! R:/*************:8091]
2025-07-18 19:49:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x95d94d0e, L:/*************:2720 ! R:/*************:8091]
2025-07-18 19:49:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:49:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd3ab5b27, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe503eb1, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:31 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:31 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839371257
timestamp=1752839371257
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:49:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:49:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:49:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd34313c5, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4946175d, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:41 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:41 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839381257
timestamp=1752839381257
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:49:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:49:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:49:43 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc3593648, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:43 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4b687d3f, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:51 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:51 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839391269
timestamp=1752839391269
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:49:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:49:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:49:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:49:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdce8aa07, L:null ! R:/*************:8091]) will closed
2025-07-18 19:49:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc648bfec, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:01 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:01 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839401260
timestamp=1752839401260
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:50:01 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:01 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:50:01 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:50:03 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x25db1551, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:03 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x43a31deb, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:11 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:11 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839411265
timestamp=1752839411265
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:50:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:50:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:50:13 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd61baa74, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9cdbce97, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:21 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:21 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839421255
timestamp=1752839421255
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:50:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:50:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:50:23 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb4e8f292, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:23 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd7c124dc, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:31 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:31 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839431253
timestamp=1752839431253
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:50:31 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:31 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:50:31 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:50:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x69854b58, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf6775857, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:41 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:41 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839441253
timestamp=1752839441253
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:50:41 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:41 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:50:41 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:50:43 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x225c71bb, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:43 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc5ac52c0, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:51 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:51 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839451253
timestamp=1752839451253
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:50:51 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:50:51 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:50:51 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 19:50:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0ac7ba02, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 19:50:53 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x89daab70, L:null ! R:/*************:8091]) will closed
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 19:50:53 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 19:57:27 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 19:57:27 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 34148 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 19:57:27 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 19:57:27 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 19:57:27 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 19:57:27 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 19:57:32 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 19:57:32 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 19:57:32 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 19:57:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 19:57:32 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 19:57:32 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 19:57:32 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:57:32 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752839852667
timestamp=1752839852667
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 19:57:32 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5ce19045, L:/*************:11718 - R:/*************:8091]
2025-07-18 19:57:32 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 75 ms, version:1.7.1,role:TMROLE,channel:[id: 0x5ce19045, L:/*************:11718 - R:/*************:8091]
2025-07-18 19:57:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 19:57:32 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 19:57:32 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 19:57:32 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 19:57:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 19:57:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 19:57:34 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 19:57:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 19:57:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 19:57:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 19:57:36 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@681f5a1e
2025-07-18 19:57:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 19:57:36 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 19:57:36 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 19:57:36 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 19:57:36 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x96529bb7, L:/*************:11743 - R:/*************:8091]
2025-07-18 19:57:36 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x96529bb7, L:/*************:11743 - R:/*************:8091]
2025-07-18 19:57:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 19:57:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 19:57:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 19:57:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 19:57:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 19:57:38 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 19:57:39 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 19:57:39 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 19:57:39 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 19:57:40 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 19:57:42 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 19:57:42 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 19:57:42 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 19:57:42 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 19:57:42 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-18 19:57:46 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 21.754 seconds (process running for 23.08)
2025-07-18 19:57:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 19:57:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 19:57:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-18 19:57:46 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 20:00:27 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*******************************************', saveDays=7, branchType=AT}
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x96529bb7, L:/*************:11743 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5ce19045, L:/*************:11718 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5ce19045, L:/*************:11718 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x96529bb7, L:/*************:11743 - R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5ce19045, L:/*************:11718 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x96529bb7, L:/*************:11743 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5ce19045, L:/*************:11718 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x96529bb7, L:/*************:11743 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x96529bb7, L:/*************:11743 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5ce19045, L:/*************:11718 ! R:/*************:8091]
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5ce19045, L:/*************:11718 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x96529bb7, L:/*************:11743 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5ce19045, L:/*************:11718 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x96529bb7, L:/*************:11743 ! R:/*************:8091]) will closed
2025-07-18 20:16:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841002410
timestamp=1752841002410
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:16:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:16:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x96529bb7, L:/*************:11743 ! R:/*************:8091]
2025-07-18 20:16:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x96529bb7, L:/*************:11743 ! R:/*************:8091]
2025-07-18 20:16:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:16:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe153eadd, L:null ! R:/*************:8091]) will closed
2025-07-18 20:16:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf0a6e4e4, L:null ! R:/*************:8091]) will closed
2025-07-18 20:16:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841012409
timestamp=1752841012409
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:16:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:16:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:16:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:16:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x591e25f1, L:null ! R:/*************:8091]) will closed
2025-07-18 20:16:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdbe9a0d8, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841022409
timestamp=1752841022409
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:17:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:17:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:17:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8511d9d2, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd652944a, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841032410
timestamp=1752841032410
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:17:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:17:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:17:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x74fe8ae9, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4dfcb5ac, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:22 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:22 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841042409
timestamp=1752841042409
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:17:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:17:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:17:24 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe92c8b87, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:24 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd6a6b7ab, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:32 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:32 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841052408
timestamp=1752841052408
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:17:32 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:32 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:17:32 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:17:34 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0daf879a, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:34 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd02e8b4b, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:42 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:42 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841062408
timestamp=1752841062408
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:17:42 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:42 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:17:42 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:17:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeb99e367, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x27060798, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:52 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:52 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841072407
timestamp=1752841072407
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:17:52 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:17:52 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:17:52 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:17:54 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe11cd336, L:null ! R:/*************:8091]) will closed
2025-07-18 20:17:54 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x086d022b, L:null ! R:/*************:8091]) will closed
2025-07-18 20:18:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:18:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841082410
timestamp=1752841082410
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:18:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:18:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:18:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:18:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5c59f661, L:null ! R:/*************:8091]) will closed
2025-07-18 20:18:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcdb7fe8e, L:null ! R:/*************:8091]) will closed
2025-07-18 20:18:12 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:18:12 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841092407
timestamp=1752841092407
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:18:12 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:18:12 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:18:12 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:18:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x74969ff8, L:null ! R:/*************:8091]) will closed
2025-07-18 20:18:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe146215f, L:null ! R:/*************:8091]) will closed
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 20:18:22 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:18:22 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,*************
timestamp=*************
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 20:18:22 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:18:22 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:18:22 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 20:18:22 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-18 20:18:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4f05ef03, L:null ! R:/*************:8091]) will closed
2025-07-18 20:18:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x897a0696, L:null ! R:/*************:8091]) will closed
2025-07-18 20:28:11 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 20:28:11 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 36916 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-18 20:28:11 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-18 20:28:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-18 20:28:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-18 20:28:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-18 20:28:14 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-18 20:28:14 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-18 20:28:14 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-18 20:28:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-18 20:28:15 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 20:28:15 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-18 20:28:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 20:28:15 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-18 20:28:15 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-18 20:28:15 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-18 20:28:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-18 20:28:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-18 20:28:16 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-18 20:28:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-18 20:28:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-18 20:28:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-18 20:28:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5cbdc23b
2025-07-18 20:28:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-18 20:28:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-18 20:28:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-18 20:28:18 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 20:28:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 20:28:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-18 20:28:19 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-18 20:28:20 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-18 20:28:20 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-18 20:28:21 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-18 20:28:21 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-18 20:28:23 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-18 20:28:23 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-18 20:28:23 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-18 20:28:23 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-18 20:28:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-18 20:28:26 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 17.987 seconds (process running for 19.128)
2025-07-18 20:28:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-18 20:28:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-18 20:28:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-18 20:28:27 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-18 20:32:25 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:32:25 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752841945066
timestamp=1752841945066
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:32:25 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x13d718e7, L:/*************:4095 - R:/*************:8091]
2025-07-18 20:32:25 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 69 ms, version:1.7.1,role:TMROLE,channel:[id: 0x13d718e7, L:/*************:4095 - R:/*************:8091]
2025-07-18 20:32:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:32:25 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:32:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:32:25 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2d332ac9, L:/*************:4096 - R:/*************:8091]
2025-07-18 20:32:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x2d332ac9, L:/*************:4096 - R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2d332ac9, L:/*************:4096 - R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x13d718e7, L:/*************:4095 - R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x2d332ac9, L:/*************:4096 - R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x13d718e7, L:/*************:4095 - R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2d332ac9, L:/*************:4096 ! R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x13d718e7, L:/*************:4095 ! R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2d332ac9, L:/*************:4096 ! R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x13d718e7, L:/*************:4095 ! R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2d332ac9, L:/*************:4096 ! R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x13d718e7, L:/*************:4095 ! R:/*************:8091]
2025-07-18 20:33:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2d332ac9, L:/*************:4096 ! R:/*************:8091]) will closed
2025-07-18 20:33:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x13d718e7, L:/*************:4095 ! R:/*************:8091]) will closed
2025-07-18 20:33:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2d332ac9, L:/*************:4096 ! R:/*************:8091]) will closed
2025-07-18 20:33:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x13d718e7, L:/*************:4095 ! R:/*************:8091]) will closed
2025-07-18 20:33:55 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:33:55 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752842035026
timestamp=1752842035026
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:33:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:33:55 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:33:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2d332ac9, L:/*************:4096 ! R:/*************:8091]
2025-07-18 20:33:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2d332ac9, L:/*************:4096 ! R:/*************:8091]
2025-07-18 20:33:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:33:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbbff910d, L:null ! R:/*************:8091]) will closed
2025-07-18 20:33:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9c21ec70, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:05 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:05 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752842045024
timestamp=1752842045024
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:34:05 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:05 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:34:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:34:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0d62d279, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf9c77f30, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:15 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:15 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752842055024
timestamp=1752842055024
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:34:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:15 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:34:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:34:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5a335d7d, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc16fe9de, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:25 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:25 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752842065023
timestamp=1752842065023
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:34:25 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:25 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:34:25 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:34:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3db2c024, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6b97d4e5, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:35 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:35 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752842075025
timestamp=1752842075025
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:34:35 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:35 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:34:35 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:34:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0e4d5a98, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x42b62858, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:45 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:45 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752842085024
timestamp=1752842085024
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:34:45 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:45 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:34:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:34:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xec85e5a5, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0ca2c222, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:55 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:55 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752842095023
timestamp=1752842095023
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:34:55 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:34:55 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:34:55 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:34:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x802dca15, L:null ! R:/*************:8091]) will closed
2025-07-18 20:34:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1e7432e7, L:null ! R:/*************:8091]) will closed
2025-07-18 20:35:05 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:35:05 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752842105036
timestamp=1752842105036
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:35:05 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:35:05 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:35:05 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:35:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xac93999a, L:null ! R:/*************:8091]) will closed
2025-07-18 20:35:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd9f8d9e7, L:null ! R:/*************:8091]) will closed
2025-07-18 20:35:15 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:35:15 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752842115022
timestamp=1752842115022
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-18 20:35:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-18 20:35:15 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-18 20:35:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-18 20:35:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x078cd5c1, L:null ! R:/*************:8091]) will closed
2025-07-18 20:35:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x65fbbd1e, L:null ! R:/*************:8091]) will closed
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-18 20:35:17 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
