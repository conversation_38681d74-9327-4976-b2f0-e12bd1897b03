2025-07-22 08:35:05 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 08:35:06 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 30160 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 08:35:06 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 08:35:06 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 08:35:06 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 08:35:06 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 08:35:19 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 08:35:19 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 08:35:19 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 08:35:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 08:35:19 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 08:35:20 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 08:35:20 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 08:35:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1753144520335
timestamp=1753144520335
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'} >
2025-07-22 08:35:20 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xefde870a, L:/************:2268 - R:/************:8091]
2025-07-22 08:35:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 92 ms, version:1.7.1,role:TMROLE,channel:[id: 0xefde870a, L:/************:2268 - R:/************:8091]
2025-07-22 08:35:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 08:35:20 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 08:35:20 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 08:35:20 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 08:35:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 08:35:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 08:37:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 08:37:46 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 20120 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 08:37:46 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 08:37:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 08:37:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 08:37:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 08:37:50 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 08:37:50 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 08:37:50 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 08:37:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 08:37:50 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 08:37:50 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 08:37:50 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 08:37:50 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1753144670916
timestamp=1753144670916
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'} >
2025-07-22 08:37:51 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa1cd83e9, L:/************:2519 - R:/************:8091]
2025-07-22 08:37:51 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 54 ms, version:1.7.1,role:TMROLE,channel:[id: 0xa1cd83e9, L:/************:2519 - R:/************:8091]
2025-07-22 08:37:51 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 08:37:51 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 08:37:51 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 08:37:51 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 08:37:51 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 08:37:51 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 08:37:51 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 08:37:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 08:37:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 08:37:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 08:37:53 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2c174215
2025-07-22 08:37:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 08:37:53 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 08:37:53 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 08:37:53 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 08:37:53 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf1bdec2e, L:/************:2539 - R:/************:8091]
2025-07-22 08:37:53 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 4 ms, version:1.7.1,role:RMROLE,channel:[id: 0xf1bdec2e, L:/************:2539 - R:/************:8091]
2025-07-22 08:37:53 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 08:37:53 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 08:37:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 08:37:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 08:37:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 08:37:55 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 08:37:56 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 08:37:56 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 08:37:56 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 08:37:56 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 08:37:58 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 08:37:58 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 08:37:58 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 08:37:58 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 08:37:58 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement ************:9401 register finished
2025-07-22 08:38:02 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 17.348 seconds (process running for 18.302)
2025-07-22 08:38:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 08:38:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 08:38:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 08:38:02 [RMI TCP Connection(1)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[135ms]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[65ms]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[145ms]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 08:41:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 09:07:45 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 09:07:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa1cd83e9, L:/************:2519 ! R:/************:8091]
2025-07-22 09:07:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa1cd83e9, L:/************:2519 ! R:/************:8091]
2025-07-22 09:07:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa1cd83e9, L:/************:2519 ! R:/************:8091]
2025-07-22 09:07:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa1cd83e9, L:/************:2519 ! R:/************:8091]
2025-07-22 09:07:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa1cd83e9, L:/************:2519 ! R:/************:8091]) will closed
2025-07-22 09:07:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa1cd83e9, L:/************:2519 ! R:/************:8091]) will closed
2025-07-22 09:07:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf1bdec2e, L:/************:2539 ! R:/************:8091]
2025-07-22 09:07:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf1bdec2e, L:/************:2539 ! R:/************:8091]
2025-07-22 09:07:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf1bdec2e, L:/************:2539 ! R:/************:8091]
2025-07-22 09:07:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf1bdec2e, L:/************:2539 ! R:/************:8091]
2025-07-22 09:07:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1bdec2e, L:/************:2539 ! R:/************:8091]) will closed
2025-07-22 09:07:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1bdec2e, L:/************:2539 ! R:/************:8091]) will closed
2025-07-22 09:07:57 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 09:07:57 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 22388 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 09:07:57 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 09:07:57 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 09:07:57 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 09:07:57 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 09:08:02 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 09:08:02 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 09:08:02 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 09:08:02 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 09:08:02 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 09:08:02 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 09:08:02 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 09:08:02 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,************,1753146482867
timestamp=1753146482867
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=************
'} >
2025-07-22 09:08:03 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x80f23fe4, L:/************:4478 - R:/************:8091]
2025-07-22 09:08:03 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 107 ms, version:1.7.1,role:TMROLE,channel:[id: 0x80f23fe4, L:/************:4478 - R:/************:8091]
2025-07-22 09:08:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 09:08:03 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 09:08:03 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 09:08:03 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 09:08:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 09:08:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 09:08:04 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 09:08:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 09:08:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 09:08:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 09:08:06 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5d9cd8b7
2025-07-22 09:08:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 09:08:06 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ************:8091
2025-07-22 09:08:06 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 09:08:06 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 09:08:06 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x07e0d110, L:/************:4483 - R:/************:8091]
2025-07-22 09:08:06 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x07e0d110, L:/************:4483 - R:/************:8091]
2025-07-22 09:08:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 09:08:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 09:08:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 09:08:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 09:08:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 09:08:09 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 09:08:10 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 09:08:10 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 09:08:10 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 09:08:11 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 09:08:13 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 09:08:13 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 09:08:13 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 09:08:13 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 09:08:13 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement ************:9401 register finished
2025-07-22 09:08:17 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 22.603 seconds (process running for 24.026)
2025-07-22 09:08:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 09:08:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 09:08:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 09:08:17 [RMI TCP Connection(8)-************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[103ms]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:44:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 09:45:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[64ms]
2025-07-22 09:45:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 09:45:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 09:45:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-22 09:45:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 09:45:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 09:45:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-22 09:45:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[89ms]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[56ms]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[3ms]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[3ms]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 09:45:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-22 09:45:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 09:45:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-22 10:23:13 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [DubboShutdownHook] INFO  o.a.dubbo.config.DubboShutdownHook -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](yumeng-salary-settlement), dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [yumeng-resource], dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [GRPC-UNSUBSCRIBE] service:yumeng-resource, group:DUBBO_GROUP, cluster: 
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x6bbd49c7, L:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:4519 - R:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880], dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [NettyClientWorker-7-2] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:4519 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20880 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Destroying instance listener of  [yumeng-system], dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [GRPC-UNSUBSCRIBE] service:yumeng-system, group:DUBBO_GROUP, cluster: 
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0xd5a0905f, L:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:4517 - R:/[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881], dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [NettyClientWorker-7-1] INFO  o.a.d.r.t.netty4.NettyClientHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:4517 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20881 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](yumeng-salary-settlement) to null, dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](yumeng-salary-settlement) is stopping., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-salary-settlement with instance: Instance{instanceId='null', ip='************', port=20882, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='null', serviceName='yumeng-salary-settlement', metadata={dubbo.endpoints=[{"port":20882,"protocol":"dubbo"}], dubbo.metadata.revision=a820867234d3b6a6e13099bbc235a26c, dubbo.metadata.storage-type=remote, ipv6=2408:8215:a11:ed30:603e:f8b2:cae1:741a, timestamp=1753146494867}}
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.r.protocol.dubbo.DubboProtocol -  [DUBBO] Closing dubbo server: /************:20882, dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [NettyServerWorker-6-1] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection of /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:4526 -> /[2408:8215:a11:ed30:603e:f8b2:cae1:741a]:20882 is disconnected., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [SpringContextShutdownHook] INFO  o.a.d.r.transport.AbstractServer -  [DUBBO] Close NettyServer bind /0.0.0.0:20882, export /************:20882, dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:13 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation REMOVE for DUBBO_GROUP@@yumeng-salary-settlement
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [nacos://localhost:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=yumeng-salary-settlement&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=22388&qos.enable=false&register=false&register-mode=instance&release=3.2.14, nacos://localhost:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=yumeng-salary-settlement&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=22388&qos.enable=false&register-mode=instance&release=3.2.14, nacos://localhost:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=yumeng-salary-settlement&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=22388&qos.enable=false&register-mode=instance&release=3.2.14], dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->99bae928-e3ec-4b3f-9ae9-5030a8fc77be
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@4cc97c5f[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 1498]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6e07f8ec[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Close current connection 1753146494537_127.0.0.1_4513
2025-07-22 10:23:15 [nacos-grpc-client-executor-localhost-894] INFO  c.a.n.c.r.client.grpc.GrpcClient - [1753146494537_127.0.0.1_4513]Ignore complete event,isRunning:false,isAbandon=false
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.r.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1e1eb160[Running, pool size = 29, active threads = 0, queued tasks = 0, completed tasks = 895]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->99bae928-e3ec-4b3f-9ae9-5030a8fc77be
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialService - [null] CredentialService is freed
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.registry.nacos.NacosRegistry -  [DUBBO] Destroy registry:nacos://localhost:8848/org.apache.dubbo.registry.RegistryService?REGISTRY_CLUSTER=default:dev&application=yumeng-salary-settlement&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=DUBBO_GROUP&interface=org.apache.dubbo.registry.RegistryService&logger=slf4j&metadata-type=remote&namespace=dev&pid=22388&qos.enable=false&register-mode=instance&release=3.2.14, dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->1cd2cd3b-c1d3-43d3-9c78-2a87de6de658
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@4427d0a0[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 1498]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3ae7a826[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Close current connection 1753146495011_127.0.0.1_4516
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.r.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7c6838c1[Running, pool size = 2, active threads = 0, queued tasks = 0, completed tasks = 864]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->1cd2cd3b-c1d3-43d3-9c78-2a87de6de658
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](yumeng-salary-settlement) has stopped., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.config.impl.ClientWorker - com.alibaba.nacos.client.config.impl.ClientWorker do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.config.impl.ClientWorker - Trying to shutdown transport client com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient@36dfcdf8
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.config.impl.ClientWorker - Trying to shutdown rpc client 56894b42-b4a0-4a71-9ff5-9958c6a06eb6_config-0
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@133efce3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Close current connection 1753146485131_127.0.0.1_4480
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.r.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5dd90e35[Running, pool size = 2, active threads = 0, queued tasks = 0, completed tasks = 1168]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.config.impl.ClientWorker - Remove rpc client 56894b42-b4a0-4a71-9ff5-9958c6a06eb6_config-0
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.config.impl.ClientWorker - Shutdown executor java.util.concurrent.ScheduledThreadPoolExecutor@26b388f1[Running, pool size = 16, active threads = 1, queued tasks = 1, completed tasks = 902]
2025-07-22 10:23:15 [com.alibaba.nacos.client.Worker.9] INFO  c.a.n.c.a.r.i.CredentialWatcher - null No credential found
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.config.impl.ClientWorker - com.alibaba.nacos.client.config.impl.ClientWorker do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.deploy.DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.r.support.RegistryManager -  [DUBBO] Close all registries [], dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.t.m.DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.d.DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.dubbo.rpc.model.FrameworkModel -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Destroying global resources ..., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.r.GlobalResourcesRepository -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.t.m.FrameworkExecutorRepository -  [DUBBO] destroying framework executor repository .., dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  o.a.d.c.s.c.DubboSpringInitializer -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@4a62062a, dubbo version: 3.2.14, current host: ************
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-salary-settlement with instance: Instance{instanceId='null', ip='************', port=9401, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->ab586152-9f7b-4c40-9201-3ad1d4fbf6ba
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@3f636eea[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 1499]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2a2035b1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Close current connection 1753146493549_127.0.0.1_4512
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.r.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@10fa8d9c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 866]
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->ab586152-9f7b-4c40-9201-3ad1d4fbf6ba
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.a.n.c.a.r.i.CredentialService - [null] CredentialService is freed
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 10:23:15 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 10:23:15 [com.alibaba.nacos.client.naming.security.0] INFO  c.a.n.c.a.r.i.CredentialWatcher - null No credential found
2025-07-22 10:23:16 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 10:23:16 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 10:23:16 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 10:23:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x80f23fe4, L:/************:4478 ! R:/************:8091]
2025-07-22 10:23:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x80f23fe4, L:/************:4478 ! R:/************:8091]
2025-07-22 10:23:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x80f23fe4, L:/************:4478 ! R:/************:8091]
2025-07-22 10:23:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x80f23fe4, L:/************:4478 ! R:/************:8091]
2025-07-22 10:23:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x80f23fe4, L:/************:4478 ! R:/************:8091]) will closed
2025-07-22 10:23:16 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x80f23fe4, L:/************:4478 ! R:/************:8091]) will closed
2025-07-22 10:23:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x07e0d110, L:/************:4483 ! R:/************:8091]
2025-07-22 10:23:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x07e0d110, L:/************:4483 ! R:/************:8091]
2025-07-22 10:23:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x07e0d110, L:/************:4483 ! R:/************:8091]
2025-07-22 10:23:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x07e0d110, L:/************:4483 ! R:/************:8091]
2025-07-22 10:23:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x07e0d110, L:/************:4483 ! R:/************:8091]) will closed
2025-07-22 10:23:16 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x07e0d110, L:/************:4483 ! R:/************:8091]) will closed
2025-07-22 11:10:10 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 11:10:10 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 26756 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 11:10:10 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 11:10:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 11:10:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 11:10:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 11:10:14 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 11:10:14 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 11:10:14 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 11:10:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 11:10:15 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 11:10:15 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 11:10:15 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 11:10:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753153815345
timestamp=1753153815345
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 11:10:15 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x13d9e24b, L:/**************:1728 - R:/**************:8091]
2025-07-22 11:10:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 56 ms, version:1.7.1,role:TMROLE,channel:[id: 0x13d9e24b, L:/**************:1728 - R:/**************:8091]
2025-07-22 11:10:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 11:10:15 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 11:10:15 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 11:10:15 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 11:10:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 11:10:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 11:10:16 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 11:10:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 11:10:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 11:10:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 11:10:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1396ee3b
2025-07-22 11:10:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 11:10:18 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 11:10:18 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 11:10:18 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 11:10:18 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xeceb2b00, L:/**************:1738 - R:/**************:8091]
2025-07-22 11:10:18 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0xeceb2b00, L:/**************:1738 - R:/**************:8091]
2025-07-22 11:10:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 11:10:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 11:10:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 11:10:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 11:10:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 11:10:20 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 11:10:20 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 11:10:20 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 11:10:21 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 11:10:21 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 11:10:22 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 11:10:22 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 11:10:22 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 11:10:22 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 11:10:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 11:10:26 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 18.138 seconds (process running for 19.239)
2025-07-22 11:10:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 11:10:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 11:10:26 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 11:10:26 [RMI TCP Connection(4)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[97ms]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:11:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-22 11:12:15 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*******************************************', saveDays=7, branchType=AT}
2025-07-22 11:16:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[72ms]
2025-07-22 11:16:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 11:16:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 11:16:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[119ms]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[13ms]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 11:16:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[52ms]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 11:16:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-22 11:18:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 11:18:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[22ms]
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 14:24:27 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 14:24:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x13d9e24b, L:/**************:1728 ! R:/**************:8091]
2025-07-22 14:24:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x13d9e24b, L:/**************:1728 ! R:/**************:8091]
2025-07-22 14:24:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x13d9e24b, L:/**************:1728 ! R:/**************:8091]
2025-07-22 14:24:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x13d9e24b, L:/**************:1728 ! R:/**************:8091]
2025-07-22 14:24:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x13d9e24b, L:/**************:1728 ! R:/**************:8091]) will closed
2025-07-22 14:24:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x13d9e24b, L:/**************:1728 ! R:/**************:8091]) will closed
2025-07-22 14:24:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xeceb2b00, L:/**************:1738 ! R:/**************:8091]
2025-07-22 14:24:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xeceb2b00, L:/**************:1738 ! R:/**************:8091]
2025-07-22 14:24:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xeceb2b00, L:/**************:1738 ! R:/**************:8091]
2025-07-22 14:24:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xeceb2b00, L:/**************:1738 ! R:/**************:8091]
2025-07-22 14:24:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeceb2b00, L:/**************:1738 ! R:/**************:8091]) will closed
2025-07-22 14:24:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xeceb2b00, L:/**************:1738 ! R:/**************:8091]) will closed
2025-07-22 14:24:42 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 14:24:42 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 19352 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 14:24:42 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 14:24:42 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 14:24:42 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 14:24:42 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 14:24:47 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 14:24:47 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 14:24:47 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 14:24:48 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 14:24:48 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 14:24:48 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 14:24:48 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 14:24:48 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753165488449
timestamp=1753165488449
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 14:24:48 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x84b6e3e1, L:/**************:7478 - R:/**************:8091]
2025-07-22 14:24:48 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 113 ms, version:1.7.1,role:TMROLE,channel:[id: 0x84b6e3e1, L:/**************:7478 - R:/**************:8091]
2025-07-22 14:24:48 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 14:24:48 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 14:24:48 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 14:24:48 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 14:24:48 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 14:24:48 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 14:24:49 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 14:24:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 14:24:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 14:24:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 14:24:52 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6253df71
2025-07-22 14:24:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 14:24:52 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 14:24:52 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 14:24:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 14:24:52 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe24040e1, L:/**************:7483 - R:/**************:8091]
2025-07-22 14:24:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 15 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe24040e1, L:/**************:7483 - R:/**************:8091]
2025-07-22 14:24:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 14:24:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 14:24:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 14:24:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 14:24:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 14:24:55 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 14:24:55 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 14:24:56 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 14:24:56 [redisson-netty-2-2] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 14:24:56 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 14:24:58 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 14:24:58 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 14:24:59 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 14:24:59 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 14:24:59 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 14:25:03 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 23.04 seconds (process running for 24.781)
2025-07-22 14:25:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 14:25:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 14:25:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 14:25:03 [RMI TCP Connection(7)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[139ms]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[36ms]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-22 14:26:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:26:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[80ms]
2025-07-22 14:26:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:26:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 14:26:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:26:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 14:26:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:26:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 14:26:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:26:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[57ms]
2025-07-22 14:26:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:26:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 14:26:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:26:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[31ms]
2025-07-22 14:28:02 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:28:02 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[25ms]
2025-07-22 14:28:15 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:28:15 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[67ms]
2025-07-22 14:28:15 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:28:15 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-22 14:29:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:29:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[80ms]
2025-07-22 14:29:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:29:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-22 14:29:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[45ms]
2025-07-22 14:29:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[51ms]
2025-07-22 14:29:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:29:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[57ms]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[3ms]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[3ms]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:29:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 14:31:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:31:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[73ms]
2025-07-22 14:31:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:31:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 14:31:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:31:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[59ms]
2025-07-22 14:31:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:31:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 14:36:56 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 14:36:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x84b6e3e1, L:/**************:7478 ! R:/**************:8091]
2025-07-22 14:36:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x84b6e3e1, L:/**************:7478 ! R:/**************:8091]
2025-07-22 14:36:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x84b6e3e1, L:/**************:7478 ! R:/**************:8091]
2025-07-22 14:36:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x84b6e3e1, L:/**************:7478 ! R:/**************:8091]
2025-07-22 14:36:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x84b6e3e1, L:/**************:7478 ! R:/**************:8091]) will closed
2025-07-22 14:36:56 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x84b6e3e1, L:/**************:7478 ! R:/**************:8091]) will closed
2025-07-22 14:36:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe24040e1, L:/**************:7483 ! R:/**************:8091]
2025-07-22 14:36:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe24040e1, L:/**************:7483 ! R:/**************:8091]
2025-07-22 14:36:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe24040e1, L:/**************:7483 ! R:/**************:8091]
2025-07-22 14:36:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe24040e1, L:/**************:7483 ! R:/**************:8091]
2025-07-22 14:36:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe24040e1, L:/**************:7483 ! R:/**************:8091]) will closed
2025-07-22 14:36:56 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe24040e1, L:/**************:7483 ! R:/**************:8091]) will closed
2025-07-22 14:37:01 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 14:37:02 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 36244 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 14:37:02 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 14:37:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 14:37:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 14:37:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 14:37:06 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 14:37:06 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 14:37:06 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 14:37:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 14:37:06 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 14:37:06 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 14:37:06 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 14:37:07 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753166227007
timestamp=1753166227007
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 14:37:07 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd46248b1, L:/**************:8735 - R:/**************:8091]
2025-07-22 14:37:07 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 87 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd46248b1, L:/**************:8735 - R:/**************:8091]
2025-07-22 14:37:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 14:37:07 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 14:37:07 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 14:37:07 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 14:37:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 14:37:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 14:37:08 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 14:37:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 14:37:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 14:37:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 14:37:10 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4db11495
2025-07-22 14:37:10 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 14:37:10 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 14:37:10 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 14:37:10 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 14:37:10 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x545ae290, L:/**************:8744 - R:/**************:8091]
2025-07-22 14:37:10 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 9 ms, version:1.7.1,role:RMROLE,channel:[id: 0x545ae290, L:/**************:8744 - R:/**************:8091]
2025-07-22 14:37:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 14:37:10 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 14:37:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 14:37:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 14:37:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 14:37:12 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 14:37:13 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 14:37:13 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 14:37:14 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 14:37:14 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 14:37:16 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 14:37:16 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 14:37:16 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 14:37:16 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 14:37:16 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 14:37:20 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 20.58 seconds (process running for 21.683)
2025-07-22 14:37:20 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 14:37:20 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 14:37:20 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 14:37:20 [RMI TCP Connection(1)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 14:41:21 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 14:41:21 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 14:41:21 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 14:41:21 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 14:41:21 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 14:41:21 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 14:41:21 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 14:41:22 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 14:41:22 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 14:41:22 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 14:41:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd46248b1, L:/**************:8735 ! R:/**************:8091]
2025-07-22 14:41:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd46248b1, L:/**************:8735 ! R:/**************:8091]
2025-07-22 14:41:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd46248b1, L:/**************:8735 ! R:/**************:8091]
2025-07-22 14:41:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd46248b1, L:/**************:8735 ! R:/**************:8091]
2025-07-22 14:41:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd46248b1, L:/**************:8735 ! R:/**************:8091]) will closed
2025-07-22 14:41:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd46248b1, L:/**************:8735 ! R:/**************:8091]) will closed
2025-07-22 14:41:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x545ae290, L:/**************:8744 ! R:/**************:8091]
2025-07-22 14:41:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x545ae290, L:/**************:8744 ! R:/**************:8091]
2025-07-22 14:41:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x545ae290, L:/**************:8744 ! R:/**************:8091]
2025-07-22 14:41:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x545ae290, L:/**************:8744 ! R:/**************:8091]
2025-07-22 14:41:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x545ae290, L:/**************:8744 ! R:/**************:8091]) will closed
2025-07-22 14:41:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x545ae290, L:/**************:8744 ! R:/**************:8091]) will closed
2025-07-22 14:41:26 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 14:41:26 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 31604 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 14:41:26 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 14:41:26 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 14:41:26 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 14:41:26 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 14:41:31 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 14:41:31 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 14:41:31 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 14:41:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 14:41:31 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 14:41:31 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 14:41:31 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 14:41:31 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753166491748
timestamp=1753166491748
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 14:41:31 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x12329533, L:/**************:10160 - R:/**************:8091]
2025-07-22 14:41:31 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 89 ms, version:1.7.1,role:TMROLE,channel:[id: 0x12329533, L:/**************:10160 - R:/**************:8091]
2025-07-22 14:41:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 14:41:32 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 14:41:32 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 14:41:32 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 14:41:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 14:41:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 14:41:33 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 14:41:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 14:41:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 14:41:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 14:41:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@47ce44ab
2025-07-22 14:41:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 14:41:35 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 14:41:35 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 14:41:35 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 14:41:35 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe2073101, L:/**************:10180 - R:/**************:8091]
2025-07-22 14:41:35 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 13 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe2073101, L:/**************:10180 - R:/**************:8091]
2025-07-22 14:41:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 14:41:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 14:41:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 14:41:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 14:41:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 14:41:37 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 14:41:38 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 14:41:38 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 14:41:38 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 14:41:39 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 14:41:41 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 14:41:41 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 14:41:41 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 14:41:41 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 14:41:41 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 14:41:45 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 20.538 seconds (process running for 21.815)
2025-07-22 14:41:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 14:41:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 14:41:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 14:41:45 [RMI TCP Connection(5)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 14:41:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:41:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[65ms]
2025-07-22 14:42:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:42:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[37ms]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[89ms]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[61ms]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[26ms]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 14:43:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[62ms]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:43:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 14:44:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:44:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:44:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:44:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:44:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:44:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[31ms]
2025-07-22 14:44:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:44:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[37ms]
2025-07-22 14:45:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:45:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[21ms]
2025-07-22 14:46:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:46:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[98ms]
2025-07-22 14:46:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 14:46:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 14:46:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 14:46:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[54ms]
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 15:00:32 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 15:00:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x12329533, L:/**************:10160 ! R:/**************:8091]
2025-07-22 15:00:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x12329533, L:/**************:10160 ! R:/**************:8091]
2025-07-22 15:00:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x12329533, L:/**************:10160 ! R:/**************:8091]
2025-07-22 15:00:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x12329533, L:/**************:10160 ! R:/**************:8091]
2025-07-22 15:00:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x12329533, L:/**************:10160 ! R:/**************:8091]) will closed
2025-07-22 15:00:32 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x12329533, L:/**************:10160 ! R:/**************:8091]) will closed
2025-07-22 15:00:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe2073101, L:/**************:10180 ! R:/**************:8091]
2025-07-22 15:00:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe2073101, L:/**************:10180 ! R:/**************:8091]
2025-07-22 15:00:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe2073101, L:/**************:10180 ! R:/**************:8091]
2025-07-22 15:00:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe2073101, L:/**************:10180 ! R:/**************:8091]
2025-07-22 15:00:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2073101, L:/**************:10180 ! R:/**************:8091]) will closed
2025-07-22 15:00:32 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2073101, L:/**************:10180 ! R:/**************:8091]) will closed
2025-07-22 15:02:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 15:02:00 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 37416 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 15:02:00 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 15:02:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 15:02:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 15:02:00 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 15:02:05 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 15:02:05 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 15:02:05 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 15:02:05 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 15:02:05 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:02:05 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 15:02:05 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:02:05 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753167725767
timestamp=1753167725767
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 15:02:06 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe0955faf, L:/**************:12543 - R:/**************:8091]
2025-07-22 15:02:06 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 102 ms, version:1.7.1,role:TMROLE,channel:[id: 0xe0955faf, L:/**************:12543 - R:/**************:8091]
2025-07-22 15:02:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:02:06 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 15:02:06 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 15:02:06 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:02:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:02:06 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 15:02:07 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 15:02:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 15:02:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 15:02:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 15:02:09 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@798e73de
2025-07-22 15:02:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 15:02:09 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:02:09 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 15:02:09 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 15:02:09 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xb6a1ad33, L:/**************:12550 - R:/**************:8091]
2025-07-22 15:02:09 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0xb6a1ad33, L:/**************:12550 - R:/**************:8091]
2025-07-22 15:02:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 15:02:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 15:02:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:02:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:02:11 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:02:12 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 15:02:13 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 15:02:13 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 15:02:13 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 15:02:13 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 15:02:16 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 15:02:16 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 15:02:16 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 15:02:16 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 15:02:16 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 15:02:20 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 22.735 seconds (process running for 24.265)
2025-07-22 15:02:20 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 15:02:20 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 15:02:20 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 15:02:21 [RMI TCP Connection(12)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 15:16:16 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 15:16:16 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 15:16:16 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 15:16:16 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 15:16:17 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 15:16:17 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 15:16:17 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 15:16:17 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 15:16:17 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 15:16:17 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 15:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe0955faf, L:/**************:12543 ! R:/**************:8091]
2025-07-22 15:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe0955faf, L:/**************:12543 ! R:/**************:8091]
2025-07-22 15:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe0955faf, L:/**************:12543 ! R:/**************:8091]
2025-07-22 15:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe0955faf, L:/**************:12543 ! R:/**************:8091]
2025-07-22 15:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe0955faf, L:/**************:12543 ! R:/**************:8091]) will closed
2025-07-22 15:16:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe0955faf, L:/**************:12543 ! R:/**************:8091]) will closed
2025-07-22 15:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xb6a1ad33, L:/**************:12550 ! R:/**************:8091]
2025-07-22 15:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xb6a1ad33, L:/**************:12550 ! R:/**************:8091]
2025-07-22 15:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb6a1ad33, L:/**************:12550 ! R:/**************:8091]
2025-07-22 15:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb6a1ad33, L:/**************:12550 ! R:/**************:8091]
2025-07-22 15:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb6a1ad33, L:/**************:12550 ! R:/**************:8091]) will closed
2025-07-22 15:16:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb6a1ad33, L:/**************:12550 ! R:/**************:8091]) will closed
2025-07-22 15:16:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 15:16:29 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 38768 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 15:16:29 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 15:16:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 15:16:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 15:16:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 15:16:34 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 15:16:34 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 15:16:34 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 15:16:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 15:16:34 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:16:34 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 15:16:34 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:16:34 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753168594749
timestamp=1753168594749
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 15:16:34 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x676a95ed, L:/**************:14917 - R:/**************:8091]
2025-07-22 15:16:34 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 93 ms, version:1.7.1,role:TMROLE,channel:[id: 0x676a95ed, L:/**************:14917 - R:/**************:8091]
2025-07-22 15:16:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:16:35 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 15:16:35 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 15:16:35 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:16:35 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:16:35 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 15:16:36 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 15:16:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 15:16:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 15:16:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 15:16:38 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6cd9cafb
2025-07-22 15:16:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 15:16:38 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:16:38 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 15:16:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 15:16:38 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x8bc85ce0, L:/**************:14943 - R:/**************:8091]
2025-07-22 15:16:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x8bc85ce0, L:/**************:14943 - R:/**************:8091]
2025-07-22 15:16:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 15:16:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 15:16:39 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:16:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:16:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:16:40 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 15:16:41 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 15:16:41 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 15:16:41 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 15:16:42 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 15:16:44 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 15:16:44 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 15:16:44 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 15:16:44 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 15:16:44 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 15:16:48 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 20.845 seconds (process running for 22.145)
2025-07-22 15:16:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 15:16:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 15:16:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 15:16:48 [RMI TCP Connection(3)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 15:19:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:19:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[189ms]
2025-07-22 15:19:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:19:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 15:19:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:19:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[73ms]
2025-07-22 15:19:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:19:48 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 15:20:25 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:20:25 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-22 15:20:25 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:20:25 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-22 15:20:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:20:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[71ms]
2025-07-22 15:20:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:20:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 15:21:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:21:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[74ms]
2025-07-22 15:21:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:21:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 15:22:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:22:46 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[40ms]
2025-07-22 15:24:24 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:24:24 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[65ms]
2025-07-22 15:24:24 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:24:24 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 15:24:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:24:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[76ms]
2025-07-22 15:24:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:24:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 15:27:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:27:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[73ms]
2025-07-22 15:27:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:27:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 15:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[74ms]
2025-07-22 15:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 15:30:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:30:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[82ms]
2025-07-22 15:30:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:30:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 15:30:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:30:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[34ms]
2025-07-22 15:31:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:31:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[25ms]
2025-07-22 15:31:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:31:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[26ms]
2025-07-22 15:33:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:33:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[72ms]
2025-07-22 15:33:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:33:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 15:34:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:34:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[69ms]
2025-07-22 15:34:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:34:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 15:34:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:34:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[82ms]
2025-07-22 15:34:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:34:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 15:35:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:35:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[68ms]
2025-07-22 15:35:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:35:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 15:36:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:36:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[78ms]
2025-07-22 15:36:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:36:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 15:37:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:37:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[21ms]
2025-07-22 15:38:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:38:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[69ms]
2025-07-22 15:38:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:38:21 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 15:39:33 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 15:39:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x676a95ed, L:/**************:14917 ! R:/**************:8091]
2025-07-22 15:39:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x676a95ed, L:/**************:14917 ! R:/**************:8091]
2025-07-22 15:39:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x676a95ed, L:/**************:14917 ! R:/**************:8091]
2025-07-22 15:39:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x676a95ed, L:/**************:14917 ! R:/**************:8091]
2025-07-22 15:39:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x676a95ed, L:/**************:14917 ! R:/**************:8091]) will closed
2025-07-22 15:39:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x676a95ed, L:/**************:14917 ! R:/**************:8091]) will closed
2025-07-22 15:39:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x8bc85ce0, L:/**************:14943 ! R:/**************:8091]
2025-07-22 15:39:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x8bc85ce0, L:/**************:14943 ! R:/**************:8091]
2025-07-22 15:39:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x8bc85ce0, L:/**************:14943 ! R:/**************:8091]
2025-07-22 15:39:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x8bc85ce0, L:/**************:14943 ! R:/**************:8091]
2025-07-22 15:39:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8bc85ce0, L:/**************:14943 ! R:/**************:8091]) will closed
2025-07-22 15:39:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8bc85ce0, L:/**************:14943 ! R:/**************:8091]) will closed
2025-07-22 15:39:41 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 15:39:42 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 28136 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 15:39:42 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 15:39:42 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 15:39:42 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 15:39:42 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 15:39:46 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 15:39:46 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 15:39:46 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 15:39:47 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 15:39:47 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:39:47 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 15:39:47 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:39:47 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753169987402
timestamp=1753169987402
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 15:39:47 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7f7885dd, L:/**************:3996 - R:/**************:8091]
2025-07-22 15:39:47 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 94 ms, version:1.7.1,role:TMROLE,channel:[id: 0x7f7885dd, L:/**************:3996 - R:/**************:8091]
2025-07-22 15:39:47 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:39:47 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 15:39:47 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 15:39:47 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:39:47 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:39:47 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 15:39:48 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 15:39:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 15:39:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 15:39:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 15:39:50 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@47ce44ab
2025-07-22 15:39:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 15:39:51 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:39:51 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 15:39:51 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 15:39:51 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xb217a39a, L:/**************:4002 - R:/**************:8091]
2025-07-22 15:39:51 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0xb217a39a, L:/**************:4002 - R:/**************:8091]
2025-07-22 15:39:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 15:39:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 15:39:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:39:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:39:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:39:53 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 15:39:54 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 15:39:54 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 15:39:54 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 15:39:54 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 15:39:56 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 15:39:56 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 15:39:56 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 15:39:57 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 15:39:57 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 15:40:00 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 21.097 seconds (process running for 22.407)
2025-07-22 15:40:01 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 15:40:01 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 15:40:01 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 15:40:01 [RMI TCP Connection(1)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 15:41:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:41:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[99ms]
2025-07-22 15:42:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:42:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[26ms]
2025-07-22 15:42:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:42:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[21ms]
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 15:42:58 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 15:42:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7f7885dd, L:/**************:3996 ! R:/**************:8091]
2025-07-22 15:42:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7f7885dd, L:/**************:3996 ! R:/**************:8091]
2025-07-22 15:42:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7f7885dd, L:/**************:3996 ! R:/**************:8091]
2025-07-22 15:42:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7f7885dd, L:/**************:3996 ! R:/**************:8091]
2025-07-22 15:42:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7f7885dd, L:/**************:3996 ! R:/**************:8091]) will closed
2025-07-22 15:42:58 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7f7885dd, L:/**************:3996 ! R:/**************:8091]) will closed
2025-07-22 15:42:58 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xb217a39a, L:/**************:4002 ! R:/**************:8091]
2025-07-22 15:42:58 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xb217a39a, L:/**************:4002 ! R:/**************:8091]
2025-07-22 15:42:58 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xb217a39a, L:/**************:4002 ! R:/**************:8091]
2025-07-22 15:42:58 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xb217a39a, L:/**************:4002 ! R:/**************:8091]
2025-07-22 15:42:58 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb217a39a, L:/**************:4002 ! R:/**************:8091]) will closed
2025-07-22 15:42:58 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb217a39a, L:/**************:4002 ! R:/**************:8091]) will closed
2025-07-22 15:43:03 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 15:43:03 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 37428 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 15:43:03 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 15:43:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 15:43:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 15:43:03 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 15:43:08 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 15:43:08 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 15:43:08 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 15:43:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 15:43:08 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:43:08 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 15:43:08 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:43:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753170188841
timestamp=1753170188841
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 15:43:09 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2f27fcbc, L:/**************:4304 - R:/**************:8091]
2025-07-22 15:43:09 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 92 ms, version:1.7.1,role:TMROLE,channel:[id: 0x2f27fcbc, L:/**************:4304 - R:/**************:8091]
2025-07-22 15:43:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:43:09 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 15:43:09 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 15:43:09 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:43:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:43:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 15:43:10 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 15:43:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 15:43:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 15:43:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 15:43:12 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@61df07d0
2025-07-22 15:43:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 15:43:12 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:43:12 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 15:43:12 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 15:43:12 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x6b4b0ceb, L:/**************:4314 - R:/**************:8091]
2025-07-22 15:43:12 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x6b4b0ceb, L:/**************:4314 - R:/**************:8091]
2025-07-22 15:43:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 15:43:13 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 15:43:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:43:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:43:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:43:15 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 15:43:16 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 15:43:16 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 15:43:16 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 15:43:17 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 15:43:19 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 15:43:19 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 15:43:19 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 15:43:19 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 15:43:19 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 15:43:23 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 21.718 seconds (process running for 23.144)
2025-07-22 15:43:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 15:43:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 15:43:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 15:43:23 [RMI TCP Connection(3)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 15:43:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:43:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[77ms]
2025-07-22 15:43:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:43:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[38ms]
2025-07-22 15:47:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:47:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[70ms]
2025-07-22 15:47:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:47:35 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 15:47:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:47:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[67ms]
2025-07-22 15:47:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:47:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 15:51:37 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 15:51:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2f27fcbc, L:/**************:4304 ! R:/**************:8091]
2025-07-22 15:51:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2f27fcbc, L:/**************:4304 ! R:/**************:8091]
2025-07-22 15:51:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2f27fcbc, L:/**************:4304 ! R:/**************:8091]
2025-07-22 15:51:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2f27fcbc, L:/**************:4304 ! R:/**************:8091]
2025-07-22 15:51:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2f27fcbc, L:/**************:4304 ! R:/**************:8091]) will closed
2025-07-22 15:51:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2f27fcbc, L:/**************:4304 ! R:/**************:8091]) will closed
2025-07-22 15:51:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x6b4b0ceb, L:/**************:4314 ! R:/**************:8091]
2025-07-22 15:51:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x6b4b0ceb, L:/**************:4314 ! R:/**************:8091]
2025-07-22 15:51:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x6b4b0ceb, L:/**************:4314 ! R:/**************:8091]
2025-07-22 15:51:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x6b4b0ceb, L:/**************:4314 ! R:/**************:8091]
2025-07-22 15:51:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6b4b0ceb, L:/**************:4314 ! R:/**************:8091]) will closed
2025-07-22 15:51:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6b4b0ceb, L:/**************:4314 ! R:/**************:8091]) will closed
2025-07-22 15:51:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 15:51:44 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 29272 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 15:51:44 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 15:51:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 15:51:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 15:51:44 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 15:51:48 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 15:51:48 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 15:51:48 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 15:51:48 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 15:51:48 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:51:48 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 15:51:49 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:51:49 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753170709100
timestamp=1753170709100
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 15:51:49 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd5eb2bdf, L:/**************:5167 - R:/**************:8091]
2025-07-22 15:51:49 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 82 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd5eb2bdf, L:/**************:5167 - R:/**************:8091]
2025-07-22 15:51:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:51:49 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 15:51:49 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 15:51:49 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 15:51:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 15:51:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 15:51:50 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 15:51:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 15:51:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 15:51:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 15:51:52 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@279afa4e
2025-07-22 15:51:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 15:51:52 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 15:51:52 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 15:51:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 15:51:52 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xab11c027, L:/**************:5173 - R:/**************:8091]
2025-07-22 15:51:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xab11c027, L:/**************:5173 - R:/**************:8091]
2025-07-22 15:51:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 15:51:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 15:51:53 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:51:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:51:54 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 15:51:54 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 15:51:55 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 15:51:55 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 15:51:55 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 15:51:56 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 15:51:58 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 15:51:58 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 15:51:58 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 15:51:58 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 15:51:58 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 15:52:02 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 19.792 seconds (process running for 20.88)
2025-07-22 15:52:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 15:52:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 15:52:02 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 15:52:02 [RMI TCP Connection(2)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 15:52:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 15:52:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[71ms]
2025-07-22 15:57:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:57:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[68ms]
2025-07-22 15:57:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 15:57:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 16:00:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:00:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[74ms]
2025-07-22 16:00:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:00:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 16:20:40 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 16:20:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd5eb2bdf, L:/**************:5167 ! R:/**************:8091]
2025-07-22 16:20:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd5eb2bdf, L:/**************:5167 ! R:/**************:8091]
2025-07-22 16:20:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd5eb2bdf, L:/**************:5167 ! R:/**************:8091]
2025-07-22 16:20:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd5eb2bdf, L:/**************:5167 ! R:/**************:8091]
2025-07-22 16:20:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd5eb2bdf, L:/**************:5167 ! R:/**************:8091]) will closed
2025-07-22 16:20:40 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd5eb2bdf, L:/**************:5167 ! R:/**************:8091]) will closed
2025-07-22 16:20:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xab11c027, L:/**************:5173 ! R:/**************:8091]
2025-07-22 16:20:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xab11c027, L:/**************:5173 ! R:/**************:8091]
2025-07-22 16:20:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xab11c027, L:/**************:5173 ! R:/**************:8091]
2025-07-22 16:20:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xab11c027, L:/**************:5173 ! R:/**************:8091]
2025-07-22 16:20:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xab11c027, L:/**************:5173 ! R:/**************:8091]) will closed
2025-07-22 16:20:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xab11c027, L:/**************:5173 ! R:/**************:8091]) will closed
2025-07-22 16:20:45 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 16:20:46 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 16448 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 16:20:46 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 16:20:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 16:20:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 16:20:46 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 16:20:51 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 16:20:51 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 16:20:51 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 16:20:51 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 16:20:51 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 16:20:51 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 16:20:51 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 16:20:51 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753172451718
timestamp=1753172451718
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 16:20:51 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x945e6b1c, L:/**************:9983 - R:/**************:8091]
2025-07-22 16:20:51 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 98 ms, version:1.7.1,role:TMROLE,channel:[id: 0x945e6b1c, L:/**************:9983 - R:/**************:8091]
2025-07-22 16:20:51 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 16:20:51 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 16:20:51 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 16:20:52 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 16:20:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 16:20:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 16:20:53 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 16:20:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 16:20:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 16:20:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 16:20:55 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-07-22 16:20:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 16:20:55 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 16:20:55 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 16:20:55 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 16:20:55 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4a242c76, L:/**************:9988 - R:/**************:8091]
2025-07-22 16:20:55 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x4a242c76, L:/**************:9988 - R:/**************:8091]
2025-07-22 16:20:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 16:20:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 16:20:56 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 16:20:57 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 16:20:57 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 16:20:57 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 16:20:58 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 16:20:58 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 16:20:58 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 16:20:59 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 16:21:01 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 16:21:01 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 16:21:01 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 16:21:01 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 16:21:01 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 16:21:05 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 21.577 seconds (process running for 23.033)
2025-07-22 16:21:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 16:21:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 16:21:05 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 16:23:38 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[109ms]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[62ms]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:39:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 16:39:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 16:39:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[32ms]
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 16:49:52 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 16:49:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x945e6b1c, L:/**************:9983 ! R:/**************:8091]
2025-07-22 16:49:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x945e6b1c, L:/**************:9983 ! R:/**************:8091]
2025-07-22 16:49:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x945e6b1c, L:/**************:9983 ! R:/**************:8091]
2025-07-22 16:49:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x945e6b1c, L:/**************:9983 ! R:/**************:8091]
2025-07-22 16:49:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x945e6b1c, L:/**************:9983 ! R:/**************:8091]) will closed
2025-07-22 16:49:52 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x945e6b1c, L:/**************:9983 ! R:/**************:8091]) will closed
2025-07-22 16:49:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4a242c76, L:/**************:9988 ! R:/**************:8091]
2025-07-22 16:49:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4a242c76, L:/**************:9988 ! R:/**************:8091]
2025-07-22 16:49:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4a242c76, L:/**************:9988 ! R:/**************:8091]
2025-07-22 16:49:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4a242c76, L:/**************:9988 ! R:/**************:8091]
2025-07-22 16:49:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4a242c76, L:/**************:9988 ! R:/**************:8091]) will closed
2025-07-22 16:49:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4a242c76, L:/**************:9988 ! R:/**************:8091]) will closed
2025-07-22 16:49:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 16:49:59 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 2536 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 16:49:59 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 16:49:59 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 16:49:59 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 16:49:59 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 16:50:03 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 16:50:03 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 16:50:03 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 16:50:04 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 16:50:04 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 16:50:04 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 16:50:04 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 16:50:04 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753174204406
timestamp=1753174204406
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 16:50:04 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe29294e8, L:/**************:13153 - R:/**************:8091]
2025-07-22 16:50:04 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 101 ms, version:1.7.1,role:TMROLE,channel:[id: 0xe29294e8, L:/**************:13153 - R:/**************:8091]
2025-07-22 16:50:04 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 16:50:04 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 16:50:04 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 16:50:04 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 16:50:04 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 16:50:04 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 16:50:06 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 16:50:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 16:50:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 16:50:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 16:50:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6cd9cafb
2025-07-22 16:50:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 16:50:08 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 16:50:08 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 16:50:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 16:50:08 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x50794023, L:/**************:13159 - R:/**************:8091]
2025-07-22 16:50:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x50794023, L:/**************:13159 - R:/**************:8091]
2025-07-22 16:50:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 16:50:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 16:50:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 16:50:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 16:50:10 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 16:50:10 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 16:50:11 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 16:50:11 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 16:50:11 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 16:50:12 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 16:50:14 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 16:50:14 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 16:50:14 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 16:50:14 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 16:50:14 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 16:50:18 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 21.6 seconds (process running for 22.784)
2025-07-22 16:50:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 16:50:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 16:50:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 16:50:18 [RMI TCP Connection(2)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 16:50:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:50:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[86ms]
2025-07-22 16:50:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:50:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-22 16:50:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:50:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 16:50:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:50:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 16:52:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:52:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[52ms]
2025-07-22 16:52:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:52:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 16:52:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:52:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-22 16:52:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:52:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[4ms]
2025-07-22 16:53:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:53:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[40ms]
2025-07-22 16:53:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:53:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 16:53:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:53:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 16:53:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:53:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 16:55:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:55:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[139ms]
2025-07-22 16:55:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:55:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 16:56:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:56:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[75ms]
2025-07-22 16:56:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:56:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[117ms]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[25ms]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:56:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 16:56:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:56:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 16:56:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 16:56:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 16:59:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:59:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[46ms]
2025-07-22 16:59:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 16:59:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[38ms]
2025-07-22 16:59:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-22 16:59:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-22 17:05:04 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 17:05:04 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 17:05:04 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 17:05:04 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 17:05:04 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 17:05:04 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 17:05:04 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 17:05:05 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 17:05:05 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 17:05:05 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 17:05:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe29294e8, L:/**************:13153 ! R:/**************:8091]
2025-07-22 17:05:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe29294e8, L:/**************:13153 ! R:/**************:8091]
2025-07-22 17:05:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe29294e8, L:/**************:13153 ! R:/**************:8091]
2025-07-22 17:05:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe29294e8, L:/**************:13153 ! R:/**************:8091]
2025-07-22 17:05:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe29294e8, L:/**************:13153 ! R:/**************:8091]) will closed
2025-07-22 17:05:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe29294e8, L:/**************:13153 ! R:/**************:8091]) will closed
2025-07-22 17:05:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x50794023, L:/**************:13159 ! R:/**************:8091]
2025-07-22 17:05:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x50794023, L:/**************:13159 ! R:/**************:8091]
2025-07-22 17:05:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x50794023, L:/**************:13159 ! R:/**************:8091]
2025-07-22 17:05:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x50794023, L:/**************:13159 ! R:/**************:8091]
2025-07-22 17:05:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x50794023, L:/**************:13159 ! R:/**************:8091]) will closed
2025-07-22 17:05:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x50794023, L:/**************:13159 ! R:/**************:8091]) will closed
2025-07-22 17:05:10 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 17:05:10 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 18460 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 17:05:10 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 17:05:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 17:05:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 17:05:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 17:05:14 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 17:05:15 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 17:05:15 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 17:05:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 17:05:15 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 17:05:15 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 17:05:15 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 17:05:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753175115558
timestamp=1753175115558
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 17:05:15 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2bee44de, L:/**************:14978 - R:/**************:8091]
2025-07-22 17:05:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 92 ms, version:1.7.1,role:TMROLE,channel:[id: 0x2bee44de, L:/**************:14978 - R:/**************:8091]
2025-07-22 17:05:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 17:05:15 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 17:05:15 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 17:05:15 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 17:05:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 17:05:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 17:05:17 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 17:05:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 17:05:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 17:05:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 17:05:18 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@1be7b7de
2025-07-22 17:05:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 17:05:19 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 17:05:19 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 17:05:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 17:05:19 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x9de2dad3, L:/**************:14985 - R:/**************:8091]
2025-07-22 17:05:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x9de2dad3, L:/**************:14985 - R:/**************:8091]
2025-07-22 17:05:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 17:05:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 17:05:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 17:05:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 17:05:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 17:05:21 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 17:05:22 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 17:05:22 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 17:05:22 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 17:05:22 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 17:05:24 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 17:05:24 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 17:05:25 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 17:05:25 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 17:05:25 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 17:05:29 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 21.025 seconds (process running for 22.513)
2025-07-22 17:05:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 17:05:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 17:05:29 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 19:20:53 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[106ms]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:21:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-22 19:21:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[84ms]
2025-07-22 19:21:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:21:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[15ms]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[60ms]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:21:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[64ms]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:24:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:24:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:24:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-22 19:24:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:24:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[21ms]
2025-07-22 19:24:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:24:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[26ms]
2025-07-22 19:24:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:24:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[21ms]
2025-07-22 19:26:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:26:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[22ms]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[66ms]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:27:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[71ms]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[62ms]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:28:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 19:28:22 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 19:28:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2bee44de, L:/**************:14978 ! R:/**************:8091]
2025-07-22 19:28:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2bee44de, L:/**************:14978 ! R:/**************:8091]
2025-07-22 19:28:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2bee44de, L:/**************:14978 ! R:/**************:8091]
2025-07-22 19:28:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2bee44de, L:/**************:14978 ! R:/**************:8091]
2025-07-22 19:28:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2bee44de, L:/**************:14978 ! R:/**************:8091]) will closed
2025-07-22 19:28:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2bee44de, L:/**************:14978 ! R:/**************:8091]) will closed
2025-07-22 19:28:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x9de2dad3, L:/**************:14985 ! R:/**************:8091]
2025-07-22 19:28:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x9de2dad3, L:/**************:14985 ! R:/**************:8091]
2025-07-22 19:28:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x9de2dad3, L:/**************:14985 ! R:/**************:8091]
2025-07-22 19:28:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x9de2dad3, L:/**************:14985 ! R:/**************:8091]
2025-07-22 19:28:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9de2dad3, L:/**************:14985 ! R:/**************:8091]) will closed
2025-07-22 19:28:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9de2dad3, L:/**************:14985 ! R:/**************:8091]) will closed
2025-07-22 19:34:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 19:34:34 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 18628 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 19:34:34 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 19:34:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 19:34:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 19:34:34 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 19:34:37 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 19:34:38 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 19:34:38 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 19:34:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 19:34:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 19:34:38 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 19:34:38 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 19:34:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753184078381
timestamp=1753184078381
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 19:34:38 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x3a9e5c3f, L:/**************:7918 - R:/**************:8091]
2025-07-22 19:34:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 74 ms, version:1.7.1,role:TMROLE,channel:[id: 0x3a9e5c3f, L:/**************:7918 - R:/**************:8091]
2025-07-22 19:34:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 19:34:38 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 19:34:38 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 19:34:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 19:34:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 19:34:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 19:34:39 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 19:34:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 19:34:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 19:34:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 19:34:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@a0d3cc7
2025-07-22 19:34:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 19:34:41 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 19:34:41 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 19:34:41 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 19:34:41 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd09f375d, L:/**************:7925 - R:/**************:8091]
2025-07-22 19:34:41 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xd09f375d, L:/**************:7925 - R:/**************:8091]
2025-07-22 19:34:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 19:34:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 19:34:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:34:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:34:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:34:43 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 19:34:43 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 19:34:43 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 19:34:43 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 19:34:44 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 19:34:45 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 19:34:45 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 19:34:45 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 19:34:45 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 19:34:45 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 19:34:49 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 17.554 seconds (process running for 18.55)
2025-07-22 19:34:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 19:34:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 19:34:49 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 19:34:49 [RMI TCP Connection(4)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[79ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:35:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:35:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[9ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[59ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:35:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[58ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:35:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[59ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 19:36:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 19:50:30 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 19:50:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x3a9e5c3f, L:/**************:7918 ! R:/**************:8091]
2025-07-22 19:50:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x3a9e5c3f, L:/**************:7918 ! R:/**************:8091]
2025-07-22 19:50:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x3a9e5c3f, L:/**************:7918 ! R:/**************:8091]
2025-07-22 19:50:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x3a9e5c3f, L:/**************:7918 ! R:/**************:8091]
2025-07-22 19:50:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a9e5c3f, L:/**************:7918 ! R:/**************:8091]) will closed
2025-07-22 19:50:30 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3a9e5c3f, L:/**************:7918 ! R:/**************:8091]) will closed
2025-07-22 19:50:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd09f375d, L:/**************:7925 ! R:/**************:8091]
2025-07-22 19:50:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd09f375d, L:/**************:7925 ! R:/**************:8091]
2025-07-22 19:50:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd09f375d, L:/**************:7925 ! R:/**************:8091]
2025-07-22 19:50:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd09f375d, L:/**************:7925 ! R:/**************:8091]
2025-07-22 19:50:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd09f375d, L:/**************:7925 ! R:/**************:8091]) will closed
2025-07-22 19:50:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd09f375d, L:/**************:7925 ! R:/**************:8091]) will closed
2025-07-22 19:50:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 19:50:38 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 39248 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 19:50:38 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 19:50:38 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 19:50:38 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 19:50:38 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 19:50:43 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 19:50:43 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 19:50:43 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 19:50:44 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 19:50:44 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 19:50:44 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 19:50:44 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 19:50:44 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753185044455
timestamp=1753185044455
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 19:50:44 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa2398be6, L:/**************:9993 - R:/**************:8091]
2025-07-22 19:50:44 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 95 ms, version:1.7.1,role:TMROLE,channel:[id: 0xa2398be6, L:/**************:9993 - R:/**************:8091]
2025-07-22 19:50:44 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 19:50:44 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 19:50:44 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 19:50:44 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 19:50:44 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 19:50:44 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 19:50:46 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 19:50:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 19:50:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 19:50:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 19:50:48 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@519f1a7d
2025-07-22 19:50:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 19:50:48 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 19:50:48 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 19:50:48 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 19:50:48 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf7aafe29, L:/**************:10002 - R:/**************:8091]
2025-07-22 19:50:48 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0xf7aafe29, L:/**************:10002 - R:/**************:8091]
2025-07-22 19:50:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 19:50:48 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 19:50:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:50:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:50:50 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:50:50 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 19:50:51 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 19:50:51 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 19:50:52 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 19:50:52 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 19:50:54 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 19:50:54 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 19:50:54 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 19:50:54 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 19:50:54 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 19:50:58 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 22.328 seconds (process running for 23.826)
2025-07-22 19:50:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 19:50:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 19:50:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 19:50:59 [RMI TCP Connection(2)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 19:51:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[123ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:51:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:51:17 [XNIO-1 task-2] INFO  c.d.poi.resolver.TemplateResolver - Resolve the document start...
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 19:54:20 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 19:54:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa2398be6, L:/**************:9993 ! R:/**************:8091]
2025-07-22 19:54:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa2398be6, L:/**************:9993 ! R:/**************:8091]
2025-07-22 19:54:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa2398be6, L:/**************:9993 ! R:/**************:8091]
2025-07-22 19:54:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa2398be6, L:/**************:9993 ! R:/**************:8091]
2025-07-22 19:54:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa2398be6, L:/**************:9993 ! R:/**************:8091]) will closed
2025-07-22 19:54:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa2398be6, L:/**************:9993 ! R:/**************:8091]) will closed
2025-07-22 19:54:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf7aafe29, L:/**************:10002 ! R:/**************:8091]
2025-07-22 19:54:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf7aafe29, L:/**************:10002 ! R:/**************:8091]
2025-07-22 19:54:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf7aafe29, L:/**************:10002 ! R:/**************:8091]
2025-07-22 19:54:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf7aafe29, L:/**************:10002 ! R:/**************:8091]
2025-07-22 19:54:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7aafe29, L:/**************:10002 ! R:/**************:8091]) will closed
2025-07-22 19:54:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7aafe29, L:/**************:10002 ! R:/**************:8091]) will closed
2025-07-22 19:54:26 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 19:54:26 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 18832 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 19:54:26 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 19:54:26 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 19:54:26 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 19:54:26 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 19:54:31 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 19:54:31 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 19:54:31 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 19:54:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 19:54:31 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 19:54:31 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 19:54:31 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 19:54:31 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753185271676
timestamp=1753185271676
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 19:54:31 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd8646ec2, L:/**************:10706 - R:/**************:8091]
2025-07-22 19:54:31 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 91 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd8646ec2, L:/**************:10706 - R:/**************:8091]
2025-07-22 19:54:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 19:54:31 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 19:54:31 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 19:54:31 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 19:54:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 19:54:31 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 19:54:33 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 19:54:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 19:54:34 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 19:54:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 19:54:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@61df07d0
2025-07-22 19:54:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 19:54:35 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 19:54:35 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 19:54:35 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 19:54:35 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xc9f54601, L:/**************:10713 - R:/**************:8091]
2025-07-22 19:54:35 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xc9f54601, L:/**************:10713 - R:/**************:8091]
2025-07-22 19:54:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 19:54:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 19:54:36 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:54:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:54:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:54:37 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 19:54:38 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 19:54:38 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 19:54:39 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 19:54:39 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 19:54:41 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 19:54:41 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 19:54:41 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 19:54:41 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 19:54:41 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 19:54:45 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 22.191 seconds (process running for 23.662)
2025-07-22 19:54:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 19:54:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 19:54:46 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 19:54:46 [RMI TCP Connection(1)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[119ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:55:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:55:04 [XNIO-1 task-2] INFO  c.d.poi.resolver.TemplateResolver - Resolve the document start...
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[67ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 19:57:30 [XNIO-1 task-2] INFO  c.d.poi.resolver.TemplateResolver - Resolve the document start...
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 19:58:57 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 19:58:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd8646ec2, L:/**************:10706 ! R:/**************:8091]
2025-07-22 19:58:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd8646ec2, L:/**************:10706 ! R:/**************:8091]
2025-07-22 19:58:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd8646ec2, L:/**************:10706 ! R:/**************:8091]
2025-07-22 19:58:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd8646ec2, L:/**************:10706 ! R:/**************:8091]
2025-07-22 19:58:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd8646ec2, L:/**************:10706 ! R:/**************:8091]) will closed
2025-07-22 19:58:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd8646ec2, L:/**************:10706 ! R:/**************:8091]) will closed
2025-07-22 19:58:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xc9f54601, L:/**************:10713 ! R:/**************:8091]
2025-07-22 19:58:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xc9f54601, L:/**************:10713 ! R:/**************:8091]
2025-07-22 19:58:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc9f54601, L:/**************:10713 ! R:/**************:8091]
2025-07-22 19:58:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc9f54601, L:/**************:10713 ! R:/**************:8091]
2025-07-22 19:58:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc9f54601, L:/**************:10713 ! R:/**************:8091]) will closed
2025-07-22 19:58:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc9f54601, L:/**************:10713 ! R:/**************:8091]) will closed
2025-07-22 19:59:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 19:59:02 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 39376 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 19:59:02 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 19:59:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 19:59:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 19:59:02 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 19:59:07 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 19:59:07 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 19:59:07 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 19:59:07 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 19:59:08 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 19:59:08 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 19:59:08 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 19:59:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753185548254
timestamp=1753185548254
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 19:59:08 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf1b0c463, L:/**************:11530 - R:/**************:8091]
2025-07-22 19:59:08 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 88 ms, version:1.7.1,role:TMROLE,channel:[id: 0xf1b0c463, L:/**************:11530 - R:/**************:8091]
2025-07-22 19:59:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 19:59:08 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 19:59:08 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 19:59:08 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 19:59:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 19:59:08 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 19:59:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 19:59:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 19:59:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 19:59:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 19:59:11 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@56e0e953
2025-07-22 19:59:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 19:59:12 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 19:59:12 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 19:59:12 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 19:59:12 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x020c5347, L:/**************:11544 - R:/**************:8091]
2025-07-22 19:59:12 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x020c5347, L:/**************:11544 - R:/**************:8091]
2025-07-22 19:59:12 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 19:59:12 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 19:59:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:59:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:59:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 19:59:15 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 19:59:16 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 19:59:16 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 19:59:16 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 19:59:17 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 19:59:19 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 19:59:19 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 19:59:19 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 19:59:19 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 19:59:19 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 19:59:23 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 23.026 seconds (process running for 24.481)
2025-07-22 19:59:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 19:59:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 19:59:23 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 19:59:23 [RMI TCP Connection(4)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 20:09:06 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-22 20:09:06 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-22 20:09:06 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 20:09:06 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 20:09:06 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-22 20:09:06 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-22 20:09:06 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-22 20:09:07 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-22 20:09:07 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-22 20:09:07 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-22 20:09:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf1b0c463, L:/**************:11530 ! R:/**************:8091]
2025-07-22 20:09:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf1b0c463, L:/**************:11530 ! R:/**************:8091]
2025-07-22 20:09:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf1b0c463, L:/**************:11530 ! R:/**************:8091]
2025-07-22 20:09:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf1b0c463, L:/**************:11530 ! R:/**************:8091]
2025-07-22 20:09:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1b0c463, L:/**************:11530 ! R:/**************:8091]) will closed
2025-07-22 20:09:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf1b0c463, L:/**************:11530 ! R:/**************:8091]) will closed
2025-07-22 20:09:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x020c5347, L:/**************:11544 ! R:/**************:8091]
2025-07-22 20:09:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x020c5347, L:/**************:11544 ! R:/**************:8091]
2025-07-22 20:09:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x020c5347, L:/**************:11544 ! R:/**************:8091]
2025-07-22 20:09:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x020c5347, L:/**************:11544 ! R:/**************:8091]
2025-07-22 20:09:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x020c5347, L:/**************:11544 ! R:/**************:8091]) will closed
2025-07-22 20:09:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x020c5347, L:/**************:11544 ! R:/**************:8091]) will closed
2025-07-22 20:09:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 20:09:15 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 1288 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 20:09:15 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-22 20:09:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-22 20:09:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-22 20:09:15 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 20:09:20 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-22 20:09:20 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-22 20:09:20 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-22 20:09:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-22 20:09:20 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 20:09:20 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-22 20:09:20 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 20:09:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753186160608
timestamp=1753186160608
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 20:09:20 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf9d6d6c6, L:/**************:13356 - R:/**************:8091]
2025-07-22 20:09:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 101 ms, version:1.7.1,role:TMROLE,channel:[id: 0xf9d6d6c6, L:/**************:13356 - R:/**************:8091]
2025-07-22 20:09:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 20:09:20 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-22 20:09:20 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-22 20:09:20 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-22 20:09:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-22 20:09:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-22 20:09:22 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-22 20:09:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-22 20:09:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-22 20:09:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-22 20:09:24 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6f475ede
2025-07-22 20:09:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-22 20:09:24 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 20:09:24 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 20:09:24 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 20:09:24 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x85e10c94, L:/**************:13366 - R:/**************:8091]
2025-07-22 20:09:24 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x85e10c94, L:/**************:13366 - R:/**************:8091]
2025-07-22 20:09:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-22 20:09:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-22 20:09:26 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 20:09:26 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 20:09:26 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-22 20:09:27 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 20:09:28 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 20:09:28 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 20:09:28 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 20:09:28 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 20:09:31 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-22 20:09:31 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-22 20:09:31 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-22 20:09:31 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-22 20:09:31 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement **************:9401 register finished
2025-07-22 20:09:35 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 22.35 seconds (process running for 23.596)
2025-07-22 20:09:35 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-22 20:09:35 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 20:09:35 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-22 20:09:36 [RMI TCP Connection(5)-**************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[145ms]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:09:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:09:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:09:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:09:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:09:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:09:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 20:09:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[21ms]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[81ms]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[68ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 20:12:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[82ms]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:41 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 20:12:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[61ms]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:13:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[67ms]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[65ms]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:13:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-22 20:15:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[76ms]
2025-07-22 20:15:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:15:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:15:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:15:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:15:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:15:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[75ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 20:15:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[52ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 20:15:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-22 20:15:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[75ms]
2025-07-22 20:15:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:15:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[68ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[36ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 20:17:06 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[70ms]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 20:17:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[71ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 20:17:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[75ms]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[49ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-22 20:17:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-22 20:17:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[75ms]
2025-07-22 20:17:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-22 20:17:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:17:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-22 20:17:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-22 20:17:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-22 20:17:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-22 20:17:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf9d6d6c6, L:/**************:13356 - R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x85e10c94, L:/**************:13366 - R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xf9d6d6c6, L:/**************:13356 - R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x85e10c94, L:/**************:13366 - R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf9d6d6c6, L:/**************:13356 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x85e10c94, L:/**************:13366 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf9d6d6c6, L:/**************:13356 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x85e10c94, L:/**************:13366 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf9d6d6c6, L:/**************:13356 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x85e10c94, L:/**************:13366 ! R:/**************:8091]
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf9d6d6c6, L:/**************:13356 ! R:/**************:8091]) will closed
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x85e10c94, L:/**************:13366 ! R:/**************:8091]) will closed
2025-07-22 20:19:15 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf9d6d6c6, L:/**************:13356 ! R:/**************:8091]) will closed
2025-07-22 20:19:15 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x85e10c94, L:/**************:13366 ! R:/**************:8091]) will closed
2025-07-22 20:19:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 20:19:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:**************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,**************,1753186760385
timestamp=1753186760385
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=**************
'} >
2025-07-22 20:19:20 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to **************:8091
2025-07-22 20:19:20 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-22 20:19:20 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x85e10c94, L:/**************:13366 ! R:/**************:8091]
2025-07-22 20:19:20 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x85e10c94, L:/**************:13366 ! R:/**************:8091]
2025-07-22 20:19:20 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:**************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-22 20:19:22 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd907c417, L:null ! R:/**************:8091]) will closed
2025-07-22 20:19:22 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x099bb4dc, L:null ! R:/**************:8091]) will closed
