2025-07-17 08:45:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-17 08:45:55 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 24868 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-17 08:45:55 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-17 08:45:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-17 08:45:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-17 08:45:55 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-17 08:46:02 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-17 08:46:02 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-17 08:46:02 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-17 08:46:02 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-17 08:46:03 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 08:46:03 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-17 08:46:03 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 08:46:03 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752713163319
timestamp=1752713163319
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 08:46:03 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xef11c0be, L:/*************:4121 - R:/*************:8091]
2025-07-17 08:46:03 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 181 ms, version:1.7.1,role:TMROLE,channel:[id: 0xef11c0be, L:/*************:4121 - R:/*************:8091]
2025-07-17 08:46:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 08:46:03 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-17 08:46:03 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-17 08:46:03 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 08:46:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 08:46:03 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-17 08:46:05 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-17 08:46:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-17 08:46:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-17 08:46:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-17 08:46:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@74dde921
2025-07-17 08:46:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-17 08:46:07 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 08:46:07 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 08:46:07 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 08:46:07 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xc3cdc2ad, L:/*************:4218 - R:/*************:8091]
2025-07-17 08:46:07 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 11 ms, version:1.7.1,role:RMROLE,channel:[id: 0xc3cdc2ad, L:/*************:4218 - R:/*************:8091]
2025-07-17 08:46:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-17 08:46:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-17 08:46:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 08:46:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 08:46:09 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 08:46:10 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-17 08:46:11 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-17 08:46:11 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-17 08:46:11 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-17 08:46:11 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-17 08:46:13 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-17 08:46:13 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-17 08:46:13 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-17 08:46:13 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-17 08:46:13 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-17 08:46:17 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 25.619 seconds (process running for 26.788)
2025-07-17 08:46:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-17 08:46:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-17 08:46:17 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-17 08:46:18 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17 08:47:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:47:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-17 08:47:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:47:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[78ms]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 08:48:00 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[51ms]
2025-07-17 08:48:00 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[54ms]
2025-07-17 08:48:00 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:00 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[59ms]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[21ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:48:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:49:02 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*******************************************', saveDays=7, branchType=AT}
2025-07-17 08:52:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:52:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:52:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 08:52:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[70ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[30ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[4ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[29ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[4ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:52:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:53:02 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:53:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[83ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[28ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[5ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[24ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[7ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:03 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-17 08:53:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 08:53:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[51ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[6ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:53:21 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 08:56:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[65ms]
2025-07-17 08:56:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[54ms]
2025-07-17 08:56:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 08:56:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[66ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[25ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[5ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[61ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[69ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[90ms]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:56:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[61ms]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 08:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[66ms]
2025-07-17 08:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-17 08:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 08:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[50ms]
2025-07-17 08:57:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[31ms]
2025-07-17 08:57:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[65ms]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 08:57:15 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 08:57:15 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[230ms]
2025-07-17 08:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-17 08:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 08:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 08:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 08:57:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 08:57:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[21ms]
2025-07-17 08:57:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-17 08:57:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 08:57:28 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 08:57:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 08:57:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[119ms]
2025-07-17 08:57:30 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 11:16:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[80ms]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:16:24 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 11:16:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[57ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:16:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[71ms]
2025-07-17 11:16:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-17 11:16:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[29ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:16:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-17 11:18:20 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-17 11:18:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xef11c0be, L:/*************:4121 ! R:/*************:8091]
2025-07-17 11:18:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xef11c0be, L:/*************:4121 ! R:/*************:8091]
2025-07-17 11:18:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xef11c0be, L:/*************:4121 ! R:/*************:8091]
2025-07-17 11:18:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xef11c0be, L:/*************:4121 ! R:/*************:8091]
2025-07-17 11:18:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xef11c0be, L:/*************:4121 ! R:/*************:8091]) will closed
2025-07-17 11:18:20 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xef11c0be, L:/*************:4121 ! R:/*************:8091]) will closed
2025-07-17 11:18:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xc3cdc2ad, L:/*************:4218 ! R:/*************:8091]
2025-07-17 11:18:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xc3cdc2ad, L:/*************:4218 ! R:/*************:8091]
2025-07-17 11:18:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc3cdc2ad, L:/*************:4218 ! R:/*************:8091]
2025-07-17 11:18:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc3cdc2ad, L:/*************:4218 ! R:/*************:8091]
2025-07-17 11:18:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc3cdc2ad, L:/*************:4218 ! R:/*************:8091]) will closed
2025-07-17 11:18:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc3cdc2ad, L:/*************:4218 ! R:/*************:8091]) will closed
2025-07-17 11:18:30 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-17 11:18:30 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 32816 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-17 11:18:30 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-17 11:18:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-17 11:18:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-17 11:18:30 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-17 11:18:34 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-17 11:18:34 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-17 11:18:34 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-17 11:18:34 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-17 11:18:34 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 11:18:34 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-17 11:18:35 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 11:18:35 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752722315176
timestamp=1752722315176
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 11:18:35 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xfe51ba6b, L:/*************:14174 - R:/*************:8091]
2025-07-17 11:18:35 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 104 ms, version:1.7.1,role:TMROLE,channel:[id: 0xfe51ba6b, L:/*************:14174 - R:/*************:8091]
2025-07-17 11:18:35 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 11:18:35 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-17 11:18:35 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-17 11:18:35 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 11:18:35 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 11:18:35 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-17 11:18:36 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-17 11:18:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-17 11:18:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-17 11:18:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-17 11:18:38 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2158d1d1
2025-07-17 11:18:38 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-17 11:18:38 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 11:18:38 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 11:18:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 11:18:38 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x2b616c5a, L:/*************:14179 - R:/*************:8091]
2025-07-17 11:18:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x2b616c5a, L:/*************:14179 - R:/*************:8091]
2025-07-17 11:18:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-17 11:18:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-17 11:18:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 11:18:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 11:18:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 11:18:40 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-17 11:18:41 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-17 11:18:41 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-17 11:18:42 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-17 11:18:42 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-17 11:18:44 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-17 11:18:44 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-17 11:18:44 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-17 11:18:44 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-17 11:18:44 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-17 11:18:48 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 19.939 seconds (process running for 21.491)
2025-07-17 11:18:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-17 11:18:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-17 11:18:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-17 11:18:48 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17 11:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[98ms]
2025-07-17 11:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[84ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[36ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:20:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:18 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:20:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:20:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 11:20:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-17 11:20:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:20:21 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:20:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:20:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[161ms]
2025-07-17 11:20:23 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 11:20:46 [XNIO-1 task-2] INFO  c.y.s.c.RmbWorkorderController - 更新交易类型和渠道金额: RmbDetailChannelBo(rmbdetailId=1945385012276293634, grantType=1, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1, channelAmount=0.12)])
2025-07-17 11:20:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:20:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[42ms]
2025-07-17 11:22:11 [XNIO-1 task-2] INFO  c.y.s.c.RmbWorkorderController - 更新交易类型和渠道金额: RmbDetailChannelBo(rmbdetailId=1945385012276293634, grantType=1, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1, channelAmount=50), RmbDetailChannelBo.ChannelInfo(channelId=2, channelAmount=40), RmbDetailChannelBo.ChannelInfo(channelId=3, channelAmount=10)])
2025-07-17 11:22:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:22:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[59ms]
2025-07-17 11:22:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:22:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[187ms]
2025-07-17 11:22:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:22:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[160ms]
2025-07-17 11:23:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:23:44 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 11:23:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-17 11:23:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 11:23:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:44 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[88ms]
2025-07-17 11:23:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[116ms]
2025-07-17 11:23:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 11:23:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 11:23:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:23:44 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[31ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[104ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[54ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:23:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:23:45 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:23:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:23:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[25ms]
2025-07-17 11:23:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:00 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[74ms]
2025-07-17 11:24:00 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:00 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-17 11:24:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:24:01 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[139ms]
2025-07-17 11:24:01 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 11:24:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:24:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[79ms]
2025-07-17 11:24:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[60ms]
2025-07-17 11:24:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-17 11:24:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[125ms]
2025-07-17 11:24:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:24:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:24:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[29ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:24:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[112ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:24:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:24:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 11:25:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:25:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[61ms]
2025-07-17 11:25:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[67ms]
2025-07-17 11:25:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[105ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:25:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:25:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 11:25:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 11:25:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[87ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:25:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 11:25:39 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 11:25:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[118ms]
2025-07-17 11:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-17 11:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[128ms]
2025-07-17 11:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:25:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[24ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[119ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:25:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 11:25:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:25:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 11:25:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[59ms]
2025-07-17 11:25:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:25:41 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 11:25:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:25:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[103ms]
2025-07-17 11:25:43 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 11:26:19 [XNIO-1 task-4] INFO  c.y.s.c.RmbWorkorderController - 更新交易类型和渠道金额: RmbDetailChannelBo(rmbdetailId=1945385012276293634, grantType=2, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1934638490754846722, channelAmount=50), RmbDetailChannelBo.ChannelInfo(channelId=1934638605565530114, channelAmount=40), RmbDetailChannelBo.ChannelInfo(channelId=1934638650209701889, channelAmount=10)])
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xfe51ba6b, L:/*************:14174 - R:/*************:8091] read idle.
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x2b616c5a, L:/*************:14179 - R:/*************:8091] read idle.
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2b616c5a, L:/*************:14179 - R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfe51ba6b, L:/*************:14174 - R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2b616c5a, L:/*************:14179 - R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe51ba6b, L:/*************:14174 - R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]
2025-07-17 11:26:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe51ba6b, L:/*************:14174 ! R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]) will closed
2025-07-17 11:26:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2b616c5a, L:/*************:14179 ! R:/*************:8091]) will closed
2025-07-17 11:26:38 [XNIO-1 task-3] INFO  c.y.s.c.RmbWorkorderController - 更新交易类型和渠道金额: RmbDetailChannelBo(rmbdetailId=1945385012276293634, grantType=2, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1934638490754846722, channelAmount=50), RmbDetailChannelBo.ChannelInfo(channelId=1934638605565530114, channelAmount=40), RmbDetailChannelBo.ChannelInfo(channelId=1934638650209701889, channelAmount=10)])
2025-07-17 11:26:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:26:40 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[20ms]
2025-07-17 11:26:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:26:40 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 11:26:44 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 11:26:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752722804964
timestamp=1752722804964
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 11:26:44 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xea4b680e, L:/*************:1038 - R:/*************:8091]
2025-07-17 11:26:44 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:TMROLE,channel:[id: 0xea4b680e, L:/*************:1038 - R:/*************:8091]
2025-07-17 11:26:45 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 11:26:45 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 11:26:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 11:26:45 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x394c38df, L:/*************:1043 - R:/*************:8091]
2025-07-17 11:26:45 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 9 ms, version:1.7.1,role:RMROLE,channel:[id: 0x394c38df, L:/*************:1043 - R:/*************:8091]
2025-07-17 11:27:00 [XNIO-1 task-3] INFO  c.y.s.c.RmbWorkorderController - 更新交易类型和渠道金额: RmbDetailChannelBo(rmbdetailId=1945385012276293634, grantType=2, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1934638490754846722, channelAmount=50), RmbDetailChannelBo.ChannelInfo(channelId=1934638605565530114, channelAmount=40), RmbDetailChannelBo.ChannelInfo(channelId=1934638650209701889, channelAmount=10)])
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xea4b680e, L:/*************:1038 - R:/*************:8091] read idle.
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x394c38df, L:/*************:1043 - R:/*************:8091] read idle.
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xea4b680e, L:/*************:1038 - R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xea4b680e, L:/*************:1038 - R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x394c38df, L:/*************:1043 - R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x394c38df, L:/*************:1043 - R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]
2025-07-17 11:27:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xea4b680e, L:/*************:1038 ! R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]) will closed
2025-07-17 11:27:12 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x394c38df, L:/*************:1043 ! R:/*************:8091]) will closed
2025-07-17 11:27:14 [XNIO-1 task-4] INFO  c.y.s.c.RmbWorkorderController - 更新交易类型和渠道金额: RmbDetailChannelBo(rmbdetailId=1945385012276293634, grantType=2, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1934638490754846722, channelAmount=50), RmbDetailChannelBo.ChannelInfo(channelId=1934638605565530114, channelAmount=40), RmbDetailChannelBo.ChannelInfo(channelId=1934638650209701889, channelAmount=10)])
2025-07-17 11:27:15 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 11:27:15 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752722835449
timestamp=1752722835449
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 11:27:15 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x51c0f3db, L:/*************:1088 - R:/*************:8091]
2025-07-17 11:27:15 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0x51c0f3db, L:/*************:1088 - R:/*************:8091]
2025-07-17 11:27:15 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 11:27:15 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 11:27:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 11:27:15 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x28010aae, L:/*************:1089 - R:/*************:8091]
2025-07-17 11:27:15 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:RMROLE,channel:[id: 0x28010aae, L:/*************:1089 - R:/*************:8091]
2025-07-17 11:27:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:27:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[26ms]
2025-07-17 11:27:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:27:16 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-17 11:28:56 [XNIO-1 task-4] INFO  c.y.s.c.RmbWorkorderController - 更新交易类型和渠道金额: RmbDetailChannelBo(rmbdetailId=1945385012276293634, grantType=2, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1934638490754846722, channelAmount=50), RmbDetailChannelBo.ChannelInfo(channelId=1934638605565530114, channelAmount=40), RmbDetailChannelBo.ChannelInfo(channelId=1934638650209701889, channelAmount=10)])
2025-07-17 11:29:03 [XNIO-1 task-4] INFO  c.y.s.s.i.RmbWorkorderDetailServiceImpl - 成功更新工单明细审核状态，明细ID: 1945385012276293634, 交易类型: 2
2025-07-17 11:29:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:29:03 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 11:29:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:29:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[90ms]
2025-07-17 11:29:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[52ms]
2025-07-17 11:29:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[112ms]
2025-07-17 11:29:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 11:29:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:29:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 11:29:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[20ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 11:29:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[78ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[20ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 11:29:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 11:29:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:56 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 11:29:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:29:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 11:29:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-17 11:29:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:29:57 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 11:29:58 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:29:58 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[95ms]
2025-07-17 11:29:58 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 11:30:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:30:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[27ms]
2025-07-17 11:30:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:30:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 11:30:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:30:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-17 11:30:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:30:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-17 11:30:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:30:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[99ms]
2025-07-17 11:30:06 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945372242868768770, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945371377764233218, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 14:37:41 CST 2025, updateTime=Wed Jul 16 14:37:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945374845329588225, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945371377764233218, status=3, opinion=65651, sort=8, doAction=2, createTime=Wed Jul 16 14:48:01 CST 2025, updateTime=Wed Jul 16 14:48:01 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945377871918329857, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945371377764233218, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:00:03 CST 2025, updateTime=Wed Jul 16 15:00:03 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945380845994438658, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945371377764233218, status=3, opinion=i是哪个hi哦是能够i还是弄i供货商, sort=10, doAction=2, createTime=Wed Jul 16 15:11:52 CST 2025, updateTime=Wed Jul 16 15:11:52 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945381100139900930, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945371377764233218, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:12:52 CST 2025, updateTime=Wed Jul 16 15:12:52 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 11:30:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:30:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 11:30:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:30:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-17 11:30:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:30:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-17 11:30:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:30:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[216ms]
2025-07-17 11:30:10 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945367337466687490, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945367202070360066, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 14:18:11 CST 2025, updateTime=Wed Jul 16 14:18:11 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 11:30:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:30:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[34ms]
2025-07-17 11:30:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 11:30:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 11:30:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:30:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 11:30:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 11:30:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[19ms]
2025-07-17 11:30:14 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 11:30:14 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[104ms]
2025-07-17 11:30:14 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945010033894916097, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945007984226656257, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Tue Jul 15 14:38:23 CST 2025, updateTime=Tue Jul 15 14:38:23 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945317858004733953, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945007984226656257, status=3, opinion=null, sort=2, doAction=2, createTime=Wed Jul 16 11:01:34 CST 2025, updateTime=Wed Jul 16 11:01:34 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945322243728379906, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945007984226656257, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 11:19:00 CST 2025, updateTime=Wed Jul 16 11:19:00 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945322722566901761, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945007984226656257, status=3, opinion=第一条与第16条不对, sort=4, doAction=2, createTime=Wed Jul 16 11:20:54 CST 2025, updateTime=Wed Jul 16 11:20:54 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945323781783851010, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945007984226656257, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 11:25:07 CST 2025, updateTime=Wed Jul 16 11:25:07 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945324738521370626, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945007984226656257, status=3, opinion=你这个不对21333333333333333333333333333333336544444444444444444444444444444444444444444444444444444444444, sort=6, doAction=2, createTime=Wed Jul 16 11:28:55 CST 2025, updateTime=Wed Jul 16 11:28:55 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945363613079916546, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945007984226656257, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 14:03:23 CST 2025, updateTime=Wed Jul 16 14:03:23 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x28010aae, L:/*************:1089 - R:/*************:8091] read idle.
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x28010aae, L:/*************:1089 - R:/*************:8091]
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x28010aae, L:/*************:1089 - R:/*************:8091]) will closed
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]) will closed
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]) will closed
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]) will closed
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]) will closed
2025-07-17 12:24:52 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x28010aae, L:/*************:1089 ! R:/*************:8091]) will closed
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x51c0f3db, L:/*************:1088 - R:/*************:8091] read idle.
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x51c0f3db, L:/*************:1088 - R:/*************:8091]
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x51c0f3db, L:/*************:1088 - R:/*************:8091]) will closed
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]) will closed
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]) will closed
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]) will closed
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]) will closed
2025-07-17 12:24:53 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x51c0f3db, L:/*************:1088 ! R:/*************:8091]) will closed
2025-07-17 12:25:02 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 12:25:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752726302256
timestamp=1752726302256
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 12:25:02 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xfdef7de5, L:/*************:6937 - R:/*************:8091]
2025-07-17 12:25:02 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:TMROLE,channel:[id: 0xfdef7de5, L:/*************:6937 - R:/*************:8091]
2025-07-17 12:25:02 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 12:25:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 12:25:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 12:25:02 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x86667dda, L:/*************:6939 - R:/*************:8091]
2025-07-17 12:25:02 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x86667dda, L:/*************:6939 - R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xfdef7de5, L:/*************:6937 - R:/*************:8091] read idle.
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfdef7de5, L:/*************:6937 - R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfdef7de5, L:/*************:6937 - R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfdef7de5, L:/*************:6937 ! R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x86667dda, L:/*************:6939 - R:/*************:8091] read idle.
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x86667dda, L:/*************:6939 - R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x86667dda, L:/*************:6939 - R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]) will closed
2025-07-17 13:28:33 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x86667dda, L:/*************:6939 ! R:/*************:8091]) will closed
2025-07-17 13:28:39 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 13:28:39 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752730119422
timestamp=1752730119422
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 13:28:39 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x68d5391b, L:/*************:12727 - R:/*************:8091]
2025-07-17 13:28:39 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 63 ms, version:1.7.1,role:TMROLE,channel:[id: 0x68d5391b, L:/*************:12727 - R:/*************:8091]
2025-07-17 13:28:39 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 13:28:39 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 13:28:39 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 13:28:40 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xcf484309, L:/*************:12741 - R:/*************:8091]
2025-07-17 13:28:40 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 155 ms, version:1.7.1,role:RMROLE,channel:[id: 0xcf484309, L:/*************:12741 - R:/*************:8091]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[70ms]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:34:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:34:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 13:34:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[74ms]
2025-07-17 13:34:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:34:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:34:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:34:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[126ms]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[27ms]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[144ms]
2025-07-17 13:34:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[170ms]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:34:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[111ms]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:34:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:34:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:34:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:34:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[19ms]
2025-07-17 13:34:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-17 13:34:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:34:18 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:34:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:34:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[136ms]
2025-07-17 13:34:19 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:39:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:39:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:39:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[52ms]
2025-07-17 13:39:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[132ms]
2025-07-17 13:39:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[132ms]
2025-07-17 13:39:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-17 13:39:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 13:39:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:39:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[59ms]
2025-07-17 13:39:04 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[92ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[36ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[197ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:39:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[7ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[62ms]
2025-07-17 13:39:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:39:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[44ms]
2025-07-17 13:39:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-17 13:39:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:39:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:39:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:39:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[110ms]
2025-07-17 13:39:37 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:40:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:40:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:40:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:40:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[120ms]
2025-07-17 13:40:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:40:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:40:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[121ms]
2025-07-17 13:40:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:40:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:40:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[26ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:40:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[106ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:40:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:40:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 13:41:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:41:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[60ms]
2025-07-17 13:41:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:41:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 13:41:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:41:11 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:41:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:41:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[142ms]
2025-07-17 13:41:17 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:43:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:43:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[76ms]
2025-07-17 13:43:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 13:43:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:43:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:43:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:43:55 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-17 13:43:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[131ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[21ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[25ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[96ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 13:43:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:43:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:43:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 13:44:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:44:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[21ms]
2025-07-17 13:44:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-17 13:44:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:44:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:44:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[102ms]
2025-07-17 13:44:11 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:44:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:44:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:44:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[86ms]
2025-07-17 13:44:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:44:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 13:44:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[122ms]
2025-07-17 13:44:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:44:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[21ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[28ms]
2025-07-17 13:44:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[105ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:44:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:44:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:45:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:45:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[39ms]
2025-07-17 13:45:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:45:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 13:45:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:45:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:45:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:45:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[116ms]
2025-07-17 13:45:25 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:48:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:48:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[48ms]
2025-07-17 13:48:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:48:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 13:48:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:48:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:48:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:48:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[118ms]
2025-07-17 13:48:23 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:49:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:49:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[48ms]
2025-07-17 13:49:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:49:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 13:49:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:49:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-17 13:49:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:49:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[117ms]
2025-07-17 13:49:46 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:50:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:50:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-17 13:50:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:50:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[59ms]
2025-07-17 13:50:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[164ms]
2025-07-17 13:50:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:50:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[73ms]
2025-07-17 13:50:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[124ms]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:50:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:50:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[45ms]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[49ms]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[5ms]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:10 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 13:50:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:50:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[40ms]
2025-07-17 13:50:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-17 13:50:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:50:42 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-17 13:50:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:50:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[137ms]
2025-07-17 13:50:43 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:51:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:51:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:51:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 13:51:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[76ms]
2025-07-17 13:51:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:51:32 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[111ms]
2025-07-17 13:51:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:51:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:51:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[21ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[81ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[24ms]
2025-07-17 13:51:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:51:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:51:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:51:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-17 13:51:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:51:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-17 13:51:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:51:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[105ms]
2025-07-17 13:51:36 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:53:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:53:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[49ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[88ms]
2025-07-17 13:53:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[99ms]
2025-07-17 13:53:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:53:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 13:53:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:53:17 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[32ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:53:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[102ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[29ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:53:17 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[5ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:17 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:53:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:53:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[54ms]
2025-07-17 13:53:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:53:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:53:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[118ms]
2025-07-17 13:53:20 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:53:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:53:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[72ms]
2025-07-17 13:53:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-17 13:53:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[97ms]
2025-07-17 13:53:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 13:53:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:53:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[94ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:53:34 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:53:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:53:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 13:53:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-17 13:53:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:53:45 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:53:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:53:46 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[110ms]
2025-07-17 13:53:46 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:54:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:54:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:54:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[61ms]
2025-07-17 13:54:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[56ms]
2025-07-17 13:54:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[103ms]
2025-07-17 13:54:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:54:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:54:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:54:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 13:54:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:13 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[97ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[19ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 13:54:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:54:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:54:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:54:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-17 13:54:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:54:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:54:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[107ms]
2025-07-17 13:54:15 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:54:32 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:54:32 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:54:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[71ms]
2025-07-17 13:54:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-17 13:54:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[98ms]
2025-07-17 13:54:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:54:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:54:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[96ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:54:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:54:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:54:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:54:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[68ms]
2025-07-17 13:54:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:54:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:54:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:54:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[122ms]
2025-07-17 13:54:35 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:55:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:55:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:55:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[70ms]
2025-07-17 13:55:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-17 13:55:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 13:55:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[115ms]
2025-07-17 13:55:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:55:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:55:05 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[27ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[104ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[25ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:55:06 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:06 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:55:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[24ms]
2025-07-17 13:55:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 13:55:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:55:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:55:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[128ms]
2025-07-17 13:55:13 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:55:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:55:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:55:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[99ms]
2025-07-17 13:55:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-17 13:55:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:55:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:55:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[130ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:55:37 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[39ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[30ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:55:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[98ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:55:37 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:55:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:55:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:55:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[46ms]
2025-07-17 13:55:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:55:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:55:39 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:55:39 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[115ms]
2025-07-17 13:55:39 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:56:07 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:56:07 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:56:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:07 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[95ms]
2025-07-17 13:56:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-17 13:56:07 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[100ms]
2025-07-17 13:56:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:56:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:56:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:56:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:56:08 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[79ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[24ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:56:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:08 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 13:56:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:56:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:56:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-17 13:56:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:56:12 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:56:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:56:13 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[137ms]
2025-07-17 13:56:13 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:57:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:57:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[60ms]
2025-07-17 13:57:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[110ms]
2025-07-17 13:57:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[110ms]
2025-07-17 13:57:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-17 13:57:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:57:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 13:57:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:57:50 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[88ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[20ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:57:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[4ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[0ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 13:57:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-17 13:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:57:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:57:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:57:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[117ms]
2025-07-17 13:57:53 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 13:58:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:58:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:58:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 13:58:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[111ms]
2025-07-17 13:58:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-17 13:58:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-17 13:58:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:58:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[34ms]
2025-07-17 13:58:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[193ms]
2025-07-17 13:58:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:43 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[51ms]
2025-07-17 13:58:43 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:58:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 13:58:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:43 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[64ms]
2025-07-17 13:58:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[185ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:58:44 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[83ms]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:58:44 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 13:59:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 13:59:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:59:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[55ms]
2025-07-17 13:59:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-17 13:59:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-17 13:59:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:59:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:59:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[132ms]
2025-07-17 13:59:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[55ms]
2025-07-17 13:59:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:09 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 13:59:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[72ms]
2025-07-17 13:59:09 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:09 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[132ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 13:59:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 13:59:10 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 13:59:10 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 14:00:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:00:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[90ms]
2025-07-17 14:00:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:00:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[60ms]
2025-07-17 14:00:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:00:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:00:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:00:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[108ms]
2025-07-17 14:00:46 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:01:02 [XNIO-1 task-2] INFO  c.y.s.c.RmbWorkorderController - 更新交易类型和渠道金额: RmbDetailChannelBo(rmbdetailId=1945385012276293634, grantType=2, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1934638605565530114, channelAmount=100)])
2025-07-17 14:01:08 [XNIO-1 task-2] INFO  c.y.s.s.i.RmbWorkorderDetailServiceImpl - 成功更新工单明细审核状态，明细ID: 1945385012276293634, 交易类型: 2
2025-07-17 14:01:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 14:01:08 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[29ms]
2025-07-17 14:02:51 [XNIO-1 task-2] INFO  c.y.s.c.RmbWorkorderController - 准备处理工单，ID: ProcessWorkorderBo(workorderId=1945385011085111297, remark=, details=[{detailId=1945385012276293634, grantType=2, channelAmount=0}])
2025-07-17 14:02:51 [XNIO-1 task-2] INFO  c.y.s.s.impl.RmbWorkorderServiceImpl - 费用配置信息: RmbConfig(id=1934635993986338817, transportId=1934261959020875778, shippingId=1934261052682113025, personServicefee=1, personManagefee=10.00, companyServicefee=1.00, companyManagefee=20.00, balance=84304.00, freeze=400.00, maxCredit=5000.00, usedCredit=0.00, remark=第一个服务公司, delFlag=0)
2025-07-17 14:02:51 [XNIO-1 task-2] INFO  c.y.s.s.impl.RmbWorkorderServiceImpl -  总派遣费: 1.00, 总保险费: 20.00
2025-07-17 14:02:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTodoInfoService],MethodName=[updateTodoStatus]
2025-07-17 14:02:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTodoInfoService],MethodName=[updateTodoStatus],SpendTime=[438ms]
2025-07-17 14:02:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:02:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[92ms]
2025-07-17 14:02:52 [XNIO-1 task-2] INFO  c.y.s.s.impl.RmbWorkorderServiceImpl - 修改工单信息：RmbWorkorder(id=1945385011085111297, transportId=1934261959020875778, shippingId=1934261052682113025, createrId=1934265922029981698, processId=null, processTime=Thu Jul 17 14:02:51 CST 2025, salary=100.00, taxes=null, dispatchFee=1.00, securityFee=20.00, freeze=100.00, usedCredit=0.00, remark=, delFlag=null, workorderStatus=6)
2025-07-17 14:02:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 14:02:52 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[50ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[30ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[264ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 14:02:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[77ms]
2025-07-17 14:02:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-17 14:02:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 14:02:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-17 14:02:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:58 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:02:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:02:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:02:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-17 14:02:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:02:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:03:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:03:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[119ms]
2025-07-17 14:03:01 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:03:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:03:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[26ms]
2025-07-17 14:03:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:03:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-17 14:03:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:03:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:03:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:03:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[100ms]
2025-07-17 14:03:14 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:04:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:04:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[67ms]
2025-07-17 14:04:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:04:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-17 14:04:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:04:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 14:04:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:04:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[103ms]
2025-07-17 14:04:34 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 14:09:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:09:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:09:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[86ms]
2025-07-17 14:09:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:09:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:09:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[113ms]
2025-07-17 14:09:19 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[26ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[97ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 14:09:19 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 14:09:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[5ms]
2025-07-17 14:09:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-17 14:09:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:09:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:09:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[119ms]
2025-07-17 14:09:23 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:09:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[20ms]
2025-07-17 14:09:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:09:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-17 14:09:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:09:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:09:30 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[98ms]
2025-07-17 14:09:30 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945372242868768770, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945371377764233218, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 14:37:41 CST 2025, updateTime=Wed Jul 16 14:37:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945374845329588225, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945371377764233218, status=3, opinion=65651, sort=8, doAction=2, createTime=Wed Jul 16 14:48:01 CST 2025, updateTime=Wed Jul 16 14:48:01 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945377871918329857, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945371377764233218, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:00:03 CST 2025, updateTime=Wed Jul 16 15:00:03 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945380845994438658, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945371377764233218, status=3, opinion=i是哪个hi哦是能够i还是弄i供货商, sort=10, doAction=2, createTime=Wed Jul 16 15:11:52 CST 2025, updateTime=Wed Jul 16 15:11:52 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945381100139900930, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945371377764233218, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:12:52 CST 2025, updateTime=Wed Jul 16 15:12:52 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:09:45 [XNIO-1 task-2] INFO  c.y.s.c.RmbWorkorderController - 更新交易类型和渠道金额: RmbDetailChannelBo(rmbdetailId=1945381029344243713, grantType=2, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1934638490754846722, channelAmount=100)])
2025-07-17 14:09:48 [XNIO-1 task-2] INFO  c.y.s.s.i.RmbWorkorderDetailServiceImpl - 成功更新工单明细审核状态，明细ID: 1945381029344243713, 交易类型: 2
2025-07-17 14:09:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 14:09:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[29ms]
2025-07-17 14:09:53 [XNIO-1 task-2] INFO  c.y.s.c.RmbWorkorderController - 准备处理工单，ID: ProcessWorkorderBo(workorderId=1945371377764233218, remark=, details=[{detailId=1945381029344243713, grantType=2, channelAmount=0}])
2025-07-17 14:09:53 [XNIO-1 task-2] INFO  c.y.s.s.impl.RmbWorkorderServiceImpl - 费用配置信息: RmbConfig(id=1934635993986338817, transportId=1934261959020875778, shippingId=1934261052682113025, personServicefee=1, personManagefee=10.00, companyServicefee=1.00, companyManagefee=20.00, balance=84283.00, freeze=421.00, maxCredit=5000.00, usedCredit=0.00, remark=第一个服务公司, delFlag=0)
2025-07-17 14:09:53 [XNIO-1 task-2] INFO  c.y.s.s.impl.RmbWorkorderServiceImpl -  总派遣费: 1.00, 总保险费: 20.00
2025-07-17 14:09:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTodoInfoService],MethodName=[updateTodoStatus]
2025-07-17 14:09:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTodoInfoService],MethodName=[updateTodoStatus],SpendTime=[255ms]
2025-07-17 14:09:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:09:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[89ms]
2025-07-17 14:09:54 [XNIO-1 task-2] INFO  c.y.s.s.impl.RmbWorkorderServiceImpl - 修改工单信息：RmbWorkorder(id=1945371377764233218, transportId=1934261959020875778, shippingId=1934261052682113025, createrId=1934265922029981698, processId=1934259119388987393, processTime=Thu Jul 17 14:09:53 CST 2025, salary=100.00, taxes=null, dispatchFee=1.00, securityFee=20.00, freeze=100.00, usedCredit=0.00, remark=, delFlag=null, workorderStatus=6)
2025-07-17 14:09:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 14:09:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[67ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[21ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[23ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:09:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[26ms]
2025-07-17 14:09:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:09:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[1ms]
2025-07-17 14:09:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-17 14:09:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:09:59 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 14:10:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:10:00 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[101ms]
2025-07-17 14:10:00 [XNIO-1 task-2] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945372242868768770, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945371377764233218, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 14:37:41 CST 2025, updateTime=Wed Jul 16 14:37:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945374845329588225, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945371377764233218, status=3, opinion=65651, sort=8, doAction=2, createTime=Wed Jul 16 14:48:01 CST 2025, updateTime=Wed Jul 16 14:48:01 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945377871918329857, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945371377764233218, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:00:03 CST 2025, updateTime=Wed Jul 16 15:00:03 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945380845994438658, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945371377764233218, status=3, opinion=i是哪个hi哦是能够i还是弄i供货商, sort=10, doAction=2, createTime=Wed Jul 16 15:11:52 CST 2025, updateTime=Wed Jul 16 15:11:52 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945381100139900930, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945371377764233218, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:12:52 CST 2025, updateTime=Wed Jul 16 15:12:52 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:10:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x68d5391b, L:/*************:12727 - R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xcf484309, L:/*************:12741 - R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xcf484309, L:/*************:12741 - R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x68d5391b, L:/*************:12727 - R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x68d5391b, L:/*************:12727 ! R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xcf484309, L:/*************:12741 ! R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x68d5391b, L:/*************:12727 ! R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xcf484309, L:/*************:12741 ! R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x68d5391b, L:/*************:12727 ! R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xcf484309, L:/*************:12741 ! R:/*************:8091]
2025-07-17 14:10:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x68d5391b, L:/*************:12727 ! R:/*************:8091]) will closed
2025-07-17 14:10:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcf484309, L:/*************:12741 ! R:/*************:8091]) will closed
2025-07-17 14:10:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x68d5391b, L:/*************:12727 ! R:/*************:8091]) will closed
2025-07-17 14:10:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcf484309, L:/*************:12741 ! R:/*************:8091]) will closed
2025-07-17 14:31:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-17 14:31:33 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 24132 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-17 14:31:33 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-17 14:31:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-17 14:31:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-17 14:31:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-17 14:31:38 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-17 14:31:38 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-17 14:31:38 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-17 14:31:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-17 14:31:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 14:31:38 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-17 14:31:38 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 14:31:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752733898732
timestamp=1752733898732
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 14:31:38 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x79f8d4f2, L:/*************:2567 - R:/*************:8091]
2025-07-17 14:31:38 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 57 ms, version:1.7.1,role:TMROLE,channel:[id: 0x79f8d4f2, L:/*************:2567 - R:/*************:8091]
2025-07-17 14:31:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 14:31:38 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-17 14:31:38 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-17 14:31:38 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 14:31:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 14:31:38 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-17 14:31:39 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-17 14:31:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-17 14:31:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-17 14:31:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-17 14:31:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@42e74fa3
2025-07-17 14:31:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-17 14:31:41 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 14:31:41 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 14:31:41 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 14:31:41 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa3dcfafe, L:/*************:2590 - R:/*************:8091]
2025-07-17 14:31:41 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xa3dcfafe, L:/*************:2590 - R:/*************:8091]
2025-07-17 14:31:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-17 14:31:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-17 14:31:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 14:31:43 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 14:31:43 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 14:31:43 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-17 14:31:44 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-17 14:31:44 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-17 14:31:44 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-17 14:31:44 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-17 14:31:46 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-17 14:31:46 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-17 14:31:46 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-17 14:31:46 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-17 14:31:46 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-17 14:31:50 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 19.653 seconds (process running for 20.62)
2025-07-17 14:31:50 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-17 14:31:50 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-17 14:31:50 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-17 14:31:50 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[121ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[72ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:32:38 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:32:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:32:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:39 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:32:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[57ms]
2025-07-17 14:32:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-17 14:32:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 14:32:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:47 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 14:32:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:32:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:32:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 14:32:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 14:32:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[69ms]
2025-07-17 14:32:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[110ms]
2025-07-17 14:32:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[42ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[70ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[144ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[46ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-17 14:32:51 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[21ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[34ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:32:51 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 14:34:30 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*******************************************', saveDays=7, branchType=AT}
2025-07-17 14:34:36 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:34:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[159ms]
2025-07-17 14:34:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:34:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[86ms]
2025-07-17 14:34:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:34:37 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 14:34:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:34:38 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[152ms]
2025-07-17 14:34:38 [XNIO-1 task-4] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945367337466687490, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945367202070360066, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 14:18:11 CST 2025, updateTime=Wed Jul 16 14:18:11 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-17 14:39:19 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-17 14:39:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x79f8d4f2, L:/*************:2567 ! R:/*************:8091]
2025-07-17 14:39:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x79f8d4f2, L:/*************:2567 ! R:/*************:8091]
2025-07-17 14:39:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x79f8d4f2, L:/*************:2567 ! R:/*************:8091]
2025-07-17 14:39:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x79f8d4f2, L:/*************:2567 ! R:/*************:8091]
2025-07-17 14:39:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x79f8d4f2, L:/*************:2567 ! R:/*************:8091]) will closed
2025-07-17 14:39:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x79f8d4f2, L:/*************:2567 ! R:/*************:8091]) will closed
2025-07-17 14:39:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa3dcfafe, L:/*************:2590 ! R:/*************:8091]
2025-07-17 14:39:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa3dcfafe, L:/*************:2590 ! R:/*************:8091]
2025-07-17 14:39:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa3dcfafe, L:/*************:2590 ! R:/*************:8091]
2025-07-17 14:39:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa3dcfafe, L:/*************:2590 ! R:/*************:8091]
2025-07-17 14:39:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa3dcfafe, L:/*************:2590 ! R:/*************:8091]) will closed
2025-07-17 14:39:19 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa3dcfafe, L:/*************:2590 ! R:/*************:8091]) will closed
2025-07-17 14:39:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-17 14:39:28 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 22844 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-17 14:39:28 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-17 14:39:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-17 14:39:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-17 14:39:28 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-17 14:39:32 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-17 14:39:32 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-17 14:39:32 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-17 14:39:32 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-17 14:39:32 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 14:39:32 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-17 14:39:32 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 14:39:32 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752734372800
timestamp=1752734372800
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 14:39:33 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xaa996abb, L:/*************:3465 - R:/*************:8091]
2025-07-17 14:39:33 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 89 ms, version:1.7.1,role:TMROLE,channel:[id: 0xaa996abb, L:/*************:3465 - R:/*************:8091]
2025-07-17 14:39:33 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 14:39:33 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-17 14:39:33 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-17 14:39:33 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 14:39:33 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 14:39:33 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-17 14:39:34 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-17 14:39:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-17 14:39:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-17 14:39:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-17 14:39:36 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6bd4d24b
2025-07-17 14:39:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-17 14:39:36 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 14:39:36 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 14:39:36 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 14:39:36 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x41b829a1, L:/*************:3475 - R:/*************:8091]
2025-07-17 14:39:36 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x41b829a1, L:/*************:3475 - R:/*************:8091]
2025-07-17 14:39:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-17 14:39:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-17 14:39:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 14:39:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 14:39:37 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 14:39:38 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-17 14:39:39 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-17 14:39:39 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-17 14:39:39 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-17 14:39:39 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-17 14:39:41 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-17 14:39:41 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-17 14:39:41 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-17 14:39:41 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-17 14:39:42 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-17 14:39:45 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 19.633 seconds (process running for 21.179)
2025-07-17 14:39:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-17 14:39:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-17 14:39:45 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-17 14:39:46 [RMI TCP Connection(3)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17 14:44:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[64ms]
2025-07-17 14:44:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:44:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 14:44:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-17 14:44:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 14:44:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:44:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[65ms]
2025-07-17 14:44:22 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[96ms]
2025-07-17 14:44:23 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:44:23 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[87ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[27ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[22ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-17 14:44:23 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 14:44:23 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[18ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[4ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:44:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:44:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 14:44:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-17 14:44:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-17 14:44:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:44:25 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[143ms]
2025-07-17 14:44:25 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945385080207241218, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945385011085111297, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 15:28:41 CST 2025, updateTime=Wed Jul 16 15:28:41 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:44:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:44:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[26ms]
2025-07-17 14:44:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:44:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:44:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-17 14:44:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:44:29 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-17 14:44:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:44:30 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[129ms]
2025-07-17 14:44:30 [XNIO-1 task-3] INFO  c.y.s.s.i.AuditingWorkorderInfoServiceImpl - 获取审批记录：[AuditingWorkorderInfoVo(id=1945010033894916097, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945007984226656257, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Tue Jul 15 14:38:23 CST 2025, updateTime=Tue Jul 15 14:38:23 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945317858004733953, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945007984226656257, status=3, opinion=null, sort=2, doAction=2, createTime=Wed Jul 16 11:01:34 CST 2025, updateTime=Wed Jul 16 11:01:34 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945322243728379906, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945007984226656257, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 11:19:00 CST 2025, updateTime=Wed Jul 16 11:19:00 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945322722566901761, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945007984226656257, status=3, opinion=第一条与第16条不对, sort=4, doAction=2, createTime=Wed Jul 16 11:20:54 CST 2025, updateTime=Wed Jul 16 11:20:54 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945323781783851010, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945007984226656257, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 11:25:07 CST 2025, updateTime=Wed Jul 16 11:25:07 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945324738521370626, userId=1934259119388987393, userName=xhry, companyName=null, businessId=1945007984226656257, status=3, opinion=你这个不对21333333333333333333333333333333336544444444444444444444444444444444444444444444444444444444444, sort=6, doAction=2, createTime=Wed Jul 16 11:28:55 CST 2025, updateTime=Wed Jul 16 11:28:55 CST 2025, remark=null, businessType=1, createDept=1934262875681820673), AuditingWorkorderInfoVo(id=1945363613079916546, userId=null, userName=青岛孚润德船舶管理有限公司负责人, companyName=null, businessId=1945007984226656257, status=2, opinion=船舶公司已审批通过, sort=2, doAction=2, createTime=Wed Jul 16 14:03:23 CST 2025, updateTime=Wed Jul 16 14:03:23 CST 2025, remark=null, businessType=1, createDept=1934262875681820673)]
2025-07-17 14:44:53 [XNIO-1 task-3] INFO  c.y.s.c.RmbWorkorderDetailController - 修改工作单明细核对状态:RmbDetailChannelBo(rmbdetailId=1945326156397449217, grantType=1, channels=[RmbDetailChannelBo.ChannelInfo(channelId=1934638490754846722, channelAmount=100)])
2025-07-17 14:44:56 [XNIO-1 task-3] INFO  c.y.s.s.i.RmbWorkorderDetailServiceImpl - 成功更新工单明细审核状态，明细ID: 1945326156397449217, 交易类型: 1
2025-07-17 14:44:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 14:44:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[23ms]
2025-07-17 14:45:03 [XNIO-1 task-3] INFO  c.y.s.c.RmbWorkorderController - 准备处理工单，ID: ProcessWorkorderBo(workorderId=1945007984226656257, remark=, details=[{detailId=1945326156397449217, grantType=1, channelAmount=0}])
2025-07-17 14:45:03 [XNIO-1 task-3] INFO  c.y.s.s.impl.RmbWorkorderServiceImpl - 费用配置信息: RmbConfig(id=1934635993986338817, transportId=1934261959020875778, shippingId=1934261052682113025, personServicefee=1, personManagefee=10.00, companyServicefee=1.00, companyManagefee=20.00, balance=84262.00, freeze=442.00, maxCredit=5000.00, usedCredit=0.00, remark=第一个服务公司, delFlag=0)
2025-07-17 14:45:03 [XNIO-1 task-3] INFO  c.y.s.s.impl.RmbWorkorderServiceImpl -  总派遣费: 1.00, 总保险费: 10.00
2025-07-17 14:45:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteTodoInfoService],MethodName=[updateTodoStatus]
2025-07-17 14:45:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteTodoInfoService],MethodName=[updateTodoStatus],SpendTime=[265ms]
2025-07-17 14:45:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-17 14:45:03 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[86ms]
2025-07-17 14:45:03 [XNIO-1 task-3] INFO  c.y.s.s.impl.RmbWorkorderServiceImpl - 修改工单信息：RmbWorkorder(id=1945007984226656257, transportId=1934261959020875778, shippingId=1934261052682113025, createrId=1934265922029981698, processId=1934259119388987393, processTime=Thu Jul 17 14:45:03 CST 2025, salary=100.00, taxes=null, dispatchFee=1.00, securityFee=10.00, freeze=100.00, usedCredit=0.00, remark=, delFlag=null, workorderStatus=6)
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[3ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[64ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[32ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[26ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[3ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectUserNameById],SpendTime=[2ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-17 14:45:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-17 15:30:03 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-17 15:30:03 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-17 15:30:03 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-17 15:30:03 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-17 15:30:04 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-17 15:30:04 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-17 15:30:04 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-17 15:30:04 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-17 15:30:04 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-17 15:30:04 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-17 15:30:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xaa996abb, L:/*************:3465 ! R:/*************:8091]
2025-07-17 15:30:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xaa996abb, L:/*************:3465 ! R:/*************:8091]
2025-07-17 15:30:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xaa996abb, L:/*************:3465 ! R:/*************:8091]
2025-07-17 15:30:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xaa996abb, L:/*************:3465 ! R:/*************:8091]
2025-07-17 15:30:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaa996abb, L:/*************:3465 ! R:/*************:8091]) will closed
2025-07-17 15:30:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaa996abb, L:/*************:3465 ! R:/*************:8091]) will closed
2025-07-17 15:30:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x41b829a1, L:/*************:3475 ! R:/*************:8091]
2025-07-17 15:30:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x41b829a1, L:/*************:3475 ! R:/*************:8091]
2025-07-17 15:30:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x41b829a1, L:/*************:3475 ! R:/*************:8091]
2025-07-17 15:30:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x41b829a1, L:/*************:3475 ! R:/*************:8091]
2025-07-17 15:30:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x41b829a1, L:/*************:3475 ! R:/*************:8091]) will closed
2025-07-17 15:30:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x41b829a1, L:/*************:3475 ! R:/*************:8091]) will closed
2025-07-17 15:30:09 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-17 15:30:09 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 31728 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-17 15:30:09 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-17 15:30:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-17 15:30:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-17 15:30:09 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-17 15:30:15 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-17 15:30:15 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-17 15:30:15 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-17 15:30:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-17 15:30:15 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 15:30:15 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-17 15:30:15 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 15:30:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752737415886
timestamp=1752737415886
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 15:30:16 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4b258f39, L:/*************:6492 - R:/*************:8091]
2025-07-17 15:30:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 179 ms, version:1.7.1,role:TMROLE,channel:[id: 0x4b258f39, L:/*************:6492 - R:/*************:8091]
2025-07-17 15:30:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 15:30:16 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-17 15:30:16 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-17 15:30:16 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-17 15:30:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-17 15:30:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-17 15:30:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-17 15:30:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-17 15:30:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-17 15:30:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-17 15:30:20 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@618cfd1
2025-07-17 15:30:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-17 15:30:20 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 15:30:20 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 15:30:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 15:30:20 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe2b5417f, L:/*************:6502 - R:/*************:8091]
2025-07-17 15:30:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 9 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe2b5417f, L:/*************:6502 - R:/*************:8091]
2025-07-17 15:30:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-17 15:30:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-17 15:30:22 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 15:30:22 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 15:30:22 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-17 15:30:23 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-17 15:30:24 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-17 15:30:24 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-17 15:30:24 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-17 15:30:24 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-17 15:30:26 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-17 15:30:26 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-17 15:30:26 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-17 15:30:26 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-17 15:30:27 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-17 15:30:31 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 24.178 seconds (process running for 25.55)
2025-07-17 15:30:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-17 15:30:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-17 15:30:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-17 15:30:31 [RMI TCP Connection(5)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-17 18:28:46 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0xe2b5417f, L:/*************:6502 - R:/*************:8091] read idle.
2025-07-17 18:28:46 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel [id: 0x4b258f39, L:/*************:6492 - R:/*************:8091] read idle.
2025-07-17 18:28:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4b258f39, L:/*************:6492 - R:/*************:8091]
2025-07-17 18:28:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe2b5417f, L:/*************:6502 - R:/*************:8091]
2025-07-17 18:28:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2b5417f, L:/*************:6502 - R:/*************:8091]) will closed
2025-07-17 18:28:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4b258f39, L:/*************:6492 - R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4b258f39, L:/*************:6492 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe2b5417f, L:/*************:6502 ! R:/*************:8091]) will closed
2025-07-17 18:28:48 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 18:28:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752748128650
timestamp=1752748128650
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 18:28:48 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xf00e6e3c, L:/*************:10375 - R:/*************:8091]
2025-07-17 18:28:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 164 ms, version:1.7.1,role:TMROLE,channel:[id: 0xf00e6e3c, L:/*************:10375 - R:/*************:8091]
2025-07-17 18:28:49 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 18:28:49 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 18:28:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-17 18:28:49 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe036df01, L:/*************:10502 - R:/*************:8091]
2025-07-17 18:28:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 89 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe036df01, L:/*************:10502 - R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xf00e6e3c, L:/*************:10375 - R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe036df01, L:/*************:10502 - R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xf00e6e3c, L:/*************:10375 - R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xe036df01, L:/*************:10502 - R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xf00e6e3c, L:/*************:10375 ! R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe036df01, L:/*************:10502 ! R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xf00e6e3c, L:/*************:10375 ! R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe036df01, L:/*************:10502 ! R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xf00e6e3c, L:/*************:10375 ! R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe036df01, L:/*************:10502 ! R:/*************:8091]
2025-07-17 18:30:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf00e6e3c, L:/*************:10375 ! R:/*************:8091]) will closed
2025-07-17 18:30:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe036df01, L:/*************:10502 ! R:/*************:8091]) will closed
2025-07-17 18:30:45 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf00e6e3c, L:/*************:10375 ! R:/*************:8091]) will closed
2025-07-17 18:30:45 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe036df01, L:/*************:10502 ! R:/*************:8091]) will closed
2025-07-17 18:30:48 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 18:30:48 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1752748248664
timestamp=1752748248664
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-17 18:30:49 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-17 18:30:49 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-17 18:30:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe036df01, L:/*************:10502 ! R:/*************:8091]
2025-07-17 18:30:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe036df01, L:/*************:10502 ! R:/*************:8091]
2025-07-17 18:30:49 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
