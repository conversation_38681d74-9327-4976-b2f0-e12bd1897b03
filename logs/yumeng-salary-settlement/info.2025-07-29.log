2025-07-29 09:20:34 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 09:20:35 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 31300 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 09:20:35 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-29 09:20:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 09:20:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-29 09:20:35 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 09:20:40 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 09:20:40 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 09:20:40 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 09:20:40 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 09:20:40 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 09:20:40 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 09:20:40 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 09:20:40 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753752040730
timestamp=1753752040730
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 09:20:41 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x6f00bc15, L:/***************:13926 - R:/***************:8091]
2025-07-29 09:20:41 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 86 ms, version:1.7.1,role:TMROLE,channel:[id: 0x6f00bc15, L:/***************:13926 - R:/***************:8091]
2025-07-29 09:20:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 09:20:41 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 09:20:41 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 09:20:41 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 09:20:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 09:20:41 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 09:20:42 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 09:20:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 09:20:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 09:20:43 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 09:20:44 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@6383ea52
2025-07-29 09:20:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 09:20:44 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 09:20:44 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 09:20:44 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 09:20:44 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x9f91f0a4, L:/***************:13969 - R:/***************:8091]
2025-07-29 09:20:44 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x9f91f0a4, L:/***************:13969 - R:/***************:8091]
2025-07-29 09:20:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 09:20:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 09:20:46 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 09:20:47 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 09:20:47 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 09:20:49 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 09:20:50 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 09:20:50 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 09:20:51 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 09:20:51 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 09:20:54 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 09:20:54 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 09:20:54 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 09:20:54 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 09:20:54 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement ***************:9401 register finished
2025-07-29 09:20:59 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 26.645 seconds (process running for 27.769)
2025-07-29 09:20:59 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 09:20:59 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 09:20:59 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-29 09:20:59 [RMI TCP Connection(1)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 09:23:19 [rpcDispatch_RMROLE_1_1_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*******************************************', saveDays=7, branchType=AT}
2025-07-29 10:14:04 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x6f00bc15, L:/***************:13926 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x9f91f0a4, L:/***************:13969 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x6f00bc15, L:/***************:13926 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x9f91f0a4, L:/***************:13969 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x9f91f0a4, L:/***************:13969 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x6f00bc15, L:/***************:13926 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x9f91f0a4, L:/***************:13969 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x6f00bc15, L:/***************:13926 ! R:/***************:8091]
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6f00bc15, L:/***************:13926 ! R:/***************:8091]) will closed
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9f91f0a4, L:/***************:13969 ! R:/***************:8091]) will closed
2025-07-29 10:14:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6f00bc15, L:/***************:13926 ! R:/***************:8091]) will closed
2025-07-29 10:14:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9f91f0a4, L:/***************:13969 ! R:/***************:8091]) will closed
2025-07-29 10:14:10 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 10:14:10 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753755250394
timestamp=1753755250394
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 10:14:11 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 10:14:11 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 10:14:11 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 10:14:12 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x958037a7, L:null ! R:/***************:8091]) will closed
2025-07-29 10:14:13 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcd46bf34, L:null ! R:/***************:8091]) will closed
2025-07-29 10:14:20 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 10:14:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753755260385
timestamp=1753755260385
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 10:14:20 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x5cd9cd24, L:/***************:9144 - R:/***************:8091]
2025-07-29 10:14:20 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:TMROLE,channel:[id: 0x5cd9cd24, L:/***************:9144 - R:/***************:8091]
2025-07-29 10:14:21 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 10:14:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 10:14:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 10:14:21 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xe64314f4, L:/***************:9148 - R:/***************:8091]
2025-07-29 10:14:21 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 25 ms, version:1.7.1,role:RMROLE,channel:[id: 0xe64314f4, L:/***************:9148 - R:/***************:8091]
2025-07-29 10:17:19 [rpcDispatch_RMROLE_1_2_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*******************************************', saveDays=7, branchType=AT}
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x5cd9cd24, L:/***************:9144 - R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x5cd9cd24, L:/***************:9144 - R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x5cd9cd24, L:/***************:9144 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x5cd9cd24, L:/***************:9144 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x5cd9cd24, L:/***************:9144 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5cd9cd24, L:/***************:9144 ! R:/***************:8091]) will closed
2025-07-29 13:28:41 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5cd9cd24, L:/***************:9144 ! R:/***************:8091]) will closed
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xe64314f4, L:/***************:9148 - R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xe64314f4, L:/***************:9148 - R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xe64314f4, L:/***************:9148 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe64314f4, L:/***************:9148 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe64314f4, L:/***************:9148 ! R:/***************:8091]
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe64314f4, L:/***************:9148 ! R:/***************:8091]) will closed
2025-07-29 13:28:41 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe64314f4, L:/***************:9148 ! R:/***************:8091]) will closed
2025-07-29 13:28:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:28:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753766927218
timestamp=1753766927218
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:28:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:28:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:28:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xe64314f4, L:/***************:9148 ! R:/***************:8091]
2025-07-29 13:28:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xe64314f4, L:/***************:9148 ! R:/***************:8091]
2025-07-29 13:28:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:28:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0a647584, L:null ! R:/***************:8091]) will closed
2025-07-29 13:28:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:28:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753766937221
timestamp=1753766937221
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:28:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:28:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:28:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:28:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x80531759, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:07 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:07 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753766947223
timestamp=1753766947223
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:29:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xe72c96ee, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xf7d735f0, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:07 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:07 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:29:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:29:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753766957224
timestamp=1753766957224
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:29:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x03129bb3, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:17 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:29:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:29:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xdfc14680, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x244d7fcb, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753766967226
timestamp=1753766967226
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:29:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x270e98c6, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:29:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:29:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753766977227
timestamp=1753766977227
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:29:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x74d23127, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:29:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:29:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3cd8163e, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753766987229
timestamp=1753766987229
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:29:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc13a797d, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x05d3e687, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:29:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:29:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7475d933, L:null ! R:/***************:8091]) will closed
2025-07-29 13:29:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753766997231
timestamp=1753766997231
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:29:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:29:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:29:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:29:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x97b4818f, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd9b0d0a3, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:07 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:07 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767007235
timestamp=1753767007235
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:30:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xcc47c70b, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:07 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:07 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:30:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:30:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767017236
timestamp=1753767017236
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:30:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xb9fcfee4, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:17 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:30:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:30:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x73a46a2f, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbe939edc, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767027239
timestamp=1753767027239
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:30:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:30:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:30:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbfa014ef, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xae271b83, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767037241
timestamp=1753767037241
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:30:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:30:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:30:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfa3703ad, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:47 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc968d23b, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767047243
timestamp=1753767047243
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:30:47 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:47 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:30:47 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:30:47 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9c06e6b1, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:57 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6011d74d, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767057244
timestamp=1753767057244
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:30:57 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1148df36, L:null ! R:/***************:8091]) will closed
2025-07-29 13:30:57 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:30:57 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:30:57 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:31:07 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5336b14a, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:07 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:07 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767067246
timestamp=1753767067246
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:31:07 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3c2fe75a, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:07 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:07 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:31:07 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:31:17 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xbc1c672b, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767077248
timestamp=1753767077248
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:31:17 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x5ac1db33, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:17 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:17 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:31:17 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:31:19 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  i.s.d.r.n.NacosRegistryServiceImpl - receive empty server list,cluster:default
2025-07-29 13:31:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:***************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767087250
timestamp=1753767087250
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:31:27 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa9805b11, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:27 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to ***************:8091
2025-07-29 13:31:27 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:31:27 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:***************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:31:27 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd6dfdf29, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:31:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,***************,1753767097252
timestamp=1753767097252
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=***************
'} >
2025-07-29 13:31:37 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x14d92e28, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:37 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x59921864, L:/*************:13401 - R:/*************:8091]
2025-07-29 13:31:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:TMROLE,channel:[id: 0x59921864, L:/*************:13401 - R:/*************:8091]
2025-07-29 13:31:37 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:31:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:31:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:31:37 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2fb2d484, L:null ! R:/***************:8091]) will closed
2025-07-29 13:31:37 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x8fca99ce, L:/*************:13402 - R:/*************:8091]
2025-07-29 13:31:37 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0x8fca99ce, L:/*************:13402 - R:/*************:8091]
2025-07-29 13:34:28 [rpcDispatch_RMROLE_1_3_32] INFO  i.s.c.r.p.client.RmUndoLogProcessor - rm handle undo log process:UndoLogDeleteRequest{resourceId='*******************************************', saveDays=7, branchType=AT}
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 13:37:14 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 13:37:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x59921864, L:/*************:13401 ! R:/*************:8091]
2025-07-29 13:37:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x59921864, L:/*************:13401 ! R:/*************:8091]
2025-07-29 13:37:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x59921864, L:/*************:13401 ! R:/*************:8091]
2025-07-29 13:37:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x59921864, L:/*************:13401 ! R:/*************:8091]
2025-07-29 13:37:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x59921864, L:/*************:13401 ! R:/*************:8091]) will closed
2025-07-29 13:37:14 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x59921864, L:/*************:13401 ! R:/*************:8091]) will closed
2025-07-29 13:37:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x8fca99ce, L:/*************:13402 ! R:/*************:8091]
2025-07-29 13:37:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x8fca99ce, L:/*************:13402 ! R:/*************:8091]
2025-07-29 13:37:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x8fca99ce, L:/*************:13402 ! R:/*************:8091]
2025-07-29 13:37:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x8fca99ce, L:/*************:13402 ! R:/*************:8091]
2025-07-29 13:37:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8fca99ce, L:/*************:13402 ! R:/*************:8091]) will closed
2025-07-29 13:37:14 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8fca99ce, L:/*************:13402 ! R:/*************:8091]) will closed
2025-07-29 13:37:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 13:37:19 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 31680 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 13:37:19 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-29 13:37:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 13:37:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-29 13:37:19 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 13:37:24 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 13:37:24 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 13:37:24 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 13:37:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 13:37:24 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 13:37:24 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 13:37:24 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:37:24 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753767444718
timestamp=1753767444718
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 13:37:24 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x940bc9cf, L:/*************:14428 - R:/*************:8091]
2025-07-29 13:37:24 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 51 ms, version:1.7.1,role:TMROLE,channel:[id: 0x940bc9cf, L:/*************:14428 - R:/*************:8091]
2025-07-29 13:37:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 13:37:24 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 13:37:24 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 13:37:24 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 13:37:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 13:37:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 13:37:25 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 13:37:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 13:37:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 13:37:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 13:37:27 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@370bd951
2025-07-29 13:37:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 13:37:27 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:37:27 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:37:27 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:37:27 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x93646ee0, L:/*************:14440 - R:/*************:8091]
2025-07-29 13:37:27 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x93646ee0, L:/*************:14440 - R:/*************:8091]
2025-07-29 13:37:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 13:37:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 13:37:28 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 13:37:28 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 13:37:28 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 13:37:29 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 13:37:30 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 13:37:30 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 13:37:30 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 13:37:30 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 13:37:32 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 13:37:32 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 13:37:32 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 13:37:32 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 13:37:32 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-29 13:37:36 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 19.52 seconds (process running for 20.664)
2025-07-29 13:37:36 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 13:37:36 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 13:37:36 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-29 13:37:36 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x93646ee0, L:/*************:14440 - R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x93646ee0, L:/*************:14440 - R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x93646ee0, L:/*************:14440 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x93646ee0, L:/*************:14440 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x93646ee0, L:/*************:14440 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93646ee0, L:/*************:14440 ! R:/*************:8091]) will closed
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x93646ee0, L:/*************:14440 ! R:/*************:8091]) will closed
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x940bc9cf, L:/*************:14428 - R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x940bc9cf, L:/*************:14428 - R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x940bc9cf, L:/*************:14428 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x940bc9cf, L:/*************:14428 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x940bc9cf, L:/*************:14428 ! R:/*************:8091]
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x940bc9cf, L:/*************:14428 ! R:/*************:8091]) will closed
2025-07-29 13:48:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x940bc9cf, L:/*************:14428 ! R:/*************:8091]) will closed
2025-07-29 13:48:04 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:48:04 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:48:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x93646ee0, L:/*************:14440 ! R:/*************:8091]
2025-07-29 13:48:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x93646ee0, L:/*************:14440 ! R:/*************:8091]
2025-07-29 13:48:04 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:48:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8a5aeb45, L:null ! R:/*************:8091]) will closed
2025-07-29 13:48:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:48:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753768094515
timestamp=1753768094515
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 13:48:14 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7912428c, L:/*************:3484 - R:/*************:8091]
2025-07-29 13:48:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:TMROLE,channel:[id: 0x7912428c, L:/*************:3484 - R:/*************:8091]
2025-07-29 13:48:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:48:14 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:48:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:48:14 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa95f5620, L:/*************:3487 - R:/*************:8091]
2025-07-29 13:48:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 3 ms, version:1.7.1,role:RMROLE,channel:[id: 0xa95f5620, L:/*************:3487 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa95f5620, L:/*************:3487 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xa95f5620, L:/*************:3487 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7912428c, L:/*************:3484 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa95f5620, L:/*************:3487 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x7912428c, L:/*************:3484 - R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa95f5620, L:/*************:3487 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa95f5620, L:/*************:3487 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa95f5620, L:/*************:3487 ! R:/*************:8091]) will closed
2025-07-29 13:53:06 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa95f5620, L:/*************:3487 ! R:/*************:8091]) will closed
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7912428c, L:/*************:3484 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7912428c, L:/*************:3484 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7912428c, L:/*************:3484 ! R:/*************:8091]
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7912428c, L:/*************:3484 ! R:/*************:8091]) will closed
2025-07-29 13:53:06 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7912428c, L:/*************:3484 ! R:/*************:8091]) will closed
2025-07-29 13:53:14 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:53:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753768394506
timestamp=1753768394506
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 13:53:14 [timeoutChecker_1_1] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x4a7b8da8, L:/*************:4274 - R:/*************:8091]
2025-07-29 13:53:14 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 1 ms, version:1.7.1,role:TMROLE,channel:[id: 0x4a7b8da8, L:/*************:4274 - R:/*************:8091]
2025-07-29 13:53:14 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 13:53:14 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 13:53:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa95f5620, L:/*************:3487 ! R:/*************:8091]
2025-07-29 13:53:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa95f5620, L:/*************:3487 ! R:/*************:8091]
2025-07-29 13:53:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 13:53:14 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x7e3a6669, L:/*************:4276 - R:/*************:8091]
2025-07-29 13:53:14 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 2 ms, version:1.7.1,role:RMROLE,channel:[id: 0x7e3a6669, L:/*************:4276 - R:/*************:8091]
2025-07-29 14:28:25 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 14:28:25 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 14:28:26 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 14:28:26 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 14:28:26 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 14:28:26 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 14:28:26 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 14:28:26 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 14:28:26 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 14:28:26 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 14:28:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x4a7b8da8, L:/*************:4274 ! R:/*************:8091]
2025-07-29 14:28:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x4a7b8da8, L:/*************:4274 ! R:/*************:8091]
2025-07-29 14:28:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x4a7b8da8, L:/*************:4274 ! R:/*************:8091]
2025-07-29 14:28:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x4a7b8da8, L:/*************:4274 ! R:/*************:8091]
2025-07-29 14:28:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4a7b8da8, L:/*************:4274 ! R:/*************:8091]) will closed
2025-07-29 14:28:26 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4a7b8da8, L:/*************:4274 ! R:/*************:8091]) will closed
2025-07-29 14:28:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x7e3a6669, L:/*************:4276 ! R:/*************:8091]
2025-07-29 14:28:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x7e3a6669, L:/*************:4276 ! R:/*************:8091]
2025-07-29 14:28:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x7e3a6669, L:/*************:4276 ! R:/*************:8091]
2025-07-29 14:28:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x7e3a6669, L:/*************:4276 ! R:/*************:8091]
2025-07-29 14:28:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e3a6669, L:/*************:4276 ! R:/*************:8091]) will closed
2025-07-29 14:28:26 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e3a6669, L:/*************:4276 ! R:/*************:8091]) will closed
2025-07-29 14:58:47 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 14:58:47 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 32996 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 14:58:47 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-29 14:58:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 14:58:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-29 14:58:47 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 14:58:51 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 14:58:51 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 14:58:51 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 14:58:51 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 14:58:52 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 14:58:52 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 14:58:52 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 14:58:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753772332251
timestamp=1753772332251
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 14:58:52 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xaa36b3c4, L:/*************:2146 - R:/*************:8091]
2025-07-29 14:58:52 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 62 ms, version:1.7.1,role:TMROLE,channel:[id: 0xaa36b3c4, L:/*************:2146 - R:/*************:8091]
2025-07-29 14:58:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 14:58:52 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 14:58:52 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 14:58:52 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 14:58:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 14:58:52 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 14:58:53 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 14:58:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 14:58:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 14:58:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 14:58:54 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5d05bb29
2025-07-29 14:58:54 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 14:58:54 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 14:58:54 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 14:58:54 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 14:58:54 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xc7e5fcc3, L:/*************:2154 - R:/*************:8091]
2025-07-29 14:58:54 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0xc7e5fcc3, L:/*************:2154 - R:/*************:8091]
2025-07-29 14:58:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 14:58:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 14:58:56 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 14:58:56 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 14:58:56 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 14:58:57 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 14:58:57 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 14:58:57 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 14:58:57 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 14:58:58 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 14:58:59 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 14:58:59 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 14:58:59 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 14:58:59 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 14:59:00 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-29 14:59:03 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 18.697 seconds (process running for 19.987)
2025-07-29 14:59:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 14:59:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 14:59:03 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-29 14:59:04 [RMI TCP Connection(1)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[56ms]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:00:11 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:00:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:00:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 15:00:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[29ms]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:01:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:04:27 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:04:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-29 15:07:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 15:07:40 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[41ms]
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 15:19:18 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 15:19:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xaa36b3c4, L:/*************:2146 ! R:/*************:8091]
2025-07-29 15:19:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xaa36b3c4, L:/*************:2146 ! R:/*************:8091]
2025-07-29 15:19:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xaa36b3c4, L:/*************:2146 ! R:/*************:8091]
2025-07-29 15:19:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xaa36b3c4, L:/*************:2146 ! R:/*************:8091]
2025-07-29 15:19:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaa36b3c4, L:/*************:2146 ! R:/*************:8091]) will closed
2025-07-29 15:19:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xaa36b3c4, L:/*************:2146 ! R:/*************:8091]) will closed
2025-07-29 15:19:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xc7e5fcc3, L:/*************:2154 ! R:/*************:8091]
2025-07-29 15:19:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xc7e5fcc3, L:/*************:2154 ! R:/*************:8091]
2025-07-29 15:19:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xc7e5fcc3, L:/*************:2154 ! R:/*************:8091]
2025-07-29 15:19:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xc7e5fcc3, L:/*************:2154 ! R:/*************:8091]
2025-07-29 15:19:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc7e5fcc3, L:/*************:2154 ! R:/*************:8091]) will closed
2025-07-29 15:19:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xc7e5fcc3, L:/*************:2154 ! R:/*************:8091]) will closed
2025-07-29 15:19:36 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 15:19:36 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 40164 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 15:19:36 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-29 15:19:37 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 15:19:37 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-29 15:19:37 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 15:19:42 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 15:19:42 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 15:19:42 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 15:19:42 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 15:19:42 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 15:19:42 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 15:19:42 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 15:19:42 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753773582748
timestamp=1753773582748
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 15:19:43 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xd475fe4c, L:/*************:5782 - R:/*************:8091]
2025-07-29 15:19:43 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 114 ms, version:1.7.1,role:TMROLE,channel:[id: 0xd475fe4c, L:/*************:5782 - R:/*************:8091]
2025-07-29 15:19:43 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 15:19:43 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 15:19:43 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 15:19:43 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 15:19:43 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 15:19:43 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 15:19:44 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 15:19:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 15:19:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 15:19:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 15:19:46 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3fde6c3b
2025-07-29 15:19:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 15:19:47 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 15:19:47 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 15:19:47 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 15:19:47 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xef105b02, L:/*************:5797 - R:/*************:8091]
2025-07-29 15:19:47 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0xef105b02, L:/*************:5797 - R:/*************:8091]
2025-07-29 15:19:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 15:19:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 15:19:48 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 15:19:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 15:19:49 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 15:19:49 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 15:19:50 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 15:19:50 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 15:19:51 [redisson-netty-2-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 15:19:51 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 15:19:53 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 15:19:53 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 15:19:53 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 15:19:53 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 15:19:53 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-29 15:19:58 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 24.598 seconds (process running for 26.874)
2025-07-29 15:19:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 15:19:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 15:19:58 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-29 15:19:58 [RMI TCP Connection(11)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 15:21:11 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[78ms]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[88ms]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[81ms]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[30ms]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[3ms]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[3ms]
2025-07-29 15:21:12 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-29 15:21:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:21:14 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:21:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 15:21:19 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[35ms]
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 15:23:44 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 15:23:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xd475fe4c, L:/*************:5782 ! R:/*************:8091]
2025-07-29 15:23:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xd475fe4c, L:/*************:5782 ! R:/*************:8091]
2025-07-29 15:23:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xd475fe4c, L:/*************:5782 ! R:/*************:8091]
2025-07-29 15:23:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xd475fe4c, L:/*************:5782 ! R:/*************:8091]
2025-07-29 15:23:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd475fe4c, L:/*************:5782 ! R:/*************:8091]) will closed
2025-07-29 15:23:44 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd475fe4c, L:/*************:5782 ! R:/*************:8091]) will closed
2025-07-29 15:23:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xef105b02, L:/*************:5797 ! R:/*************:8091]
2025-07-29 15:23:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xef105b02, L:/*************:5797 ! R:/*************:8091]
2025-07-29 15:23:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xef105b02, L:/*************:5797 ! R:/*************:8091]
2025-07-29 15:23:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xef105b02, L:/*************:5797 ! R:/*************:8091]
2025-07-29 15:23:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xef105b02, L:/*************:5797 ! R:/*************:8091]) will closed
2025-07-29 15:23:44 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xef105b02, L:/*************:5797 ! R:/*************:8091]) will closed
2025-07-29 15:23:49 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 15:23:50 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 32092 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 15:23:50 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-29 15:23:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 15:23:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-29 15:23:50 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 15:23:55 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 15:23:55 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 15:23:55 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 15:23:55 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 15:23:55 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 15:23:55 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 15:23:55 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 15:23:55 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753773835921
timestamp=1753773835921
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 15:23:56 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xfe5f29b9, L:/*************:6670 - R:/*************:8091]
2025-07-29 15:23:56 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 86 ms, version:1.7.1,role:TMROLE,channel:[id: 0xfe5f29b9, L:/*************:6670 - R:/*************:8091]
2025-07-29 15:23:56 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 15:23:56 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 15:23:56 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 15:23:56 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 15:23:56 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 15:23:56 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 15:23:57 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 15:23:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 15:23:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 15:23:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 15:23:59 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@29beac4e
2025-07-29 15:23:59 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 15:24:00 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 15:24:00 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 15:24:00 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 15:24:00 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x261c3c11, L:/*************:6682 - R:/*************:8091]
2025-07-29 15:24:00 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x261c3c11, L:/*************:6682 - R:/*************:8091]
2025-07-29 15:24:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 15:24:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 15:24:01 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 15:24:01 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 15:24:01 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 15:24:02 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 15:24:03 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 15:24:03 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 15:24:03 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 15:24:04 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 15:24:06 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 15:24:06 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 15:24:06 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 15:24:06 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 15:24:06 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-29 15:24:10 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 23.164 seconds (process running for 24.758)
2025-07-29 15:24:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 15:24:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 15:24:10 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-29 15:24:40 [XNIO-1 task-2] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[109ms]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:24:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:24:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 15:24:53 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[13ms]
2025-07-29 15:32:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[67ms]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:32:56 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:33:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-29 15:33:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[116ms]
2025-07-29 15:33:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 15:33:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[36ms]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:36:36 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[49ms]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[29ms]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:55 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:38:59 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[50ms]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[24ms]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:01 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 15:40:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:02 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:05 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:40:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:40:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:40:51 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:47:36 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 15:57:22 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:57:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:59:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 15:59:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 15:59:50 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 15:59:50 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 15:59:50 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 15:59:50 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 15:59:50 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 15:59:50 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 15:59:50 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 15:59:51 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 15:59:51 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 15:59:51 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 15:59:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xfe5f29b9, L:/*************:6670 ! R:/*************:8091]
2025-07-29 15:59:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xfe5f29b9, L:/*************:6670 ! R:/*************:8091]
2025-07-29 15:59:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xfe5f29b9, L:/*************:6670 ! R:/*************:8091]
2025-07-29 15:59:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xfe5f29b9, L:/*************:6670 ! R:/*************:8091]
2025-07-29 15:59:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe5f29b9, L:/*************:6670 ! R:/*************:8091]) will closed
2025-07-29 15:59:51 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xfe5f29b9, L:/*************:6670 ! R:/*************:8091]) will closed
2025-07-29 15:59:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x261c3c11, L:/*************:6682 ! R:/*************:8091]
2025-07-29 15:59:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x261c3c11, L:/*************:6682 ! R:/*************:8091]
2025-07-29 15:59:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x261c3c11, L:/*************:6682 ! R:/*************:8091]
2025-07-29 15:59:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x261c3c11, L:/*************:6682 ! R:/*************:8091]
2025-07-29 15:59:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x261c3c11, L:/*************:6682 ! R:/*************:8091]) will closed
2025-07-29 15:59:51 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x261c3c11, L:/*************:6682 ! R:/*************:8091]) will closed
2025-07-29 16:00:08 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 16:00:08 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 28896 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 16:00:08 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-29 16:00:08 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 16:00:08 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-29 16:00:08 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 16:00:14 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 16:00:14 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 16:00:14 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 16:00:14 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 16:00:14 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 16:00:14 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 16:00:15 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 16:00:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753776015131
timestamp=1753776015131
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 16:00:15 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x913ed97a, L:/*************:12328 - R:/*************:8091]
2025-07-29 16:00:15 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 212 ms, version:1.7.1,role:TMROLE,channel:[id: 0x913ed97a, L:/*************:12328 - R:/*************:8091]
2025-07-29 16:00:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 16:00:15 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 16:00:15 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 16:00:15 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 16:00:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 16:00:15 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 16:00:17 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 16:00:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 16:00:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 16:00:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 16:00:19 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@7de54be7
2025-07-29 16:00:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 16:00:19 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 16:00:19 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 16:00:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 16:00:19 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x1cbb02f1, L:/*************:12355 - R:/*************:8091]
2025-07-29 16:00:19 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 7 ms, version:1.7.1,role:RMROLE,channel:[id: 0x1cbb02f1, L:/*************:12355 - R:/*************:8091]
2025-07-29 16:00:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 16:00:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 16:00:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:00:21 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:00:21 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:00:22 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 16:00:23 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 16:00:23 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 16:00:23 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 16:00:24 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 16:00:26 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 16:00:26 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 16:00:26 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 16:00:26 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 16:00:26 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-29 16:00:30 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 25.994 seconds (process running for 28.807)
2025-07-29 16:00:30 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 16:00:30 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 16:00:30 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-29 16:00:31 [RMI TCP Connection(18)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[84ms]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:06:39 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[86ms]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[32ms]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:13:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[53ms]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[30ms]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:14:23 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 16:19:00 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 16:19:00 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 16:19:00 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 16:19:00 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 16:19:00 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 16:19:00 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 16:19:00 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 16:19:01 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 16:19:01 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 16:19:01 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 16:19:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x913ed97a, L:/*************:12328 ! R:/*************:8091]
2025-07-29 16:19:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x913ed97a, L:/*************:12328 ! R:/*************:8091]
2025-07-29 16:19:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x913ed97a, L:/*************:12328 ! R:/*************:8091]
2025-07-29 16:19:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x913ed97a, L:/*************:12328 ! R:/*************:8091]
2025-07-29 16:19:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x913ed97a, L:/*************:12328 ! R:/*************:8091]) will closed
2025-07-29 16:19:01 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x913ed97a, L:/*************:12328 ! R:/*************:8091]) will closed
2025-07-29 16:19:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x1cbb02f1, L:/*************:12355 ! R:/*************:8091]
2025-07-29 16:19:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x1cbb02f1, L:/*************:12355 ! R:/*************:8091]
2025-07-29 16:19:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x1cbb02f1, L:/*************:12355 ! R:/*************:8091]
2025-07-29 16:19:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x1cbb02f1, L:/*************:12355 ! R:/*************:8091]
2025-07-29 16:19:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1cbb02f1, L:/*************:12355 ! R:/*************:8091]) will closed
2025-07-29 16:19:01 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x1cbb02f1, L:/*************:12355 ! R:/*************:8091]) will closed
2025-07-29 16:19:06 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 16:19:07 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 43428 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 16:19:07 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-29 16:19:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 16:19:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-29 16:19:07 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 16:19:12 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 16:19:12 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 16:19:12 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 16:19:12 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 16:19:12 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 16:19:12 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 16:19:12 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 16:19:12 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753777152772
timestamp=1753777152772
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 16:19:13 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x021e2dbe, L:/*************:1755 - R:/*************:8091]
2025-07-29 16:19:13 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 170 ms, version:1.7.1,role:TMROLE,channel:[id: 0x021e2dbe, L:/*************:1755 - R:/*************:8091]
2025-07-29 16:19:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 16:19:13 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 16:19:13 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 16:19:13 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 16:19:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 16:19:13 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 16:19:14 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 16:19:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 16:19:15 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 16:19:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 16:19:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@4fbc04de
2025-07-29 16:19:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 16:19:16 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 16:19:16 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 16:19:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 16:19:16 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x8f1cc5e1, L:/*************:1767 - R:/*************:8091]
2025-07-29 16:19:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 8 ms, version:1.7.1,role:RMROLE,channel:[id: 0x8f1cc5e1, L:/*************:1767 - R:/*************:8091]
2025-07-29 16:19:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 16:19:16 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 16:19:18 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:19:18 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:19:18 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:19:19 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 16:19:20 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 16:19:20 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 16:19:20 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 16:19:20 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 16:19:23 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 16:19:23 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 16:19:23 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 16:19:23 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 16:19:23 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-29 16:19:28 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 23.402 seconds (process running for 24.847)
2025-07-29 16:19:28 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 16:19:28 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 16:19:28 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-29 16:19:28 [RMI TCP Connection(9)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 16:20:04 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 16:20:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x021e2dbe, L:/*************:1755 ! R:/*************:8091]
2025-07-29 16:20:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x021e2dbe, L:/*************:1755 ! R:/*************:8091]
2025-07-29 16:20:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x021e2dbe, L:/*************:1755 ! R:/*************:8091]
2025-07-29 16:20:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x021e2dbe, L:/*************:1755 ! R:/*************:8091]
2025-07-29 16:20:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x021e2dbe, L:/*************:1755 ! R:/*************:8091]) will closed
2025-07-29 16:20:04 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x021e2dbe, L:/*************:1755 ! R:/*************:8091]) will closed
2025-07-29 16:20:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x8f1cc5e1, L:/*************:1767 ! R:/*************:8091]
2025-07-29 16:20:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x8f1cc5e1, L:/*************:1767 ! R:/*************:8091]
2025-07-29 16:20:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x8f1cc5e1, L:/*************:1767 ! R:/*************:8091]
2025-07-29 16:20:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x8f1cc5e1, L:/*************:1767 ! R:/*************:8091]
2025-07-29 16:20:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8f1cc5e1, L:/*************:1767 ! R:/*************:8091]) will closed
2025-07-29 16:20:04 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8f1cc5e1, L:/*************:1767 ! R:/*************:8091]) will closed
2025-07-29 16:20:14 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 16:20:14 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 42808 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 16:20:14 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-29 16:20:14 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 16:20:14 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-29 16:20:14 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 16:20:19 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 16:20:19 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 16:20:19 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 16:20:19 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 16:20:19 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 16:20:19 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 16:20:19 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 16:20:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753777220047
timestamp=1753777220047
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 16:20:20 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x88567ca8, L:/*************:1903 - R:/*************:8091]
2025-07-29 16:20:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 94 ms, version:1.7.1,role:TMROLE,channel:[id: 0x88567ca8, L:/*************:1903 - R:/*************:8091]
2025-07-29 16:20:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 16:20:20 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 16:20:20 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 16:20:20 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 16:20:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 16:20:20 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 16:20:21 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 16:20:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 16:20:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 16:20:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 16:20:23 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@3413effc
2025-07-29 16:20:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 16:20:23 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 16:20:23 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 16:20:23 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 16:20:23 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x150e890c, L:/*************:1909 - R:/*************:8091]
2025-07-29 16:20:23 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 5 ms, version:1.7.1,role:RMROLE,channel:[id: 0x150e890c, L:/*************:1909 - R:/*************:8091]
2025-07-29 16:20:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 16:20:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 16:20:24 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:20:25 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:20:25 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:20:26 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 16:20:26 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 16:20:26 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 16:20:27 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 16:20:27 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 16:20:29 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 16:20:29 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 16:20:29 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 16:20:29 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 16:20:29 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-29 16:20:33 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 21.689 seconds (process running for 22.912)
2025-07-29 16:20:33 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 16:20:33 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 16:20:33 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-29 16:20:34 [RMI TCP Connection(8)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:20:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 16:20:54 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[80ms]
2025-07-29 16:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[47ms]
2025-07-29 16:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:20:55 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:21:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-29 16:21:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 16:21:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:33 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[65ms]
2025-07-29 16:21:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 16:21:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[28ms]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:34 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 16:21:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-29 16:21:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:21:35 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 16:23:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:23:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-29 16:23:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:23:20 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:24:00 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:00 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[70ms]
2025-07-29 16:24:00 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:00 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 16:24:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 16:24:04 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[1ms]
2025-07-29 16:24:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[76ms]
2025-07-29 16:24:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:05 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 16:24:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[64ms]
2025-07-29 16:24:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:16 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:24:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[71ms]
2025-07-29 16:24:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:47 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:24:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[60ms]
2025-07-29 16:24:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:24:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 16:53:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-29 16:53:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-29 16:53:25 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-29 16:53:25 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[66ms]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[99ms]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:53:26 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[34ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 16:53:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[114ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:53:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 16:53:26 [XNIO-1 task-4] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[77ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[26ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:53:28 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 16:54:05 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 16:54:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x88567ca8, L:/*************:1903 ! R:/*************:8091]
2025-07-29 16:54:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x88567ca8, L:/*************:1903 ! R:/*************:8091]
2025-07-29 16:54:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x88567ca8, L:/*************:1903 ! R:/*************:8091]
2025-07-29 16:54:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x88567ca8, L:/*************:1903 ! R:/*************:8091]
2025-07-29 16:54:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88567ca8, L:/*************:1903 ! R:/*************:8091]) will closed
2025-07-29 16:54:05 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x88567ca8, L:/*************:1903 ! R:/*************:8091]) will closed
2025-07-29 16:54:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x150e890c, L:/*************:1909 ! R:/*************:8091]
2025-07-29 16:54:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x150e890c, L:/*************:1909 ! R:/*************:8091]
2025-07-29 16:54:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x150e890c, L:/*************:1909 ! R:/*************:8091]
2025-07-29 16:54:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x150e890c, L:/*************:1909 ! R:/*************:8091]
2025-07-29 16:54:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x150e890c, L:/*************:1909 ! R:/*************:8091]) will closed
2025-07-29 16:54:05 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x150e890c, L:/*************:1909 ! R:/*************:8091]) will closed
2025-07-29 16:54:11 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-29 16:54:11 [main] INFO  c.y.s.SalarySettlementApplication - Starting SalarySettlementApplication using Java 17.0.10 with PID 25408 (D:\project\yumeng\ship-Integrated-management-api\yumeng-example\yumeng-salary-settlement\target\classes started by qingyi in D:\project\yumeng)
2025-07-29 16:54:11 [main] INFO  c.y.s.SalarySettlementApplication - The following 1 profile is active: "dev"
2025-07-29 16:54:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=datasource.yml, group=DEFAULT_GROUP] success
2025-07-29 16:54:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP] success
2025-07-29 16:54:11 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-29 16:54:16 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - Automatically configure Seata
2025-07-29 16:54:16 [main] INFO  i.seata.config.ConfigurationFactory - load Configuration from :Spring Configuration
2025-07-29 16:54:16 [main] INFO  i.s.config.nacos.NacosConfiguration - Nacos check auth with userName/password.
2025-07-29 16:54:16 [main] INFO  i.s.s.a.GlobalTransactionScanner - Initializing Global Transaction Clients ... 
2025-07-29 16:54:16 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 16:54:16 [main] INFO  i.s.d.registry.RegistryFactory - use registry center type: nacos
2025-07-29 16:54:16 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 16:54:16 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753779256980
timestamp=1753779256980
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 16:54:17 [main] INFO  i.s.c.r.netty.TmNettyRemotingClient - register TM success. client version:1.7.1, server version:1.7.1,channel:[id: 0xa2163147, L:/*************:6174 - R:/*************:8091]
2025-07-29 16:54:17 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 102 ms, version:1.7.1,role:TMROLE,channel:[id: 0xa2163147, L:/*************:6174 - R:/*************:8091]
2025-07-29 16:54:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Transaction Manager Client is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 16:54:17 [main] INFO  io.seata.rm.datasource.AsyncWorker - Async Commit Buffer Limit: 10000
2025-07-29 16:54:17 [main] INFO  i.s.r.d.xa.ResourceManagerXA - ResourceManagerXA init ...
2025-07-29 16:54:17 [main] INFO  i.s.c.rpc.netty.NettyClientBootstrap - NettyClientBootstrap has started
2025-07-29 16:54:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Resource Manager is initialized. applicationId[yumeng-salary-settlement] txServiceGroup[yumeng-salary-settlement-group]
2025-07-29 16:54:17 [main] INFO  i.s.s.a.GlobalTransactionScanner - Global Transaction Clients are initialized. 
2025-07-29 16:54:18 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 16:54:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-29 16:54:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect ALIBABA SEATA and enabled it
2025-07-29 16:54:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-29 16:54:20 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@156290d9
2025-07-29 16:54:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-29 16:54:20 [main] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 16:54:20 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 16:54:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 16:54:20 [main] INFO  i.s.c.r.netty.RmNettyRemotingClient - register RM success. client version:1.7.1, server version:1.7.1,channel:[id: 0x9516c3fd, L:/*************:6185 - R:/*************:8091]
2025-07-29 16:54:20 [main] INFO  i.s.c.rpc.netty.NettyPoolableFactory - register success, cost 6 ms, version:1.7.1,role:RMROLE,channel:[id: 0x9516c3fd, L:/*************:6185 - R:/*************:8091]
2025-07-29 16:54:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-29 16:54:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-29 16:54:22 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.AuditingWorkorderInfoServiceImpl] with name [auditingWorkorderInfoServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:54:22 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:54:22 [main] INFO  i.s.s.a.GlobalTransactionScanner - Bean [com.ym.salary.service.impl.UsdWorkorderDetailServiceImpl$$SpringCGLIB$$0] with name [usdWorkorderDetailServiceImpl] would use interceptor [io.seata.spring.annotation.GlobalTransactionalInterceptor]
2025-07-29 16:54:23 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-29 16:54:24 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-29 16:54:24 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-29 16:54:24 [redisson-netty-2-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-29 16:54:24 [redisson-netty-2-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-29 16:54:27 [main] INFO  io.undertow - starting server: Undertow - 2.3.17.Final
2025-07-29 16:54:27 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 16:54:27 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 16:54:27 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 16:54:27 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-salary-settlement *************:9401 register finished
2025-07-29 16:54:31 [main] INFO  c.y.s.SalarySettlementApplication - Started SalarySettlementApplication in 22.762 seconds (process running for 24.139)
2025-07-29 16:54:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=datasource.yml, group=DEFAULT_GROUP
2025-07-29 16:54:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-29 16:54:31 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-salary-settlement.yml, group=DEFAULT_GROUP
2025-07-29 16:54:31 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 16:54:47 [XNIO-1 task-2] INFO  c.y.s.c.UsdDistributorsController - 封存经销商,对应的配置id:1950109258941059073
2025-07-29 16:54:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 16:54:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[71ms]
2025-07-29 16:54:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:54:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[40ms]
2025-07-29 16:54:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:54:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:55:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 16:55:15 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[28ms]
2025-07-29 16:55:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:55:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-29 16:55:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:55:16 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:55:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:55:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-29 16:55:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:55:29 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:55:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 16:55:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[19ms]
2025-07-29 16:55:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:55:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-29 16:55:33 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:55:34 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 16:55:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:55:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[68ms]
2025-07-29 16:55:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 16:55:45 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:02:01 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[35ms]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[96ms]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:05:14 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 17:08:56 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:08:57 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 17:09:23 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[48ms]
2025-07-29 17:09:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 17:09:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:24 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 17:09:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 17:09:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:31 [XNIO-1 task-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:31 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 17:09:31 [XNIO-1 task-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[44ms]
2025-07-29 17:09:31 [XNIO-1 task-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:31 [XNIO-1 task-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 17:09:50 [XNIO-1 task-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 17:09:50 [XNIO-1 task-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-29 17:09:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:50 [XNIO-1 task-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:09:50 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 17:09:50 [XNIO-1 task-5] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[42ms]
2025-07-29 17:14:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[37ms]
2025-07-29 17:14:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-3] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[36ms]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 17:14:42 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 17:14:42 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[38ms]
2025-07-29 17:14:42 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[29ms]
2025-07-29 20:02:48 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:02:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[41ms]
2025-07-29 20:02:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:02:49 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:28:50 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[64ms]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[33ms]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[28ms]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[26ms]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:29:57 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[81ms]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[25ms]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:45:26 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[31ms]
2025-07-29 20:47:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:47:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[45ms]
2025-07-29 20:47:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:47:13 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[79ms]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[20ms]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 20:56:04 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[56ms]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:09:46 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[43ms]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getCompanyTypeByUserId],SpendTime=[62ms]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 21:11:20 [XNIO-1 task-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[99ms]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[24ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-6] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 21:11:20 [XNIO-1 task-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[20ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteDeptService],MethodName=[selectDeptIdsByUserId],SpendTime=[85ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[1ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog]
2025-07-29 21:11:20 [XNIO-1 task-7] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLog],SpendTime=[2ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[selectNicknameById],SpendTime=[2ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[24ms]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:20 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[22ms]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[27ms]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[21ms]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName]
2025-07-29 21:11:22 [XNIO-1 task-2] INFO  c.y.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteCompanyInfoService],MethodName=[getTransportName],SpendTime=[23ms]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0xa2163147, L:/*************:6174 - R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - return to pool, rm channel:[id: 0x9516c3fd, L:/*************:6185 - R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0xa2163147, L:/*************:6174 - R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - remove exception rm channel:[id: 0x9516c3fd, L:/*************:6185 - R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0x9516c3fd, L:/*************:6185 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - channel inactive: [id: 0xa2163147, L:/*************:6174 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0xa2163147, L:/*************:6174 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x9516c3fd, L:/*************:6185 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0xa2163147, L:/*************:6174 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x9516c3fd, L:/*************:6185 ! R:/*************:8091]
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9516c3fd, L:/*************:6185 ! R:/*************:8091]) will closed
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa2163147, L:/*************:6174 ! R:/*************:8091]) will closed
2025-07-29 21:49:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa2163147, L:/*************:6174 ! R:/*************:8091]) will closed
2025-07-29 21:49:29 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9516c3fd, L:/*************:6185 ! R:/*************:8091]) will closed
2025-07-29 21:49:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753796977567
timestamp=1753796977567
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:49:38 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:38 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:49:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - channel valid false,channel:[id: 0x9516c3fd, L:/*************:6185 ! R:/*************:8091]
2025-07-29 21:49:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - will destroy channel:[id: 0x9516c3fd, L:/*************:6185 ! R:/*************:8091]
2025-07-29 21:49:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:49:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x220f9950, L:null ! R:/*************:8091]) will closed
2025-07-29 21:49:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0301f9d4, L:null ! R:/*************:8091]) will closed
2025-07-29 21:49:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753796987567
timestamp=1753796987567
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:49:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:49:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:49:49 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x455aaff0, L:null ! R:/*************:8091]) will closed
2025-07-29 21:49:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xa3dd7715, L:null ! R:/*************:8091]) will closed
2025-07-29 21:49:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753796997567
timestamp=1753796997567
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:49:58 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:49:58 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:49:58 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:49:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x0fa2938e, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:00 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x78fd4942, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:07 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:07 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753797007568
timestamp=1753797007568
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:50:08 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:08 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:50:08 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:50:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x51e0dd1e, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x8e7484e6, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753797017568
timestamp=1753797017568
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:50:18 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:18 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:50:18 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:50:19 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x02405c32, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:20 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x952b5644, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:27 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:27 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753797027568
timestamp=1753797027568
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:50:28 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:28 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:50:28 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:50:29 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x3c8e3dd8, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:30 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x09617745, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:37 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:37 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753797037568
timestamp=1753797037568
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:50:38 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:38 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:50:38 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:50:39 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x4e41c26d, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:40 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x533d2f8b, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:47 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:47 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753797047567
timestamp=1753797047567
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:50:48 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:48 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:50:48 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:50:49 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x6f354047, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:50 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x42dcbdc0, L:null ! R:/*************:8091]) will closed
2025-07-29 21:50:57 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:57 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753797057571
timestamp=1753797057571
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:50:58 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:50:58 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:50:58 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:50:59 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x2f2232e0, L:null ! R:/*************:8091]) will closed
2025-07-29 21:51:00 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7e848025, L:null ! R:/*************:8091]) will closed
2025-07-29 21:51:07 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:51:07 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753797067567
timestamp=1753797067567
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:51:08 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:51:08 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:51:08 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:51:09 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0xd9a66238, L:null ! R:/*************:8091]) will closed
2025-07-29 21:51:10 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x99ef3ce7, L:null ! R:/*************:8091]) will closed
2025-07-29 21:51:17 [timeoutChecker_1_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:51:17 [timeoutChecker_1_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:TMROLE,address:*************:8091,msg:< RegisterTMRequest{version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='ak=null
digest=yumeng-salary-settlement-group,*************,1753797077568
timestamp=1753797077568
authVersion=V4
vgroup=yumeng-salary-settlement-group
ip=*************
'} >
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.17.Final
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-29 21:51:18 [timeoutChecker_2_1] INFO  i.s.c.r.n.NettyClientChannelManager - will connect to *************:8091
2025-07-29 21:51:18 [timeoutChecker_2_1] INFO  i.s.c.r.netty.RmNettyRemotingClient - RM will register :*******************************************
2025-07-29 21:51:18 [timeoutChecker_2_1] INFO  i.s.c.rpc.netty.NettyPoolableFactory - NettyPool create channel to transactionRole:RMROLE,address:*************:8091,msg:< RegisterRMRequest{resourceIds='*******************************************', version='1.7.1', applicationId='yumeng-salary-settlement', transactionServiceGroup='yumeng-salary-settlement-group', extraData='null'} >
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-29 21:51:18 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-29 21:51:18 [NettyClientSelector_TMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x9f627b09, L:null ! R:/*************:8091]) will closed
2025-07-29 21:51:18 [NettyClientSelector_RMROLE_1_1] INFO  i.s.c.r.n.AbstractNettyRemotingClient - ChannelHandlerContext(AbstractNettyRemotingClient$ClientHandler#0, [id: 0x7488ee67, L:null ! R:/*************:8091]) will closed
