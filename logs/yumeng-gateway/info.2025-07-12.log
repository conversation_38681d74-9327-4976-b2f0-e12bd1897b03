2025-07-12 08:32:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-12 08:32:33 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 28344 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-12 08:32:33 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-12 08:32:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-12 08:32:33 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-12 08:32:35 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-12 08:32:36 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-12 08:32:36 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-12 08:32:36 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-12 08:32:36 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-12 08:32:41 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway ************:8080 register finished
2025-07-12 08:32:41 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-12 08:32:41 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 10.334 seconds (process running for 11.042)
2025-07-12 08:32:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-12 08:32:41 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-12 08:34:57 [boundedElastic-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-12 08:34:57 [boundedElastic-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-12 08:34:58 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1053]毫秒
2025-07-12 08:34:58 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1238]毫秒
2025-07-12 08:34:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 08:34:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 08:34:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-12 08:34:59 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[354]毫秒
2025-07-12 08:35:05 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 08:35:07 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1603]毫秒
2025-07-12 08:35:07 [boundedElastic-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 08:35:10 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[2845]毫秒
2025-07-12 08:35:11 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 08:35:11 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[140]毫秒
2025-07-12 08:35:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 08:35:11 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[47]毫秒
2025-07-12 08:35:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 08:35:11 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-12 08:35:11 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTM0MjY2Njk3MDQ1MDg2MjEwIiwicm5TdHIiOiJhNVRINDBBY05URHZ5dGQ4dlUxQTNvQ2JXRGZGVFlPWCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTkzNDI2NjY5NzA0NTA4NjIxMCwidXNlck5hbWUiOiIxNTkwMDQxOTI4NiIsImRlcHRJZCI6MTkzNDI2MjE2MTE4NjMyODU3OCwiZGVwdE5hbWUiOiLpnZLlspvlrZrmtqblvrfoiLnoiLbnrqHnkIbmnInpmZDlhazlj7giLCJkZXB0Q2F0ZWdvcnkiOiIifQ.GU0LTf2d5tGSeON0tGzbfKh-h7M3TmjmKqhotbm6BQU"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 08:35:12 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[21]毫秒
2025-07-12 08:47:14 [boundedElastic-21] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/wechat/ReceiveWXMessage],参数类型[param],参数:[{"signature":["f4af941f68925d0d594103a70c9da425d32a538e"],"echostr":["9060021302709976319"],"timestamp":["**********"],"nonce":["**********"]}]
2025-07-12 08:47:14 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/wechat/ReceiveWXMessage],耗时:[203]毫秒
2025-07-12 09:13:53 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:14:09 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:14:24 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:14:39 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:14:54 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:15:09 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:15:24 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:15:39 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:15:54 [RMI TCP Connection(9)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:16:09 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:16:24 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:16:39 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:16:54 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 09:17:09 [RMI TCP Connection(8)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 10:11:07 [nacos-grpc-client-executor-localhost-1475] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 177
2025-07-12 10:11:07 [nacos-grpc-client-executor-localhost-1475] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:11:07 [nacos-grpc-client-executor-localhost-1475] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 10:11:07 [nacos-grpc-client-executor-localhost-1475] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 177
2025-07-12 10:11:28 [nacos-grpc-client-executor-localhost-1481] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 180
2025-07-12 10:11:28 [nacos-grpc-client-executor-localhost-1481] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:11:28 [nacos-grpc-client-executor-localhost-1481] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:11:28 [nacos-grpc-client-executor-localhost-1481] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 180
2025-07-12 10:18:46 [nacos-grpc-client-executor-localhost-1587] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 223
2025-07-12 10:18:46 [nacos-grpc-client-executor-localhost-1587] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:18:46 [nacos-grpc-client-executor-localhost-1587] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 10:18:46 [nacos-grpc-client-executor-localhost-1587] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 223
2025-07-12 10:19:04 [nacos-grpc-client-executor-localhost-1590] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 226
2025-07-12 10:19:04 [nacos-grpc-client-executor-localhost-1590] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:19:04 [nacos-grpc-client-executor-localhost-1590] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:19:04 [nacos-grpc-client-executor-localhost-1590] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 226
2025-07-12 10:22:59 [nacos-grpc-client-executor-localhost-1649] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 246
2025-07-12 10:22:59 [nacos-grpc-client-executor-localhost-1649] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:22:59 [nacos-grpc-client-executor-localhost-1649] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 10:22:59 [nacos-grpc-client-executor-localhost-1649] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 246
2025-07-12 10:23:18 [nacos-grpc-client-executor-localhost-1654] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 249
2025-07-12 10:23:18 [nacos-grpc-client-executor-localhost-1654] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:23:18 [nacos-grpc-client-executor-localhost-1654] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:23:18 [nacos-grpc-client-executor-localhost-1654] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 249
2025-07-12 10:28:46 [nacos-grpc-client-executor-localhost-1733] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 283
2025-07-12 10:28:46 [nacos-grpc-client-executor-localhost-1733] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:28:46 [nacos-grpc-client-executor-localhost-1733] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 10:28:46 [nacos-grpc-client-executor-localhost-1733] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 283
2025-07-12 10:28:56 [RMI TCP Connection(12)-************] INFO  c.a.c.s.e.SentinelHealthIndicator - Find sentinel dashboard server list: []
2025-07-12 10:29:07 [nacos-grpc-client-executor-localhost-1737] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 286
2025-07-12 10:29:07 [nacos-grpc-client-executor-localhost-1737] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:29:07 [nacos-grpc-client-executor-localhost-1737] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:29:07 [nacos-grpc-client-executor-localhost-1737] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 286
2025-07-12 10:49:12 [nacos-grpc-client-executor-localhost-2020] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 311
2025-07-12 10:49:12 [nacos-grpc-client-executor-localhost-2020] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:49:12 [nacos-grpc-client-executor-localhost-2020] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 10:49:12 [nacos-grpc-client-executor-localhost-2020] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 311
2025-07-12 10:49:35 [nacos-grpc-client-executor-localhost-2027] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 314
2025-07-12 10:49:35 [nacos-grpc-client-executor-localhost-2027] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:49:35 [nacos-grpc-client-executor-localhost-2027] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 10:49:35 [nacos-grpc-client-executor-localhost-2027] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 314
2025-07-12 11:00:39 [boundedElastic-161] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/getPhone],参数类型[json],参数:[null]
2025-07-12 11:00:41 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/getPhone],耗时:[2246]毫秒
2025-07-12 11:00:41 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 11:00:42 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[916]毫秒
2025-07-12 11:00:42 [boundedElastic-161] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/userplus_info/certification/status],参数类型[json],参数:[null]
2025-07-12 11:00:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/userplus_info/certification/status],耗时:[1217]毫秒
2025-07-12 11:00:43 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],参数类型[json],参数:[null]
2025-07-12 11:00:44 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[823]毫秒
2025-07-12 11:00:44 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:00:44 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 11:00:44 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:00:44 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[55]毫秒
2025-07-12 11:00:45 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[181]毫秒
2025-07-12 11:00:45 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[182]毫秒
2025-07-12 11:00:47 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/profile],参数类型[json],参数:[null]
2025-07-12 11:00:47 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[153]毫秒
2025-07-12 11:00:51 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/wechat/checkBindStatus],参数类型[json],参数:[null]
2025-07-12 11:00:51 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/wechat/checkBindStatus],耗时:[32]毫秒
2025-07-12 11:02:49 [boundedElastic-171] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/wechat/checkBindStatus],参数类型[json],参数:[null]
2025-07-12 11:02:49 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/wechat/checkBindStatus],耗时:[31]毫秒
2025-07-12 11:04:13 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 11:04:13 [boundedElastic-162] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:04:13 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:04:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[53]毫秒
2025-07-12 11:04:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[82]毫秒
2025-07-12 11:04:13 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[122]毫秒
2025-07-12 11:05:13 [boundedElastic-162] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:05:13 [boundedElastic-137] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:05:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[92]毫秒
2025-07-12 11:05:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[116]毫秒
2025-07-12 11:06:13 [boundedElastic-172] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:06:13 [boundedElastic-172] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:06:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[87]毫秒
2025-07-12 11:06:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[88]毫秒
2025-07-12 11:06:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:06:26 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 11:06:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:06:26 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[51]毫秒
2025-07-12 11:06:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[102]毫秒
2025-07-12 11:06:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[140]毫秒
2025-07-12 11:06:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 11:06:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:06:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:06:36 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[24]毫秒
2025-07-12 11:06:36 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[93]毫秒
2025-07-12 11:06:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[107]毫秒
2025-07-12 11:06:55 [boundedElastic-173] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 11:06:55 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:06:55 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:06:55 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[22]毫秒
2025-07-12 11:06:55 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[74]毫秒
2025-07-12 11:06:55 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[116]毫秒
2025-07-12 11:07:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:07:06 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 11:07:06 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:07:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[50]毫秒
2025-07-12 11:07:06 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[97]毫秒
2025-07-12 11:07:06 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[123]毫秒
2025-07-12 11:07:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 11:07:24 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:07:24 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:07:24 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[24]毫秒
2025-07-12 11:07:24 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[90]毫秒
2025-07-12 11:07:24 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[108]毫秒
2025-07-12 11:07:59 [boundedElastic-173] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:07:59 [boundedElastic-175] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:07:59 [boundedElastic-173] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 11:07:59 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[44]毫秒
2025-07-12 11:07:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[113]毫秒
2025-07-12 11:07:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[122]毫秒
2025-07-12 11:08:32 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:08:32 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:08:32 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 11:08:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[73]毫秒
2025-07-12 11:08:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[113]毫秒
2025-07-12 11:08:32 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[145]毫秒
2025-07-12 11:09:32 [boundedElastic-178] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:09:32 [boundedElastic-178] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:09:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[90]毫秒
2025-07-12 11:09:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[95]毫秒
2025-07-12 11:10:32 [boundedElastic-175] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:10:32 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:10:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[118]毫秒
2025-07-12 11:10:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[142]毫秒
2025-07-12 11:11:32 [boundedElastic-173] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:11:32 [boundedElastic-175] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:11:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[107]毫秒
2025-07-12 11:11:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[127]毫秒
2025-07-12 11:12:32 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 11:12:32 [boundedElastic-178] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 11:12:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[90]毫秒
2025-07-12 11:12:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[110]毫秒
2025-07-12 11:13:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/profile],参数类型[json],参数:[null]
2025-07-12 11:13:06 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[208]毫秒
2025-07-12 11:13:08 [boundedElastic-178] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/wechat/checkBindStatus],参数类型[json],参数:[null]
2025-07-12 11:13:08 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/wechat/checkBindStatus],耗时:[31]毫秒
2025-07-12 11:13:29 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/wechat/bind],参数类型[json],参数:[{"wcxcode":"0a32lT1w3ZQ6h53gle0w3RCNOz12lT1H"}]
2025-07-12 11:13:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/wechat/bind],耗时:[37]毫秒
2025-07-12 11:13:37 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/wechat/bind],参数类型[json],参数:[{"wcxcode":"0a3wiC1w3TTnh53FhN3w3p4NPV2wiC17"}]
2025-07-12 11:13:37 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/wechat/bind],耗时:[22]毫秒
2025-07-12 13:33:59 [boundedElastic-181] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],参数类型[json],参数:[null]
2025-07-12 13:33:59 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[345]毫秒
2025-07-12 13:34:06 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/getPhone],参数类型[json],参数:[null]
2025-07-12 13:34:07 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/getPhone],耗时:[962]毫秒
2025-07-12 13:34:07 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 13:34:08 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[666]毫秒
2025-07-12 13:34:08 [boundedElastic-181] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/userplus_info/certification/status],参数类型[json],参数:[null]
2025-07-12 13:34:08 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/userplus_info/certification/status],耗时:[77]毫秒
2025-07-12 13:34:08 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],参数类型[json],参数:[null]
2025-07-12 13:34:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[671]毫秒
2025-07-12 13:34:09 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/taskInfo/wx/list],参数类型[json],参数:[null]
2025-07-12 13:34:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/wx/list],参数类型[json],参数:[null]
2025-07-12 13:34:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/todo_info_type],参数类型[json],参数:[null]
2025-07-12 13:34:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/todo_info_type],耗时:[32]毫秒
2025-07-12 13:34:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/taskInfo/wx/list],耗时:[88]毫秒
2025-07-12 13:34:10 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/wx/list],耗时:[114]毫秒
2025-07-12 13:34:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/profile],参数类型[json],参数:[null]
2025-07-12 13:34:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[132]毫秒
2025-07-12 13:34:13 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/wechat/checkBindStatus],参数类型[json],参数:[null]
2025-07-12 13:34:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/wechat/checkBindStatus],耗时:[24]毫秒
2025-07-12 13:34:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/wechat/bind],参数类型[json],参数:[{"wcxcode":"0e3Gwaml24XJVf4SJcnl2NnS1k4GwamF"}]
2025-07-12 13:34:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/wechat/bind],耗时:[3]毫秒
2025-07-12 14:17:09 [nacos-grpc-client-executor-localhost-3318] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 339
2025-07-12 14:17:09 [nacos-grpc-client-executor-localhost-3318] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 14:17:09 [nacos-grpc-client-executor-localhost-3318] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 14:17:09 [nacos-grpc-client-executor-localhost-3318] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 339
2025-07-12 14:17:33 [nacos-grpc-client-executor-localhost-3323] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 342
2025-07-12 14:17:33 [nacos-grpc-client-executor-localhost-3323] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 14:17:33 [nacos-grpc-client-executor-localhost-3323] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 14:17:33 [nacos-grpc-client-executor-localhost-3323] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 342
2025-07-12 14:20:22 [boundedElastic-226] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-12 14:20:22 [boundedElastic-233] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-12 14:20:22 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[67]毫秒
2025-07-12 14:20:22 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[366]毫秒
2025-07-12 14:20:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 14:20:22 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 14:20:22 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-07-12 14:20:22 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[218]毫秒
2025-07-12 14:30:24 [boundedElastic-241] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 14:30:25 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[792]毫秒
2025-07-12 14:30:25 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 14:30:25 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[107]毫秒
2025-07-12 14:30:29 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 14:30:30 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1005]毫秒
2025-07-12 14:30:30 [boundedElastic-241] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 14:30:32 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1909]毫秒
2025-07-12 14:30:32 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 14:30:32 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[123]毫秒
2025-07-12 14:30:33 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 14:30:33 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[44]毫秒
2025-07-12 14:30:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 14:30:33 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[30]毫秒
2025-07-12 14:30:33 [boundedElastic-241] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM4NTc3MDY5NjQ2Mzg1MTU0Iiwicm5TdHIiOiJ5eUVnS1FmREJwNnVNR2FPbHozWXlLVjFocWR2d3JaTiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzODU3NzA2OTY0NjM4NTE1NCwidXNlck5hbWUiOiJzeGRsIiwiZGVwdElkIjoxOTM4Nzk1NzYzODkwNzUzNTM4LCJkZXB0TmFtZSI6IumdkuWym-WPjOS_oeeUteWKm-iuvuWkh-aciemZkOWFrOWPuCIsImRlcHRDYXRlZ29yeSI6IiJ9.CmDJKymg4z0POP1MtcqMIuFvldMDFUJe9FqfkHZJJCg"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 14:30:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[22]毫秒
2025-07-12 14:30:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:30:36 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 14:30:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-12 14:30:36 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-12 14:30:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-12 14:30:37 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[75]毫秒
2025-07-12 14:30:37 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[75]毫秒
2025-07-12 14:30:37 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[83]毫秒
2025-07-12 14:30:37 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[470]毫秒
2025-07-12 14:30:37 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[1025]毫秒
2025-07-12 14:30:41 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:30:42 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[787]毫秒
2025-07-12 14:30:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:30:59 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[745]毫秒
2025-07-12 14:31:01 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:31:02 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[665]毫秒
2025-07-12 14:31:07 [boundedElastic-223] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:31:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[813]毫秒
2025-07-12 14:33:45 [boundedElastic-242] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:33:46 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[866]毫秒
2025-07-12 14:39:13 [boundedElastic-240] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"deptId":["1938795763890753538"]}]
2025-07-12 14:39:14 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[922]毫秒
2025-07-12 14:39:18 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 14:39:18 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:39:18 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[163]毫秒
2025-07-12 14:39:19 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[432]毫秒
2025-07-12 14:39:22 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getCompanyInfoQr],无参数
2025-07-12 14:39:22 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getCompanyInfoQr],耗时:[543]毫秒
2025-07-12 14:46:47 [boundedElastic-243] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-12 14:46:47 [boundedElastic-235] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-12 14:46:47 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[44]毫秒
2025-07-12 14:46:48 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[403]毫秒
2025-07-12 14:46:49 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 14:46:49 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 14:46:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[6]毫秒
2025-07-12 14:46:49 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[99]毫秒
2025-07-12 14:47:02 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 14:47:03 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[782]毫秒
2025-07-12 14:47:03 [boundedElastic-249] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 14:47:03 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[525]毫秒
2025-07-12 14:47:04 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 14:47:04 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[87]毫秒
2025-07-12 14:47:04 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 14:47:04 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-07-12 14:47:04 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 14:47:04 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[10]毫秒
2025-07-12 14:47:04 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJteGxGa3lEY3Zqanhyc1NubTlkdE5kNmV2aWlVNXV3aCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuiIqui_kOe7vOWQiOeuoeeQhuW5s-WPsCIsImRlcHRDYXRlZ29yeSI6IiJ9.u_AxV9yEJhVhfcAclEu85Qr702D_qyVSqBFri6WYmxI"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 14:47:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[29]毫秒
2025-07-12 14:47:06 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-12 14:47:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:47:06 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-12 14:47:06 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 14:47:06 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[29]毫秒
2025-07-12 14:47:06 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[32]毫秒
2025-07-12 14:47:07 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-12 14:47:07 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[25]毫秒
2025-07-12 14:47:07 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[229]毫秒
2025-07-12 14:47:07 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[677]毫秒
2025-07-12 14:47:14 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-07-12 14:47:14 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[767]毫秒
2025-07-12 14:47:23 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 14:47:24 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[467]毫秒
2025-07-12 14:47:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 14:47:33 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:47:33 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-12 14:47:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[6]毫秒
2025-07-12 14:47:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[306]毫秒
2025-07-12 14:47:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[760]毫秒
2025-07-12 14:47:40 [boundedElastic-242] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_common_status],无参数
2025-07-12 14:47:40 [boundedElastic-244] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_oper_type],无参数
2025-07-12 14:47:40 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/operlog/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["operTime"],"isAsc":["descending"]}]
2025-07-12 14:47:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_common_status],耗时:[49]毫秒
2025-07-12 14:47:40 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_oper_type],耗时:[72]毫秒
2025-07-12 14:47:40 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/operlog/list],耗时:[292]毫秒
2025-07-12 14:47:41 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/logininfor/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"orderByColumn":["loginTime"],"isAsc":["descending"]}]
2025-07-12 14:47:41 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_device_type],无参数
2025-07-12 14:47:41 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_device_type],耗时:[32]毫秒
2025-07-12 14:47:41 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/logininfor/list],耗时:[227]毫秒
2025-07-12 14:47:52 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_grant_type],无参数
2025-07-12 14:47:52 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_grant_type],耗时:[45]毫秒
2025-07-12 14:47:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/client/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:47:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/client/list],耗时:[253]毫秒
2025-07-12 14:48:04 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/online/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:48:05 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/online/list],耗时:[1592]毫秒
2025-07-12 14:48:18 [boundedElastic-244] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /monitor/online/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTQzMTQzNTc0MjU2MzA4MjI2Iiwicm5TdHIiOiJzN09MTUZiWUhsNnAzdmtBbVRmc1RRRkd5R1BoZUdZMyIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTk0MzE0MzU3NDI1NjMwODIyNiwiZGVwdElkIjoxOTM4Nzk1NzYzODkwNzUzNTM4fQ.nxALxBE6D81rg3Y6_o8OO4qtDOtUjk3w2RG9hbSIFTc],无参数
2025-07-12 14:48:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /monitor/online/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJhcHBfdXNlcjoxOTQzMTQzNTc0MjU2MzA4MjI2Iiwicm5TdHIiOiJzN09MTUZiWUhsNnAzdmtBbVRmc1RRRkd5R1BoZUdZMyIsImNsaWVudGlkIjoiNDI4YTgzMTBjZDQ0Mjc1N2FlNjk5ZGY1ZDg5NGYwNTEiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MTk0MzE0MzU3NDI1NjMwODIyNiwiZGVwdElkIjoxOTM4Nzk1NzYzODkwNzUzNTM4fQ.nxALxBE6D81rg3Y6_o8OO4qtDOtUjk3w2RG9hbSIFTc],耗时:[396]毫秒
2025-07-12 14:48:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/online/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:48:20 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/online/list],耗时:[1502]毫秒
2025-07-12 14:48:48 [boundedElastic-240] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-12 14:48:48 [boundedElastic-243] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-12 14:48:48 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[27]毫秒
2025-07-12 14:48:48 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[352]毫秒
2025-07-12 14:48:49 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 14:48:49 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 14:48:49 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-12 14:48:50 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[108]毫秒
2025-07-12 14:48:55 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 14:48:56 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[856]毫秒
2025-07-12 14:48:56 [boundedElastic-240] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 14:48:56 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[566]毫秒
2025-07-12 14:48:57 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 14:48:57 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[119]毫秒
2025-07-12 14:48:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 14:48:57 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-12 14:48:57 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 14:48:57 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-12 14:48:57 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJwY2I5S1Jrbk9vUkM1V05YZU9zN2hUV25FUEJSaXNJcCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.LBtdGlRMpGru-6y6g8IZoUgyY_8WR4uv5efwNj9BzIY"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 14:48:57 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[23]毫秒
2025-07-12 14:49:00 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-12 14:49:00 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-12 14:49:00 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 14:49:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:49:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[31]毫秒
2025-07-12 14:49:00 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[31]毫秒
2025-07-12 14:49:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-12 14:49:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[24]毫秒
2025-07-12 14:49:00 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[330]毫秒
2025-07-12 14:49:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[766]毫秒
2025-07-12 14:49:02 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getCompanyInfoQr],无参数
2025-07-12 14:49:02 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getCompanyInfoQr],耗时:[343]毫秒
2025-07-12 14:49:12 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:49:13 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[746]毫秒
2025-07-12 14:49:44 [boundedElastic-251] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:49:45 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[839]毫秒
2025-07-12 14:49:53 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:49:53 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 14:49:54 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[223]毫秒
2025-07-12 14:49:54 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[663]毫秒
2025-07-12 14:49:56 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 14:49:56 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:49:56 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[322]毫秒
2025-07-12 14:49:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[356]毫秒
2025-07-12 14:49:57 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_status],无参数
2025-07-12 14:49:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_type],无参数
2025-07-12 14:49:57 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_type],耗时:[24]毫秒
2025-07-12 14:49:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_status],耗时:[24]毫秒
2025-07-12 14:49:57 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:49:58 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[283]毫秒
2025-07-12 14:50:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:50:14 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[777]毫秒
2025-07-12 14:51:04 [boundedElastic-254] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/emp/1934265922029981698],无参数
2025-07-12 14:51:05 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/emp/1934265922029981698],耗时:[1002]毫秒
2025-07-12 14:51:08 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/emp/1943143574256308226],无参数
2025-07-12 14:51:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/emp/1943143574256308226],耗时:[896]毫秒
2025-07-12 14:52:11 [boundedElastic-238] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/optionselect],参数类型[param],参数:[{"deptId":["1937027771376963585"]}]
2025-07-12 14:52:11 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/optionselect],耗时:[354]毫秒
2025-07-12 14:52:16 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/emp],参数类型[json],参数:[{"userId":"1943143574256308226","deptId":"1937027771376963585","userName":"15606392168","nickName":"qingyi","password":"","phonenumber":"15606392168","email":"","sex":"0","status":"0","remark":null,"postIds":[],"roleIds":["1934263628970430466"],"tenantId":"000000","userType":"app_user","avatar":"http://xhry-bucket.oss-cn-beijing.aliyuncs.com/2025/07/10/4aaa4b07195f41edb7e57fc011de7054.jpeg","isSubscribed":"0","loginIp":"**************","loginDate":"2025-07-12 14:48:42","createTime":"2025-07-10 11:01:45","deptName":"青岛孚润德船舶管理有限公司","companyName":null,"roles":[],"roleId":null}]
2025-07-12 14:52:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/emp],耗时:[1127]毫秒
2025-07-12 14:52:17 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:52:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[811]毫秒
2025-07-12 14:52:54 [boundedElastic-256] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 14:52:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[189]毫秒
2025-07-12 14:56:33 [boundedElastic-240] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:56:34 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[1061]毫秒
2025-07-12 14:56:37 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 14:56:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[613]毫秒
2025-07-12 14:56:38 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 14:56:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[94]毫秒
2025-07-12 14:56:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 14:56:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[69]毫秒
2025-07-12 14:56:39 [boundedElastic-240] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 14:56:39 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[69]毫秒
2025-07-12 14:56:39 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:56:39 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-12 14:56:39 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 14:56:39 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-12 14:56:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-12 14:56:39 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[34]毫秒
2025-07-12 14:56:39 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[32]毫秒
2025-07-12 14:56:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[31]毫秒
2025-07-12 14:56:39 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[374]毫秒
2025-07-12 14:56:39 [boundedElastic-240] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJwY2I5S1Jrbk9vUkM1V05YZU9zN2hUV25FUEJSaXNJcCIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.LBtdGlRMpGru-6y6g8IZoUgyY_8WR4uv5efwNj9BzIY"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 14:56:39 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[40]毫秒
2025-07-12 14:56:40 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[811]毫秒
2025-07-12 14:57:58 [boundedElastic-259] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-12 14:57:58 [boundedElastic-258] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-12 14:57:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[27]毫秒
2025-07-12 14:57:58 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[110]毫秒
2025-07-12 14:57:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 14:57:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[107]毫秒
2025-07-12 14:57:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 14:57:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-12 14:58:01 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 14:58:02 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1002]毫秒
2025-07-12 14:58:02 [boundedElastic-258] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 14:58:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[617]毫秒
2025-07-12 14:58:03 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 14:58:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[75]毫秒
2025-07-12 14:58:03 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 14:58:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-12 14:58:04 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 14:58:04 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 14:58:04 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-12 14:58:04 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJVODVISGFGc3p3TDFSR3l0N0ZlTWt3ckJlWmc3WjNGMSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.zecv9lemY1m7AvV9FEkoHN8_xp4egVFNdSKi76rd-kg"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 14:58:04 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 14:58:04 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-12 14:58:04 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[320]毫秒
2025-07-12 14:58:04 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[25]毫秒
2025-07-12 14:58:04 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[24]毫秒
2025-07-12 14:58:05 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[792]毫秒
2025-07-12 14:58:10 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 14:58:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[850]毫秒
2025-07-12 15:13:47 [nacos-grpc-client-executor-localhost-4108] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 353
2025-07-12 15:13:47 [nacos-grpc-client-executor-localhost-4108] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 15:13:47 [nacos-grpc-client-executor-localhost-4108] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 15:13:47 [nacos-grpc-client-executor-localhost-4108] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 353
2025-07-12 15:14:10 [nacos-grpc-client-executor-localhost-4115] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 356
2025-07-12 15:14:10 [nacos-grpc-client-executor-localhost-4115] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 15:14:10 [nacos-grpc-client-executor-localhost-4115] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 15:14:10 [nacos-grpc-client-executor-localhost-4115] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 356
2025-07-12 15:14:22 [boundedElastic-266] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 15:14:24 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[1741]毫秒
2025-07-12 15:18:10 [boundedElastic-268] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 15:18:10 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[544]毫秒
2025-07-12 15:21:17 [boundedElastic-273] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 15:21:17 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[612]毫秒
2025-07-12 15:21:34 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 15:21:34 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[547]毫秒
2025-07-12 15:38:20 [nacos-grpc-client-executor-localhost-4457] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 367
2025-07-12 15:38:20 [nacos-grpc-client-executor-localhost-4457] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 15:38:20 [nacos-grpc-client-executor-localhost-4457] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 15:38:20 [nacos-grpc-client-executor-localhost-4457] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 367
2025-07-12 15:38:43 [nacos-grpc-client-executor-localhost-4461] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 370
2025-07-12 15:38:43 [nacos-grpc-client-executor-localhost-4461] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 15:38:43 [nacos-grpc-client-executor-localhost-4461] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 15:38:43 [nacos-grpc-client-executor-localhost-4461] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 370
2025-07-12 16:05:15 [nacos-grpc-client-executor-localhost-4840] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 381
2025-07-12 16:05:15 [nacos-grpc-client-executor-localhost-4840] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:05:15 [nacos-grpc-client-executor-localhost-4840] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 16:05:15 [nacos-grpc-client-executor-localhost-4840] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 381
2025-07-12 16:05:37 [nacos-grpc-client-executor-localhost-4846] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 384
2025-07-12 16:05:37 [nacos-grpc-client-executor-localhost-4846] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:05:37 [nacos-grpc-client-executor-localhost-4846] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:05:37 [nacos-grpc-client-executor-localhost-4846] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 384
2025-07-12 16:06:00 [boundedElastic-293] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-12 16:06:00 [boundedElastic-294] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-12 16:06:00 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[101]毫秒
2025-07-12 16:06:00 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[363]毫秒
2025-07-12 16:06:01 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 16:06:01 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 16:06:01 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[9]毫秒
2025-07-12 16:06:01 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[175]毫秒
2025-07-12 16:06:04 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 16:06:06 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1312]毫秒
2025-07-12 16:06:06 [boundedElastic-293] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 16:06:08 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1796]毫秒
2025-07-12 16:06:08 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 16:06:08 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[117]毫秒
2025-07-12 16:06:08 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 16:06:08 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[57]毫秒
2025-07-12 16:06:09 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 16:06:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 16:06:09 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-07-12 16:06:09 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-12 16:06:09 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:06:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJ1NVRjdWlUb3U4VVB6Y1hGNW9xRnpHTVFSME1WR2cwZSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.WGrWsC2iP79ORnl7o2oBRibBKdaR4OBHQptB-DvCgWQ"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 16:06:09 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[54]毫秒
2025-07-12 16:06:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[30]毫秒
2025-07-12 16:06:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[489]毫秒
2025-07-12 16:06:10 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[904]毫秒
2025-07-12 16:06:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 16:06:16 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[880]毫秒
2025-07-12 16:06:55 [boundedElastic-298] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:06:56 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[897]毫秒
2025-07-12 16:08:53 [boundedElastic-298] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 16:08:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[771]毫秒
2025-07-12 16:08:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:08:55 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[790]毫秒
2025-07-12 16:12:14 [boundedElastic-295] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 16:12:18 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[4245]毫秒
2025-07-12 16:15:11 [nacos-grpc-client-executor-localhost-4979] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 395
2025-07-12 16:15:11 [nacos-grpc-client-executor-localhost-4979] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:15:11 [nacos-grpc-client-executor-localhost-4979] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 16:15:11 [nacos-grpc-client-executor-localhost-4979] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 395
2025-07-12 16:15:37 [nacos-grpc-client-executor-localhost-4985] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 398
2025-07-12 16:15:37 [nacos-grpc-client-executor-localhost-4985] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:15:37 [nacos-grpc-client-executor-localhost-4985] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:15:37 [nacos-grpc-client-executor-localhost-4985] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 398
2025-07-12 16:15:43 [boundedElastic-301] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:15:46 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[2661]毫秒
2025-07-12 16:15:51 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 16:15:59 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[8301]毫秒
2025-07-12 16:15:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:16:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[730]毫秒
2025-07-12 16:21:39 [nacos-grpc-client-executor-localhost-5069] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 409
2025-07-12 16:21:39 [nacos-grpc-client-executor-localhost-5069] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:21:39 [nacos-grpc-client-executor-localhost-5069] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 16:21:39 [nacos-grpc-client-executor-localhost-5069] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 409
2025-07-12 16:22:00 [nacos-grpc-client-executor-localhost-5074] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 412
2025-07-12 16:22:00 [nacos-grpc-client-executor-localhost-5074] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:22:00 [nacos-grpc-client-executor-localhost-5074] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:22:00 [nacos-grpc-client-executor-localhost-5074] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 412
2025-07-12 16:22:58 [boundedElastic-276] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 16:23:54 [nacos-grpc-client-executor-localhost-5100] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 433
2025-07-12 16:23:54 [nacos-grpc-client-executor-localhost-5100] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:23:54 [nacos-grpc-client-executor-localhost-5100] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 16:23:54 [nacos-grpc-client-executor-localhost-5100] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 433
2025-07-12 16:24:57 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[118593]毫秒
2025-07-12 16:24:59 [nacos-grpc-client-executor-localhost-5114] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 443
2025-07-12 16:24:59 [nacos-grpc-client-executor-localhost-5114] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:24:59 [nacos-grpc-client-executor-localhost-5114] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:24:59 [nacos-grpc-client-executor-localhost-5114] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 443
2025-07-12 16:28:06 [boundedElastic-309] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:28:07 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[1003]毫秒
2025-07-12 16:28:12 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 16:28:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[468]毫秒
2025-07-12 16:28:30 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:28:31 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[787]毫秒
2025-07-12 16:39:37 [nacos-grpc-client-executor-localhost-5317] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 449
2025-07-12 16:39:37 [nacos-grpc-client-executor-localhost-5317] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:39:37 [nacos-grpc-client-executor-localhost-5317] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 16:39:37 [nacos-grpc-client-executor-localhost-5317] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 449
2025-07-12 16:40:00 [nacos-grpc-client-executor-localhost-5322] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 452
2025-07-12 16:40:00 [nacos-grpc-client-executor-localhost-5322] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:40:00 [nacos-grpc-client-executor-localhost-5322] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:40:00 [nacos-grpc-client-executor-localhost-5322] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 452
2025-07-12 16:40:13 [boundedElastic-318] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:40:16 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[2635]毫秒
2025-07-12 16:40:18 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getCompanyInfoQr],无参数
2025-07-12 16:40:18 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getCompanyInfoQr],耗时:[370]毫秒
2025-07-12 16:40:49 [boundedElastic-308] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:40:50 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[831]毫秒
2025-07-12 16:41:32 [boundedElastic-307] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 16:41:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[897]毫秒
2025-07-12 16:43:46 [boundedElastic-317] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:43:47 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[881]毫秒
2025-07-12 16:44:38 [boundedElastic-309] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 16:44:38 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[814]毫秒
2025-07-12 16:44:39 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:44:40 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[800]毫秒
2025-07-12 16:51:28 [nacos-grpc-client-executor-localhost-5481] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 463
2025-07-12 16:51:28 [nacos-grpc-client-executor-localhost-5481] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:51:28 [nacos-grpc-client-executor-localhost-5481] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 16:51:28 [nacos-grpc-client-executor-localhost-5481] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 463
2025-07-12 16:51:56 [nacos-grpc-client-executor-localhost-5487] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 466
2025-07-12 16:51:56 [nacos-grpc-client-executor-localhost-5487] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:51:56 [nacos-grpc-client-executor-localhost-5487] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:51:56 [nacos-grpc-client-executor-localhost-5487] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 466
2025-07-12 16:56:10 [boundedElastic-328] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:56:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getCompanyInfoQr],无参数
2025-07-12 16:56:13 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[3549]毫秒
2025-07-12 16:56:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getCompanyInfoQr],耗时:[447]毫秒
2025-07-12 16:56:27 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:56:28 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[786]毫秒
2025-07-12 16:56:31 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 16:56:32 [boundedElastic-331] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-12 16:56:32 [boundedElastic-331] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-12 16:56:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[68]毫秒
2025-07-12 16:56:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[822]毫秒
2025-07-12 16:56:32 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[454]毫秒
2025-07-12 16:56:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 16:56:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 16:56:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-12 16:56:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[109]毫秒
2025-07-12 16:57:12 [nacos-grpc-client-executor-localhost-5563] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 477
2025-07-12 16:57:12 [nacos-grpc-client-executor-localhost-5563] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:57:12 [nacos-grpc-client-executor-localhost-5563] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 16:57:12 [nacos-grpc-client-executor-localhost-5563] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 477
2025-07-12 16:57:14 [boundedElastic-330] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 16:57:14 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[84]毫秒
2025-07-12 16:57:14 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 16:57:14 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[91]毫秒
2025-07-12 16:58:59 [nacos-grpc-client-executor-localhost-5588] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 481
2025-07-12 16:58:59 [nacos-grpc-client-executor-localhost-5588] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:58:59 [nacos-grpc-client-executor-localhost-5588] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 16:58:59 [nacos-grpc-client-executor-localhost-5588] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 481
2025-07-12 16:59:46 [boundedElastic-334] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 16:59:46 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[435]毫秒
2025-07-12 16:59:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-12 16:59:46 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[91]毫秒
2025-07-12 16:59:50 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-12 16:59:51 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[981]毫秒
2025-07-12 16:59:51 [boundedElastic-313] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 16:59:53 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1954]毫秒
2025-07-12 16:59:54 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 16:59:54 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[100]毫秒
2025-07-12 16:59:54 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 16:59:54 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[51]毫秒
2025-07-12 16:59:54 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 16:59:54 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-12 16:59:54 [boundedElastic-313] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJrSHNjRjBPekw5dkd4blp0RHYyb25rNmJRbjJKSWlyZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.JUZWGyeTBBvso0tDGMI89r8RDSXmF6w32fMBs5ij8RY"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 16:59:54 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[27]毫秒
2025-07-12 16:59:57 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 16:59:57 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 16:59:57 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-12 16:59:57 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[46]毫秒
2025-07-12 16:59:58 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[484]毫秒
2025-07-12 16:59:58 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[805]毫秒
2025-07-12 17:00:00 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:00:01 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[771]毫秒
2025-07-12 17:00:02 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getCompanyInfoQr],无参数
2025-07-12 17:00:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getCompanyInfoQr],耗时:[333]毫秒
2025-07-12 17:00:23 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:00:23 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[817]毫秒
2025-07-12 17:00:34 [boundedElastic-319] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 17:00:35 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[870]毫秒
2025-07-12 17:00:36 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:00:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[802]毫秒
2025-07-12 17:05:22 [nacos-grpc-client-executor-localhost-5678] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 492
2025-07-12 17:05:22 [nacos-grpc-client-executor-localhost-5678] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 17:05:22 [nacos-grpc-client-executor-localhost-5678] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-system -> []
2025-07-12 17:05:22 [nacos-grpc-client-executor-localhost-5678] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 492
2025-07-12 17:05:47 [nacos-grpc-client-executor-localhost-5683] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = NotifySubscriberRequest, requestId = 495
2025-07-12 17:05:47 [nacos-grpc-client-executor-localhost-5683] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 17:05:47 [nacos-grpc-client-executor-localhost-5683] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
2025-07-12 17:05:47 [nacos-grpc-client-executor-localhost-5683] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = NotifySubscriberRequest, requestId = 495
2025-07-12 17:06:43 [boundedElastic-309] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:06:46 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[2716]毫秒
2025-07-12 17:06:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getCompanyInfoQr],无参数
2025-07-12 17:06:48 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getCompanyInfoQr],耗时:[370]毫秒
2025-07-12 17:07:01 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:07:01 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[802]毫秒
2025-07-12 17:07:29 [boundedElastic-309] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:07:30 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[861]毫秒
2025-07-12 17:07:59 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/emp/leave/1943143574256308226],无参数
2025-07-12 17:08:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/emp/leave/1943143574256308226],耗时:[779]毫秒
2025-07-12 17:08:01 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:08:01 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[850]毫秒
2025-07-12 17:15:01 [boundedElastic-337] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:15:03 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[2401]毫秒
2025-07-12 17:15:03 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:15:04 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[374]毫秒
2025-07-12 17:15:05 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:15:05 [boundedElastic-336] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-12 17:15:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:15:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-12 17:15:06 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[295]毫秒
2025-07-12 17:15:07 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[1593]毫秒
2025-07-12 17:15:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/rmb_purpose],无参数
2025-07-12 17:15:15 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:15:15 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/rmb_purpose],耗时:[60]毫秒
2025-07-12 17:15:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[323]毫秒
2025-07-12 17:15:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:15:16 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbFlow/list],耗时:[200]毫秒
2025-07-12 17:19:31 [boundedElastic-338] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 17:19:31 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[690]毫秒
2025-07-12 17:19:32 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 17:19:32 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[89]毫秒
2025-07-12 17:19:32 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 17:19:32 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[69]毫秒
2025-07-12 17:19:32 [boundedElastic-338] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:19:32 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/rmb_purpose],无参数
2025-07-12 17:19:32 [boundedElastic-338] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 17:19:32 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/rmb_purpose],耗时:[28]毫秒
2025-07-12 17:19:32 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[141]毫秒
2025-07-12 17:19:33 [boundedElastic-338] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJrSHNjRjBPekw5dkd4blp0RHYyb25rNmJRbjJKSWlyZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.JUZWGyeTBBvso0tDGMI89r8RDSXmF6w32fMBs5ij8RY"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 17:19:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[333]毫秒
2025-07-12 17:19:33 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[80]毫秒
2025-07-12 17:19:33 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:19:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbFlow/list],耗时:[189]毫秒
2025-07-12 17:22:58 [boundedElastic-337] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-12 17:22:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[816]毫秒
2025-07-12 17:22:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 17:22:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[86]毫秒
2025-07-12 17:22:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-12 17:22:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[35]毫秒
2025-07-12 17:22:59 [boundedElastic-337] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-12 17:22:59 [boundedElastic-337] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:22:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/rmb_purpose],无参数
2025-07-12 17:22:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/rmb_purpose],耗时:[30]毫秒
2025-07-12 17:22:59 [boundedElastic-337] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxOTM0MjU5MjU4MTA3MjAzNTg1Iiwicm5TdHIiOiJrSHNjRjBPekw5dkd4blp0RHYyb25rNmJRbjJKSWlyZiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IiIsInVzZXJJZCI6MTkzNDI1OTI1ODEwNzIwMzU4NSwidXNlck5hbWUiOiJmcmQiLCJkZXB0SWQiOjE5MzQyNjIxNjExODYzMjg1NzgsImRlcHROYW1lIjoi6Z2S5bKb5a2a5ram5b636Ii56Ii2566h55CG5pyJ6ZmQ5YWs5Y-4IiwiZGVwdENhdGVnb3J5IjoiIn0.JUZWGyeTBBvso0tDGMI89r8RDSXmF6w32fMBs5ij8RY"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-07-12 17:23:00 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[233]毫秒
2025-07-12 17:23:00 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[189]毫秒
2025-07-12 17:23:00 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[588]毫秒
2025-07-12 17:23:00 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:00 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbFlow/list],耗时:[275]毫秒
2025-07-12 17:23:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:23:19 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-12 17:23:19 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[55]毫秒
2025-07-12 17:23:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[466]毫秒
2025-07-12 17:23:20 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-12 17:23:20 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 17:23:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:23:20 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[42]毫秒
2025-07-12 17:23:21 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-12 17:23:21 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[324]毫秒
2025-07-12 17:23:21 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[54]毫秒
2025-07-12 17:23:21 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[831]毫秒
2025-07-12 17:23:23 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 17:23:23 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[106]毫秒
2025-07-12 17:23:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:23:24 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[371]毫秒
2025-07-12 17:23:27 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_status],无参数
2025-07-12 17:23:27 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_todo_status],无参数
2025-07-12 17:23:27 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_todo_status],耗时:[37]毫秒
2025-07-12 17:23:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_status],耗时:[58]毫秒
2025-07-12 17:23:27 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:23:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/list],耗时:[363]毫秒
2025-07-12 17:23:31 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:23:31 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:31 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[48]毫秒
2025-07-12 17:23:31 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:23:31 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:31 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[25]毫秒
2025-07-12 17:23:31 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[219]毫秒
2025-07-12 17:23:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1189]毫秒
2025-07-12 17:23:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:23:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-12 17:23:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[273]毫秒
2025-07-12 17:23:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:23:34 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[193]毫秒
2025-07-12 17:23:38 [boundedElastic-336] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:38 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[284]毫秒
2025-07-12 17:23:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[271]毫秒
2025-07-12 17:23:41 [boundedElastic-336] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-12 17:23:41 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:41 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-12 17:23:42 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[238]毫秒
2025-07-12 17:23:42 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:42 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[595]毫秒
2025-07-12 17:23:44 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:44 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:23:44 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getTotalProperty],耗时:[230]毫秒
2025-07-12 17:23:44 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[236]毫秒
2025-07-12 17:23:45 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:23:45 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/list],耗时:[331]毫秒
2025-07-12 17:23:49 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:23:49 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-12 17:27:07 [boundedElastic-350] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:27:08 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-12 17:27:39 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:27:39 [boundedElastic-328] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:27:39 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:27:39 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-12 17:27:40 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[311]毫秒
2025-07-12 17:27:41 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1364]毫秒
2025-07-12 17:27:41 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:27:41 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[32]毫秒
2025-07-12 17:27:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:27:43 [boundedElastic-328] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:27:43 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[58]毫秒
2025-07-12 17:27:44 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1032]毫秒
2025-07-12 17:27:45 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:27:45 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[74]毫秒
2025-07-12 17:27:52 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:27:52 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-12 17:27:53 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[407]毫秒
2025-07-12 17:27:53 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[295]毫秒
2025-07-12 17:27:55 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:27:55 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[354]毫秒
2025-07-12 17:29:33 [boundedElastic-333] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:29:33 [boundedElastic-326] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:29:33 [boundedElastic-339] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:29:33 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[62]毫秒
2025-07-12 17:29:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:29:33 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-12 17:29:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[304]毫秒
2025-07-12 17:29:34 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:29:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-12 17:29:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1204]毫秒
2025-07-12 17:29:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:29:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-12 17:29:58 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:29:58 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:29:58 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:29:58 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[66]毫秒
2025-07-12 17:29:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:29:58 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[28]毫秒
2025-07-12 17:29:59 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[332]毫秒
2025-07-12 17:29:59 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1095]毫秒
2025-07-12 17:30:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:00 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[43]毫秒
2025-07-12 17:30:08 [boundedElastic-347] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-12 17:30:08 [boundedElastic-351] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:08 [boundedElastic-337] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-12 17:30:08 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[66]毫秒
2025-07-12 17:30:08 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:08 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[24]毫秒
2025-07-12 17:30:08 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[288]毫秒
2025-07-12 17:30:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1164]毫秒
2025-07-12 17:30:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-12 17:30:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1943563238748774401],无参数
2025-07-12 17:30:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],无参数
2025-07-12 17:30:16 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[64]毫秒
2025-07-12 17:30:17 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1943563238748774401],耗时:[494]毫秒
2025-07-12 17:30:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],耗时:[515]毫秒
2025-07-12 17:30:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-12 17:30:17 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],耗时:[352]毫秒
2025-07-12 17:30:23 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[65]毫秒
2025-07-12 17:30:25 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],无参数
2025-07-12 17:30:25 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1943563238748774401],无参数
2025-07-12 17:30:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-12 17:30:25 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],耗时:[378]毫秒
2025-07-12 17:30:25 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1943563238748774401],耗时:[378]毫秒
2025-07-12 17:30:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-12 17:30:26 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],耗时:[275]毫秒
2025-07-12 17:30:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:27 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[41]毫秒
2025-07-12 17:30:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:38 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[60]毫秒
2025-07-12 17:30:52 [boundedElastic-326] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],无参数
2025-07-12 17:30:52 [boundedElastic-328] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:30:52 [boundedElastic-348] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1943563238748774401],无参数
2025-07-12 17:30:52 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[69]毫秒
2025-07-12 17:30:53 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],耗时:[361]毫秒
2025-07-12 17:30:53 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1943563238748774401],耗时:[399]毫秒
2025-07-12 17:30:53 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-12 17:30:53 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],耗时:[245]毫秒
2025-07-12 17:31:16 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:31:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[58]毫秒
2025-07-12 17:31:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_type],无参数
2025-07-12 17:31:25 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_status],无参数
2025-07-12 17:31:25 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_status],耗时:[53]毫秒
2025-07-12 17:31:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_type],耗时:[53]毫秒
2025-07-12 17:31:25 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:31:26 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[396]毫秒
2025-07-12 17:31:26 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderConfig/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-12 17:31:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],无参数
2025-07-12 17:31:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/business_type],无参数
2025-07-12 17:31:26 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/business_type],耗时:[29]毫秒
2025-07-12 17:31:27 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderConfig/list],耗时:[309]毫秒
2025-07-12 17:31:27 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[763]毫秒
2025-07-12 17:31:27 [boundedElastic-348] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-12 17:31:28 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[90]毫秒
2025-07-12 17:31:28 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-12 17:31:28 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[167]毫秒
2025-07-12 17:31:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:31:36 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-12 17:31:57 [boundedElastic-353] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1943563238748774401],无参数
2025-07-12 17:31:57 [boundedElastic-352] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],无参数
2025-07-12 17:31:57 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:31:58 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[83]毫秒
2025-07-12 17:31:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],耗时:[348]毫秒
2025-07-12 17:31:58 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1943563238748774401],耗时:[376]毫秒
2025-07-12 17:31:58 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-12 17:31:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],耗时:[265]毫秒
2025-07-12 17:32:29 [boundedElastic-351] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:32:29 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-12 17:32:50 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:32:50 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[74]毫秒
2025-07-12 17:32:51 [boundedElastic-328] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],无参数
2025-07-12 17:32:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:32:51 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1943563238748774401],无参数
2025-07-12 17:32:51 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-12 17:32:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1943563238748774401],耗时:[510]毫秒
2025-07-12 17:32:52 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1943563238748774401],耗时:[519]毫秒
2025-07-12 17:32:52 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-12 17:32:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1943563238748774401],耗时:[268]毫秒
2025-07-12 17:32:57 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:32:57 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[58]毫秒
2025-07-12 17:33:03 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:33:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[67]毫秒
2025-07-12 17:33:07 [boundedElastic-328] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:33:07 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[41]毫秒
2025-07-12 17:33:09 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1942582529079148545],无参数
2025-07-12 17:33:09 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1942582529079148545],无参数
2025-07-12 17:33:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:33:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[64]毫秒
2025-07-12 17:33:10 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1942582529079148545],耗时:[388]毫秒
2025-07-12 17:33:10 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1942582529079148545],耗时:[400]毫秒
2025-07-12 17:33:10 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1942582529079148545],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-12 17:33:10 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1942582529079148545],耗时:[269]毫秒
2025-07-12 17:33:16 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:33:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-12 17:33:46 [boundedElastic-342] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-12 17:33:46 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[62]毫秒
2025-07-12 18:41:12 [nacos-grpc-client-executor-localhost-5315] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 505
2025-07-12 18:41:12 [nacos-grpc-client-executor-localhost-5310] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 531
2025-07-12 18:41:12 [nacos-grpc-client-executor-localhost-5315] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 505
2025-07-12 18:41:12 [nacos-grpc-client-executor-localhost-5310] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 531
2025-07-12 19:03:11 [nacos-grpc-client-executor-localhost-6686] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Receive server push request, request = ClientDetectionRequest, requestId = 539
2025-07-12 19:03:11 [nacos-grpc-client-executor-localhost-5432] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 534
2025-07-12 19:03:11 [nacos-grpc-client-executor-localhost-5432] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 534
2025-07-12 19:03:11 [nacos-grpc-client-executor-localhost-6686] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Ack server push request, request = ClientDetectionRequest, requestId = 539
2025-07-12 19:03:11 [nacos-grpc-client-executor-localhost-5428] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 547
2025-07-12 19:03:11 [nacos-grpc-client-executor-localhost-5428] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 547
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - [DEREGISTER-SERVICE] dev deregistering service yumeng-gateway with instance: Instance{instanceId='null', ip='************', port=8080, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
2025-07-12 19:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-12 19:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [6f0905a4-3ac3-4e38-9b02-746fd5e0d889_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [8c25f983-bfcf-44c9-8c01-795301fd4f30_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-12 19:03:45 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming - Redo instance operation UNREGISTER for DEFAULT_GROUP@@yumeng-gateway
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown naming grpc client proxy for  uuid->3306556b-9089-4c23-b35d-0c42dc49d068
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@56133890[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 9395]
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown rpc client, set status to shutdown
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@77ec5428[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-12 19:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [3306556b-9089-4c23-b35d-0c42dc49d068] Client is shutdown, stop reconnect to server
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  c.alibaba.nacos.common.remote.client - Close current connection 1752280359886_127.0.0.1_2163
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client.grpc.GrpcClient - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@69d8563c[Running, pool size = 31, active threads = 0, queued tasks = 0, completed tasks = 6743]
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - shutdown and remove naming rpc client  for uuid ->3306556b-9089-4c23-b35d-0c42dc49d068
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  c.a.n.c.a.r.i.CredentialWatcher - [null] CredentialWatcher is stopped
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  c.a.n.c.a.r.i.CredentialService - [null] CredentialService is freed
2025-07-12 19:03:45 [SpringApplicationShutdownHook] INFO  com.alibaba.nacos.client.naming - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
