2025-07-22 08:35:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 08:35:14 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 14396 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 08:35:14 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-22 08:35:14 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-22 08:35:14 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 08:35:20 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 08:35:20 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 08:35:20 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 08:35:21 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 08:35:21 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 08:35:26 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway ************:8080 register finished
2025-07-22 08:35:27 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-22 08:35:27 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 18.487 seconds (process running for 21.376)
2025-07-22 08:35:27 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 08:35:27 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-22 08:38:27 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:yumeng-salary-settlement, group:DEFAULT_GROUP, clusters: 
2025-07-22 08:38:27 [boundedElastic-9] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:yumeng-system, group:DEFAULT_GROUP, clusters: 
2025-07-22 08:38:27 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:yumeng-system, group:DEFAULT_GROUP, clusters: 
2025-07-22 08:38:27 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:yumeng-salary-settlement, group:DEFAULT_GROUP, clusters: 
2025-07-22 08:38:27 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:yumeng-salary-settlement, group:DEFAULT_GROUP, cluster: 
2025-07-22 08:38:27 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:yumeng-system, group:DEFAULT_GROUP, cluster: 
2025-07-22 08:38:27 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:yumeng-salary-settlement, group:DEFAULT_GROUP, cluster: 
2025-07-22 08:38:27 [boundedElastic-9] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:yumeng-system, group:DEFAULT_GROUP, cluster: 
2025-07-22 08:38:27 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 08:38:27 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@yumeng-salary-settlement -> [{"instanceId":"************#9401##DEFAULT_GROUP@@yumeng-salary-settlement","ip":"************","port":9401,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-salary-settlement","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 08:38:27 [boundedElastic-6] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-system -> [{"instanceId":"************#9201##DEFAULT_GROUP@@yumeng-system","ip":"************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-system","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 08:38:27 [boundedElastic-5] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-salary-settlement -> [{"instanceId":"************#9401##DEFAULT_GROUP@@yumeng-salary-settlement","ip":"************","port":9401,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-salary-settlement","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 08:38:27 [nacos-grpc-client-executor-localhost-91] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Receive server push request, request = NotifySubscriberRequest, requestId = 31
2025-07-22 08:38:27 [nacos-grpc-client-executor-localhost-91] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Ack server push request, request = NotifySubscriberRequest, requestId = 31
2025-07-22 08:38:27 [nacos-grpc-client-executor-localhost-92] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Receive server push request, request = NotifySubscriberRequest, requestId = 30
2025-07-22 08:38:27 [nacos-grpc-client-executor-localhost-92] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Ack server push request, request = NotifySubscriberRequest, requestId = 30
2025-07-22 08:38:49 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 08:38:49 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 08:38:49 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[542]毫秒
2025-07-22 08:38:49 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[700]毫秒
2025-07-22 08:39:15 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 08:39:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[2313]毫秒
2025-07-22 08:39:18 [boundedElastic-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 08:39:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1480]毫秒
2025-07-22 08:39:20 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 08:39:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[272]毫秒
2025-07-22 08:39:20 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 08:39:21 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[61]毫秒
2025-07-22 08:39:21 [boundedElastic-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 08:39:21 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[31]毫秒
2025-07-22 08:41:21 [boundedElastic-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 08:41:23 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[1448]毫秒
2025-07-22 08:41:25 [boundedElastic-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 08:41:25 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 08:41:25 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[381]毫秒
2025-07-22 08:41:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 08:41:26 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 08:41:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[6]毫秒
2025-07-22 08:41:26 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1319]毫秒
2025-07-22 08:41:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[149]毫秒
2025-07-22 08:41:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 08:41:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[890]毫秒
2025-07-22 08:41:48 [boundedElastic-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 08:41:48 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[596]毫秒
2025-07-22 08:41:49 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 08:41:49 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[107]毫秒
2025-07-22 08:41:49 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 08:41:49 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[38]毫秒
2025-07-22 08:41:49 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 08:41:49 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[6]毫秒
2025-07-22 08:41:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 08:41:53 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 08:41:53 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[28]毫秒
2025-07-22 08:41:54 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[307]毫秒
2025-07-22 08:41:55 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 08:41:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 08:41:55 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-22 08:41:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[118]毫秒
2025-07-22 08:41:57 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:yumeng-gen, group:DEFAULT_GROUP, clusters: 
2025-07-22 08:41:57 [boundedElastic-13] INFO  com.alibaba.nacos.client.naming - [SUBSCRIBE-SERVICE] service:yumeng-gen, group:DEFAULT_GROUP, clusters: 
2025-07-22 08:41:57 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:yumeng-gen, group:DEFAULT_GROUP, cluster: 
2025-07-22 08:41:57 [boundedElastic-13] INFO  com.alibaba.nacos.client.naming - [GRPC-SUBSCRIBE] service:yumeng-gen, group:DEFAULT_GROUP, cluster: 
2025-07-22 08:41:57 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - init new ips(1) service: DEFAULT_GROUP@@yumeng-gen -> [{"instanceId":"************#9202##DEFAULT_GROUP@@yumeng-gen","ip":"************","port":9202,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-gen","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 08:41:57 [boundedElastic-10] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-gen -> [{"instanceId":"************#9202##DEFAULT_GROUP@@yumeng-gen","ip":"************","port":9202,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-gen","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 08:41:57 [nacos-grpc-client-executor-localhost-161] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Receive server push request, request = NotifySubscriberRequest, requestId = 33
2025-07-22 08:41:57 [nacos-grpc-client-executor-localhost-161] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Ack server push request, request = NotifySubscriberRequest, requestId = 33
2025-07-22 08:42:02 [boundedElastic-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 08:42:03 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[809]毫秒
2025-07-22 08:42:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 08:42:04 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[574]毫秒
2025-07-22 08:42:04 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 08:42:04 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[95]毫秒
2025-07-22 08:42:04 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 08:42:04 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[74]毫秒
2025-07-22 08:42:04 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 08:42:04 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-22 08:42:25 [boundedElastic-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 08:42:25 [boundedElastic-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 08:42:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[78]毫秒
2025-07-22 08:42:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 08:42:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-22 08:42:25 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[793]毫秒
2025-07-22 08:42:25 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 08:42:26 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[46]毫秒
2025-07-22 08:42:29 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 08:42:29 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[600]毫秒
2025-07-22 08:42:30 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 08:42:30 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[151]毫秒
2025-07-22 08:42:31 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 08:42:31 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-22 08:42:45 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_device_type],无参数
2025-07-22 08:42:45 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/online/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 08:42:45 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_device_type],耗时:[47]毫秒
2025-07-22 08:42:47 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/online/list],耗时:[1647]毫秒
2025-07-22 08:43:08 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-07-22 08:43:08 [boundedElastic-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 08:43:08 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[507]毫秒
2025-07-22 08:43:09 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[605]毫秒
2025-07-22 08:43:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-07-22 08:43:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[213]毫秒
2025-07-22 08:57:47 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-07-22 08:57:47 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/db/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"dataName":["master"]}]
2025-07-22 08:57:47 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[496]毫秒
2025-07-22 08:57:48 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/db/list],耗时:[838]毫秒
2025-07-22 08:57:50 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /tool/gen/importTable],参数类型[param],参数:[{"tables":["auth_code"],"dataName":["master"]}]
2025-07-22 08:57:53 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /tool/gen/importTable],耗时:[2375]毫秒
2025-07-22 08:57:53 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 08:57:53 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[243]毫秒
2025-07-22 08:57:55 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["2"],"pageSize":["10"]}]
2025-07-22 08:57:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[200]毫秒
2025-07-22 08:57:59 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1947461053984030722],无参数
2025-07-22 08:57:59 [boundedElastic-36] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-22 08:58:00 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1947461053984030722],耗时:[583]毫秒
2025-07-22 08:58:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[617]毫秒
2025-07-22 08:58:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-07-22 08:58:01 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[72]毫秒
2025-07-22 08:58:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-07-22 08:58:05 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 08:58:05 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[211]毫秒
2025-07-22 08:58:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[276]毫秒
2025-07-22 08:58:08 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 08:58:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[213]毫秒
2025-07-22 08:59:05 [nacos-grpc-client-executor-localhost-419] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Receive server push request, request = NotifySubscriberRequest, requestId = 34
2025-07-22 08:59:05 [nacos-grpc-client-executor-localhost-419] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-gen -> [{"instanceId":"************#9202##DEFAULT_GROUP@@yumeng-gen","ip":"************","port":9202,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-gen","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 08:59:05 [nacos-grpc-client-executor-localhost-419] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-gen -> []
2025-07-22 08:59:05 [nacos-grpc-client-executor-localhost-419] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Ack server push request, request = NotifySubscriberRequest, requestId = 34
2025-07-22 08:59:28 [nacos-grpc-client-executor-localhost-427] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Receive server push request, request = NotifySubscriberRequest, requestId = 35
2025-07-22 08:59:28 [nacos-grpc-client-executor-localhost-427] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-gen -> [{"instanceId":"************#9202##DEFAULT_GROUP@@yumeng-gen","ip":"************","port":9202,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-gen","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 08:59:28 [nacos-grpc-client-executor-localhost-427] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-gen -> [{"instanceId":"************#9202##DEFAULT_GROUP@@yumeng-gen","ip":"************","port":9202,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-gen","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 08:59:28 [nacos-grpc-client-executor-localhost-427] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Ack server push request, request = NotifySubscriberRequest, requestId = 35
2025-07-22 08:59:41 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 08:59:42 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[901]毫秒
2025-07-22 08:59:47 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1947461053984030722],无参数
2025-07-22 08:59:47 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-22 08:59:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1947461053984030722],耗时:[389]毫秒
2025-07-22 08:59:47 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[468]毫秒
2025-07-22 08:59:48 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-07-22 08:59:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[48]毫秒
2025-07-22 09:02:35 [boundedElastic-19] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1947461053984030722","dataName":"master","tableName":"auth_code","tableComment":"授权码表","subTableName":null,"subTableFkName":null,"className":"AuthCode","tplCategory":"crud","packageName":"com.ym.salary","moduleName":"salary","businessName":"code","functionName":"授权码","functionAuthor":"liushuo","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":null,"createBy":1,"createTime":"2025-07-22 08:57:52","updateBy":null,"updateTime":"2025-07-22 08:57:52","columnId":"1947461055158435842","tableId":"1947461053984030722","columnName":"id","columnComment":"主键ID","columnType":"bigint","javaType":"Long","javaField":"id","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":null,"isEdit":"1","isLis]
2025-07-22 09:02:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[1933]毫秒
2025-07-22 09:02:37 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:02:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[202]毫秒
2025-07-22 09:02:37 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-07-22 09:02:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[130]毫秒
2025-07-22 09:02:38 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:02:38 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[227]毫秒
2025-07-22 09:02:40 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/batchGenCode],参数类型[param],参数:[{"tableIdStr":["1947461053984030722"]}]
2025-07-22 09:02:40 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/batchGenCode],耗时:[360]毫秒
2025-07-22 09:05:45 [boundedElastic-37] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 09:05:46 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[732]毫秒
2025-07-22 09:05:46 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 09:05:46 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[102]毫秒
2025-07-22 09:05:46 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 09:05:46 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-07-22 09:05:47 [boundedElastic-37] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 09:05:47 [boundedElastic-37] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-07-22 09:05:47 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:05:47 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[91]毫秒
2025-07-22 09:05:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[190]毫秒
2025-07-22 09:05:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[265]毫秒
2025-07-22 09:07:33 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:07:33 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[83]毫秒
2025-07-22 09:07:45 [nacos-grpc-client-executor-localhost-546] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Receive server push request, request = NotifySubscriberRequest, requestId = 38
2025-07-22 09:07:45 [nacos-grpc-client-executor-localhost-546] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-salary-settlement -> [{"instanceId":"************#9401##DEFAULT_GROUP@@yumeng-salary-settlement","ip":"************","port":9401,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-salary-settlement","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 09:07:45 [nacos-grpc-client-executor-localhost-546] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-salary-settlement -> []
2025-07-22 09:07:45 [nacos-grpc-client-executor-localhost-546] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Ack server push request, request = NotifySubscriberRequest, requestId = 38
2025-07-22 09:08:14 [nacos-grpc-client-executor-localhost-554] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Receive server push request, request = NotifySubscriberRequest, requestId = 40
2025-07-22 09:08:14 [nacos-grpc-client-executor-localhost-554] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@yumeng-salary-settlement -> [{"instanceId":"************#9401##DEFAULT_GROUP@@yumeng-salary-settlement","ip":"************","port":9401,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-salary-settlement","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 09:08:14 [nacos-grpc-client-executor-localhost-554] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@yumeng-salary-settlement -> [{"instanceId":"************#9401##DEFAULT_GROUP@@yumeng-salary-settlement","ip":"************","port":9401,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-salary-settlement","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 09:08:14 [nacos-grpc-client-executor-localhost-554] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Ack server push request, request = NotifySubscriberRequest, requestId = 40
2025-07-22 09:08:41 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:08:42 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[1112]毫秒
2025-07-22 09:08:42 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:08:42 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[200]毫秒
2025-07-22 09:44:38 [boundedElastic-52] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 09:44:38 [boundedElastic-94] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 09:44:38 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[174]毫秒
2025-07-22 09:44:38 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[233]毫秒
2025-07-22 09:44:39 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 09:44:39 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 09:44:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-22 09:44:39 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[115]毫秒
2025-07-22 09:44:41 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 09:44:42 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1060]毫秒
2025-07-22 09:44:42 [boundedElastic-94] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 09:44:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[752]毫秒
2025-07-22 09:44:43 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 09:44:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[136]毫秒
2025-07-22 09:44:43 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 09:44:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[47]毫秒
2025-07-22 09:44:43 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 09:44:43 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-22 09:44:46 [boundedElastic-94] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 09:44:46 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[837]毫秒
2025-07-22 09:44:49 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-22 09:44:49 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[512]毫秒
2025-07-22 09:44:50 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:44:50 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[234]毫秒
2025-07-22 09:44:57 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 09:44:57 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 09:44:57 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[26]毫秒
2025-07-22 09:44:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[346]毫秒
2025-07-22 09:44:58 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 09:44:58 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 09:44:58 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-22 09:44:58 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[88]毫秒
2025-07-22 09:45:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 09:45:05 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[797]毫秒
2025-07-22 09:45:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 09:45:06 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[597]毫秒
2025-07-22 09:45:06 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 09:45:06 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[94]毫秒
2025-07-22 09:45:06 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 09:45:06 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[29]毫秒
2025-07-22 09:45:07 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 09:45:07 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-22 09:45:09 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"],"params[timeRange]":[""]}]
2025-07-22 09:45:09 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[460]毫秒
2025-07-22 09:45:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 09:45:11 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 09:45:11 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-22 09:45:11 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[76]毫秒
2025-07-22 09:45:11 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 09:45:11 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-22 09:45:11 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[239]毫秒
2025-07-22 09:45:13 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 09:45:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1515]毫秒
2025-07-22 09:45:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[274]毫秒
2025-07-22 09:45:13 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:45:13 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 09:45:13 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-22 09:45:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[216]毫秒
2025-07-22 09:48:18 [boundedElastic-91] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"shippingId":["1934261052682113025"],"transactionType":["1"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:48:19 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[300]毫秒
2025-07-22 09:48:24 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"transactionType":["1"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:48:24 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[273]毫秒
2025-07-22 09:48:27 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:48:28 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[1630]毫秒
2025-07-22 09:48:34 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 09:48:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[294]毫秒
2025-07-22 10:23:16 [nacos-grpc-client-executor-localhost-1613] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Receive server push request, request = NotifySubscriberRequest, requestId = 45
2025-07-22 10:23:16 [nacos-grpc-client-executor-localhost-1613] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@yumeng-salary-settlement -> [{"instanceId":"************#9401##DEFAULT_GROUP@@yumeng-salary-settlement","ip":"************","port":9401,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@yumeng-salary-settlement","metadata":{"preserved.register.source":"SPRING_CLOUD","IPv6":"[2408:8215:a11:ed30:603e:f8b2:cae1:741a]","username":"yumeng","userpassword":"yumeng@2025!"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-22 10:23:16 [nacos-grpc-client-executor-localhost-1613] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@yumeng-salary-settlement -> []
2025-07-22 10:23:16 [nacos-grpc-client-executor-localhost-1613] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Ack server push request, request = NotifySubscriberRequest, requestId = 45
2025-07-22 10:59:32 [boundedElastic-179] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 10:59:32 [boundedElastic-169] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 10:59:32 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[70]毫秒
2025-07-22 10:59:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[368]毫秒
2025-07-22 10:59:33 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 10:59:33 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 10:59:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-22 10:59:33 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[130]毫秒
2025-07-22 10:59:34 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 10:59:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 10:59:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[3]毫秒
2025-07-22 10:59:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[181]毫秒
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:38 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:39 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:40 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:41 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:42 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:43 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:44 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:45 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:46 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:47 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:48 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:49 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:49 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:50 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:51 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:52 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:52 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:53 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:54 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:54 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:55 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:56 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:56 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:57 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:58 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:58 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:03:59 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:00 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:00 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:01 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:02 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:02 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:03 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:04 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:04 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:05 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:07 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:07 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 23 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:08 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:09 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:09 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 24 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:10 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:11 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:11 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 25 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:13 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:14 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:14 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 26 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:16 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:17 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 27 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [4f9a5c79-7f9e-42fc-bdbc-948cedd91156] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:19 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [f7a22e8e-b93a-4eb8-9ab9-f6eb63cedbe6_config-0] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:04:20 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.n.c.r.client.grpc.GrpcClient - grpc client connection server:localhost ip,serverPort:9848,grpcTslConfig:{"sslProvider":"","enableTls":false,"mutualAuthEnable":false,"trustAll":false}
2025-07-22 11:04:20 [com.alibaba.nacos.client.remote.worker.1] INFO  c.alibaba.nacos.common.remote.client - [74e5d911-c512-49ff-b2a4-a3381f1a505e_config-0] Fail to connect server, after trying 28 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
2025-07-22 11:10:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-22 11:10:04 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 9876 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-22 11:10:04 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-22 11:10:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-22 11:10:04 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-22 11:10:07 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-22 11:10:07 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-22 11:10:08 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-22 11:10:08 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-22 11:10:08 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-22 11:10:11 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway 192.168.124.10:8080 register finished
2025-07-22 11:10:12 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-22 11:10:12 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 10.193 seconds (process running for 11.026)
2025-07-22 11:10:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-22 11:10:12 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-22 11:11:04 [boundedElastic-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 11:11:04 [boundedElastic-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 11:11:04 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[412]毫秒
2025-07-22 11:11:04 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[542]毫秒
2025-07-22 11:11:10 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 11:11:13 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[2248]毫秒
2025-07-22 11:11:13 [boundedElastic-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 11:11:15 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1634]毫秒
2025-07-22 11:11:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 11:11:16 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[119]毫秒
2025-07-22 11:11:16 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 11:11:16 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[50]毫秒
2025-07-22 11:11:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 11:11:16 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-22 11:11:42 [boundedElastic-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 11:11:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[1226]毫秒
2025-07-22 11:16:19 [boundedElastic-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 11:16:19 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 11:16:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[391]毫秒
2025-07-22 11:16:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1049]毫秒
2025-07-22 11:16:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 11:16:20 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 11:16:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-22 11:16:20 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[99]毫秒
2025-07-22 11:16:24 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 11:16:25 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[880]毫秒
2025-07-22 11:16:25 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 11:16:26 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[650]毫秒
2025-07-22 11:16:26 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 11:16:26 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[90]毫秒
2025-07-22 11:16:27 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 11:16:27 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-22 11:16:27 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 11:16:27 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-22 11:16:31 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"],"params[timeRange]":[""]}]
2025-07-22 11:16:31 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[449]毫秒
2025-07-22 11:16:36 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/usdReconciliation/confirm],参数类型[param],参数:[{"id":["1947219921831301122"]}]
2025-07-22 11:16:37 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/usdReconciliation/confirm],耗时:[1110]毫秒
2025-07-22 11:16:37 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"],"params[timeRange]":[""]}]
2025-07-22 11:16:37 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[315]毫秒
2025-07-22 11:16:41 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1947219921831301122],无参数
2025-07-22 11:16:42 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1947219921831301122],耗时:[540]毫秒
2025-07-22 11:17:59 [boundedElastic-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/export],无参数
2025-07-22 11:18:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/export],耗时:[904]毫秒
2025-07-22 12:42:24 [boundedElastic-95] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 12:42:24 [boundedElastic-95] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 12:42:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[73]毫秒
2025-07-22 12:42:24 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[306]毫秒
2025-07-22 12:42:24 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 12:42:25 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[108]毫秒
2025-07-22 12:42:25 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 12:42:25 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-22 13:38:02 [boundedElastic-118] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 13:38:02 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[296]毫秒
2025-07-22 13:38:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 13:38:02 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[103]毫秒
2025-07-22 13:38:07 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 13:38:08 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[912]毫秒
2025-07-22 13:38:08 [boundedElastic-118] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 13:38:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[825]毫秒
2025-07-22 13:38:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 13:38:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[116]毫秒
2025-07-22 13:38:09 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 13:38:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[49]毫秒
2025-07-22 13:38:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 13:38:09 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-22 14:01:34 [boundedElastic-158] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 14:01:34 [boundedElastic-158] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 14:01:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[36]毫秒
2025-07-22 14:01:34 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[366]毫秒
2025-07-22 14:01:35 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:01:35 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 14:01:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-22 14:01:35 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[104]毫秒
2025-07-22 14:01:41 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 14:01:42 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[868]毫秒
2025-07-22 14:01:42 [boundedElastic-158] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 14:01:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[658]毫秒
2025-07-22 14:01:43 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 14:01:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[112]毫秒
2025-07-22 14:01:44 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 14:01:44 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[79]毫秒
2025-07-22 14:01:44 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:01:44 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[9]毫秒
2025-07-22 14:02:28 [boundedElastic-141] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:02:29 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[774]毫秒
2025-07-22 14:02:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/synchDb/1947461053984030722],无参数
2025-07-22 14:02:35 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/synchDb/1947461053984030722],耗时:[2468]毫秒
2025-07-22 14:02:37 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1947461053984030722],无参数
2025-07-22 14:02:37 [boundedElastic-141] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-22 14:02:37 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1947461053984030722],耗时:[333]毫秒
2025-07-22 14:02:38 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[691]毫秒
2025-07-22 14:02:38 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-07-22 14:02:38 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[50]毫秒
2025-07-22 14:03:35 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /tool/gen],参数类型[json],参数:[{"createDept":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"tableId":"1947461053984030722","dataName":"master","tableName":"auth_code","tableComment":"授权码表","subTableName":null,"subTableFkName":null,"className":"AuthCode","tplCategory":"crud","packageName":"com.ym.salary","moduleName":"salary","businessName":"code","functionName":"授权码","functionAuthor":"liushuo","genType":"0","genPath":"/","pkColumn":null,"columns":[{"createDept":null,"createBy":1,"createTime":"2025-07-22 08:57:52","updateBy":1,"updateTime":"2025-07-22 14:02:34","columnId":"1947461055158435842","tableId":"1947461053984030722","columnName":"id","columnComment":"主键ID","columnType":"bigint","javaType":"Long","javaField":"id","isPk":"1","isIncrement":"1","isRequired":"1","isInsert":null,"isEdit":"1","isList":]
2025-07-22 14:03:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /tool/gen],耗时:[1148]毫秒
2025-07-22 14:03:37 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-07-22 14:03:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:03:37 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[155]毫秒
2025-07-22 14:03:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[184]毫秒
2025-07-22 14:03:37 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:03:37 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[198]毫秒
2025-07-22 14:03:38 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/batchGenCode],参数类型[param],参数:[{"tableIdStr":["1947461053984030722"]}]
2025-07-22 14:03:39 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/batchGenCode],耗时:[336]毫秒
2025-07-22 14:20:15 [boundedElastic-182] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 14:20:15 [boundedElastic-181] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 14:20:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[196]毫秒
2025-07-22 14:20:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[703]毫秒
2025-07-22 14:20:16 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 14:20:16 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:20:16 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-22 14:20:16 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[102]毫秒
2025-07-22 14:20:44 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 14:20:46 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1171]毫秒
2025-07-22 14:20:46 [boundedElastic-182] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 14:20:46 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[768]毫秒
2025-07-22 14:20:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 14:20:47 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[113]毫秒
2025-07-22 14:20:47 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 14:20:47 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[50]毫秒
2025-07-22 14:20:47 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:20:48 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-22 14:20:51 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-22 14:20:51 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-07-22 14:20:51 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-22 14:20:51 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[179]毫秒
2025-07-22 14:20:51 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[179]毫秒
2025-07-22 14:20:51 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[550]毫秒
2025-07-22 14:21:23 [boundedElastic-183] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-22 14:21:23 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[613]毫秒
2025-07-22 14:21:34 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/menu],参数类型[json],参数:[{"path":"","parentId":"1947462263059255297","menuName":"生成授权码","icon":"","menuType":"F","orderNum":1,"isFrame":"1","isCache":"0","visible":"0","status":"0","perms":"salary:code:generate"}]
2025-07-22 14:21:35 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/menu],耗时:[1091]毫秒
2025-07-22 14:21:36 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-22 14:21:36 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[408]毫秒
2025-07-22 14:21:45 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 14:21:45 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:21:45 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[166]毫秒
2025-07-22 14:21:45 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[660]毫秒
2025-07-22 14:21:48 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/1913482625502998530],无参数
2025-07-22 14:21:48 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/1913482625502998530],耗时:[333]毫秒
2025-07-22 14:21:48 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/roleMenuTreeselect/1913482625502998530],无参数
2025-07-22 14:21:49 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/roleMenuTreeselect/1913482625502998530],耗时:[387]毫秒
2025-07-22 14:21:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/1913482734521348097],无参数
2025-07-22 14:21:52 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/1913482734521348097],耗时:[579]毫秒
2025-07-22 14:21:52 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/roleMenuTreeselect/1913482734521348097],无参数
2025-07-22 14:21:52 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/roleMenuTreeselect/1913482734521348097],耗时:[308]毫秒
2025-07-22 14:21:57 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/role],参数类型[json],参数:[{"roleId":"1913482734521348097","roleSort":1,"status":"0","roleName":"船舶公司管理员","roleKey":"chuanbo_admin","menuCheckStrictly":true,"deptCheckStrictly":true,"remark":"","dataScope":"5","menuIds":["1921568335862923266","1923382315782725634","1921570342803828738","1932003110232330241","1926646670158565378",1,"1923382315782725635","1925024185457676291","1926287762364514306","1926287864642617345","1930905560572334081","1923382315782725636","1925024185457676292","1923382315782725637","1925024185457676293","1923382315782725638","1925024185457676294","1923382315782725639","1925024185457676295","1934492230727077890","1947102229249998850","1947102229249998851","1947221356789469185","1947495699318673409","1947102229249998855","1947462263059255297","1947462263059255298","1947542522183598081","1947462263059255299","1947462263059255300","194746226305925]
2025-07-22 14:21:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/role],耗时:[2380]毫秒
2025-07-22 14:21:59 [boundedElastic-172] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:22:00 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[271]毫秒
2025-07-22 14:25:27 [boundedElastic-167] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 14:25:27 [boundedElastic-184] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 14:25:27 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[62]毫秒
2025-07-22 14:25:28 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[429]毫秒
2025-07-22 14:25:28 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:25:28 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 14:25:28 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-22 14:25:29 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[248]毫秒
2025-07-22 14:25:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 14:25:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[973]毫秒
2025-07-22 14:25:34 [boundedElastic-167] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 14:25:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[2186]毫秒
2025-07-22 14:25:36 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 14:25:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[97]毫秒
2025-07-22 14:25:37 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 14:25:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[57]毫秒
2025-07-22 14:25:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:25:37 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-22 14:25:39 [boundedElastic-167] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 14:25:41 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[1740]毫秒
2025-07-22 14:25:44 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 14:25:44 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 14:25:44 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[40]毫秒
2025-07-22 14:25:44 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[388]毫秒
2025-07-22 14:25:45 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:25:45 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 14:25:45 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-22 14:25:45 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[128]毫秒
2025-07-22 14:25:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 14:26:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1025]毫秒
2025-07-22 14:26:00 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 14:26:01 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[789]毫秒
2025-07-22 14:26:02 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 14:26:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[100]毫秒
2025-07-22 14:26:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 14:26:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[45]毫秒
2025-07-22 14:26:02 [boundedElastic-182] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:26:02 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-22 14:26:04 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:26:04 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[266]毫秒
2025-07-22 14:26:06 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"],"params[timeRange]":[""]}]
2025-07-22 14:26:07 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[405]毫秒
2025-07-22 14:26:08 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:26:08 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:26:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[230]毫秒
2025-07-22 14:26:09 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[303]毫秒
2025-07-22 14:26:27 [boundedElastic-185] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/code/generate],参数类型[param],参数:[{"verificationCode":["123456"],"sourceId":["1934273477204611073"],"expireSeconds":["3600"]}]
2025-07-22 14:26:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/code/generate],耗时:[1768]毫秒
2025-07-22 14:27:30 [boundedElastic-182] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:27:30 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[280]毫秒
2025-07-22 14:28:01 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/code/generate],参数类型[param],参数:[{"verificationCode":["123456"],"sourceId":["1934273477204611073"],"expireSeconds":["3600"]}]
2025-07-22 14:28:02 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/code/generate],耗时:[362]毫秒
2025-07-22 14:28:14 [boundedElastic-169] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:28:14 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[245]毫秒
2025-07-22 14:28:15 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:28:15 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[287]毫秒
2025-07-22 14:29:03 [boundedElastic-188] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:29:04 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[359]毫秒
2025-07-22 14:29:04 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:29:05 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[304]毫秒
2025-07-22 14:29:10 [boundedElastic-183] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_type],无参数
2025-07-22 14:29:10 [boundedElastic-169] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:29:10 [boundedElastic-188] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_status],无参数
2025-07-22 14:29:10 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_status],耗时:[46]毫秒
2025-07-22 14:29:10 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_type],耗时:[62]毫秒
2025-07-22 14:29:10 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[417]毫秒
2025-07-22 14:29:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:29:38 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 14:29:38 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-22 14:29:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[85]毫秒
2025-07-22 14:29:38 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 14:29:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[28]毫秒
2025-07-22 14:29:39 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[328]毫秒
2025-07-22 14:29:39 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[591]毫秒
2025-07-22 14:29:39 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 14:29:39 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[30]毫秒
2025-07-22 14:30:04 [boundedElastic-169] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 14:30:04 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[73]毫秒
2025-07-22 14:31:43 [boundedElastic-179] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:31:43 [boundedElastic-183] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:31:43 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[252]毫秒
2025-07-22 14:31:43 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[361]毫秒
2025-07-22 14:31:58 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:31:58 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:31:58 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:31:58 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[236]毫秒
2025-07-22 14:31:58 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[248]毫秒
2025-07-22 14:31:58 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[320]毫秒
2025-07-22 14:41:47 [boundedElastic-185] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/code/generate],参数类型[param],参数:[{"verificationCode":["1312"],"sourceId":["1934273477204611073"],"expireSeconds":["3600"],"amount":["100"]}]
2025-07-22 14:41:49 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/code/generate],耗时:[2167]毫秒
2025-07-22 14:42:55 [boundedElastic-185] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/code/generate],参数类型[param],参数:[{"verificationCode":["123456"],"sourceId":["1934273477204611073"],"expireSeconds":["3600"],"amount":["9"]}]
2025-07-22 14:42:56 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/code/generate],耗时:[548]毫秒
2025-07-22 14:43:17 [boundedElastic-191] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:43:17 [boundedElastic-190] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 14:43:17 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-22 14:43:17 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[125]毫秒
2025-07-22 14:43:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 14:43:24 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1008]毫秒
2025-07-22 14:43:24 [boundedElastic-190] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 14:43:25 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[819]毫秒
2025-07-22 14:43:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 14:43:25 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[111]毫秒
2025-07-22 14:43:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 14:43:25 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[45]毫秒
2025-07-22 14:43:26 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_status],无参数
2025-07-22 14:43:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:43:26 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_todo_status],无参数
2025-07-22 14:43:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_status],耗时:[23]毫秒
2025-07-22 14:43:26 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_todo_status],耗时:[25]毫秒
2025-07-22 14:43:26 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 14:43:26 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-22 14:43:27 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/list],耗时:[546]毫秒
2025-07-22 14:43:33 [boundedElastic-190] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 14:43:34 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[619]毫秒
2025-07-22 14:43:39 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 14:43:39 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[114]毫秒
2025-07-22 14:43:39 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-22 14:43:39 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-22 14:43:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 14:43:39 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[65]毫秒
2025-07-22 14:43:39 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[530]毫秒
2025-07-22 14:43:39 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[557]毫秒
2025-07-22 14:43:39 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 14:43:39 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[55]毫秒
2025-07-22 14:43:41 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-22 14:43:41 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[476]毫秒
2025-07-22 14:43:41 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:43:42 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[226]毫秒
2025-07-22 14:43:42 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-22 14:43:42 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[432]毫秒
2025-07-22 14:43:42 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1934261959020875778"],"beginDate":["2024-07-22"],"endDate":["2025-07-22"],"dateRange":["last1year"]}]
2025-07-22 14:43:43 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/getStatisticsData],耗时:[210]毫秒
2025-07-22 14:44:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-22 14:44:05 [boundedElastic-189] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"]}]
2025-07-22 14:44:05 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-22 14:44:05 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[321]毫秒
2025-07-22 14:44:05 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[98]毫秒
2025-07-22 14:44:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[440]毫秒
2025-07-22 14:44:27 [boundedElastic-185] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow],参数类型[json],参数:[{"transportId":"1934261959020875778","shippingId":"1934261052682113025","transactionType":"2","transactionAmount":200,"transactionTime":"2025-07-22 14:44:09","usdworkoderId":"0","purpose":"123","remark":""}]
2025-07-22 14:44:28 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow],耗时:[733]毫秒
2025-07-22 14:44:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 14:44:29 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[213]毫秒
2025-07-22 14:44:47 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/code/generate],参数类型[param],参数:[{"verificationCode":["123456"],"sourceId":["1934273477204611073"],"expireSeconds":["3600"],"amount":["100"]}]
2025-07-22 14:44:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/code/generate],耗时:[854]毫秒
2025-07-22 14:45:57 [boundedElastic-197] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/code/generate],参数类型[param],参数:[{"verificationCode":["123456"],"sourceId":["1934273477204611073"],"expireSeconds":["3600"],"amount":["100"]}]
2025-07-22 14:45:58 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/code/generate],耗时:[667]毫秒
2025-07-22 14:46:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:46:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[251]毫秒
2025-07-22 14:46:18 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 14:46:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[301]毫秒
2025-07-22 14:46:49 [boundedElastic-190] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/code/generate],参数类型[param],参数:[{"verificationCode":["123456"],"sourceId":["1934273477204611073"],"expireSeconds":["3600"],"amount":["100"]}]
2025-07-22 14:46:50 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/code/generate],耗时:[679]毫秒
2025-07-22 15:00:21 [boundedElastic-208] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:00:22 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[377]毫秒
2025-07-22 15:19:15 [boundedElastic-226] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:19:15 [boundedElastic-222] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:19:15 [boundedElastic-213] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:19:17 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[1208]毫秒
2025-07-22 15:19:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1208]毫秒
2025-07-22 15:19:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[1208]毫秒
2025-07-22 15:19:48 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:19:48 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:19:48 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[271]毫秒
2025-07-22 15:19:48 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[363]毫秒
2025-07-22 15:20:23 [boundedElastic-227] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 15:20:23 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[217]毫秒
2025-07-22 15:20:25 [boundedElastic-227] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:20:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[345]毫秒
2025-07-22 15:20:25 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:20:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[244]毫秒
2025-07-22 15:20:27 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 15:20:27 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[47]毫秒
2025-07-22 15:20:38 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:20:38 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:20:38 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[253]毫秒
2025-07-22 15:20:38 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[344]毫秒
2025-07-22 15:21:19 [boundedElastic-197] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:21:19 [boundedElastic-198] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:21:19 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[299]毫秒
2025-07-22 15:21:19 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[392]毫秒
2025-07-22 15:22:44 [boundedElastic-228] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/code/generate],参数类型[param],参数:[{"verificationCode":["123455"],"sourceId":["1934273477204611073"],"expireSeconds":["3600"],"amount":["100"]}]
2025-07-22 15:22:46 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/code/generate],耗时:[1895]毫秒
2025-07-22 15:24:23 [boundedElastic-227] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:24:23 [boundedElastic-231] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:24:24 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[249]毫秒
2025-07-22 15:24:24 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[333]毫秒
2025-07-22 15:24:48 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:24:48 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:24:48 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[246]毫秒
2025-07-22 15:24:48 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[374]毫秒
2025-07-22 15:27:41 [boundedElastic-233] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:27:41 [boundedElastic-221] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:27:41 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[262]毫秒
2025-07-22 15:27:41 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[358]毫秒
2025-07-22 15:27:51 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:27:51 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:27:51 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[245]毫秒
2025-07-22 15:27:51 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[358]毫秒
2025-07-22 15:28:25 [boundedElastic-228] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 15:28:25 [boundedElastic-226] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 15:28:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[43]毫秒
2025-07-22 15:28:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[428]毫秒
2025-07-22 15:28:25 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 15:28:25 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 15:28:25 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[6]毫秒
2025-07-22 15:28:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[124]毫秒
2025-07-22 15:30:36 [boundedElastic-234] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:30:36 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[271]毫秒
2025-07-22 15:30:36 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:30:37 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[289]毫秒
2025-07-22 15:30:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/code/generate],参数类型[param],参数:[{"verificationCode":["522424"],"sourceId":["1934273477204611073"],"expireSeconds":["3600"],"amount":["120"]}]
2025-07-22 15:30:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/code/generate],耗时:[670]毫秒
2025-07-22 15:31:38 [boundedElastic-237] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/use],参数类型[param],参数:[{"code":["1934273477204611073"]}]
2025-07-22 15:31:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/use],耗时:[372]毫秒
2025-07-22 15:31:43 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/use],参数类型[param],参数:[{"code":["1934273477204611073"]}]
2025-07-22 15:31:43 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/use],耗时:[337]毫秒
2025-07-22 15:33:50 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:33:50 [boundedElastic-226] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:33:51 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[257]毫秒
2025-07-22 15:33:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[352]毫秒
2025-07-22 15:34:20 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:34:20 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:34:20 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[237]毫秒
2025-07-22 15:34:20 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[340]毫秒
2025-07-22 15:34:55 [boundedElastic-227] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:34:55 [boundedElastic-198] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:34:55 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[278]毫秒
2025-07-22 15:34:55 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[369]毫秒
2025-07-22 15:35:52 [boundedElastic-241] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:35:52 [boundedElastic-240] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:35:53 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[253]毫秒
2025-07-22 15:35:53 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[363]毫秒
2025-07-22 15:36:13 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:36:13 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:36:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:36:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[255]毫秒
2025-07-22 15:36:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[295]毫秒
2025-07-22 15:36:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[355]毫秒
2025-07-22 15:37:01 [boundedElastic-241] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/use],参数类型[param],参数:[{"code":["00000000559825D2"],"companyId":["1934273477204611073"]}]
2025-07-22 15:37:01 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/use],耗时:[372]毫秒
2025-07-22 15:37:10 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:37:11 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[265]毫秒
2025-07-22 15:38:21 [boundedElastic-240] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:38:21 [boundedElastic-198] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:38:21 [boundedElastic-227] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:38:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[319]毫秒
2025-07-22 15:38:21 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[359]毫秒
2025-07-22 15:38:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[394]毫秒
2025-07-22 15:40:44 [boundedElastic-239] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:40:45 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[1216]毫秒
2025-07-22 15:40:48 [boundedElastic-246] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 15:40:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[163]毫秒
2025-07-22 15:41:10 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/use],参数类型[param],参数:[{"code":["00000000559825D2"],"targetId":["1934273477204611073"]}]
2025-07-22 15:41:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/use],耗时:[1961]毫秒
2025-07-22 15:42:37 [boundedElastic-247] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/use],参数类型[param],参数:[{"code":["00000000559825D2"],"targetId":["1934273477204611073"]}]
2025-07-22 15:42:37 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/use],耗时:[284]毫秒
2025-07-22 15:42:50 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/use],参数类型[param],参数:[{"code":["00000000559825D2"],"targetId":["1934273477204611073"]}]
2025-07-22 15:42:50 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/use],耗时:[273]毫秒
2025-07-22 15:43:42 [boundedElastic-198] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/use],参数类型[param],参数:[{"code":["00000000559825D2"],"targetId":["1934273477204611073"]}]
2025-07-22 15:43:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/use],耗时:[2204]毫秒
2025-07-22 15:43:51 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/use],参数类型[param],参数:[{"code":["00000000559825D2"],"targetId":["1934273477204611073"]}]
2025-07-22 15:43:52 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/use],耗时:[373]毫秒
2025-07-22 15:47:35 [boundedElastic-254] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:47:35 [boundedElastic-255] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:47:35 [boundedElastic-240] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:47:35 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[278]毫秒
2025-07-22 15:47:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[391]毫秒
2025-07-22 15:47:35 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[447]毫秒
2025-07-22 15:47:42 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:47:42 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:47:42 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:47:42 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[245]毫秒
2025-07-22 15:47:42 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[258]毫秒
2025-07-22 15:47:42 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[324]毫秒
2025-07-22 15:52:27 [boundedElastic-253] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/use],参数类型[param],参数:[{"code":["00000000559825D2"],"targetId":["1934273477204611073"]}]
2025-07-22 15:52:29 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/use],耗时:[2249]毫秒
2025-07-22 15:57:04 [boundedElastic-198] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:57:04 [boundedElastic-262] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 15:57:04 [boundedElastic-262] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 15:57:04 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[272]毫秒
2025-07-22 15:57:04 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[372]毫秒
2025-07-22 15:57:04 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[429]毫秒
2025-07-22 16:00:15 [boundedElastic-250] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 16:00:15 [boundedElastic-261] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 16:00:15 [boundedElastic-243] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:00:15 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[236]毫秒
2025-07-22 16:00:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[261]毫秒
2025-07-22 16:00:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[334]毫秒
2025-07-22 16:23:38 [boundedElastic-279] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:23:39 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[1633]毫秒
2025-07-22 16:30:12 [boundedElastic-289] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:30:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[330]毫秒
2025-07-22 16:30:45 [boundedElastic-286] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 16:30:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1250]毫秒
2025-07-22 16:30:46 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 16:30:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[149]毫秒
2025-07-22 16:30:47 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 16:30:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[45]毫秒
2025-07-22 16:30:47 [boundedElastic-289] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 16:30:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[43]毫秒
2025-07-22 16:31:12 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 16:31:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[676]毫秒
2025-07-22 16:31:13 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 16:31:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[92]毫秒
2025-07-22 16:31:13 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 16:31:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[27]毫秒
2025-07-22 16:31:14 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 16:31:14 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[37]毫秒
2025-07-22 16:31:14 [boundedElastic-289] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:31:14 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[307]毫秒
2025-07-22 16:31:34 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:31:35 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[366]毫秒
2025-07-22 16:32:05 [boundedElastic-293] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:32:05 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[324]毫秒
2025-07-22 16:32:16 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:32:16 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[305]毫秒
2025-07-22 16:36:53 [boundedElastic-298] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 16:36:53 [boundedElastic-284] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 16:36:53 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[82]毫秒
2025-07-22 16:36:53 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[222]毫秒
2025-07-22 16:37:10 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 16:37:11 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1001]毫秒
2025-07-22 16:37:11 [boundedElastic-284] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 16:37:12 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[677]毫秒
2025-07-22 16:37:12 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 16:37:12 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[101]毫秒
2025-07-22 16:37:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 16:37:13 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[40]毫秒
2025-07-22 16:37:13 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_status],无参数
2025-07-22 16:37:13 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_todo_status],无参数
2025-07-22 16:37:13 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 16:37:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-22 16:37:13 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_todo_status],耗时:[47]毫秒
2025-07-22 16:37:13 [boundedElastic-284] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:37:13 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_status],耗时:[77]毫秒
2025-07-22 16:37:14 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/list],耗时:[1215]毫秒
2025-07-22 16:37:26 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-22 16:37:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-22 16:37:26 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-07-22 16:37:26 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[51]毫秒
2025-07-22 16:37:26 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[54]毫秒
2025-07-22 16:37:27 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[503]毫秒
2025-07-22 16:37:59 [boundedElastic-303] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-22 16:38:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[469]毫秒
2025-07-22 16:38:14 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/menu],参数类型[json],参数:[{"path":"","parentId":"1947462263059255297","menuName":"确认转账","icon":"","menuType":"F","orderNum":1,"isFrame":"1","isCache":"0","visible":"0","status":"0","perms":"salary:code:confirm"}]
2025-07-22 16:38:14 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/menu],耗时:[644]毫秒
2025-07-22 16:38:15 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-22 16:38:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[451]毫秒
2025-07-22 16:38:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:38:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 16:38:20 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[408]毫秒
2025-07-22 16:38:20 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[161]毫秒
2025-07-22 16:38:24 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/1913483122532216833],无参数
2025-07-22 16:38:24 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/1913483122532216833],耗时:[274]毫秒
2025-07-22 16:38:24 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/roleMenuTreeselect/1913483122532216833],无参数
2025-07-22 16:38:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/roleMenuTreeselect/1913483122532216833],耗时:[366]毫秒
2025-07-22 16:38:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/1913482734521348097],无参数
2025-07-22 16:38:33 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/1913482734521348097],耗时:[345]毫秒
2025-07-22 16:38:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/roleMenuTreeselect/1913482734521348097],无参数
2025-07-22 16:38:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/roleMenuTreeselect/1913482734521348097],耗时:[302]毫秒
2025-07-22 16:38:43 [boundedElastic-297] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/role],参数类型[json],参数:[{"roleId":"1913482734521348097","roleSort":1,"status":"0","roleName":"船舶公司管理员","roleKey":"chuanbo_admin","menuCheckStrictly":true,"deptCheckStrictly":true,"remark":"","dataScope":"5","menuIds":["1921568335862923266","1923382315782725634","1921570342803828738","1932003110232330241","1926646670158565378",1,"1923382315782725635","1925024185457676291","1926287762364514306","1926287864642617345","1930905560572334081","1923382315782725636","1925024185457676292","1923382315782725637","1925024185457676293","1923382315782725638","1925024185457676294","1923382315782725639","1925024185457676295","1934492230727077890","1947102229249998850","1947102229249998851","1947221356789469185","1947495699318673409","1947102229249998855","1947462263059255297","1947462263059255298","1947542522183598081","1947576911403728898","1947462263059255299","1947462263059255300","19474622630592553]
2025-07-22 16:38:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/role],耗时:[2676]毫秒
2025-07-22 16:38:45 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:38:46 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[285]毫秒
2025-07-22 16:38:50 [boundedElastic-305] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 16:38:50 [boundedElastic-305] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 16:38:50 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[130]毫秒
2025-07-22 16:38:50 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[115]毫秒
2025-07-22 16:38:50 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 16:38:50 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 16:38:50 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-22 16:38:50 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[103]毫秒
2025-07-22 16:38:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 16:38:54 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[931]毫秒
2025-07-22 16:38:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 16:38:55 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[811]毫秒
2025-07-22 16:38:55 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 16:38:55 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[113]毫秒
2025-07-22 16:38:56 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 16:38:56 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[74]毫秒
2025-07-22 16:38:56 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 16:38:56 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-22 16:38:59 [boundedElastic-305] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 16:39:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[923]毫秒
2025-07-22 16:39:06 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 16:39:06 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 16:39:06 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[30]毫秒
2025-07-22 16:39:07 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[351]毫秒
2025-07-22 16:39:07 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 16:39:07 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 16:39:07 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-22 16:39:07 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[90]毫秒
2025-07-22 16:39:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 16:39:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[878]毫秒
2025-07-22 16:39:18 [boundedElastic-299] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 16:39:19 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[623]毫秒
2025-07-22 16:39:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 16:39:19 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[107]毫秒
2025-07-22 16:39:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 16:39:20 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[50]毫秒
2025-07-22 16:39:20 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 16:39:20 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-22 16:39:31 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:39:32 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[317]毫秒
2025-07-22 16:39:40 [boundedElastic-299] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/confirm/1947548655912837122],无参数
2025-07-22 16:39:41 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/confirm/1947548655912837122],耗时:[981]毫秒
2025-07-22 16:39:41 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:39:41 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[211]毫秒
2025-07-22 16:40:52 [boundedElastic-308] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:40:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[332]毫秒
2025-07-22 16:41:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:41:15 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[341]毫秒
2025-07-22 16:49:31 [boundedElastic-315] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:49:31 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[335]毫秒
2025-07-22 16:50:23 [boundedElastic-300] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:50:24 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[1357]毫秒
2025-07-22 16:52:18 [boundedElastic-314] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:52:18 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[346]毫秒
2025-07-22 16:53:22 [boundedElastic-308] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:53:22 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[342]毫秒
2025-07-22 16:55:30 [boundedElastic-318] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 16:55:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[479]毫秒
2025-07-22 16:55:31 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:55:31 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[202]毫秒
2025-07-22 16:56:18 [boundedElastic-286] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 16:56:18 [boundedElastic-286] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 16:56:18 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-22 16:56:18 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[316]毫秒
2025-07-22 16:56:18 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[366]毫秒
2025-07-22 16:56:18 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 16:56:18 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[28]毫秒
2025-07-22 16:56:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1322]毫秒
2025-07-22 16:56:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-22 16:56:19 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-22 16:56:19 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 16:56:20 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[219]毫秒
2025-07-22 16:56:20 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-22 16:56:20 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[170]毫秒
2025-07-22 16:58:40 [boundedElastic-326] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:58:41 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[286]毫秒
2025-07-22 16:59:26 [boundedElastic-319] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:59:26 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[326]毫秒
2025-07-22 16:59:31 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/code/confirm/1947548655912837122],无参数
2025-07-22 16:59:32 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/code/confirm/1947548655912837122],耗时:[881]毫秒
2025-07-22 16:59:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:59:33 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[203]毫秒
2025-07-22 16:59:47 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-22 16:59:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[338]毫秒
2025-07-22 18:25:24 [boundedElastic-383] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 18:25:24 [boundedElastic-371] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 18:25:24 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[34]毫秒
2025-07-22 18:25:24 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[284]毫秒
2025-07-22 18:25:25 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 18:25:25 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 18:25:25 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-22 18:25:25 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[106]毫秒
2025-07-22 19:20:32 [boundedElastic-395] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 19:20:32 [boundedElastic-395] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 19:20:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[138]毫秒
2025-07-22 19:20:32 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[221]毫秒
2025-07-22 19:20:34 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 19:20:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[8]毫秒
2025-07-22 19:20:35 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 19:20:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[48]毫秒
2025-07-22 19:20:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 19:20:37 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[257]毫秒
2025-07-22 19:20:37 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 19:20:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[97]毫秒
2025-07-22 19:20:44 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 19:20:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[950]毫秒
2025-07-22 19:20:45 [boundedElastic-395] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 19:20:46 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[818]毫秒
2025-07-22 19:20:47 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 19:20:47 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[107]毫秒
2025-07-22 19:20:47 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 19:20:47 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[46]毫秒
2025-07-22 19:20:47 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 19:20:47 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-22 19:20:53 [boundedElastic-388] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 19:20:56 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[2721]毫秒
2025-07-22 19:21:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/usdReconciliation/confirm],参数类型[param],参数:[{"id":["1947219922154262529"]}]
2025-07-22 19:21:04 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/usdReconciliation/confirm],耗时:[1439]毫秒
2025-07-22 19:21:32 [boundedElastic-397] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 19:21:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[513]毫秒
2025-07-22 19:21:35 [boundedElastic-397] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 19:21:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[642]毫秒
2025-07-22 19:21:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 19:21:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[127]毫秒
2025-07-22 19:21:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 19:21:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-07-22 19:21:37 [boundedElastic-397] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 19:21:37 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-22 19:21:37 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 19:21:37 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[479]毫秒
2025-07-22 19:21:40 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/download],参数类型[json],参数:[{"id":1,"pageNum":1,"pageSize":10}]
2025-07-22 19:21:40 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/download],耗时:[184]毫秒
2025-07-22 19:21:53 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/download],参数类型[json],参数:[{"id":1,"pageNum":1,"pageSize":10}]
2025-07-22 19:21:54 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/download],耗时:[220]毫秒
2025-07-22 19:24:05 [boundedElastic-384] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-22 19:24:05 [boundedElastic-395] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-22 19:24:05 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[33]毫秒
2025-07-22 19:24:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[108]毫秒
2025-07-22 19:24:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-22 19:24:05 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 19:24:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-22 19:24:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[98]毫秒
2025-07-22 19:24:07 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-22 19:24:08 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[808]毫秒
2025-07-22 19:24:08 [boundedElastic-395] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 19:24:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[670]毫秒
2025-07-22 19:24:09 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 19:24:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[100]毫秒
2025-07-22 19:24:10 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 19:24:10 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-22 19:24:10 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 19:24:10 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-22 19:24:13 [boundedElastic-395] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 19:24:14 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[551]毫秒
2025-07-22 19:24:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/download],参数类型[json],参数:[{"id":1,"pageNum":1,"pageSize":10}]
2025-07-22 19:24:15 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/download],耗时:[291]毫秒
2025-07-22 19:24:23 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/download],参数类型[json],参数:[{"id":1,"pageNum":1,"pageSize":10}]
2025-07-22 19:24:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/download],耗时:[359]毫秒
2025-07-22 19:24:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/download],参数类型[json],参数:[{"id":1,"pageNum":1,"pageSize":10}]
2025-07-22 19:24:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/download],耗时:[361]毫秒
2025-07-22 19:24:47 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/download],参数类型[json],参数:[{"id":1,"pageNum":1,"pageSize":10}]
2025-07-22 19:24:47 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/download],耗时:[366]毫秒
2025-07-22 19:26:29 [boundedElastic-356] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/download],参数类型[json],参数:[{"id":1,"pageNum":1,"pageSize":10}]
2025-07-22 19:26:29 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/download],耗时:[349]毫秒
2025-07-22 19:27:51 [boundedElastic-402] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 19:27:51 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[530]毫秒
2025-07-22 19:28:01 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 19:28:02 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[531]毫秒
2025-07-22 19:28:10 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 19:28:10 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[553]毫秒
2025-07-22 19:35:17 [boundedElastic-406] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 19:35:20 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[2454]毫秒
2025-07-22 19:35:32 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 19:35:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[706]毫秒
2025-07-22 19:35:57 [boundedElastic-411] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 19:35:58 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[681]毫秒
2025-07-22 19:36:25 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1],无参数
2025-07-22 19:36:26 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1],耗时:[754]毫秒
2025-07-22 19:36:29 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1947219921831301122],无参数
2025-07-22 19:36:29 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1947219921831301122],耗时:[590]毫秒
2025-07-22 19:51:15 [boundedElastic-402] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 19:51:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[2383]毫秒
2025-07-22 19:55:02 [boundedElastic-408] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1947219921831301122],无参数
2025-07-22 19:55:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1947219921831301122],耗时:[2505]毫秒
2025-07-22 19:57:30 [boundedElastic-426] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 19:57:30 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[644]毫秒
2025-07-22 20:09:41 [boundedElastic-427] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 20:09:43 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[1745]毫秒
2025-07-22 20:12:17 [boundedElastic-439] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:12:18 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[1087]毫秒
2025-07-22 20:12:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 20:12:33 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[605]毫秒
2025-07-22 20:12:40 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:12:41 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[537]毫秒
2025-07-22 20:12:44 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1947219921831301122],无参数
2025-07-22 20:12:44 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1947219921831301122],耗时:[521]毫秒
2025-07-22 20:13:04 [boundedElastic-439] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:13:04 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[496]毫秒
2025-07-22 20:13:22 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:13:23 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[550]毫秒
2025-07-22 20:13:39 [boundedElastic-434] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-22 20:13:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1124]毫秒
2025-07-22 20:13:40 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-22 20:13:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[104]毫秒
2025-07-22 20:13:40 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-22 20:13:41 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-07-22 20:13:43 [boundedElastic-434] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-22 20:13:43 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[289]毫秒
2025-07-22 20:13:49 [boundedElastic-434] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:13:49 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[546]毫秒
2025-07-22 20:15:08 [boundedElastic-439] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:15:09 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[551]毫秒
2025-07-22 20:15:14 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 20:15:15 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[642]毫秒
2025-07-22 20:15:18 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 20:15:18 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[563]毫秒
2025-07-22 20:15:29 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:15:30 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[612]毫秒
2025-07-22 20:17:06 [boundedElastic-449] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 20:17:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[664]毫秒
2025-07-22 20:17:12 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:17:13 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[547]毫秒
2025-07-22 20:17:16 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 20:17:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[569]毫秒
2025-07-22 20:17:17 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 20:17:18 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[646]毫秒
2025-07-22 20:17:28 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:17:28 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[579]毫秒
2025-07-22 20:17:32 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-22 20:17:33 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[552]毫秒
2025-07-22 20:17:51 [boundedElastic-439] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-22 20:17:52 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[575]毫秒
2025-07-22 20:19:15 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-22 20:19:15 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-22 20:19:16 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
