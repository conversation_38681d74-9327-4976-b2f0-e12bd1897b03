2025-07-30 08:45:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 08:45:39 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 27616 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 08:45:39 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-30 08:45:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-30 08:45:39 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-30 08:45:42 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-30 08:45:42 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-30 08:45:43 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-30 08:45:43 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-30 08:45:43 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-30 08:45:48 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway 192.168.124.4:8080 register finished
2025-07-30 08:45:48 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-30 08:45:48 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 12.679 seconds (process running for 14.083)
2025-07-30 08:45:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-30 08:45:48 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-30 08:58:29 [boundedElastic-26] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 08:58:29 [boundedElastic-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 08:58:30 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1845]毫秒
2025-07-30 08:58:30 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1845]毫秒
2025-07-30 08:58:31 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 08:58:31 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 08:58:31 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[19]毫秒
2025-07-30 08:58:32 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[509]毫秒
2025-07-30 08:58:35 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 08:58:37 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[2586]毫秒
2025-07-30 08:58:38 [boundedElastic-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 08:58:40 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[2197]毫秒
2025-07-30 08:58:40 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 08:58:41 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[117]毫秒
2025-07-30 08:58:41 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 08:58:41 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[83]毫秒
2025-07-30 08:58:41 [boundedElastic-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 08:58:41 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[44]毫秒
2025-07-30 08:58:47 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-30 08:58:47 [boundedElastic-26] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-30 08:58:47 [boundedElastic-24] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-30 08:58:47 [boundedElastic-27] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 08:58:48 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[243]毫秒
2025-07-30 08:58:50 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[2436]毫秒
2025-07-30 08:58:50 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[2850]毫秒
2025-07-30 08:58:51 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[3505]毫秒
2025-07-30 08:58:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["1"]}]
2025-07-30 08:58:55 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 08:58:55 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[149]毫秒
2025-07-30 08:58:56 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 08:58:57 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[1411]毫秒
2025-07-30 08:58:57 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[1405]毫秒
2025-07-30 08:59:03 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 08:59:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[266]毫秒
2025-07-30 08:59:04 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 08:59:04 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[262]毫秒
2025-07-30 08:59:04 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["1"]}]
2025-07-30 08:59:04 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[25]毫秒
2025-07-30 08:59:26 [boundedElastic-24] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 08:59:26 [boundedElastic-24] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 08:59:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[45]毫秒
2025-07-30 08:59:26 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[285]毫秒
2025-07-30 08:59:26 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 08:59:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 08:59:26 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-30 08:59:26 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[223]毫秒
2025-07-30 08:59:30 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 08:59:32 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1710]毫秒
2025-07-30 08:59:32 [boundedElastic-24] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 08:59:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[904]毫秒
2025-07-30 08:59:33 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 08:59:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[139]毫秒
2025-07-30 08:59:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 08:59:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[50]毫秒
2025-07-30 08:59:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 08:59:34 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-30 08:59:39 [boundedElastic-24] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 08:59:39 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 08:59:39 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 08:59:39 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-30 08:59:39 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 08:59:39 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[25]毫秒
2025-07-30 08:59:40 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[507]毫秒
2025-07-30 08:59:40 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[472]毫秒
2025-07-30 08:59:40 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 08:59:40 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[38]毫秒
2025-07-30 08:59:42 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 08:59:42 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 08:59:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[186]毫秒
2025-07-30 08:59:43 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[276]毫秒
2025-07-30 08:59:54 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 08:59:55 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[644]毫秒
2025-07-30 08:59:55 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 08:59:55 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[255]毫秒
2025-07-30 09:06:28 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:06:29 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[159]毫秒
2025-07-30 09:06:36 [boundedElastic-29] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:06:36 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:06:36 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:06:37 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-30 09:06:37 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:06:37 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[25]毫秒
2025-07-30 09:06:37 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[584]毫秒
2025-07-30 09:06:37 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[656]毫秒
2025-07-30 09:06:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:06:38 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[48]毫秒
2025-07-30 09:06:50 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:06:50 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:06:50 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:06:50 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[98]毫秒
2025-07-30 09:06:50 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:06:50 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-30 09:06:51 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[909]毫秒
2025-07-30 09:06:51 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[986]毫秒
2025-07-30 09:06:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:06:51 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[33]毫秒
2025-07-30 09:07:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:07:00 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:00 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:07:01 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[208]毫秒
2025-07-30 09:07:01 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:01 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-30 09:07:01 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1050]毫秒
2025-07-30 09:07:01 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1100]毫秒
2025-07-30 09:07:02 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:02 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[25]毫秒
2025-07-30 09:07:11 [boundedElastic-30] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:11 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:07:11 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:07:11 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-30 09:07:11 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:11 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[30]毫秒
2025-07-30 09:07:12 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[483]毫秒
2025-07-30 09:07:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[569]毫秒
2025-07-30 09:07:12 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[64]毫秒
2025-07-30 09:07:37 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:37 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 09:07:37 [boundedElastic-27] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:07:37 [boundedElastic-27] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:07:37 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-30 09:07:37 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:37 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[47]毫秒
2025-07-30 09:07:38 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1042]毫秒
2025-07-30 09:07:38 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1038]毫秒
2025-07-30 09:07:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1055]毫秒
2025-07-30 09:07:38 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:38 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[24]毫秒
2025-07-30 09:07:45 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:45 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:07:45 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:07:46 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[67]毫秒
2025-07-30 09:07:46 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 09:07:46 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[523]毫秒
2025-07-30 09:07:46 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-30 09:07:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[688]毫秒
2025-07-30 09:07:46 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[541]毫秒
2025-07-30 09:07:47 [boundedElastic-27] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:07:47 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[47]毫秒
2025-07-30 09:08:02 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:08:02 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[87]毫秒
2025-07-30 09:08:57 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:08:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[724]毫秒
2025-07-30 09:08:58 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:08:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[247]毫秒
2025-07-30 09:09:01 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:09:01 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[562]毫秒
2025-07-30 09:09:01 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1934261959020875778"],"beginDate":["2024-07-30"],"endDate":["2025-07-30"],"dateRange":["last1year"]}]
2025-07-30 09:09:01 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/getStatisticsData],耗时:[205]毫秒
2025-07-30 09:09:02 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:09:02 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:09:02 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[212]毫秒
2025-07-30 09:09:02 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 09:09:02 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[55]毫秒
2025-07-30 09:09:02 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[444]毫秒
2025-07-30 09:09:06 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:09:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:09:06 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[144]毫秒
2025-07-30 09:09:06 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-30 09:09:07 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:09:07 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[54]毫秒
2025-07-30 09:09:07 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[440]毫秒
2025-07-30 09:09:07 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[493]毫秒
2025-07-30 09:09:07 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:09:07 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-30 09:09:23 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-30 09:09:23 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"params[timeRange]":[""]}]
2025-07-30 09:09:23 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[77]毫秒
2025-07-30 09:09:24 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[512]毫秒
2025-07-30 09:09:40 [boundedElastic-36] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/download],参数类型[json],参数:[{"pageNum":1,"pageSize":10,"params":{"timeRange":[]}}]
2025-07-30 09:09:40 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/download],耗时:[56]毫秒
2025-07-30 09:10:10 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:10:10 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],无参数
2025-07-30 09:10:10 [boundedElastic-39] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:10:10 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[77]毫秒
2025-07-30 09:10:10 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[234]毫秒
2025-07-30 09:10:10 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[446]毫秒
2025-07-30 09:10:32 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],无参数
2025-07-30 09:10:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[368]毫秒
2025-07-30 09:10:32 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:10:33 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[382]毫秒
2025-07-30 09:10:33 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:10:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[93]毫秒
2025-07-30 09:13:59 [boundedElastic-39] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:13:59 [boundedElastic-27] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 09:13:59 [boundedElastic-38] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:13:59 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[105]毫秒
2025-07-30 09:13:59 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:13:59 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:13:59 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:13:59 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[33]毫秒
2025-07-30 09:13:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[295]毫秒
2025-07-30 09:13:59 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[243]毫秒
2025-07-30 09:13:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:13:59 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[532]毫秒
2025-07-30 09:13:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[162]毫秒
2025-07-30 09:13:59 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[472]毫秒
2025-07-30 09:13:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:14:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[380]毫秒
2025-07-30 09:14:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 09:14:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[46]毫秒
2025-07-30 09:14:08 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:14:08 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[249]毫秒
2025-07-30 09:14:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:14:09 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[410]毫秒
2025-07-30 09:14:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:14:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[48]毫秒
2025-07-30 09:15:48 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:15:48 [boundedElastic-43] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:15:48 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 09:15:48 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[110]毫秒
2025-07-30 09:15:48 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[278]毫秒
2025-07-30 09:15:49 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:15:49 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:15:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:15:49 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[47]毫秒
2025-07-30 09:15:49 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[489]毫秒
2025-07-30 09:15:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[247]毫秒
2025-07-30 09:15:49 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[259]毫秒
2025-07-30 09:18:34 [boundedElastic-45] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:18:34 [boundedElastic-46] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:18:34 [boundedElastic-46] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 09:18:34 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[85]毫秒
2025-07-30 09:18:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:18:34 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:18:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:18:34 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[28]毫秒
2025-07-30 09:18:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[281]毫秒
2025-07-30 09:18:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[233]毫秒
2025-07-30 09:18:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[236]毫秒
2025-07-30 09:18:34 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[493]毫秒
2025-07-30 09:18:38 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:18:39 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[185]毫秒
2025-07-30 09:18:39 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:18:39 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[198]毫秒
2025-07-30 09:18:39 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:18:39 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[69]毫秒
2025-07-30 09:19:07 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["弗瑞德"],"isDistributors":["0"]}]
2025-07-30 09:19:07 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[90]毫秒
2025-07-30 09:19:11 [boundedElastic-38] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["德"],"isDistributors":["0"]}]
2025-07-30 09:19:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[45]毫秒
2025-07-30 09:19:23 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["孚润德"],"isDistributors":["0"]}]
2025-07-30 09:19:23 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[73]毫秒
2025-07-30 09:23:50 [boundedElastic-27] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["青岛"],"isDistributors":["0"]}]
2025-07-30 09:23:50 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[69]毫秒
2025-07-30 09:26:51 [boundedElastic-52] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["青岛"],"isDistributors":["0"]}]
2025-07-30 09:26:53 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[1309]毫秒
2025-07-30 09:26:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["青岛"],"isDistributors":["0"]}]
2025-07-30 09:26:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[89]毫秒
2025-07-30 09:27:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["青岛"],"isDistributors":["0"]}]
2025-07-30 09:27:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[90]毫秒
2025-07-30 09:27:47 [boundedElastic-53] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdConfig/addCompany],参数类型[json],参数:[{"shippingId":"1934261052682113025","personDomestic":5.66,"personAbroad":5.72,"companyDomestic":3.96,"companyAbroad":6.96,"maxCredit":6000,"remark":""}]
2025-07-30 09:27:48 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdConfig/addCompany],耗时:[517]毫秒
2025-07-30 09:28:03 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdConfig/addCompany],参数类型[json],参数:[{"shippingId":"1934261052682113025","personDomestic":5.66,"personAbroad":5.72,"companyDomestic":3.96,"companyAbroad":6.96,"maxCredit":6000,"remark":""}]
2025-07-30 09:28:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdConfig/addCompany],耗时:[247]毫秒
2025-07-30 09:30:04 [boundedElastic-54] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 09:30:04 [boundedElastic-54] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:30:04 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:30:04 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[412]毫秒
2025-07-30 09:30:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[631]毫秒
2025-07-30 09:30:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:30:05 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:30:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:30:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[38]毫秒
2025-07-30 09:30:05 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[253]毫秒
2025-07-30 09:30:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[240]毫秒
2025-07-30 09:30:05 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1075]毫秒
2025-07-30 09:30:11 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["青岛"],"isDistributors":["0"]}]
2025-07-30 09:30:11 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[110]毫秒
2025-07-30 09:30:46 [boundedElastic-51] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdConfig/addCompany],参数类型[json],参数:[{"transportId":"1934261052682113025","personDomestic":6.96,"personAbroad":6.32,"companyDomestic":7.69,"companyAbroad":5.93,"maxCredit":5000,"remark":""}]
2025-07-30 09:30:47 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdConfig/addCompany],耗时:[441]毫秒
2025-07-30 09:33:10 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:33:10 [boundedElastic-52] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:33:10 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:33:10 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:33:10 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:33:10 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 09:33:10 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[76]毫秒
2025-07-30 09:33:10 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[80]毫秒
2025-07-30 09:33:10 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[256]毫秒
2025-07-30 09:33:10 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[253]毫秒
2025-07-30 09:33:10 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[295]毫秒
2025-07-30 09:33:10 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[587]毫秒
2025-07-30 09:33:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["青岛"],"isDistributors":["0"]}]
2025-07-30 09:33:18 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[95]毫秒
2025-07-30 09:33:46 [boundedElastic-57] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdConfig/addCompany],参数类型[json],参数:[{"transportId":"1950013234926407682","shippingId":"1934261052682113025","personDomestic":6.52,"personAbroad":8.36,"companyDomestic":5.76,"companyAbroad":7.69,"maxCredit":5000,"remark":""}]
2025-07-30 09:33:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdConfig/addCompany],耗时:[544]毫秒
2025-07-30 09:33:47 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:33:47 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[181]毫秒
2025-07-30 09:33:47 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:33:47 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[186]毫秒
2025-07-30 09:33:51 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:33:51 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[188]毫秒
2025-07-30 09:33:51 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:33:51 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[252]毫秒
2025-07-30 09:33:52 [boundedElastic-59] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:33:52 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[87]毫秒
2025-07-30 09:33:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:33:59 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[296]毫秒
2025-07-30 09:33:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:33:59 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[154]毫秒
2025-07-30 09:33:59 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:33:59 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[86]毫秒
2025-07-30 09:35:24 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:35:24 [boundedElastic-57] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:35:24 [boundedElastic-57] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 09:35:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:35:25 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:35:25 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[121]毫秒
2025-07-30 09:35:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:35:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[72]毫秒
2025-07-30 09:35:25 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[361]毫秒
2025-07-30 09:35:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[270]毫秒
2025-07-30 09:35:25 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[506]毫秒
2025-07-30 09:35:25 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[920]毫秒
2025-07-30 09:35:26 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1950013234926407682"]}]
2025-07-30 09:35:26 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[186]毫秒
2025-07-30 09:35:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:35:27 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[269]毫秒
2025-07-30 09:35:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:35:27 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[45]毫秒
2025-07-30 09:38:02 [boundedElastic-61] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:38:02 [boundedElastic-60] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:38:02 [boundedElastic-60] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:38:02 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[76]毫秒
2025-07-30 09:38:02 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:38:02 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:38:02 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 09:38:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[266]毫秒
2025-07-30 09:38:02 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[69]毫秒
2025-07-30 09:38:02 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[395]毫秒
2025-07-30 09:38:02 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[262]毫秒
2025-07-30 09:38:03 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[566]毫秒
2025-07-30 09:38:30 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"keyword":["test"],"isDistributors":["0"]}]
2025-07-30 09:38:30 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[65]毫秒
2025-07-30 09:38:36 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"keyword":["青岛"],"isDistributors":["0"]}]
2025-07-30 09:38:36 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[70]毫秒
2025-07-30 09:38:44 [boundedElastic-62] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdConfig/addCompany],参数类型[json],参数:[{"transportId":"1934261959020875778","shippingId":"1938805163007250433","personDomestic":0,"personAbroad":0,"companyDomestic":0,"companyAbroad":0,"maxCredit":0,"remark":""}]
2025-07-30 09:38:44 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdConfig/addCompany],耗时:[507]毫秒
2025-07-30 09:38:45 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:38:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[513]毫秒
2025-07-30 09:38:45 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 09:38:45 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[214]毫秒
2025-07-30 09:39:19 [boundedElastic-57] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 09:39:19 [boundedElastic-51] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 09:39:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[154]毫秒
2025-07-30 09:39:20 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[851]毫秒
2025-07-30 09:39:21 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 09:39:21 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 09:39:21 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-30 09:39:21 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[107]毫秒
2025-07-30 09:39:27 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 09:39:29 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1455]毫秒
2025-07-30 09:39:29 [boundedElastic-63] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 09:39:31 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1751]毫秒
2025-07-30 09:39:32 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 09:39:32 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[98]毫秒
2025-07-30 09:39:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 09:39:32 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[86]毫秒
2025-07-30 09:39:32 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 09:39:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-30 09:39:42 [boundedElastic-63] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 09:39:42 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[470]毫秒
2025-07-30 09:39:42 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:39:42 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[211]毫秒
2025-07-30 09:39:43 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:39:43 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"],"params[timeRange]":[""]}]
2025-07-30 09:39:43 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-30 09:39:43 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-30 09:39:44 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[592]毫秒
2025-07-30 09:39:44 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[403]毫秒
2025-07-30 09:39:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 09:39:44 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 09:39:44 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:39:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-30 09:39:44 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:39:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[28]毫秒
2025-07-30 09:39:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[582]毫秒
2025-07-30 09:39:45 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 09:39:45 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[894]毫秒
2025-07-30 09:39:45 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[242]毫秒
2025-07-30 09:39:45 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 09:39:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[356]毫秒
2025-07-30 09:39:45 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:39:46 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[121]毫秒
2025-07-30 09:39:57 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdConfig/confirmCooperation],参数类型[param],参数:[{"id":["1950369194603769857"]}]
2025-07-30 09:39:58 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdConfig/confirmCooperation],耗时:[858]毫秒
2025-07-30 09:39:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 09:39:59 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[757]毫秒
2025-07-30 09:39:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 09:39:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[215]毫秒
2025-07-30 09:40:14 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:40:14 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[263]毫秒
2025-07-30 09:40:15 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:40:15 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[299]毫秒
2025-07-30 09:40:15 [boundedElastic-60] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:40:15 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[55]毫秒
2025-07-30 09:42:08 [boundedElastic-66] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:42:08 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:42:08 [boundedElastic-66] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:42:08 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[73]毫秒
2025-07-30 09:42:08 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 09:42:08 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 09:42:08 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[288]毫秒
2025-07-30 09:42:08 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[383]毫秒
2025-07-30 09:42:08 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[243]毫秒
2025-07-30 09:42:08 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[377]毫秒
2025-07-30 09:56:18 [boundedElastic-86] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 09:56:18 [boundedElastic-87] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 09:56:19 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[130]毫秒
2025-07-30 09:56:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[294]毫秒
2025-07-30 09:56:19 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 09:56:19 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 09:56:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[17]毫秒
2025-07-30 09:56:19 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[253]毫秒
2025-07-30 09:56:22 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 09:56:23 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1070]毫秒
2025-07-30 09:56:23 [boundedElastic-86] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 09:56:24 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[664]毫秒
2025-07-30 09:56:24 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 09:56:24 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[85]毫秒
2025-07-30 09:56:24 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 09:56:24 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-30 09:56:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 09:56:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-30 09:56:29 [boundedElastic-86] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 09:56:29 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:56:29 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-30 09:56:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 09:56:29 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 09:56:29 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:56:29 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-30 09:56:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[533]毫秒
2025-07-30 09:56:29 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[557]毫秒
2025-07-30 09:56:29 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[693]毫秒
2025-07-30 09:56:29 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 09:56:29 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[25]毫秒
2025-07-30 09:56:30 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 09:56:30 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[181]毫秒
2025-07-30 09:56:30 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 09:56:30 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[227]毫秒
2025-07-30 09:57:00 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:57:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:57:00 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:57:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[274]毫秒
2025-07-30 09:57:00 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[86]毫秒
2025-07-30 09:57:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[256]毫秒
2025-07-30 09:57:12 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /salary/usdConfig/deleteCompany],参数类型[param],参数:[{"id":["1950369194603769857"]}]
2025-07-30 09:57:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /salary/usdConfig/deleteCompany],耗时:[436]毫秒
2025-07-30 09:57:12 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:57:12 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[217]毫秒
2025-07-30 09:57:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:57:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[201]毫秒
2025-07-30 09:59:08 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:59:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[564]毫秒
2025-07-30 09:59:08 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:59:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[405]毫秒
2025-07-30 09:59:09 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:59:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[53]毫秒
2025-07-30 09:59:11 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:59:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[398]毫秒
2025-07-30 09:59:11 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:59:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[591]毫秒
2025-07-30 09:59:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:59:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[72]毫秒
2025-07-30 09:59:30 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 09:59:31 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1344]毫秒
2025-07-30 09:59:31 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 09:59:31 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[88]毫秒
2025-07-30 09:59:31 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 09:59:31 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[40]毫秒
2025-07-30 09:59:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 09:59:32 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:59:32 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 09:59:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[36]毫秒
2025-07-30 09:59:32 [boundedElastic-76] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 09:59:32 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[40]毫秒
2025-07-30 09:59:32 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[324]毫秒
2025-07-30 09:59:32 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[386]毫秒
2025-07-30 10:00:27 [boundedElastic-76] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 10:00:27 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[142]毫秒
2025-07-30 10:00:27 [boundedElastic-76] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 10:00:27 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[32]毫秒
2025-07-30 10:00:27 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 10:00:27 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[167]毫秒
2025-07-30 10:00:28 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:00:28 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-30 10:00:29 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 10:00:31 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1168]毫秒
2025-07-30 10:00:31 [boundedElastic-76] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 10:00:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1165]毫秒
2025-07-30 10:00:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 10:00:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[77]毫秒
2025-07-30 10:00:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 10:00:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-07-30 10:00:33 [boundedElastic-76] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:00:33 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:00:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[257]毫秒
2025-07-30 10:00:33 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-30 10:00:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:00:33 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 10:00:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[44]毫秒
2025-07-30 10:00:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[198]毫秒
2025-07-30 10:00:38 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdConfig/enableCooperation],参数类型[param],参数:[{"id":["1950369194603769857"]}]
2025-07-30 10:00:38 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdConfig/enableCooperation],耗时:[398]毫秒
2025-07-30 10:00:38 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:00:39 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[295]毫秒
2025-07-30 10:00:39 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:00:39 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[177]毫秒
2025-07-30 10:00:49 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 10:00:49 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 10:00:49 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[25]毫秒
2025-07-30 10:00:49 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[109]毫秒
2025-07-30 10:00:49 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 10:00:49 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:00:49 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-30 10:00:49 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[102]毫秒
2025-07-30 10:00:51 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 10:00:52 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[844]毫秒
2025-07-30 10:00:53 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 10:00:53 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[696]毫秒
2025-07-30 10:00:53 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 10:00:54 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[101]毫秒
2025-07-30 10:00:54 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 10:00:54 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[42]毫秒
2025-07-30 10:00:54 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:00:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[41]毫秒
2025-07-30 10:00:57 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-30 10:00:57 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:00:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:00:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[136]毫秒
2025-07-30 10:00:57 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:00:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[152]毫秒
2025-07-30 10:00:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[687]毫秒
2025-07-30 10:00:57 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[768]毫秒
2025-07-30 10:00:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:00:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-30 10:01:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-30 10:01:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-30 10:01:00 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-30 10:01:00 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[396]毫秒
2025-07-30 10:01:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:01:01 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 10:01:01 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:01:02 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[41]毫秒
2025-07-30 10:01:02 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[193]毫秒
2025-07-30 10:01:02 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[563]毫秒
2025-07-30 10:01:13 [boundedElastic-88] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 10:01:14 [boundedElastic-88] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 10:01:14 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[30]毫秒
2025-07-30 10:01:14 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[345]毫秒
2025-07-30 10:01:15 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 10:01:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:01:15 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-30 10:01:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[89]毫秒
2025-07-30 10:01:21 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 10:01:22 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[941]毫秒
2025-07-30 10:01:22 [boundedElastic-72] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 10:01:23 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[655]毫秒
2025-07-30 10:01:23 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 10:01:24 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[88]毫秒
2025-07-30 10:01:24 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 10:01:24 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-30 10:01:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:01:24 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[2]毫秒
2025-07-30 10:01:27 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:01:27 [boundedElastic-77] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 10:01:27 [boundedElastic-72] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 10:01:27 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[84]毫秒
2025-07-30 10:01:27 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:01:27 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[29]毫秒
2025-07-30 10:01:27 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[405]毫秒
2025-07-30 10:01:27 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[585]毫秒
2025-07-30 10:01:27 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:01:27 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[30]毫秒
2025-07-30 10:01:30 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-30 10:01:30 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"],"params[timeRange]":[""]}]
2025-07-30 10:01:30 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[100]毫秒
2025-07-30 10:01:31 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[394]毫秒
2025-07-30 10:01:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:01:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[501]毫秒
2025-07-30 10:01:48 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-30 10:01:48 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-30 10:01:48 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-30 10:01:48 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[68]毫秒
2025-07-30 10:01:48 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[73]毫秒
2025-07-30 10:01:48 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:01:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-30 10:01:48 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[49]毫秒
2025-07-30 10:01:49 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[642]毫秒
2025-07-30 10:01:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[2737]毫秒
2025-07-30 10:01:54 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getCompanyInfoQr],无参数
2025-07-30 10:01:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getCompanyInfoQr],耗时:[869]毫秒
2025-07-30 10:02:36 [boundedElastic-73] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:02:37 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[1010]毫秒
2025-07-30 10:02:55 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:02:56 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[1105]毫秒
2025-07-30 10:03:17 [boundedElastic-74] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:03:17 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[903]毫秒
2025-07-30 10:06:38 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:06:39 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[1119]毫秒
2025-07-30 10:06:40 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getCompanyInfoQr],无参数
2025-07-30 10:06:41 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getCompanyInfoQr],耗时:[592]毫秒
2025-07-30 10:07:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:07:05 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[698]毫秒
2025-07-30 10:07:32 [boundedElastic-96] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 10:07:33 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[929]毫秒
2025-07-30 10:07:33 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:07:33 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[489]毫秒
2025-07-30 10:07:42 [boundedElastic-96] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:07:42 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[77]毫秒
2025-07-30 10:07:44 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:07:44 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[107]毫秒
2025-07-30 10:07:59 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:07:59 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[166]毫秒
2025-07-30 10:08:01 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/workorderDetail/exportTemplate],无参数
2025-07-30 10:08:01 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/workorderDetail/exportTemplate],耗时:[507]毫秒
2025-07-30 10:08:14 [boundedElastic-95] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/workorderDetail/importTemplate],参数类型[param],参数:[{"workorderId":[""]}]
2025-07-30 10:08:15 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:08:15 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[216]毫秒
2025-07-30 10:08:16 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/workorderDetail/importTemplate],耗时:[1844]毫秒
2025-07-30 10:08:17 [boundedElastic-95] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:08:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[87]毫秒
2025-07-30 10:08:20 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:08:20 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[105]毫秒
2025-07-30 10:08:26 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:08:26 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[102]毫秒
2025-07-30 10:08:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:08:34 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[151]毫秒
2025-07-30 10:08:36 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:08:36 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[87]毫秒
2025-07-30 10:08:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:08:43 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[137]毫秒
2025-07-30 10:09:05 [boundedElastic-73] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 10:09:06 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[829]毫秒
2025-07-30 10:09:06 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:09:06 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[327]毫秒
2025-07-30 10:11:15 [boundedElastic-101] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1950013234926407682"],"beginDate":["2024-07-30"],"endDate":["2025-07-30"],"dateRange":["last1year"]}]
2025-07-30 10:11:16 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/getStatisticsData],耗时:[244]毫秒
2025-07-30 10:11:44 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /salary/usdConfig/updateCompany],参数类型[json],参数:[{"id":"1950369194603769857","transportId":"1950013234926407682","shippingId":"1934261052682113025","personDomestic":6.59,"personAbroad":8.6,"companyDomestic":5.76,"companyAbroad":7.69,"maxCredit":5000,"remark":""}]
2025-07-30 10:11:45 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /salary/usdConfig/updateCompany],耗时:[652]毫秒
2025-07-30 10:11:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:11:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[561]毫秒
2025-07-30 10:11:46 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:11:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[282]毫秒
2025-07-30 10:11:55 [boundedElastic-94] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"keyword":["青岛"],"isDistributors":["0"]}]
2025-07-30 10:11:55 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[67]毫秒
2025-07-30 10:12:20 [boundedElastic-96] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdConfig/addCompany],参数类型[json],参数:[{"transportId":"1950013234926407682","shippingId":"1938805163007250433","personDomestic":3.2,"personAbroad":5,"companyDomestic":5,"companyAbroad":5,"maxCredit":9000,"remark":""}]
2025-07-30 10:12:21 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdConfig/addCompany],耗时:[511]毫秒
2025-07-30 10:12:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:12:23 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1132]毫秒
2025-07-30 10:12:23 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:12:23 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[542]毫秒
2025-07-30 10:12:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:12:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[356]毫秒
2025-07-30 10:12:46 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:12:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[329]毫秒
2025-07-30 10:12:47 [boundedElastic-96] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 10:12:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[58]毫秒
2025-07-30 10:12:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"BHFAIOHFOIA","createTime":"2025-07-30 10:07:43","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"6192.0","grantType":"","grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":"SHIP-01","grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","verif]
2025-07-30 10:12:57 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[2592]毫秒
2025-07-30 10:13:34 [boundedElastic-73] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"BHFAIOHFOIA","createTime":"2025-07-30 10:07:43","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"6192.0","grantType":"","grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":"SHIP-01","grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","verif]
2025-07-30 10:13:35 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[668]毫秒
2025-07-30 10:14:31 [boundedElastic-103] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"BHFAIOHFOIA","createTime":"2025-07-30 10:07:43","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"6192.0","grantType":"","grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":"SHIP-01","grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","verif]
2025-07-30 10:14:31 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[795]毫秒
2025-07-30 10:15:57 [boundedElastic-105] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"BHFAIOHFOIA","createTime":"2025-07-30 10:07:43","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"6192.0","grantType":"","grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":"SHIP-01","grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","verif]
2025-07-30 10:15:58 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[631]毫秒
2025-07-30 10:16:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"BHFAIOHFOIA","createTime":"2025-07-30 10:07:43","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"6192.0","grantType":"","grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":"SHIP-01","grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","verif]
2025-07-30 10:16:06 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[675]毫秒
2025-07-30 10:21:36 [boundedElastic-108] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"BHFAIOHFOIA","createTime":"2025-07-30 10:07:43","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"6192.0","grantType":"","grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":"SHIP-01","grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","verif]
2025-07-30 10:21:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[632]毫秒
2025-07-30 10:23:05 [boundedElastic-103] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:23:05 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[113]毫秒
2025-07-30 10:23:16 [boundedElastic-103] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 10:23:16 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:23:16 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 10:23:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[102]毫秒
2025-07-30 10:23:18 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1584]毫秒
2025-07-30 10:23:18 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[2285]毫秒
2025-07-30 10:23:19 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:23:19 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-30 10:26:10 [boundedElastic-112] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:26:10 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[62]毫秒
2025-07-30 10:26:20 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:26:20 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[60]毫秒
2025-07-30 10:26:27 [boundedElastic-114] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/workorderDetail/importTemplate],参数类型[param],参数:[{"workorderId":[""]}]
2025-07-30 10:26:27 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:26:27 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[65]毫秒
2025-07-30 10:26:28 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/workorderDetail/importTemplate],耗时:[1417]毫秒
2025-07-30 10:26:28 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:26:28 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-30 10:26:31 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:26:31 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[54]毫秒
2025-07-30 10:26:56 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"请尽快发放","createTime":"2025-07-30 10:26:09","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"6192.0","grantType":null,"grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":null,"grantStatus":"2","exceptionType":null,"exceptionRe]
2025-07-30 10:27:07 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[11101]毫秒
2025-07-30 10:29:20 [boundedElastic-116] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"请尽快发放","createTime":"2025-07-30 10:26:09","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"6192.0","grantType":null,"grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":null,"grantStatus":"2","exceptionType":null,"exceptionRe]
2025-07-30 10:29:21 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[720]毫秒
2025-07-30 10:29:50 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:29:50 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[262]毫秒
2025-07-30 10:29:50 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 10:29:51 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[291]毫秒
2025-07-30 10:29:51 [boundedElastic-114] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 10:29:51 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[42]毫秒
2025-07-30 10:29:55 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:29:55 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[41]毫秒
2025-07-30 10:29:59 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 10:29:59 [boundedElastic-114] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 10:29:59 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[298]毫秒
2025-07-30 10:29:59 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[430]毫秒
2025-07-30 10:30:03 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdConfig/confirmCooperation],参数类型[param],参数:[{"id":["1950369194603769857"]}]
2025-07-30 10:30:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdConfig/confirmCooperation],耗时:[297]毫秒
2025-07-30 10:30:03 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 10:30:03 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[252]毫秒
2025-07-30 10:30:04 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 10:30:04 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[173]毫秒
2025-07-30 10:31:21 [boundedElastic-106] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:31:21 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[69]毫秒
2025-07-30 10:31:23 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:31:23 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[40]毫秒
2025-07-30 10:31:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:31:35 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[59]毫秒
2025-07-30 10:31:45 [boundedElastic-106] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/workorderDetail/importTemplate],参数类型[param],参数:[{"workorderId":[""]}]
2025-07-30 10:31:45 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:31:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-30 10:31:45 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/workorderDetail/importTemplate],耗时:[429]毫秒
2025-07-30 10:31:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:31:46 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[24]毫秒
2025-07-30 10:31:48 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:31:48 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[40]毫秒
2025-07-30 10:31:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"请尽快发放","createTime":"2025-07-30 10:31:23","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"6192.0","grantType":null,"grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":null,"grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","veri]
2025-07-30 10:31:55 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[621]毫秒
2025-07-30 10:32:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 10:32:13 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 10:32:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[274]毫秒
2025-07-30 10:32:13 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[297]毫秒
2025-07-30 10:42:16 [boundedElastic-130] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 10:42:16 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 10:42:17 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[122]毫秒
2025-07-30 10:42:17 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:42:17 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[449]毫秒
2025-07-30 10:42:17 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:42:17 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[337]毫秒
2025-07-30 10:42:17 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[239]毫秒
2025-07-30 10:42:31 [boundedElastic-131] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 10:42:31 [boundedElastic-132] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 10:42:31 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[37]毫秒
2025-07-30 10:42:33 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1477]毫秒
2025-07-30 10:42:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 10:42:34 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:42:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[12]毫秒
2025-07-30 10:42:35 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[157]毫秒
2025-07-30 10:43:37 [boundedElastic-132] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:43:38 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[111]毫秒
2025-07-30 10:43:41 [boundedElastic-132] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 10:43:42 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[868]毫秒
2025-07-30 10:43:42 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 10:43:42 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[632]毫秒
2025-07-30 10:43:43 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 10:43:43 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[110]毫秒
2025-07-30 10:43:43 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 10:43:43 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-30 10:43:43 [boundedElastic-132] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 10:43:44 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:43:44 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-30 10:43:44 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[639]毫秒
2025-07-30 10:43:44 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:43:45 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[241]毫秒
2025-07-30 10:43:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 10:43:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[550]毫秒
2025-07-30 10:43:47 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:43:48 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[213]毫秒
2025-07-30 10:43:54 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 10:43:54 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[395]毫秒
2025-07-30 10:43:54 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:43:55 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[223]毫秒
2025-07-30 10:43:58 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 10:43:58 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[275]毫秒
2025-07-30 10:43:58 [boundedElastic-126] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 10:43:58 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[25]毫秒
2025-07-30 10:43:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:43:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 10:43:59 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-30 10:43:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[133]毫秒
2025-07-30 10:44:02 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 10:44:02 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[779]毫秒
2025-07-30 10:44:03 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 10:44:03 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[664]毫秒
2025-07-30 10:44:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 10:44:03 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[90]毫秒
2025-07-30 10:44:04 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 10:44:04 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-30 10:44:04 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:44:04 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-30 10:44:07 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-30 10:44:07 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:44:07 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:44:07 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[126]毫秒
2025-07-30 10:44:07 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:44:07 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[47]毫秒
2025-07-30 10:44:08 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[560]毫秒
2025-07-30 10:44:08 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[617]毫秒
2025-07-30 10:44:08 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 10:44:08 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-30 10:44:09 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:44:09 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[487]毫秒
2025-07-30 10:44:10 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:44:10 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[219]毫秒
2025-07-30 10:44:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:44:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[491]毫秒
2025-07-30 10:44:12 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:44:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[303]毫秒
2025-07-30 10:46:47 [boundedElastic-115] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 10:46:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[614]毫秒
2025-07-30 10:46:47 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:46:48 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[216]毫秒
2025-07-30 10:48:55 [boundedElastic-138] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 10:48:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[697]毫秒
2025-07-30 10:48:56 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 10:48:56 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[63]毫秒
2025-07-30 10:48:56 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 10:48:56 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-30 10:48:56 [boundedElastic-138] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:48:56 [boundedElastic-138] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:48:56 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[32]毫秒
2025-07-30 10:48:57 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[527]毫秒
2025-07-30 10:48:57 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:48:57 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[221]毫秒
2025-07-30 10:49:29 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:49:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[560]毫秒
2025-07-30 10:49:29 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:49:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[240]毫秒
2025-07-30 10:49:54 [boundedElastic-135] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 10:49:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[682]毫秒
2025-07-30 10:49:55 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 10:49:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[87]毫秒
2025-07-30 10:49:55 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 10:49:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-30 10:49:56 [boundedElastic-135] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:49:56 [boundedElastic-135] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:49:56 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[72]毫秒
2025-07-30 10:49:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[567]毫秒
2025-07-30 10:49:57 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:49:57 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[233]毫秒
2025-07-30 10:51:17 [boundedElastic-135] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 10:51:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[713]毫秒
2025-07-30 10:51:18 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 10:51:18 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[88]毫秒
2025-07-30 10:51:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 10:51:18 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[41]毫秒
2025-07-30 10:51:18 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 10:51:18 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[32]毫秒
2025-07-30 10:51:18 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:51:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[534]毫秒
2025-07-30 10:51:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:51:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[210]毫秒
2025-07-30 10:51:41 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow],参数类型[json],参数:[{"transportId":"1934261959020875778","shippingId":"1934261959020875778","transactionType":"2","transactionAmount":1000,"transactionTime":"2025-07-30 10:51:26","usdworkoderId":"0","purpose":"1233","remark":""}]
2025-07-30 10:51:41 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow],耗时:[834]毫秒
2025-07-30 10:51:42 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:51:42 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[204]毫秒
2025-07-30 10:52:12 [boundedElastic-134] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow],参数类型[json],参数:[{"transportId":"1934261959020875778","shippingId":"1934261959020875778","transactionType":"2","transactionAmount":100,"transactionTime":"2025-07-30 10:51:49","usdworkoderId":"0","purpose":"银行账户不足","remark":""}]
2025-07-30 10:52:12 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow],耗时:[613]毫秒
2025-07-30 10:52:13 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:52:13 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[250]毫秒
2025-07-30 10:53:27 [boundedElastic-135] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow],参数类型[json],参数:[{"transportId":"1934261959020875778","shippingId":"1938578452525191170","transactionType":"2","transactionAmount":1000,"transactionTime":"2025-07-30 10:52:47","usdworkoderId":"0","purpose":"23105","remark":"75244"}]
2025-07-30 10:53:27 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow],耗时:[639]毫秒
2025-07-30 10:53:28 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:53:28 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[209]毫秒
2025-07-30 10:53:47 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:53:47 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:53:47 [boundedElastic-135] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 10:53:47 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[236]毫秒
2025-07-30 10:53:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[70]毫秒
2025-07-30 10:53:48 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[406]毫秒
2025-07-30 10:53:59 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:53:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[536]毫秒
2025-07-30 10:53:59 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1934261959020875778"],"beginDate":["2024-07-30"],"endDate":["2025-07-30"],"dateRange":["last1year"]}]
2025-07-30 10:53:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/getStatisticsData],耗时:[236]毫秒
2025-07-30 10:54:26 [boundedElastic-134] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow],参数类型[json],参数:[{"transportId":"1934261959020875778","shippingId":"1938805163007250433","transactionType":"2","transactionAmount":10000,"transactionTime":"2025-07-30 10:54:01","usdworkoderId":"0","purpose":"111","remark":"111"}]
2025-07-30 10:54:26 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow],耗时:[538]毫秒
2025-07-30 10:55:08 [boundedElastic-141] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 10:55:08 [boundedElastic-141] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-30 10:55:08 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[172]毫秒
2025-07-30 10:55:08 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-30 10:55:08 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-30 10:55:08 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[523]毫秒
2025-07-30 10:55:09 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[493]毫秒
2025-07-30 10:55:09 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[650]毫秒
2025-07-30 10:56:37 [boundedElastic-135] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow],参数类型[json],参数:[{"transportId":"1934261959020875778","shippingId":"1934796209226878977","transactionType":"2","transactionAmount":10000,"transactionTime":"2025-07-30 10:55:12","usdworkoderId":"1","purpose":"111","remark":"111"}]
2025-07-30 10:56:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow],耗时:[560]毫秒
2025-07-30 10:56:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:56:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[190]毫秒
2025-07-30 10:58:29 [boundedElastic-146] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261959020875778"],"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:58:29 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[291]毫秒
2025-07-30 10:58:39 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934796209226878977"],"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 10:58:39 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[278]毫秒
2025-07-30 11:06:32 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:06:33 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[733]毫秒
2025-07-30 11:06:33 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1934261959020875778"],"beginDate":["2024-07-30"],"endDate":["2025-07-30"],"dateRange":["last1year"]}]
2025-07-30 11:06:33 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/getStatisticsData],耗时:[213]毫秒
2025-07-30 11:06:43 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:06:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[542]毫秒
2025-07-30 11:06:44 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1934261959020875778"],"beginDate":["2024-07-30"],"endDate":["2025-07-30"],"dateRange":["last1year"]}]
2025-07-30 11:06:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/getStatisticsData],耗时:[197]毫秒
2025-07-30 11:07:16 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 11:07:16 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:07:16 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[553]毫秒
2025-07-30 11:07:16 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[647]毫秒
2025-07-30 11:07:16 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 11:07:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 11:07:17 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[257]毫秒
2025-07-30 11:07:17 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[219]毫秒
2025-07-30 11:07:23 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 11:07:23 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:07:24 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[489]毫秒
2025-07-30 11:07:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 11:07:24 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[729]毫秒
2025-07-30 11:07:24 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 11:07:24 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[212]毫秒
2025-07-30 11:07:24 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[239]毫秒
2025-07-30 11:07:36 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 11:07:36 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:07:36 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[512]毫秒
2025-07-30 11:07:37 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:07:37 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[664]毫秒
2025-07-30 11:07:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 11:07:37 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[363]毫秒
2025-07-30 11:07:37 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 11:07:37 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[253]毫秒
2025-07-30 11:07:37 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 11:07:37 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[184]毫秒
2025-07-30 11:07:37 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[178]毫秒
2025-07-30 11:07:51 [boundedElastic-149] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:07:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 11:07:51 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[520]毫秒
2025-07-30 11:07:51 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:07:52 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[630]毫秒
2025-07-30 11:07:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 11:07:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[221]毫秒
2025-07-30 11:07:52 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 11:07:52 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[226]毫秒
2025-07-30 11:07:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[193]毫秒
2025-07-30 11:07:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 11:07:52 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[175]毫秒
2025-07-30 11:08:49 [boundedElastic-143] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 11:08:50 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[892]毫秒
2025-07-30 11:08:50 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 11:08:50 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[86]毫秒
2025-07-30 11:08:50 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 11:08:50 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-07-30 11:08:51 [boundedElastic-143] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 11:08:51 [boundedElastic-143] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:08:51 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[202]毫秒
2025-07-30 11:08:51 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[553]毫秒
2025-07-30 11:08:51 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:08:52 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[230]毫秒
2025-07-30 11:08:52 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 11:08:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[176]毫秒
2025-07-30 11:11:44 [boundedElastic-145] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow/addDistributorFlow],参数类型[json],参数:[{"transportId":"1934261959020875778","distributorsId":"1950013234926407682","transactionType":"2","transactionAmount":1000,"transactionTime":"2025-07-30 11:09:23","usdworkoderId":"1","purpose":"测试入账","remark":""}]
2025-07-30 11:11:44 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow/addDistributorFlow],耗时:[47]毫秒
2025-07-30 11:36:17 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:36:17 [boundedElastic-181] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:36:17 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[259]毫秒
2025-07-30 11:36:17 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["1"]}]
2025-07-30 11:36:17 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[412]毫秒
2025-07-30 11:36:17 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[149]毫秒
2025-07-30 11:36:24 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:36:25 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:36:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 11:36:25 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[258]毫秒
2025-07-30 11:36:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[73]毫秒
2025-07-30 11:36:25 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[440]毫秒
2025-07-30 11:36:57 [boundedElastic-171] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:36:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[275]毫秒
2025-07-30 11:36:58 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 11:36:58 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[463]毫秒
2025-07-30 11:36:58 [boundedElastic-171] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"],"isDistributors":["0"]}]
2025-07-30 11:36:58 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[48]毫秒
2025-07-30 11:48:36 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-30 11:48:36 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-30 11:48:37 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-30 14:15:20 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-30 14:15:20 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 7256 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-30 14:15:20 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-30 14:15:20 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-30 14:15:20 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-30 14:15:23 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-30 14:15:23 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-30 14:15:23 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-30 14:15:24 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-30 14:15:24 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-30 14:15:28 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway 192.168.124.4:8080 register finished
2025-07-30 14:15:28 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-30 14:15:28 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 10.809 seconds (process running for 11.54)
2025-07-30 14:15:28 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-30 14:15:28 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-30 14:21:09 [boundedElastic-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 14:21:09 [boundedElastic-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 14:21:10 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1093]毫秒
2025-07-30 14:21:10 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1358]毫秒
2025-07-30 14:21:11 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 14:21:11 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 14:21:11 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[19]毫秒
2025-07-30 14:21:11 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[341]毫秒
2025-07-30 14:21:14 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 14:21:14 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[206]毫秒
2025-07-30 14:21:14 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 14:21:15 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[93]毫秒
2025-07-30 14:21:21 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 14:21:21 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[37]毫秒
2025-07-30 14:21:21 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 14:21:21 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[100]毫秒
2025-07-30 14:22:51 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 14:22:53 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1341]毫秒
2025-07-30 14:22:53 [boundedElastic-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 14:22:55 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1562]毫秒
2025-07-30 14:22:55 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 14:22:56 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[101]毫秒
2025-07-30 14:22:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 14:22:56 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[39]毫秒
2025-07-30 14:22:56 [boundedElastic-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 14:22:56 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-30 14:23:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 14:23:16 [boundedElastic-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-30 14:23:16 [boundedElastic-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:23:16 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[106]毫秒
2025-07-30 14:23:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 14:23:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-30 14:23:17 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1114]毫秒
2025-07-30 14:23:18 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1558]毫秒
2025-07-30 14:23:18 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 14:23:18 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[56]毫秒
2025-07-30 14:23:22 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:23:22 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[542]毫秒
2025-07-30 14:23:22 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:23:22 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[215]毫秒
2025-07-30 14:23:22 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 14:23:23 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[178]毫秒
2025-07-30 14:23:46 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow/addDistributorFlow],参数类型[json],参数:[{"transportId":"1934261959020875778","distributorsId":"1950013234926407682","transactionType":"2","transactionAmount":1000,"transactionTime":"2025-07-30 14:23:23","usdworkoderId":"1","purpose":"充值","remark":"123456"}]
2025-07-30 14:23:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow/addDistributorFlow],耗时:[536]毫秒
2025-07-30 14:46:08 [boundedElastic-23] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow/addDistributorFlow],参数类型[json],参数:[{"transportId":"1934261959020875778","distributorsId":"1950013234926407682","transactionType":"2","transactionAmount":1000,"transactionTime":"2025-07-30 14:23:23","usdworkoderId":"1","purpose":"充值","remark":"123456"}]
2025-07-30 14:46:11 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow/addDistributorFlow],耗时:[2482]毫秒
2025-07-30 14:46:11 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 14:46:11 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[373]毫秒
2025-07-30 14:46:16 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:46:16 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:46:17 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[243]毫秒
2025-07-30 14:46:17 [boundedElastic-23] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["1"]}]
2025-07-30 14:46:17 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[379]毫秒
2025-07-30 14:46:17 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[211]毫秒
2025-07-30 14:46:19 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:46:19 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[204]毫秒
2025-07-30 14:46:19 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:46:20 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[323]毫秒
2025-07-30 14:46:20 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["1"]}]
2025-07-30 14:46:20 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[29]毫秒
2025-07-30 14:46:44 [boundedElastic-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:46:45 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[247]毫秒
2025-07-30 14:46:45 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:46:45 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[283]毫秒
2025-07-30 14:46:45 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["1"]}]
2025-07-30 14:46:45 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[30]毫秒
2025-07-30 14:49:00 [boundedElastic-42] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:49:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[623]毫秒
2025-07-30 14:49:01 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:49:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[221]毫秒
2025-07-30 14:49:01 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 14:49:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[252]毫秒
2025-07-30 14:49:12 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:49:13 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[541]毫秒
2025-07-30 14:49:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:49:13 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[258]毫秒
2025-07-30 14:49:14 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 14:49:14 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[200]毫秒
2025-07-30 14:50:25 [boundedElastic-38] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 14:50:25 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[768]毫秒
2025-07-30 14:50:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 14:50:26 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[107]毫秒
2025-07-30 14:50:26 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 14:50:26 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[55]毫秒
2025-07-30 14:50:26 [boundedElastic-38] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 14:50:26 [boundedElastic-38] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:50:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[193]毫秒
2025-07-30 14:50:27 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[544]毫秒
2025-07-30 14:50:27 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:50:27 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[246]毫秒
2025-07-30 14:50:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 14:50:27 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[193]毫秒
2025-07-30 14:51:36 [boundedElastic-45] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:51:36 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[574]毫秒
2025-07-30 14:51:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:51:36 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[228]毫秒
2025-07-30 14:51:37 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 14:51:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[14884]毫秒
2025-07-30 14:52:10 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 14:52:10 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[280]毫秒
2025-07-30 14:59:33 [boundedElastic-56] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:59:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[542]毫秒
2025-07-30 14:59:33 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 14:59:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[197]毫秒
2025-07-30 14:59:34 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 14:59:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[214]毫秒
2025-07-30 15:08:34 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 15:08:35 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1516]毫秒
2025-07-30 15:08:36 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-30 15:08:36 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[278]毫秒
2025-07-30 15:08:36 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:08:37 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[870]毫秒
2025-07-30 15:08:47 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 15:08:47 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 15:08:47 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[279]毫秒
2025-07-30 15:08:47 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[591]毫秒
2025-07-30 15:08:49 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:08:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 15:08:49 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[8]毫秒
2025-07-30 15:08:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[171]毫秒
2025-07-30 15:08:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 15:08:59 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[852]毫秒
2025-07-30 15:08:59 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 15:09:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[594]毫秒
2025-07-30 15:09:00 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 15:09:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[93]毫秒
2025-07-30 15:09:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 15:09:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-30 15:09:01 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:09:01 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[8]毫秒
2025-07-30 15:09:03 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 15:09:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 15:09:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[249]毫秒
2025-07-30 15:09:03 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[318]毫秒
2025-07-30 15:09:05 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:09:06 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[725]毫秒
2025-07-30 15:09:06 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:09:06 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[230]毫秒
2025-07-30 15:09:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:09:07 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[737]毫秒
2025-07-30 15:09:24 [boundedElastic-69] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:09:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[698]毫秒
2025-07-30 15:09:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:09:25 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[246]毫秒
2025-07-30 15:09:25 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:09:26 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[691]毫秒
2025-07-30 15:10:36 [boundedElastic-71] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:10:37 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[718]毫秒
2025-07-30 15:10:37 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:10:37 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[244]毫秒
2025-07-30 15:10:37 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:10:38 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[682]毫秒
2025-07-30 15:12:26 [boundedElastic-74] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:12:26 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[833]毫秒
2025-07-30 15:12:27 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:12:27 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[329]毫秒
2025-07-30 15:12:27 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:12:27 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[165]毫秒
2025-07-30 15:13:18 [boundedElastic-74] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:13:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[780]毫秒
2025-07-30 15:13:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:13:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[251]毫秒
2025-07-30 15:13:20 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:13:20 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[257]毫秒
2025-07-30 15:14:03 [boundedElastic-62] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:14:03 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[755]毫秒
2025-07-30 15:14:03 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:14:04 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[227]毫秒
2025-07-30 15:14:04 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:14:04 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[260]毫秒
2025-07-30 15:18:08 [boundedElastic-77] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:18:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[674]毫秒
2025-07-30 15:18:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:18:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[213]毫秒
2025-07-30 15:18:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:18:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[218]毫秒
2025-07-30 15:19:54 [boundedElastic-79] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:19:54 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[657]毫秒
2025-07-30 15:19:55 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:19:55 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[213]毫秒
2025-07-30 15:19:55 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:19:55 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[232]毫秒
2025-07-30 15:29:04 [boundedElastic-83] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 15:29:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[858]毫秒
2025-07-30 15:29:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 15:29:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[109]毫秒
2025-07-30 15:29:05 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 15:29:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-07-30 15:29:05 [boundedElastic-83] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:29:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[188]毫秒
2025-07-30 15:29:06 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 15:29:06 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[548]毫秒
2025-07-30 15:29:06 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 15:29:06 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[60]毫秒
2025-07-30 15:29:06 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 15:29:06 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-07-30 15:29:06 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:29:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-30 15:29:27 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 15:29:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[633]毫秒
2025-07-30 15:29:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 15:29:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[185]毫秒
2025-07-30 15:29:28 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 15:29:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-07-30 15:29:28 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:29:28 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-30 15:29:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 15:29:33 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[665]毫秒
2025-07-30 15:29:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 15:29:33 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[69]毫秒
2025-07-30 15:29:33 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 15:29:33 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-30 15:29:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:29:34 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-30 15:29:34 [boundedElastic-91] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:29:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[905]毫秒
2025-07-30 15:29:35 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:29:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[246]毫秒
2025-07-30 15:29:36 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:29:36 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[312]毫秒
2025-07-30 15:33:18 [boundedElastic-99] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:33:19 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[739]毫秒
2025-07-30 15:33:19 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:33:19 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[224]毫秒
2025-07-30 15:33:19 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:33:19 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[172]毫秒
2025-07-30 15:40:44 [boundedElastic-108] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:40:44 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[261]毫秒
2025-07-30 15:41:53 [boundedElastic-112] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:41:53 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[652]毫秒
2025-07-30 15:41:53 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:41:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[222]毫秒
2025-07-30 15:41:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:41:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[164]毫秒
2025-07-30 15:41:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:41:55 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[191]毫秒
2025-07-30 15:42:23 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:42:24 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[659]毫秒
2025-07-30 15:42:25 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:42:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[270]毫秒
2025-07-30 15:42:25 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:42:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[156]毫秒
2025-07-30 15:42:25 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:42:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[234]毫秒
2025-07-30 15:43:22 [boundedElastic-112] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:43:22 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[282]毫秒
2025-07-30 15:43:22 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:43:23 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[201]毫秒
2025-07-30 15:43:26 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["100"]}]
2025-07-30 15:43:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[190]毫秒
2025-07-30 15:43:28 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["100"]}]
2025-07-30 15:43:28 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[255]毫秒
2025-07-30 15:43:51 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:43:52 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[657]毫秒
2025-07-30 15:43:52 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:43:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:43:52 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[207]毫秒
2025-07-30 15:43:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:43:52 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[161]毫秒
2025-07-30 15:43:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[527]毫秒
2025-07-30 15:43:52 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:43:53 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:43:53 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[220]毫秒
2025-07-30 15:43:53 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[218]毫秒
2025-07-30 15:43:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:43:53 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[263]毫秒
2025-07-30 15:43:53 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:43:54 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[151]毫秒
2025-07-30 15:44:07 [boundedElastic-105] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:07 [boundedElastic-110] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:07 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[235]毫秒
2025-07-30 15:44:07 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[328]毫秒
2025-07-30 15:44:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:19 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:19 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[335]毫秒
2025-07-30 15:44:19 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[171]毫秒
2025-07-30 15:44:28 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:28 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:28 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[228]毫秒
2025-07-30 15:44:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[337]毫秒
2025-07-30 15:44:33 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:44:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[650]毫秒
2025-07-30 15:44:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:44:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[234]毫秒
2025-07-30 15:44:35 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:35 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[232]毫秒
2025-07-30 15:44:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:36 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[186]毫秒
2025-07-30 15:44:39 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"transactionType":["1"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:39 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"transactionType":["1"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:44:39 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[403]毫秒
2025-07-30 15:44:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[183]毫秒
2025-07-30 15:45:01 [boundedElastic-105] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdFlow],参数类型[json],参数:[{"transportId":"1950013234926407682","shippingId":"1950013234926407682","transactionType":"2","transactionAmount":1000,"transactionTime":"2025-07-30 15:44:50","usdworkoderId":"1","purpose":"11","remark":"11"}]
2025-07-30 15:45:03 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdFlow],耗时:[2087]毫秒
2025-07-30 15:45:03 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"transactionType":["1"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:45:03 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[151]毫秒
2025-07-30 15:45:07 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:45:07 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[619]毫秒
2025-07-30 15:45:07 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:45:08 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[208]毫秒
2025-07-30 15:45:08 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"transactionType":["1"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:45:08 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[167]毫秒
2025-07-30 15:45:08 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"transactionType":["1"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:45:08 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[142]毫秒
2025-07-30 15:45:15 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:45:15 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:45:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[356]毫秒
2025-07-30 15:45:15 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[397]毫秒
2025-07-30 15:50:59 [boundedElastic-105] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:50:59 [boundedElastic-120] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:50:59 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[347]毫秒
2025-07-30 15:50:59 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[662]毫秒
2025-07-30 15:50:59 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:50:59 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:51:00 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[254]毫秒
2025-07-30 15:51:00 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[354]毫秒
2025-07-30 15:51:06 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:51:06 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 15:51:07 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[311]毫秒
2025-07-30 15:51:07 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[615]毫秒
2025-07-30 15:51:07 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:51:07 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[285]毫秒
2025-07-30 15:51:07 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1950013234926407682"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:51:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[239]毫秒
2025-07-30 15:54:25 [boundedElastic-114] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1950013234926407682"],"beginDate":["2024-07-30"],"endDate":["2025-07-30"],"dateRange":["last1year"]}]
2025-07-30 15:54:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/getStatisticsData],耗时:[329]毫秒
2025-07-30 15:55:52 [boundedElastic-82] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getTotalProperty],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 15:55:52 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 15:55:52 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getTotalProperty],耗时:[239]毫秒
2025-07-30 15:55:52 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[267]毫秒
2025-07-30 15:56:32 [boundedElastic-115] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:56:32 [boundedElastic-115] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:56:32 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:56:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 15:56:33 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[213]毫秒
2025-07-30 15:56:33 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:56:33 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[24]毫秒
2025-07-30 15:56:33 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[564]毫秒
2025-07-30 15:56:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[555]毫秒
2025-07-30 15:56:33 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[564]毫秒
2025-07-30 15:56:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:56:33 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[27]毫秒
2025-07-30 15:56:46 [boundedElastic-115] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 15:56:46 [boundedElastic-100] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 15:56:46 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[162]毫秒
2025-07-30 15:56:46 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[296]毫秒
2025-07-30 15:56:46 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:56:46 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 15:56:46 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-30 15:56:47 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[154]毫秒
2025-07-30 15:56:54 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 15:56:55 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[904]毫秒
2025-07-30 15:56:56 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 15:56:56 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[626]毫秒
2025-07-30 15:56:56 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 15:56:57 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[105]毫秒
2025-07-30 15:56:57 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 15:56:57 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[54]毫秒
2025-07-30 15:56:57 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:56:57 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-30 15:57:05 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 15:57:05 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 15:57:05 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:57:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 15:57:05 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[65]毫秒
2025-07-30 15:57:05 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:57:05 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[54]毫秒
2025-07-30 15:57:05 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[374]毫秒
2025-07-30 15:57:06 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:57:06 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[27]毫秒
2025-07-30 15:57:06 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[566]毫秒
2025-07-30 15:57:06 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[670]毫秒
2025-07-30 15:57:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 15:57:13 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 15:57:13 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[25]毫秒
2025-07-30 15:57:13 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[296]毫秒
2025-07-30 15:57:14 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 15:57:14 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:57:14 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-30 15:57:14 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[89]毫秒
2025-07-30 15:57:52 [boundedElastic-98] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 15:57:52 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[914]毫秒
2025-07-30 15:57:53 [boundedElastic-98] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 15:57:53 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[565]毫秒
2025-07-30 15:57:54 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 15:57:54 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[111]毫秒
2025-07-30 15:57:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 15:57:54 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-07-30 15:57:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 15:57:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-30 15:57:57 [boundedElastic-98] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 15:57:57 [boundedElastic-113] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 15:57:57 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:57:57 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[81]毫秒
2025-07-30 15:57:57 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:57:57 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[30]毫秒
2025-07-30 15:57:57 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[347]毫秒
2025-07-30 15:57:58 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[521]毫秒
2025-07-30 15:57:58 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:57:58 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[31]毫秒
2025-07-30 15:58:08 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:58:08 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-30 15:58:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:58:27 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-30 15:58:29 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/workorderDetail/exportTemplate],无参数
2025-07-30 15:58:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/workorderDetail/exportTemplate],耗时:[208]毫秒
2025-07-30 15:58:37 [boundedElastic-113] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/workorderDetail/importTemplate],参数类型[param],参数:[{"workorderId":[""]}]
2025-07-30 15:58:37 [boundedElastic-113] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:58:38 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[69]毫秒
2025-07-30 15:58:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/workorderDetail/importTemplate],耗时:[1272]毫秒
2025-07-30 15:58:39 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:58:39 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[32]毫秒
2025-07-30 15:58:41 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:58:42 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[51]毫秒
2025-07-30 15:58:47 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 15:58:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 15:58:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 15:58:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[123]毫秒
2025-07-30 15:58:47 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[237]毫秒
2025-07-30 15:58:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[379]毫秒
2025-07-30 15:58:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:58:52 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-30 15:59:00 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:59:00 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-30 15:59:01 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:59:01 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-30 15:59:07 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 15:59:07 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-30 15:59:10 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"请尽快发放","createTime":"2025-07-30 15:58:08","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"200.00","grantType":"","grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":"","grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","verifySt]
2025-07-30 15:59:22 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[12038]毫秒
2025-07-30 16:00:36 [boundedElastic-122] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"请尽快发放","createTime":"2025-07-30 15:58:08","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"200.00","grantType":"","grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":"","grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","verifySt]
2025-07-30 16:00:38 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[2309]毫秒
2025-07-30 16:02:54 [boundedElastic-116] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdWorkorder/submit],参数类型[json],参数:[{"companyId":"1950013234926407682","companyName":"航运经销商公司","createrName":"frd","remark":"请尽快发放","createTime":"2025-07-30 15:58:08","details":[{"id":null,"workoderId":null,"grantobjectId":null,"grantobjectName":"张三","certType":"1","certNo":"372328198207080031","job":"大副","relatedAcountid":null,"accountNo":"6217856100112496135","accountPinyin":"ZHANG SAN","accountName":"张三","bankName":"中国银行","depositBranch":"中国银行南通秀山支行","grantAmount":"200.00","grantType":"","grantFee":null,"detailCommission":null,"adjustAmount":null,"feePayertype":null,"errorCorrection":null,"swiftCode":"BKCHCNBJ940","ship":"","grantStatus":"2","exceptionType":null,"exceptionReason":null,"fileId":null,"fileUrl":"","verifySt]
2025-07-30 16:02:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdWorkorder/submit],耗时:[5158]毫秒
2025-07-30 16:02:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 16:02:59 [boundedElastic-116] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:02:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[29]毫秒
2025-07-30 16:03:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[723]毫秒
2025-07-30 16:03:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:03:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-30 16:04:01 [boundedElastic-128] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:04:01 [boundedElastic-127] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:04:01 [boundedElastic-120] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 16:04:01 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[73]毫秒
2025-07-30 16:04:02 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[362]毫秒
2025-07-30 16:04:02 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[581]毫秒
2025-07-30 16:04:02 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:04:02 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[51]毫秒
2025-07-30 16:04:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:04:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[82]毫秒
2025-07-30 16:04:34 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-30 16:04:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"params[timeRange]":[""]}]
2025-07-30 16:04:34 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-30 16:04:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[497]毫秒
2025-07-30 16:05:44 [boundedElastic-128] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-30 16:05:44 [boundedElastic-122] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"],"params[timeRange]":[""]}]
2025-07-30 16:05:44 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-30 16:05:44 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[383]毫秒
2025-07-30 16:07:07 [boundedElastic-110] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:07:07 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-30 16:07:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-30 16:07:11 [boundedElastic-100] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:07:11 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[59]毫秒
2025-07-30 16:07:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/warehouse/list],耗时:[2305]毫秒
2025-07-30 16:07:14 [boundedElastic-100] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderConfig/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:07:14 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/business_type],无参数
2025-07-30 16:07:14 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],无参数
2025-07-30 16:07:14 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/business_type],耗时:[26]毫秒
2025-07-30 16:07:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderConfig/list],耗时:[285]毫秒
2025-07-30 16:07:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderConfig/0],无参数
2025-07-30 16:07:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderConfig/0],耗时:[200]毫秒
2025-07-30 16:07:17 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[3025]毫秒
2025-07-30 16:07:52 [boundedElastic-115] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1],无参数
2025-07-30 16:07:52 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1],耗时:[694]毫秒
2025-07-30 16:08:22 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1],无参数
2025-07-30 16:08:23 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1],耗时:[629]毫秒
2025-07-30 16:08:55 [boundedElastic-100] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1],无参数
2025-07-30 16:08:55 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1],耗时:[630]毫秒
2025-07-30 16:09:02 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/code/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:09:03 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/code/list],耗时:[455]毫秒
2025-07-30 16:09:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/distributors/getPropertyList],无参数
2025-07-30 16:09:03 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:09:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/distributors/getPropertyList],耗时:[147]毫秒
2025-07-30 16:09:04 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[285]毫秒
2025-07-30 16:09:04 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"shippingId":["1934261052682113025"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:09:04 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[503]毫秒
2025-07-30 16:09:58 [boundedElastic-82] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:09:58 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:09:58 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[289]毫秒
2025-07-30 16:09:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[300]毫秒
2025-07-30 16:10:11 [boundedElastic-127] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:10:11 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[78]毫秒
2025-07-30 16:10:15 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 16:10:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[227]毫秒
2025-07-30 16:10:15 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1950013234926407682"]}]
2025-07-30 16:10:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[354]毫秒
2025-07-30 16:10:16 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["0"]}]
2025-07-30 16:10:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[68]毫秒
2025-07-30 16:10:41 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:10:41 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[74]毫秒
2025-07-30 16:10:50 [boundedElastic-113] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 16:10:50 [boundedElastic-128] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:10:50 [boundedElastic-128] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 16:10:50 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-30 16:10:51 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[391]毫秒
2025-07-30 16:10:51 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[450]毫秒
2025-07-30 16:10:51 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 16:10:51 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[316]毫秒
2025-07-30 16:11:03 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:11:03 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[107]毫秒
2025-07-30 16:11:05 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-30 16:11:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-30 16:11:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:11:05 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-30 16:11:05 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[35]毫秒
2025-07-30 16:11:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[83]毫秒
2025-07-30 16:11:05 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[412]毫秒
2025-07-30 16:11:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[913]毫秒
2025-07-30 16:11:13 [boundedElastic-125] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 16:11:13 [boundedElastic-124] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 16:11:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[47]毫秒
2025-07-30 16:11:14 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[539]毫秒
2025-07-30 16:11:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 16:11:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 16:11:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-30 16:11:15 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[111]毫秒
2025-07-30 16:11:20 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-30 16:11:20 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[824]毫秒
2025-07-30 16:11:21 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 16:11:21 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[639]毫秒
2025-07-30 16:11:22 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 16:11:22 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[71]毫秒
2025-07-30 16:11:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 16:11:22 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-30 16:11:22 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 16:11:22 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-30 16:11:25 [boundedElastic-124] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:11:25 [boundedElastic-125] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:11:25 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[64]毫秒
2025-07-30 16:11:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 16:11:26 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:11:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[25]毫秒
2025-07-30 16:11:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[401]毫秒
2025-07-30 16:11:26 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[525]毫秒
2025-07-30 16:11:26 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:11:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[46]毫秒
2025-07-30 16:11:31 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_todo_status],无参数
2025-07-30 16:11:31 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:11:31 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_status],无参数
2025-07-30 16:11:31 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_status],耗时:[49]毫秒
2025-07-30 16:11:31 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_todo_status],耗时:[60]毫秒
2025-07-30 16:11:31 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/list],耗时:[299]毫秒
2025-07-30 16:11:34 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/1950467131853787138],无参数
2025-07-30 16:11:34 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/1950467131853787138],耗时:[190]毫秒
2025-07-30 16:11:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 16:11:35 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[266]毫秒
2025-07-30 16:11:35 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 16:11:35 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[307]毫秒
2025-07-30 16:11:35 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 16:11:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[235]毫秒
2025-07-30 16:11:41 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/pass],参数类型[param],参数:[{"businessId":["1950467129584668673"],"opinion":["可以的"],"businessType":["0"]}]
2025-07-30 16:11:52 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/pass],耗时:[10228]毫秒
2025-07-30 16:13:00 [boundedElastic-130] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/pass],参数类型[param],参数:[{"businessId":["1950467129584668673"],"opinion":["可以的"],"businessType":["0"]}]
2025-07-30 16:13:41 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/pass],耗时:[41553]毫秒
2025-07-30 16:15:01 [boundedElastic-122] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/pass],参数类型[param],参数:[{"businessId":["1950467129584668673"],"opinion":["可以的"],"businessType":["0"]}]
2025-07-30 16:15:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/pass],耗时:[10466]毫秒
2025-07-30 16:15:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:15:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/list],耗时:[210]毫秒
2025-07-30 16:15:16 [boundedElastic-122] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:15:16 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[64]毫秒
2025-07-30 16:15:19 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 16:15:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:15:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:15:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-30 16:15:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[256]毫秒
2025-07-30 16:15:20 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[595]毫秒
2025-07-30 16:15:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:15:20 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-30 16:15:24 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:15:24 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-30 16:15:25 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:15:25 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 16:15:25 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:15:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:15:25 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-30 16:15:26 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[594]毫秒
2025-07-30 16:15:26 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[559]毫秒
2025-07-30 16:15:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[602]毫秒
2025-07-30 16:15:26 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:15:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-30 16:18:45 [boundedElastic-128] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 16:18:45 [boundedElastic-122] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:18:45 [boundedElastic-124] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:18:45 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:18:45 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-30 16:18:45 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[574]毫秒
2025-07-30 16:18:45 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[638]毫秒
2025-07-30 16:18:45 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[651]毫秒
2025-07-30 16:18:45 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:18:45 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[35]毫秒
2025-07-30 16:18:52 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 16:18:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[593]毫秒
2025-07-30 16:18:53 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 16:18:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[73]毫秒
2025-07-30 16:18:53 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 16:18:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-30 16:18:53 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:18:53 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:18:53 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 16:18:53 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:18:53 [boundedElastic-124] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 16:18:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[29]毫秒
2025-07-30 16:18:53 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[31]毫秒
2025-07-30 16:18:54 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:18:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-30 16:18:54 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[541]毫秒
2025-07-30 16:18:54 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[616]毫秒
2025-07-30 16:18:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[633]毫秒
2025-07-30 16:18:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:18:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[42]毫秒
2025-07-30 16:26:19 [boundedElastic-132] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:19 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[66]毫秒
2025-07-30 16:26:19 [boundedElastic-132] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 16:26:19 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:19 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:26:19 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[29]毫秒
2025-07-30 16:26:19 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:19 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-30 16:26:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:26:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:26:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:19 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 16:26:19 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[25]毫秒
2025-07-30 16:26:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:20 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[385]毫秒
2025-07-30 16:26:20 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[55]毫秒
2025-07-30 16:26:20 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[744]毫秒
2025-07-30 16:26:20 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[555]毫秒
2025-07-30 16:26:20 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:20 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[24]毫秒
2025-07-30 16:26:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:20 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[620]毫秒
2025-07-30 16:26:20 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-30 16:26:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[652]毫秒
2025-07-30 16:26:20 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:20 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-30 16:26:24 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:24 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:26:24 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 16:26:24 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-30 16:26:24 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[332]毫秒
2025-07-30 16:26:24 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 16:26:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[714]毫秒
2025-07-30 16:26:25 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[400]毫秒
2025-07-30 16:26:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 16:26:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[321]毫秒
2025-07-30 16:26:48 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:48 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-30 16:26:54 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:26:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:54 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-30 16:26:54 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[663]毫秒
2025-07-30 16:26:55 [boundedElastic-138] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:26:55 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[42]毫秒
2025-07-30 16:27:11 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 16:27:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[704]毫秒
2025-07-30 16:27:12 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 16:27:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[67]毫秒
2025-07-30 16:27:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 16:27:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[40]毫秒
2025-07-30 16:27:12 [boundedElastic-141] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 16:27:12 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[46]毫秒
2025-07-30 16:27:12 [boundedElastic-128] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:27:12 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:27:12 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 16:27:12 [boundedElastic-141] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:27:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[31]毫秒
2025-07-30 16:27:13 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:27:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-30 16:27:13 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[593]毫秒
2025-07-30 16:27:13 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[663]毫秒
2025-07-30 16:27:13 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[732]毫秒
2025-07-30 16:27:13 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:27:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[43]毫秒
2025-07-30 16:31:52 [boundedElastic-152] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 16:31:52 [boundedElastic-98] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:31:52 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:31:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-30 16:31:53 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[424]毫秒
2025-07-30 16:31:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 16:31:53 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[737]毫秒
2025-07-30 16:31:53 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[289]毫秒
2025-07-30 16:31:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 16:31:54 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[277]毫秒
2025-07-30 16:31:56 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:31:56 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[50]毫秒
2025-07-30 16:32:10 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:32:10 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-30 16:32:28 [boundedElastic-154] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:32:28 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 16:32:28 [boundedElastic-154] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:32:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[55]毫秒
2025-07-30 16:32:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[367]毫秒
2025-07-30 16:32:28 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[704]毫秒
2025-07-30 16:32:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 16:32:29 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[309]毫秒
2025-07-30 16:32:29 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 16:32:30 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[304]毫秒
2025-07-30 16:32:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:32:34 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[67]毫秒
2025-07-30 16:34:18 [boundedElastic-154] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 16:34:18 [boundedElastic-156] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:34:18 [boundedElastic-139] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:34:18 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[112]毫秒
2025-07-30 16:34:18 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[505]毫秒
2025-07-30 16:34:18 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 16:34:18 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[765]毫秒
2025-07-30 16:34:19 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[317]毫秒
2025-07-30 16:34:19 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 16:34:19 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[302]毫秒
2025-07-30 16:36:30 [boundedElastic-155] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:36:30 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-30 16:37:53 [boundedElastic-156] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:37:53 [boundedElastic-147] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 16:37:53 [boundedElastic-153] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:37:53 [boundedElastic-159] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:37:53 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:37:53 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 16:37:53 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:37:53 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[77]毫秒
2025-07-30 16:37:53 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[60]毫秒
2025-07-30 16:37:53 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:37:53 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[28]毫秒
2025-07-30 16:37:53 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[312]毫秒
2025-07-30 16:37:54 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:37:54 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[38]毫秒
2025-07-30 16:37:54 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[579]毫秒
2025-07-30 16:37:54 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[626]毫秒
2025-07-30 16:37:54 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[688]毫秒
2025-07-30 16:37:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:37:54 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[43]毫秒
2025-07-30 16:37:54 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[734]毫秒
2025-07-30 16:37:54 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:37:54 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[30]毫秒
2025-07-30 16:38:03 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:03 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:03 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[84]毫秒
2025-07-30 16:38:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[54]毫秒
2025-07-30 16:38:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:09 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[99]毫秒
2025-07-30 16:38:09 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[86]毫秒
2025-07-30 16:38:18 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:18 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[94]毫秒
2025-07-30 16:38:18 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:18 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[30]毫秒
2025-07-30 16:38:29 [boundedElastic-139] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:29 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:29 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[79]毫秒
2025-07-30 16:38:29 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-30 16:38:34 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:38:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-30 16:38:34 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[54]毫秒
2025-07-30 16:38:45 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 16:38:45 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 16:38:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[976]毫秒
2025-07-30 16:38:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 16:38:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[136]毫秒
2025-07-30 16:38:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 16:38:46 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[694]毫秒
2025-07-30 16:38:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[41]毫秒
2025-07-30 16:38:46 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 16:38:46 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[83]毫秒
2025-07-30 16:38:46 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 16:38:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-30 16:38:46 [boundedElastic-159] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 16:38:47 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[125]毫秒
2025-07-30 16:38:47 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 16:38:47 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-30 16:40:08 [boundedElastic-146] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 16:40:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[664]毫秒
2025-07-30 16:40:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 16:40:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[82]毫秒
2025-07-30 16:40:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 16:40:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-30 16:40:12 [boundedElastic-146] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:40:12 [boundedElastic-147] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 16:40:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:40:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[58]毫秒
2025-07-30 16:40:12 [boundedElastic-146] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 16:40:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[45]毫秒
2025-07-30 16:40:12 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:40:13 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:40:13 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[64]毫秒
2025-07-30 16:40:13 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[959]毫秒
2025-07-30 16:40:13 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[985]毫秒
2025-07-30 16:40:13 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[815]毫秒
2025-07-30 16:40:14 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:40:14 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[57]毫秒
2025-07-30 16:41:10 [boundedElastic-147] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-30 16:41:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[768]毫秒
2025-07-30 16:41:11 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-30 16:41:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[102]毫秒
2025-07-30 16:41:11 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-30 16:41:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-30 16:41:11 [boundedElastic-147] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 16:41:12 [boundedElastic-147] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:41:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:41:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 16:41:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[76]毫秒
2025-07-30 16:41:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[28]毫秒
2025-07-30 16:41:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:41:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[29]毫秒
2025-07-30 16:41:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[339]毫秒
2025-07-30 16:41:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[555]毫秒
2025-07-30 16:41:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:41:13 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-30 16:41:18 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:41:18 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:41:18 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 16:41:18 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[51]毫秒
2025-07-30 16:41:18 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[312]毫秒
2025-07-30 16:41:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[678]毫秒
2025-07-30 16:41:19 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 16:41:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[309]毫秒
2025-07-30 16:41:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 16:41:20 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[288]毫秒
2025-07-30 16:42:21 [boundedElastic-159] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:42:21 [boundedElastic-162] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 16:42:21 [boundedElastic-163] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 16:42:21 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[123]毫秒
2025-07-30 16:42:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[411]毫秒
2025-07-30 16:42:21 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 16:42:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[544]毫秒
2025-07-30 16:42:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[279]毫秒
2025-07-30 16:43:52 [boundedElastic-98] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:43:52 [boundedElastic-159] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:52 [boundedElastic-154] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 16:43:52 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:43:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 16:43:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:52 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[77]毫秒
2025-07-30 16:43:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[66]毫秒
2025-07-30 16:43:52 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:52 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-30 16:43:52 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:52 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:43:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[35]毫秒
2025-07-30 16:43:52 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[354]毫秒
2025-07-30 16:43:52 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:52 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[35]毫秒
2025-07-30 16:43:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-30 16:43:52 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[611]毫秒
2025-07-30 16:43:52 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[652]毫秒
2025-07-30 16:43:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[712]毫秒
2025-07-30 16:43:52 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 16:43:52 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[541]毫秒
2025-07-30 16:43:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:53 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[24]毫秒
2025-07-30 16:43:53 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[253]毫秒
2025-07-30 16:43:53 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-30 16:43:53 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 16:43:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],无参数
2025-07-30 16:43:53 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:43:53 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:53 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-30 16:43:53 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[499]毫秒
2025-07-30 16:43:53 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[510]毫秒
2025-07-30 16:43:53 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[602]毫秒
2025-07-30 16:43:53 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[595]毫秒
2025-07-30 16:43:54 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:54 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:43:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-30 16:43:54 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[94]毫秒
2025-07-30 16:59:42 [boundedElastic-174] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 16:59:42 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 16:59:42 [boundedElastic-147] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 16:59:42 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[218]毫秒
2025-07-30 16:59:42 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[500]毫秒
2025-07-30 16:59:43 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 16:59:43 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[688]毫秒
2025-07-30 16:59:43 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[295]毫秒
2025-07-30 16:59:43 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 16:59:44 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[268]毫秒
2025-07-30 17:00:06 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:00:06 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[62]毫秒
2025-07-30 17:05:23 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 17:05:23 [boundedElastic-170] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 17:05:23 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:05:23 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[66]毫秒
2025-07-30 17:05:23 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[380]毫秒
2025-07-30 17:05:23 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 17:05:23 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[576]毫秒
2025-07-30 17:05:23 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[272]毫秒
2025-07-30 17:05:24 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 17:05:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[271]毫秒
2025-07-30 17:05:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:05:25 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[42]毫秒
2025-07-30 17:05:31 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:05:31 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"distributorsId":["1950013234926407682"]}]
2025-07-30 17:05:31 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1950467129584668673],无参数
2025-07-30 17:05:31 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-30 17:05:32 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1950467129584668673],耗时:[349]毫秒
2025-07-30 17:05:32 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],无参数
2025-07-30 17:05:32 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[595]毫秒
2025-07-30 17:05:32 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1950467129584668673],耗时:[277]毫秒
2025-07-30 17:05:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-30 17:05:33 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1950467129584668673],耗时:[270]毫秒
2025-07-30 17:05:36 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:05:36 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-30 17:07:00 [boundedElastic-173] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 17:07:00 [boundedElastic-147] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 17:07:00 [boundedElastic-173] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:07:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-30 17:07:00 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[319]毫秒
2025-07-30 17:07:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:07:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[30]毫秒
2025-07-30 17:07:00 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[584]毫秒
2025-07-30 17:07:01 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:07:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-30 17:09:49 [boundedElastic-182] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:09:49 [boundedElastic-183] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 17:09:49 [boundedElastic-181] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 17:09:50 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[112]毫秒
2025-07-30 17:09:50 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:09:50 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[26]毫秒
2025-07-30 17:09:50 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[374]毫秒
2025-07-30 17:09:50 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[638]毫秒
2025-07-30 17:09:50 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:09:50 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[23]毫秒
2025-07-30 17:14:38 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 17:14:38 [boundedElastic-173] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 17:14:38 [boundedElastic-178] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:14:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-30 17:14:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:14:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[29]毫秒
2025-07-30 17:14:38 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[648]毫秒
2025-07-30 17:14:38 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[874]毫秒
2025-07-30 17:14:39 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:14:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[41]毫秒
2025-07-30 17:20:39 [boundedElastic-191] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-30 17:20:39 [boundedElastic-188] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-30 17:20:39 [boundedElastic-192] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:20:40 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[124]毫秒
2025-07-30 17:20:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:20:40 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[24]毫秒
2025-07-30 17:20:40 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[535]毫秒
2025-07-30 17:20:40 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[966]毫秒
2025-07-30 17:20:41 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:20:41 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[42]毫秒
2025-07-30 17:37:17 [boundedElastic-205] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-30 17:37:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[134]毫秒
2025-07-30 18:12:09 [boundedElastic-200] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-30 18:12:09 [boundedElastic-212] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-30 18:12:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[145]毫秒
2025-07-30 18:12:09 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[331]毫秒
2025-07-30 18:12:09 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-30 18:12:09 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[137]毫秒
2025-07-30 18:12:10 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-30 18:12:10 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-30 19:16:36 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-30 19:16:36 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-30 19:16:36 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
