2025-07-26 08:50:11 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 08:50:12 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 11252 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 08:50:12 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-26 08:50:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-26 08:50:12 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 08:50:15 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 08:50:16 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 08:50:16 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 08:50:16 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 08:50:16 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 08:50:20 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway 192.168.124.4:8080 register finished
2025-07-26 08:50:21 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-26 08:50:21 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 11.672 seconds (process running for 12.512)
2025-07-26 08:50:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 08:50:21 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-26 08:58:45 [boundedElastic-19] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-26 08:58:45 [boundedElastic-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-26 08:58:46 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1080]毫秒
2025-07-26 08:58:46 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1277]毫秒
2025-07-26 08:58:47 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-26 08:58:47 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 08:58:47 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[16]毫秒
2025-07-26 08:58:47 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[335]毫秒
2025-07-26 08:59:07 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-26 08:59:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1482]毫秒
2025-07-26 08:59:10 [boundedElastic-18] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 08:59:11 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1480]毫秒
2025-07-26 08:59:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 08:59:12 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[93]毫秒
2025-07-26 08:59:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 08:59:12 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[72]毫秒
2025-07-26 08:59:12 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 08:59:12 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-26 08:59:39 [boundedElastic-18] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 08:59:40 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[722]毫秒
2025-07-26 08:59:44 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 08:59:44 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[177]毫秒
2025-07-26 08:59:54 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"移动","parentId":"1948273665943646210","isParent":0,"sort":"1","isEnable":1}]
2025-07-26 08:59:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[1173]毫秒
2025-07-26 08:59:55 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 08:59:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[186]毫秒
2025-07-26 09:02:00 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 09:02:01 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[834]毫秒
2025-07-26 09:02:02 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 09:02:03 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[187]毫秒
2025-07-26 09:02:14 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"移动","parentId":"1948273665943646210","isParent":0,"sort":"1","isEnable":1}]
2025-07-26 09:02:15 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[1126]毫秒
2025-07-26 09:02:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 09:02:15 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[179]毫秒
2025-07-26 09:39:12 [boundedElastic-37] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-26 09:39:12 [boundedElastic-37] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-26 09:39:12 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[331]毫秒
2025-07-26 09:39:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[171]毫秒
2025-07-26 09:39:13 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-26 09:39:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 09:39:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[9]毫秒
2025-07-26 09:39:13 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[142]毫秒
2025-07-26 09:39:17 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-26 09:39:18 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1039]毫秒
2025-07-26 09:39:18 [boundedElastic-37] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 09:39:19 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[644]毫秒
2025-07-26 09:39:19 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 09:39:19 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[83]毫秒
2025-07-26 09:39:19 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 09:39:19 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[66]毫秒
2025-07-26 09:39:20 [boundedElastic-37] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 09:39:20 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 09:39:20 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-26 09:39:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[1177]毫秒
2025-07-26 09:39:25 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 09:39:26 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[227]毫秒
2025-07-26 09:39:35 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"彩电","parentId":"1948620221544210434","isParent":0,"sort":"1","isEnable":1}]
2025-07-26 09:39:36 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[1064]毫秒
2025-07-26 09:39:49 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 09:39:49 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[227]毫秒
2025-07-26 09:39:49 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/1948268858713419778],无参数
2025-07-26 09:39:49 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/1948268858713419778],耗时:[150]毫秒
2025-07-26 09:39:56 [boundedElastic-57] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948268858713419778","name":"家用电器","parentId":"1948269616661901314","isParent":"1","sort":1,"isEnable":"1","ancestors":"0"}]
2025-07-26 09:39:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[344]毫秒
2025-07-26 09:40:08 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948268858713419778","name":"家用电器","parentId":"1948269616661901314","isParent":"1","sort":1,"isEnable":"1","ancestors":"0"}]
2025-07-26 09:40:08 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[344]毫秒
2025-07-26 09:40:47 [boundedElastic-56] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948268858713419778","name":"家用电器","parentId":"1948269616661901314","isParent":"1","sort":1,"isEnable":"1","ancestors":"0"}]
2025-07-26 09:40:48 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[350]毫秒
2025-07-26 09:48:28 [boundedElastic-67] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 09:48:28 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 09:48:28 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[234]毫秒
2025-07-26 09:48:29 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[268]毫秒
2025-07-26 10:01:54 [boundedElastic-32] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948269541239926785"]}]
2025-07-26 10:01:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948620221544210434"]}]
2025-07-26 10:01:55 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[929]毫秒
2025-07-26 10:01:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[515]毫秒
2025-07-26 10:02:03 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/specGroup],参数类型[json],参数:[{"categoryId":"1948620221544210434","name":"尺寸"}]
2025-07-26 10:02:04 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/specGroup],耗时:[1158]毫秒
2025-07-26 10:02:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948620221544210434"]}]
2025-07-26 10:02:05 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[233]毫秒
2025-07-26 10:04:07 [boundedElastic-78] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["0"]}]
2025-07-26 10:04:07 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[322]毫秒
2025-07-26 10:04:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["0"]}]
2025-07-26 10:04:08 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[223]毫秒
2025-07-26 10:04:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948268858713419778"]}]
2025-07-26 10:04:09 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[227]毫秒
2025-07-26 10:04:11 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948272566222303234"]}]
2025-07-26 10:04:11 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[267]毫秒
2025-07-26 10:06:53 [boundedElastic-32] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 10:06:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[39]毫秒
2025-07-26 10:09:35 [boundedElastic-80] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/param/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 10:09:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/param/list],耗时:[265]毫秒
2025-07-26 10:09:36 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 10:09:36 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:09:36 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[219]毫秒
2025-07-26 10:09:36 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[220]毫秒
2025-07-26 10:11:27 [boundedElastic-77] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 10:11:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[791]毫秒
2025-07-26 10:11:28 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 10:11:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[94]毫秒
2025-07-26 10:11:28 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 10:11:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[67]毫秒
2025-07-26 10:11:29 [boundedElastic-77] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 10:11:29 [boundedElastic-77] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:11:29 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 10:11:29 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[109]毫秒
2025-07-26 10:11:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[288]毫秒
2025-07-26 10:11:29 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[297]毫秒
2025-07-26 10:11:31 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["0"]}]
2025-07-26 10:11:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[226]毫秒
2025-07-26 10:11:34 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948268858713419778"]}]
2025-07-26 10:11:34 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[228]毫秒
2025-07-26 10:11:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948272566222303234"]}]
2025-07-26 10:11:35 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[286]毫秒
2025-07-26 10:11:36 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948269541239926785"]}]
2025-07-26 10:11:36 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[223]毫秒
2025-07-26 10:11:36 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948273170097221634"]}]
2025-07-26 10:11:37 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[220]毫秒
2025-07-26 10:11:37 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948273665943646210"]}]
2025-07-26 10:11:38 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[209]毫秒
2025-07-26 10:11:40 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948911706242097153"]}]
2025-07-26 10:11:40 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[268]毫秒
2025-07-26 10:11:41 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948666606012461057"]}]
2025-07-26 10:11:41 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[205]毫秒
2025-07-26 10:11:41 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948273170097221634"]}]
2025-07-26 10:11:42 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[225]毫秒
2025-07-26 10:11:44 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948273170097221634"]}]
2025-07-26 10:11:44 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[227]毫秒
2025-07-26 10:11:45 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948666606012461057"]}]
2025-07-26 10:11:45 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[232]毫秒
2025-07-26 10:11:46 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948273665943646210"]}]
2025-07-26 10:11:46 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[267]毫秒
2025-07-26 10:11:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948273665943646210"]}]
2025-07-26 10:11:48 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[204]毫秒
2025-07-26 10:11:49 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948272566222303234"]}]
2025-07-26 10:11:49 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[206]毫秒
2025-07-26 10:11:51 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948272566222303234"]}]
2025-07-26 10:11:51 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[221]毫秒
2025-07-26 10:11:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/specGroup/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"categoryId":["1948273170097221634"]}]
2025-07-26 10:11:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/specGroup/list],耗时:[291]毫秒
2025-07-26 10:13:01 [boundedElastic-81] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/param/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 10:13:01 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/param/list],耗时:[243]毫秒
2025-07-26 10:26:00 [boundedElastic-90] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:26:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[260]毫秒
2025-07-26 10:26:08 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:26:08 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[244]毫秒
2025-07-26 10:26:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"test","parentId":0,"isParent":1,"sort":"1","isEnable":1}]
2025-07-26 10:26:21 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[551]毫秒
2025-07-26 10:26:21 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:26:21 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[167]毫秒
2025-07-26 10:26:24 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:26:24 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[177]毫秒
2025-07-26 10:26:24 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/1948268858713419778],无参数
2025-07-26 10:26:25 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/1948268858713419778],耗时:[207]毫秒
2025-07-26 10:26:31 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948268858713419778","name":"家用电器","parentId":"1948932873900339202","isParent":"1","sort":1,"isEnable":"1","ancestors":"0"}]
2025-07-26 10:26:31 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[496]毫秒
2025-07-26 10:26:31 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:26:32 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[157]毫秒
2025-07-26 10:26:41 [boundedElastic-96] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:26:41 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[239]毫秒
2025-07-26 10:26:41 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/1948268858713419778],无参数
2025-07-26 10:26:41 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/1948268858713419778],耗时:[152]毫秒
2025-07-26 10:26:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948268858713419778","name":"家用电器","parentId":0,"isParent":"1","sort":1,"isEnable":"1","ancestors":"0,1948932873900339202"}]
2025-07-26 10:26:57 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[482]毫秒
2025-07-26 10:26:57 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:26:57 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[169]毫秒
2025-07-26 10:48:26 [boundedElastic-63] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:48:27 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[781]毫秒
2025-07-26 10:48:27 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/1948268858713419778],无参数
2025-07-26 10:48:27 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/1948268858713419778],耗时:[236]毫秒
2025-07-26 10:48:32 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948268858713419778","name":"家用电器","parentId":"1948932873900339202","isParent":"1","sort":1,"isEnable":"1","ancestors":"0"}]
2025-07-26 10:48:34 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[1299]毫秒
2025-07-26 10:49:11 [boundedElastic-97] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:49:11 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[252]毫秒
2025-07-26 10:49:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/1948269541239926785],无参数
2025-07-26 10:49:12 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/1948269541239926785],耗时:[188]毫秒
2025-07-26 10:49:15 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948269541239926785","name":"电视","parentId":"1948932873900339202","isParent":"1","sort":1,"isEnable":"1","ancestors":"0,1948268858713419778"}]
2025-07-26 10:49:16 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[500]毫秒
2025-07-26 10:49:52 [boundedElastic-78] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948269541239926785","name":"电视","parentId":"1948932873900339202","isParent":"1","sort":1,"isEnable":"1","ancestors":"0,1948268858713419778"}]
2025-07-26 10:51:15 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[82937]毫秒
2025-07-26 10:52:15 [boundedElastic-95] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948269541239926785","name":"电视","parentId":"1948932873900339202","isParent":"1","sort":1,"isEnable":"1","ancestors":"0,1948268858713419778"}]
2025-07-26 10:52:17 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[2332]毫秒
2025-07-26 10:52:17 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:52:18 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[163]毫秒
2025-07-26 10:52:50 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:52:50 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[255]毫秒
2025-07-26 10:52:56 [boundedElastic-90] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"242","parentId":"1948273170097221634","isParent":1,"sort":"242","isEnable":1}]
2025-07-26 10:52:56 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[374]毫秒
2025-07-26 10:54:25 [boundedElastic-97] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"242","parentId":"1948273170097221634","isParent":1,"sort":"242","isEnable":1}]
2025-07-26 10:54:26 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[699]毫秒
2025-07-26 10:54:30 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"242","parentId":"1948273170097221634","isParent":1,"sort":"242","isEnable":1}]
2025-07-26 10:54:31 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[552]毫秒
2025-07-26 10:54:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"242","parentId":"1948273170097221634","isParent":1,"sort":"242","isEnable":1}]
2025-07-26 10:54:34 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[447]毫秒
2025-07-26 10:54:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"242","parentId":"1948273170097221634","isParent":1,"sort":"2","isEnable":1}]
2025-07-26 10:54:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[523]毫秒
2025-07-26 10:54:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:54:54 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[188]毫秒
2025-07-26 10:54:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:54:58 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[247]毫秒
2025-07-26 10:54:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/1948940056021008385],无参数
2025-07-26 10:54:59 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/1948940056021008385],耗时:[184]毫秒
2025-07-26 10:55:07 [boundedElastic-109] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948940056021008385","name":"242242","parentId":"1948273170097221634","isParent":1,"sort":2,"isEnable":"1","ancestors":"0,1948272566222303234,1948273170097221634"}]
2025-07-26 10:55:07 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[609]毫秒
2025-07-26 10:55:08 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:55:08 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[175]毫秒
2025-07-26 10:57:13 [boundedElastic-63] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:57:14 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[270]毫秒
2025-07-26 10:57:14 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/1948620221544210434],无参数
2025-07-26 10:57:14 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/1948620221544210434],耗时:[155]毫秒
2025-07-26 10:57:19 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948620221544210434","name":"液晶电视","parentId":"1948269541239926785","isParent":1,"sort":1,"isEnable":"1","ancestors":"0,1948932873900339202,1948269541239926785"}]
2025-07-26 10:58:19 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[59841]毫秒
2025-07-26 10:59:05 [boundedElastic-97] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948620221544210434","name":"液晶电视1","parentId":"1948269541239926785","isParent":1,"sort":1,"isEnable":"1","ancestors":"0,1948932873900339202,1948269541239926785"}]
2025-07-26 10:59:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[410]毫秒
2025-07-26 10:59:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948620221544210434","name":"液晶电视1","parentId":"1948269541239926785","isParent":1,"sort":1,"isEnable":"1","ancestors":"0,1948932873900339202,1948269541239926785"}]
2025-07-26 10:59:31 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[13891]毫秒
2025-07-26 10:59:54 [boundedElastic-90] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 10:59:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[244]毫秒
2025-07-26 10:59:59 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"1","parentId":"1948273170097221634","isParent":1,"sort":"1","isEnable":1}]
2025-07-26 11:00:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[381]毫秒
2025-07-26 11:00:39 [boundedElastic-109] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"1","parentId":"1948273170097221634","isParent":1,"sort":"1","isEnable":1}]
2025-07-26 11:00:39 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[521]毫秒
2025-07-26 11:00:39 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:00:39 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[162]毫秒
2025-07-26 11:00:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:00:49 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[248]毫秒
2025-07-26 11:00:50 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/1948941506470051841],无参数
2025-07-26 11:00:50 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/1948941506470051841],耗时:[164]毫秒
2025-07-26 11:00:54 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948941506470051841","name":"165165","parentId":"1948273170097221634","isParent":"0","sort":1,"isEnable":"1","ancestors":"0,1948272566222303234,1948273170097221634"}]
2025-07-26 11:00:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[548]毫秒
2025-07-26 11:00:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:00:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[166]毫秒
2025-07-26 11:16:09 [boundedElastic-124] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:16:10 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[332]毫秒
2025-07-26 11:17:12 [boundedElastic-109] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:17:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[441]毫秒
2025-07-26 11:17:22 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:17:22 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[257]毫秒
2025-07-26 11:31:01 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:31:01 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[274]毫秒
2025-07-26 11:31:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"5464","parentId":"1948272566222303234","sort":"3","isEnable":1}]
2025-07-26 11:31:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[259]毫秒
2025-07-26 11:34:11 [boundedElastic-132] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /warehouse/category],参数类型[json],参数:[{"name":"5464","parentId":"1948272566222303234","sort":"3","isEnable":1}]
2025-07-26 11:34:13 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /warehouse/category],耗时:[1785]毫秒
2025-07-26 11:34:13 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:34:13 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[182]毫秒
2025-07-26 11:34:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:34:19 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[236]毫秒
2025-07-26 11:34:20 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/1948949949427871746],无参数
2025-07-26 11:34:20 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/1948949949427871746],耗时:[162]毫秒
2025-07-26 11:34:25 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /warehouse/category],参数类型[json],参数:[{"id":"1948949949427871746","name":"54642","parentId":"1948272566222303234","sort":3,"isEnable":"1","isParent":"0","ancestors":"0,1948272566222303234"}]
2025-07-26 11:34:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /warehouse/category],耗时:[522]毫秒
2025-07-26 11:34:25 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/category/list],无参数
2025-07-26 11:34:26 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/category/list],耗时:[152]毫秒
2025-07-26 11:44:04 [boundedElastic-90] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 11:44:04 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[749]毫秒
2025-07-26 11:44:04 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 11:44:05 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[103]毫秒
2025-07-26 11:49:11 [boundedElastic-142] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 11:49:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[626]毫秒
2025-07-26 11:49:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 11:49:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[91]毫秒
2025-07-26 11:49:12 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 11:49:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[61]毫秒
2025-07-26 11:49:13 [boundedElastic-142] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 11:49:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 11:49:13 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[90]毫秒
2025-07-26 11:49:13 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[514]毫秒
2025-07-26 11:49:13 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 11:49:13 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[76]毫秒
2025-07-26 11:49:19 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 11:49:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[575]毫秒
2025-07-26 11:49:20 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 11:49:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[77]毫秒
2025-07-26 11:49:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 11:49:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[54]毫秒
2025-07-26 11:49:22 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 11:49:22 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[523]毫秒
2025-07-26 11:49:22 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 11:49:22 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[77]毫秒
2025-07-26 11:49:22 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 11:49:22 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[66]毫秒
2025-07-26 11:49:23 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 11:49:23 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-07-26 11:50:16 [boundedElastic-152] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 11:50:17 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[571]毫秒
2025-07-26 11:50:17 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 11:50:17 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[66]毫秒
2025-07-26 11:50:17 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 11:50:17 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[49]毫秒
2025-07-26 11:50:17 [boundedElastic-152] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 11:50:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[32]毫秒
2025-07-26 13:14:38 [boundedElastic-193] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-26 13:14:38 [boundedElastic-186] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-26 13:14:38 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[92]毫秒
2025-07-26 13:14:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[247]毫秒
2025-07-26 13:14:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:14:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-26 13:14:39 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-26 13:14:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[114]毫秒
2025-07-26 13:31:27 [boundedElastic-195] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-26 13:31:27 [boundedElastic-189] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:31:27 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-26 13:31:27 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[136]毫秒
2025-07-26 13:32:41 [boundedElastic-196] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-26 13:32:41 [boundedElastic-186] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:32:41 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[30]毫秒
2025-07-26 13:32:41 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[125]毫秒
2025-07-26 13:33:20 [boundedElastic-194] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-26 13:33:20 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[923]毫秒
2025-07-26 13:33:21 [boundedElastic-194] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:33:21 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[620]毫秒
2025-07-26 13:33:22 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:33:22 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[112]毫秒
2025-07-26 13:33:22 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:33:22 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-26 13:33:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:33:22 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-26 13:36:29 [boundedElastic-186] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:36:30 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[684]毫秒
2025-07-26 13:36:30 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:36:30 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[105]毫秒
2025-07-26 13:36:30 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:36:30 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[29]毫秒
2025-07-26 13:36:30 [boundedElastic-186] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:36:30 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-26 13:36:31 [boundedElastic-176] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:36:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[1499]毫秒
2025-07-26 13:38:22 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:38:22 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[635]毫秒
2025-07-26 13:38:22 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:38:22 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[74]毫秒
2025-07-26 13:38:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:38:22 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[44]毫秒
2025-07-26 13:38:23 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:38:23 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:38:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[98]毫秒
2025-07-26 13:38:24 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[740]毫秒
2025-07-26 13:38:49 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:38:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[695]毫秒
2025-07-26 13:38:49 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:38:50 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[104]毫秒
2025-07-26 13:38:50 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:38:50 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-26 13:38:51 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:38:51 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[54]毫秒
2025-07-26 13:38:51 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:38:52 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[560]毫秒
2025-07-26 13:39:06 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-26 13:39:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[5458]毫秒
2025-07-26 13:41:50 [boundedElastic-213] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:41:51 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[944]毫秒
2025-07-26 13:41:51 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:41:51 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[103]毫秒
2025-07-26 13:41:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:41:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-07-26 13:41:53 [boundedElastic-213] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:41:53 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[71]毫秒
2025-07-26 13:41:53 [boundedElastic-213] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:41:53 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[757]毫秒
2025-07-26 13:42:16 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:42:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[623]毫秒
2025-07-26 13:42:17 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:42:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[80]毫秒
2025-07-26 13:42:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:42:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-26 13:42:17 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:42:17 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-26 13:42:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:42:18 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[539]毫秒
2025-07-26 13:42:25 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:42:25 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[768]毫秒
2025-07-26 13:42:26 [boundedElastic-213] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:42:26 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[103]毫秒
2025-07-26 13:42:26 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:42:26 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-07-26 13:42:26 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:42:26 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[38]毫秒
2025-07-26 13:42:45 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:42:46 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[676]毫秒
2025-07-26 13:42:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:42:46 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[102]毫秒
2025-07-26 13:42:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:42:46 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-26 13:42:47 [boundedElastic-213] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:42:47 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-26 13:42:56 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:42:56 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[634]毫秒
2025-07-26 13:42:57 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:42:57 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[76]毫秒
2025-07-26 13:42:57 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:42:57 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[29]毫秒
2025-07-26 13:42:57 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:42:57 [boundedElastic-197] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:42:57 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[79]毫秒
2025-07-26 13:42:58 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[639]毫秒
2025-07-26 13:44:24 [boundedElastic-206] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:44:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[669]毫秒
2025-07-26 13:44:24 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:44:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[81]毫秒
2025-07-26 13:44:24 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:44:25 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[41]毫秒
2025-07-26 13:44:25 [boundedElastic-206] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:44:25 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-07-26 13:44:25 [boundedElastic-206] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:44:26 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[555]毫秒
2025-07-26 13:48:19 [boundedElastic-213] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:48:20 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[760]毫秒
2025-07-26 13:48:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:48:20 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[95]毫秒
2025-07-26 13:48:21 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:48:21 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[35]毫秒
2025-07-26 13:48:21 [boundedElastic-213] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:48:21 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[144]毫秒
2025-07-26 13:48:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:48:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[623]毫秒
2025-07-26 13:48:26 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:48:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[78]毫秒
2025-07-26 13:48:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:48:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-26 13:48:27 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:48:27 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[38]毫秒
2025-07-26 13:48:27 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:48:27 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:48:27 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[619]毫秒
2025-07-26 13:48:27 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[742]毫秒
2025-07-26 13:48:32 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:48:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:48:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[73]毫秒
2025-07-26 13:48:32 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[432]毫秒
2025-07-26 13:48:44 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:48:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:48:44 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-26 13:48:45 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[497]毫秒
2025-07-26 13:48:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:48:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[57]毫秒
2025-07-26 13:51:27 [boundedElastic-221] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:51:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-26 13:51:35 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":true,"visible":true,"sortOrder":1,"width":134},{"columnName":"transportName","columnLabel":"transportName","fixed":false,"visible":true,"sortOrder":2,"width":107},{"columnName":"shippingName","columnLabel":"shippingName","fixed":false,"visible":true,"sortOrder":3,"width":107},{"columnName":"period","columnLabel":"period","fixed":false,"visible":true,"sortOrder":4,"width":107},{"columnName":"openBalance","columnLabel":"openBalance","fixed":false,"visible":true,"sortOrder":5,"width":107},{"columnName":"incomeAmount","columnLabel":"incomeAmount","fixed":false,"visible":true,"sortOrder":6,"width":107},{"columnName":"expenseAmount","columnLabel":"ex]
2025-07-26 13:51:37 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1471]毫秒
2025-07-26 13:51:37 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:51:37 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-26 13:51:40 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948984528012398594","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":true,"visible":true,"sortOrder":1,"width":134},{"columnName":"transportName","columnLabel":"transportName","fixed":false,"visible":true,"sortOrder":2,"width":254.99998474121094},{"columnName":"shippingName","columnLabel":"shippingName","fixed":false,"visible":true,"sortOrder":3,"width":107},{"columnName":"period","columnLabel":"period","fixed":false,"visible":true,"sortOrder":4,"width":107},{"columnName":"openBalance","columnLabel":"openBalance","fixed":false,"visible":true,"sortOrder":5,"width":107},{"columnName":"incomeAmount","columnLabel":"incomeAmount","fixed":false,"visible":true,"sortOrder":6,"width":107},{"col]
2025-07-26 13:51:41 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1011]毫秒
2025-07-26 13:51:41 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:51:41 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[78]毫秒
2025-07-26 13:51:43 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948984544533762050","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":true,"visible":true,"sortOrder":1,"width":134},{"columnName":"transportName","columnLabel":"transportName","fixed":false,"visible":true,"sortOrder":2,"width":254},{"columnName":"shippingName","columnLabel":"shippingName","fixed":false,"visible":true,"sortOrder":3,"width":181.00001525878906},{"columnName":"period","columnLabel":"period","fixed":false,"visible":true,"sortOrder":4,"width":107},{"columnName":"openBalance","columnLabel":"openBalance","fixed":false,"visible":true,"sortOrder":5,"width":107},{"columnName":"incomeAmount","columnLabel":"incomeAmount","fixed":false,"visible":true,"sortOrder":6,"width":107},{"col]
2025-07-26 13:51:44 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1025]毫秒
2025-07-26 13:51:45 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:51:45 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[67]毫秒
2025-07-26 13:51:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948984559163494401","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":true,"visible":true,"sortOrder":1,"width":134},{"columnName":"transportName","columnLabel":"transportName","fixed":false,"visible":true,"sortOrder":2,"width":254},{"columnName":"shippingName","columnLabel":"shippingName","fixed":false,"visible":true,"sortOrder":3,"width":206.00001525878906},{"columnName":"period","columnLabel":"period","fixed":false,"visible":true,"sortOrder":4,"width":107},{"columnName":"openBalance","columnLabel":"openBalance","fixed":false,"visible":true,"sortOrder":5,"width":107},{"columnName":"incomeAmount","columnLabel":"incomeAmount","fixed":false,"visible":true,"sortOrder":6,"width":107},{"col]
2025-07-26 13:51:48 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[963]毫秒
2025-07-26 13:51:48 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:51:48 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-26 13:51:49 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948984573096972289","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":true,"visible":true,"sortOrder":1,"width":134},{"columnName":"transportName","columnLabel":"transportName","fixed":false,"visible":true,"sortOrder":2,"width":254},{"columnName":"shippingName","columnLabel":"shippingName","fixed":false,"visible":true,"sortOrder":3,"width":235.00001525878906},{"columnName":"period","columnLabel":"period","fixed":false,"visible":true,"sortOrder":4,"width":107},{"columnName":"openBalance","columnLabel":"openBalance","fixed":false,"visible":true,"sortOrder":5,"width":107},{"columnName":"incomeAmount","columnLabel":"incomeAmount","fixed":false,"visible":true,"sortOrder":6,"width":107},{"col]
2025-07-26 13:51:50 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[984]毫秒
2025-07-26 13:51:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:51:51 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-26 13:51:53 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:51:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[587]毫秒
2025-07-26 13:51:54 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:51:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[93]毫秒
2025-07-26 13:51:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:51:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[184]毫秒
2025-07-26 13:51:55 [boundedElastic-221] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:51:55 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-26 13:51:55 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:51:55 [boundedElastic-221] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:51:55 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[57]毫秒
2025-07-26 13:51:55 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[493]毫秒
2025-07-26 13:51:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948984584459341826","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":true,"visible":true,"sortOrder":1,"width":134},{"columnName":"transportName","columnLabel":"transportName","fixed":false,"visible":true,"sortOrder":2,"width":254},{"columnName":"shippingName","columnLabel":"shippingName","fixed":false,"visible":true,"sortOrder":3,"width":235},{"columnName":"period","columnLabel":"period","fixed":false,"visible":true,"sortOrder":4,"width":270.00001525878906},{"columnName":"openBalance","columnLabel":"openBalance","fixed":false,"visible":true,"sortOrder":5,"width":107},{"columnName":"incomeAmount","columnLabel":"incomeAmount","fixed":false,"visible":true,"sortOrder":6,"width":107},{"col]
2025-07-26 13:52:00 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1044]毫秒
2025-07-26 13:52:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:52:00 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-26 13:52:03 [boundedElastic-222] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948984623227293698","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":true,"visible":true,"sortOrder":1,"width":134},{"columnName":"transportName","columnLabel":"transportName","fixed":false,"visible":true,"sortOrder":2,"width":254},{"columnName":"shippingName","columnLabel":"shippingName","fixed":false,"visible":true,"sortOrder":3,"width":235},{"columnName":"period","columnLabel":"period","fixed":false,"visible":true,"sortOrder":4,"width":270},{"columnName":"openBalance","columnLabel":"openBalance","fixed":false,"visible":true,"sortOrder":5,"width":225.9999542236328},{"columnName":"incomeAmount","columnLabel":"incomeAmount","fixed":false,"visible":true,"sortOrder":6,"width":107},{"colu]
2025-07-26 13:52:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[941]毫秒
2025-07-26 13:52:04 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:52:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[73]毫秒
2025-07-26 13:52:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:52:15 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:52:15 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[119]毫秒
2025-07-26 13:52:15 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[772]毫秒
2025-07-26 13:52:18 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 13:52:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[668]毫秒
2025-07-26 13:52:19 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 13:52:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[84]毫秒
2025-07-26 13:52:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 13:52:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-26 13:52:22 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 13:52:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-26 13:52:22 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:52:22 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[536]毫秒
2025-07-26 13:56:39 [boundedElastic-219] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:56:40 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[642]毫秒
2025-07-26 13:56:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:56:52 [boundedElastic-219] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:56:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[96]毫秒
2025-07-26 13:56:52 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[560]毫秒
2025-07-26 13:57:04 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:57:04 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:57:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-26 13:57:05 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[531]毫秒
2025-07-26 13:57:21 [boundedElastic-221] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:57:21 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[102]毫秒
2025-07-26 13:58:23 [boundedElastic-206] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:58:23 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[113]毫秒
2025-07-26 13:58:23 [boundedElastic-206] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:58:24 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[679]毫秒
2025-07-26 13:59:36 [boundedElastic-228] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:59:36 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[114]毫秒
2025-07-26 13:59:44 [boundedElastic-228] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 13:59:44 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 13:59:44 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[133]毫秒
2025-07-26 13:59:45 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[516]毫秒
2025-07-26 14:00:03 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:00:03 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[99]毫秒
2025-07-26 14:00:14 [boundedElastic-211] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:00:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[916]毫秒
2025-07-26 14:00:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:00:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[142]毫秒
2025-07-26 14:00:15 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:00:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[61]毫秒
2025-07-26 14:00:16 [boundedElastic-211] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:00:16 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:00:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:00:16 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[80]毫秒
2025-07-26 14:00:16 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[276]毫秒
2025-07-26 14:00:16 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[509]毫秒
2025-07-26 14:00:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:00:21 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[94]毫秒
2025-07-26 14:00:31 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:00:31 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[96]毫秒
2025-07-26 14:00:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948984640679792642","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"transportName","fixed":true,"visible":true,"sortOrder":2,"width":134},{"columnName":"shippingName","columnLabel":"shippingName","fixed":true,"visible":true,"sortOrder":3,"width":254},{"columnName":"period","columnLabel":"period","fixed":false,"visible":false,"sortOrder":0},{"columnName":"openBalance","columnLabel":"openBalance","fixed":false,"visible":false,"sortOrder":0},{"columnName":"incomeAmount","columnLabel":"incomeAmount","fixed":false,"visible":false,"sortOrder":0},{"columnName":"expenseAmount","columnLabel":"expenseAmo]
2025-07-26 14:00:40 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1361]毫秒
2025-07-26 14:00:40 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:00:40 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-26 14:00:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948986802793172993","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"transportName","fixed":true,"visible":true,"sortOrder":2,"width":226.99998474121094},{"columnName":"shippingName","columnLabel":"shippingName","fixed":true,"visible":true,"sortOrder":3,"width":134},{"columnName":"expenseAmount","columnLabel":"expenseAmount","fixed":false,"visible":true,"sortOrder":4,"width":254},{"columnName":"closeBalance","columnLabel":"closeBalance","fixed":false,"visible":true,"sortOrder":5,"width":235},{"columnName":"status","columnLabel":"status","fixed":false,"visible":true,"sortOrder":6,"width":107},{"c]
2025-07-26 14:00:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1058]毫秒
2025-07-26 14:00:46 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:00:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-26 14:00:50 [boundedElastic-210] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948986831389937665","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"transportName","fixed":true,"visible":true,"sortOrder":2,"width":226.99998474121094},{"columnName":"shippingName","columnLabel":"shippingName","fixed":true,"visible":true,"sortOrder":3,"width":226},{"columnName":"expenseAmount","columnLabel":"expenseAmount","fixed":false,"visible":true,"sortOrder":4,"width":261.01246643066406},{"columnName":"closeBalance","columnLabel":"closeBalance","fixed":false,"visible":true,"sortOrder":5,"width":254},{"columnName":"status","columnLabel":"status","fixed":false,"visible":true,"sortOrder":6,"]
2025-07-26 14:00:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1066]毫秒
2025-07-26 14:00:52 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:00:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-26 14:01:43 [boundedElastic-225] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:01:43 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[110]毫秒
2025-07-26 14:01:53 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:01:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[92]毫秒
2025-07-26 14:02:01 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:02:01 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[98]毫秒
2025-07-26 14:02:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948986854253088770","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":221.99998474121094},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":226},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":226},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"sortOrder":5,"width":261},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrder":6,]
2025-07-26 14:02:07 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1043]毫秒
2025-07-26 14:02:07 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:02:07 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[73]毫秒
2025-07-26 14:02:33 [boundedElastic-230] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/usdReconciliation/export],无参数
2025-07-26 14:02:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/usdReconciliation/export],耗时:[1098]毫秒
2025-07-26 14:02:47 [boundedElastic-230] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:02:47 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-26 14:02:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:02:58 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[92]毫秒
2025-07-26 14:03:42 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948987169769607170","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":369.99998474121094},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":221},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":226},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"sortOrder":5,"width":226},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrder":6,]
2025-07-26 14:03:43 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1043]毫秒
2025-07-26 14:03:43 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:03:43 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[69]毫秒
2025-07-26 14:03:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:03:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[616]毫秒
2025-07-26 14:03:46 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:03:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[128]毫秒
2025-07-26 14:03:46 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:03:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[40]毫秒
2025-07-26 14:03:47 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:03:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:03:47 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:03:47 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-26 14:03:47 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-26 14:03:47 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[482]毫秒
2025-07-26 14:03:51 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948987572531843074","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":167.99998474121094},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":221},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"sortOrder":5,"width":226},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrder":6,"]
2025-07-26 14:03:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1061]毫秒
2025-07-26 14:03:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:03:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[73]毫秒
2025-07-26 14:03:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:03:55 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[673]毫秒
2025-07-26 14:03:55 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:03:55 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[101]毫秒
2025-07-26 14:03:56 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:03:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-26 14:03:56 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:03:56 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[31]毫秒
2025-07-26 14:03:56 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:03:56 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:03:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-26 14:03:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[480]毫秒
2025-07-26 14:04:04 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948987609789845506","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":29.999984741210938},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"sortOrder":5,"width":221},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrder":6,"w]
2025-07-26 14:04:05 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1048]毫秒
2025-07-26 14:04:05 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:04:05 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-26 14:04:09 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948987664420655106","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":55},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"sortOrder":5,"width":29},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrder":6,"width":29.99998474]
2025-07-26 14:04:11 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1324]毫秒
2025-07-26 14:04:11 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:04:11 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[55]毫秒
2025-07-26 14:04:14 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948987687661293570","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":55},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"sortOrder":5,"width":55},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrder":6,"width":29},{"colum]
2025-07-26 14:04:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1002]毫秒
2025-07-26 14:04:15 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:04:16 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[531]毫秒
2025-07-26 14:04:18 [boundedElastic-230] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:04:18 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[625]毫秒
2025-07-26 14:04:18 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:04:18 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[78]毫秒
2025-07-26 14:04:19 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:04:19 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[40]毫秒
2025-07-26 14:04:19 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:04:19 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:04:19 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:04:19 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-26 14:04:19 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-26 14:04:19 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[469]毫秒
2025-07-26 14:04:23 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948987705952653314","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":1249.999984741211},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":55},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"sortOrder":5,"width":55},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrder":6,"wid]
2025-07-26 14:04:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1242]毫秒
2025-07-26 14:04:25 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:04:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[58]毫秒
2025-07-26 14:04:30 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:04:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[589]毫秒
2025-07-26 14:04:30 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:04:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[73]毫秒
2025-07-26 14:04:30 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:04:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-26 14:04:31 [boundedElastic-210] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:04:31 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-26 14:04:31 [boundedElastic-210] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:04:31 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:04:31 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[51]毫秒
2025-07-26 14:04:31 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[456]毫秒
2025-07-26 14:04:50 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948987746872283137","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":245},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":1249},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":55},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"sortOrder":5,"width":55},{"columnName":"status","columnLabel":"确认状态","fixed":false,"v]
2025-07-26 14:04:51 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1419]毫秒
2025-07-26 14:04:52 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:04:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-26 14:05:33 [boundedElastic-227] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:05:33 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[491]毫秒
2025-07-26 14:05:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:05:34 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[402]毫秒
2025-07-26 14:05:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:05:36 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[428]毫秒
2025-07-26 14:05:44 [boundedElastic-227] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:05:44 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[93]毫秒
2025-07-26 14:05:53 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948987858306551810","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":245},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":245},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":false,"sortOrder":0},{"columnName":"confirmerName","colu]
2025-07-26 14:05:54 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1043]毫秒
2025-07-26 14:05:54 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:05:54 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-26 14:05:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:05:57 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[552]毫秒
2025-07-26 14:05:58 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:05:58 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[75]毫秒
2025-07-26 14:05:58 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:05:58 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[46]毫秒
2025-07-26 14:05:58 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:05:58 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:05:58 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:05:58 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[79]毫秒
2025-07-26 14:05:58 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-26 14:05:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[457]毫秒
2025-07-26 14:06:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:06:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[502]毫秒
2025-07-26 14:06:09 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:06:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[663]毫秒
2025-07-26 14:06:10 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:06:10 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[126]毫秒
2025-07-26 14:06:10 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:06:10 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[50]毫秒
2025-07-26 14:06:11 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:06:11 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:06:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:06:11 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[32]毫秒
2025-07-26 14:06:11 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[95]毫秒
2025-07-26 14:06:11 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[495]毫秒
2025-07-26 14:06:37 [boundedElastic-220] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948988121528487938","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":318.99999809265137},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":245},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible]
2025-07-26 14:06:38 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1051]毫秒
2025-07-26 14:06:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:06:38 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[50]毫秒
2025-07-26 14:06:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:06:44 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[637]毫秒
2025-07-26 14:06:44 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:06:45 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[78]毫秒
2025-07-26 14:06:45 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:06:45 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[44]毫秒
2025-07-26 14:06:45 [boundedElastic-220] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:06:45 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[33]毫秒
2025-07-26 14:06:45 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:06:45 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:06:45 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[76]毫秒
2025-07-26 14:06:46 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[487]毫秒
2025-07-26 14:07:09 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948988306950279169","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":398.99999809265137},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":318},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible]
2025-07-26 14:07:10 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1069]毫秒
2025-07-26 14:07:11 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:07:11 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-26 14:08:22 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:08:22 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[95]毫秒
2025-07-26 14:08:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948988442220777474","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":398.99999809265137},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":398},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible]
2025-07-26 14:08:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1167]毫秒
2025-07-26 14:08:28 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:08:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-26 14:08:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:08:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[700]毫秒
2025-07-26 14:08:35 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:08:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[113]毫秒
2025-07-26 14:08:35 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:08:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-26 14:08:36 [boundedElastic-226] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:08:36 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[32]毫秒
2025-07-26 14:08:36 [boundedElastic-226] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:08:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:08:36 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[88]毫秒
2025-07-26 14:08:36 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[457]毫秒
2025-07-26 14:08:40 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948988766767632385","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":55},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":305.99999809265137},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":398},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":fals]
2025-07-26 14:08:42 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1166]毫秒
2025-07-26 14:08:42 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:08:42 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[56]毫秒
2025-07-26 14:08:46 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:08:47 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[627]毫秒
2025-07-26 14:08:47 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:08:47 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[78]毫秒
2025-07-26 14:08:47 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:08:47 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-26 14:08:48 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:08:48 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-07-26 14:08:48 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:08:48 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:08:48 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[79]毫秒
2025-07-26 14:08:48 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[467]毫秒
2025-07-26 14:10:14 [boundedElastic-235] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:10:14 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[94]毫秒
2025-07-26 14:10:20 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948988825114595329","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":42},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":211},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":54},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":304},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":fal]
2025-07-26 14:10:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1057]毫秒
2025-07-26 14:10:21 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:10:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-26 14:10:25 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:10:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[785]毫秒
2025-07-26 14:10:26 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:10:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[102]毫秒
2025-07-26 14:10:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:10:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[38]毫秒
2025-07-26 14:10:27 [boundedElastic-220] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:10:27 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[43]毫秒
2025-07-26 14:10:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:10:27 [boundedElastic-220] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:10:27 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[93]毫秒
2025-07-26 14:10:27 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[499]毫秒
2025-07-26 14:10:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:10:40 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[102]毫秒
2025-07-26 14:10:51 [boundedElastic-220] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:10:51 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[99]毫秒
2025-07-26 14:11:06 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948989243118931969","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":42},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":41},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":210},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":624},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"status","c]
2025-07-26 14:11:07 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1070]毫秒
2025-07-26 14:11:07 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:11:07 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[59]毫秒
2025-07-26 14:11:07 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:11:07 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[54]毫秒
2025-07-26 14:11:17 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:11:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-26 14:11:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948989435159334914","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":42},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":816},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":40},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":209},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"status","c]
2025-07-26 14:11:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1056]毫秒
2025-07-26 14:11:21 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:11:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[62]毫秒
2025-07-26 14:11:26 [boundedElastic-220] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:11:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[105]毫秒
2025-07-26 14:11:30 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948989493472743425","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":42},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":41},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":815},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":370},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"status","c]
2025-07-26 14:11:31 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1119]毫秒
2025-07-26 14:11:31 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:11:31 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[58]毫秒
2025-07-26 14:11:35 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:11:36 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[654]毫秒
2025-07-26 14:11:36 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:11:36 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[79]毫秒
2025-07-26 14:11:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:11:36 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-26 14:11:37 [boundedElastic-236] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:11:37 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:11:37 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[55]毫秒
2025-07-26 14:11:37 [boundedElastic-236] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:11:37 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-26 14:11:37 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[539]毫秒
2025-07-26 14:11:40 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948989535076044802","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":false,"visible":true,"sortOrder":1,"width":42},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":41},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":912},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":814},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0},{"columnName":"status","c]
2025-07-26 14:11:41 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1348]毫秒
2025-07-26 14:11:42 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:11:42 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-26 14:11:45 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:11:45 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[534]毫秒
2025-07-26 14:11:45 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:11:45 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[95]毫秒
2025-07-26 14:11:45 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:11:45 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-26 14:11:46 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:11:46 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-26 14:11:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:11:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:11:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[56]毫秒
2025-07-26 14:11:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[494]毫秒
2025-07-26 14:11:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:11:59 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[642]毫秒
2025-07-26 14:11:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:11:59 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[76]毫秒
2025-07-26 14:11:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:11:59 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[29]毫秒
2025-07-26 14:11:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:11:59 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-26 14:11:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:12:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[515]毫秒
2025-07-26 14:14:57 [boundedElastic-220] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:14:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[711]毫秒
2025-07-26 14:14:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:14:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[103]毫秒
2025-07-26 14:14:58 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:14:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-26 14:14:59 [boundedElastic-220] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:14:59 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[33]毫秒
2025-07-26 14:14:59 [boundedElastic-220] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:14:59 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[549]毫秒
2025-07-26 14:15:19 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:15:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:15:19 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[101]毫秒
2025-07-26 14:15:19 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[513]毫秒
2025-07-26 14:15:34 [boundedElastic-197] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:15:35 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[625]毫秒
2025-07-26 14:15:35 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:15:35 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[75]毫秒
2025-07-26 14:15:35 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:15:35 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[38]毫秒
2025-07-26 14:15:35 [boundedElastic-197] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:15:35 [boundedElastic-197] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:15:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:15:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[94]毫秒
2025-07-26 14:15:35 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-26 14:15:36 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[540]毫秒
2025-07-26 14:15:39 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1],无参数
2025-07-26 14:15:39 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1],耗时:[899]毫秒
2025-07-26 14:15:43 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:15:43 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-26 14:15:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:15:59 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[102]毫秒
2025-07-26 14:16:05 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:16:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-26 14:16:11 [boundedElastic-234] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:16:11 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[96]毫秒
2025-07-26 14:16:38 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:16:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[639]毫秒
2025-07-26 14:16:39 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:16:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[79]毫秒
2025-07-26 14:16:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:16:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-07-26 14:16:40 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:16:40 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[39]毫秒
2025-07-26 14:16:40 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:16:40 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:16:40 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-26 14:16:41 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[536]毫秒
2025-07-26 14:16:46 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948989578470313986","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":42,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":400,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":912,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":]
2025-07-26 14:16:47 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1074]毫秒
2025-07-26 14:16:47 [boundedElastic-214] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:16:47 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[58]毫秒
2025-07-26 14:16:51 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948990861675679745","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":527,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":400,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":]
2025-07-26 14:16:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1339]毫秒
2025-07-26 14:16:52 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:16:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[65]毫秒
2025-07-26 14:16:55 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:16:56 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[507]毫秒
2025-07-26 14:16:56 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:16:56 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[65]毫秒
2025-07-26 14:16:56 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:16:56 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-07-26 14:16:56 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:16:56 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-26 14:16:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:16:56 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:16:56 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-26 14:16:57 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[525]毫秒
2025-07-26 14:16:59 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948990880919146498","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":929,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":527,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":]
2025-07-26 14:17:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[989]毫秒
2025-07-26 14:17:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:17:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[47]毫秒
2025-07-26 14:17:04 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:17:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[578]毫秒
2025-07-26 14:17:05 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:17:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[67]毫秒
2025-07-26 14:17:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:17:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-07-26 14:17:05 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:17:05 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[32]毫秒
2025-07-26 14:17:05 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:17:05 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:17:06 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[89]毫秒
2025-07-26 14:17:06 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[542]毫秒
2025-07-26 14:17:14 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948990916193243138","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":929,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":152.9999542236328,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":fals]
2025-07-26 14:17:15 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1124]毫秒
2025-07-26 14:17:16 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:17:16 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[61]毫秒
2025-07-26 14:17:21 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948990980865216514","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":614,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0]
2025-07-26 14:17:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1098]毫秒
2025-07-26 14:17:22 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:17:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[59]毫秒
2025-07-26 14:17:26 [boundedElastic-244] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948991008086249474","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":55,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0,]
2025-07-26 14:17:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1131]毫秒
2025-07-26 14:17:27 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:17:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[60]毫秒
2025-07-26 14:17:30 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948991030760656897","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":55,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0,]
2025-07-26 14:17:31 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1086]毫秒
2025-07-26 14:17:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:17:32 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[81]毫秒
2025-07-26 14:17:35 [boundedElastic-244] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1947219922154262529],无参数
2025-07-26 14:17:36 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1947219922154262529],耗时:[371]毫秒
2025-07-26 14:17:38 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1947219922481418241],无参数
2025-07-26 14:17:38 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1947219922481418241],耗时:[320]毫秒
2025-07-26 14:18:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:18:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[640]毫秒
2025-07-26 14:18:01 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:18:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[70]毫秒
2025-07-26 14:18:01 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:18:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[35]毫秒
2025-07-26 14:18:01 [boundedElastic-246] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:18:01 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-26 14:18:01 [boundedElastic-246] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:18:01 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:18:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[67]毫秒
2025-07-26 14:18:02 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[616]毫秒
2025-07-26 14:18:23 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:18:23 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[93]毫秒
2025-07-26 14:18:36 [boundedElastic-248] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948991046900338690","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":55,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":false,"sortOrder":0,]
2025-07-26 14:18:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1134]毫秒
2025-07-26 14:18:38 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:18:38 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-26 14:18:43 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948991325167243265","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":55,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":348.99999809265137,"align":"center","sortable":true},{"columnName":"status","columnLabel":"确认状态]
2025-07-26 14:18:44 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1053]毫秒
2025-07-26 14:18:44 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:18:45 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-26 14:19:56 [boundedElastic-248] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:19:56 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[168]毫秒
2025-07-26 14:20:06 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:20:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-26 14:20:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:20:26 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-26 14:20:39 [boundedElastic-249] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:20:39 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[89]毫秒
2025-07-26 14:21:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:21:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[89]毫秒
2025-07-26 14:21:11 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:21:11 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[90]毫秒
2025-07-26 14:21:26 [boundedElastic-249] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:21:26 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[88]毫秒
2025-07-26 14:21:38 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:21:38 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[88]毫秒
2025-07-26 14:21:57 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948991354011471874","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":55,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":253.99999809265137,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":348,"align":"center","sortable":true},{"columnName":"status","columnLabel":"确认状�]
2025-07-26 14:21:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1032]毫秒
2025-07-26 14:21:59 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:21:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[46]毫秒
2025-07-26 14:22:05 [boundedElastic-250] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948992168021020673","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":55,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":268.99999809265137,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":253,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":348,"align":"center","sortable":true},{"columnName":"status","columnLabel":"确认状�]
2025-07-26 14:22:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1016]毫秒
2025-07-26 14:22:06 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:22:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[48]毫秒
2025-07-26 14:22:08 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948992200019365889","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":254.99999809265137,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":268,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":253,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":348,"align":"center","sortable":true},{"columnName":"status","columnLabel":"确认状]
2025-07-26 14:22:09 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[941]毫秒
2025-07-26 14:22:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:22:09 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[47]毫秒
2025-07-26 14:22:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:22:15 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[660]毫秒
2025-07-26 14:22:16 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:22:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[71]毫秒
2025-07-26 14:22:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:22:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[27]毫秒
2025-07-26 14:22:16 [boundedElastic-250] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:22:16 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:22:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[112]毫秒
2025-07-26 14:22:16 [boundedElastic-250] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:22:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[97]毫秒
2025-07-26 14:22:18 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[1027]毫秒
2025-07-26 14:22:40 [boundedElastic-248] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948992212837158914","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":254,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":268,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":253,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":348,"align":"center","sortable":true},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrder":5,"width":17]
2025-07-26 14:22:41 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1033]毫秒
2025-07-26 14:22:42 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:22:42 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[46]毫秒
2025-07-26 14:22:44 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948992348539670530","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":254,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":268,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":253,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":348,"align":"center","sortable":true},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrder":5,"width":17]
2025-07-26 14:22:45 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[949]毫秒
2025-07-26 14:22:45 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:22:45 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[47]毫秒
2025-07-26 14:22:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1948992362892578817","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"主键id","fixed":true,"visible":true,"sortOrder":1,"width":254,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"航运公司","fixed":true,"visible":true,"sortOrder":2,"width":310.60000228881836,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"船舶公司","fixed":true,"visible":true,"sortOrder":3,"width":253,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":4,"width":348,"align":"center","sortable":true},{"columnName":"status","columnLabel":"确认状态","fixed":false,"visible":true,"sortOrde]
2025-07-26 14:22:48 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1046]毫秒
2025-07-26 14:22:49 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:22:49 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[73]毫秒
2025-07-26 14:30:27 [boundedElastic-248] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:30:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[685]毫秒
2025-07-26 14:30:28 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:30:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[85]毫秒
2025-07-26 14:30:28 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:30:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-26 14:30:28 [boundedElastic-248] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:30:28 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[61]毫秒
2025-07-26 14:30:28 [boundedElastic-248] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:30:28 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:30:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[59]毫秒
2025-07-26 14:30:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[568]毫秒
2025-07-26 14:30:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:30:38 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[598]毫秒
2025-07-26 14:30:38 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:30:38 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[70]毫秒
2025-07-26 14:30:38 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:30:38 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[38]毫秒
2025-07-26 14:30:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:30:40 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:30:40 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:30:40 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-26 14:30:40 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[100]毫秒
2025-07-26 14:30:41 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[998]毫秒
2025-07-26 14:33:19 [boundedElastic-246] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:33:19 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[105]毫秒
2025-07-26 14:36:20 [boundedElastic-263] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:36:21 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[719]毫秒
2025-07-26 14:36:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:36:21 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[129]毫秒
2025-07-26 14:36:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:36:21 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[39]毫秒
2025-07-26 14:36:22 [boundedElastic-263] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:36:22 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[137]毫秒
2025-07-26 14:36:23 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:36:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[568]毫秒
2025-07-26 14:36:23 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:36:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[68]毫秒
2025-07-26 14:36:23 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:36:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-26 14:36:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:36:24 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[6]毫秒
2025-07-26 14:36:45 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:36:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[656]毫秒
2025-07-26 14:36:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:36:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[106]毫秒
2025-07-26 14:36:46 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:36:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[27]毫秒
2025-07-26 14:36:46 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:36:46 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-07-26 14:36:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:36:46 [boundedElastic-258] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:36:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-26 14:36:47 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[643]毫秒
2025-07-26 14:38:12 [boundedElastic-264] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:38:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[545]毫秒
2025-07-26 14:40:40 [boundedElastic-251] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:40:42 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[1679]毫秒
2025-07-26 14:42:17 [boundedElastic-251] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 14:42:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[693]毫秒
2025-07-26 14:42:17 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 14:42:18 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[74]毫秒
2025-07-26 14:42:18 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 14:42:18 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-07-26 14:42:18 [boundedElastic-251] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 14:42:19 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 14:42:19 [boundedElastic-251] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 14:42:19 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[71]毫秒
2025-07-26 14:42:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[77]毫秒
2025-07-26 14:42:19 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[512]毫秒
2025-07-26 15:36:45 [boundedElastic-283] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-26 15:36:45 [boundedElastic-302] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-26 15:36:46 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[122]毫秒
2025-07-26 15:36:46 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[383]毫秒
2025-07-26 15:36:46 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 15:36:46 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-26 15:36:46 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-07-26 15:36:46 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[160]毫秒
2025-07-26 15:36:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-26 15:36:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[3]毫秒
2025-07-26 15:36:47 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-26 15:36:47 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[81]毫秒
2025-07-26 15:36:49 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-26 15:36:50 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[932]毫秒
2025-07-26 15:36:51 [boundedElastic-302] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 15:36:51 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[590]毫秒
2025-07-26 15:36:51 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 15:36:51 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[64]毫秒
2025-07-26 15:36:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 15:36:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[29]毫秒
2025-07-26 15:36:52 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:36:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[51]毫秒
2025-07-26 15:36:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 15:36:52 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-26 15:36:52 [boundedElastic-302] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 15:36:53 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[675]毫秒
2025-07-26 15:39:16 [boundedElastic-300] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 15:39:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[673]毫秒
2025-07-26 15:39:17 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 15:39:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[115]毫秒
2025-07-26 15:39:17 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 15:39:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[51]毫秒
2025-07-26 15:39:18 [boundedElastic-300] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 15:39:18 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[60]毫秒
2025-07-26 15:39:38 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 15:39:39 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[640]毫秒
2025-07-26 15:39:39 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 15:39:39 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[84]毫秒
2025-07-26 15:39:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 15:39:39 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[42]毫秒
2025-07-26 15:39:40 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 15:39:40 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-07-26 15:39:40 [boundedElastic-305] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 15:39:40 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:39:40 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[69]毫秒
2025-07-26 15:39:40 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[588]毫秒
2025-07-26 15:41:24 [boundedElastic-302] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 15:41:25 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[649]毫秒
2025-07-26 15:41:25 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 15:41:25 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[66]毫秒
2025-07-26 15:41:25 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 15:41:25 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-07-26 15:41:25 [boundedElastic-302] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 15:41:25 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[57]毫秒
2025-07-26 15:41:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:41:25 [boundedElastic-302] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 15:41:25 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-26 15:41:26 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[557]毫秒
2025-07-26 15:42:16 [boundedElastic-301] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:42:16 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[79]毫秒
2025-07-26 15:43:23 [boundedElastic-283] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 15:43:24 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[818]毫秒
2025-07-26 15:43:24 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 15:43:24 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[74]毫秒
2025-07-26 15:43:24 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 15:43:24 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[40]毫秒
2025-07-26 15:43:24 [boundedElastic-283] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 15:43:24 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-26 15:43:24 [boundedElastic-283] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 15:43:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:43:24 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[89]毫秒
2025-07-26 15:43:25 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[536]毫秒
2025-07-26 15:43:28 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:43:28 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[87]毫秒
2025-07-26 15:44:11 [boundedElastic-309] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 15:44:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[631]毫秒
2025-07-26 15:44:12 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 15:44:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[65]毫秒
2025-07-26 15:44:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 15:44:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-26 15:44:13 [boundedElastic-309] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 15:44:13 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:44:13 [boundedElastic-309] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 15:44:13 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[97]毫秒
2025-07-26 15:44:13 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-26 15:44:13 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[510]毫秒
2025-07-26 15:46:06 [boundedElastic-292] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:46:06 [boundedElastic-307] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:46:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[74]毫秒
2025-07-26 15:46:06 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[83]毫秒
2025-07-26 15:46:11 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":283.99999809265137,"align":"center"},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":80,"align":"center"},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":80,"align":"center"},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":80,"align":"center"},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"sortOrder":5,"width":80,"align":"center"},{"column]
2025-07-26 15:46:12 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1099]毫秒
2025-07-26 15:46:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:46:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[59]毫秒
2025-07-26 15:46:41 [boundedElastic-294] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013365509148673","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":283,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":80,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":171.00001525878906,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":80,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"s]
2025-07-26 15:46:42 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1113]毫秒
2025-07-26 15:46:43 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:46:43 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[61]毫秒
2025-07-26 15:46:44 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013490977558529","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":283,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":156.00001525878906,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":171,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":80,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"]
2025-07-26 15:46:45 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1063]毫秒
2025-07-26 15:46:46 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:46:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[62]毫秒
2025-07-26 15:46:50 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013504604852225","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":221.99998474121094,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":156,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":171,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":80,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"]
2025-07-26 15:46:51 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1115]毫秒
2025-07-26 15:46:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:46:52 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[58]毫秒
2025-07-26 15:46:55 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013529317691394","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":187.99998474121094,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":156,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":171,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":80,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"]
2025-07-26 15:46:56 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1040]毫秒
2025-07-26 15:46:56 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:46:56 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[79]毫秒
2025-07-26 15:46:59 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013547202203649","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":187,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":201.99998474121094,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":171,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":80,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"]
2025-07-26 15:47:00 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1063]毫秒
2025-07-26 15:47:00 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:47:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[61]毫秒
2025-07-26 15:47:02 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013563899727873","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":187,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":259.99998474121094,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":171,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":80,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"]
2025-07-26 15:47:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[987]毫秒
2025-07-26 15:47:03 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:47:03 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[48]毫秒
2025-07-26 15:47:07 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013578592374785","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":187,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238.99998474121094,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":171,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":80,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"]
2025-07-26 15:47:08 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1040]毫秒
2025-07-26 15:47:08 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:47:08 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-26 15:47:10 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013597319942146","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":187,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231.00001525878906,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":80,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"]
2025-07-26 15:47:11 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[953]毫秒
2025-07-26 15:47:11 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:47:11 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-26 15:47:14 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013611643490306","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":187,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":true,"sortOrder":4,"width":84.00001525878906,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":true,"]
2025-07-26 15:47:15 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1044]毫秒
2025-07-26 15:47:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:47:16 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-26 15:50:57 [boundedElastic-305] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:50:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[100]毫秒
2025-07-26 15:51:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:51:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[107]毫秒
2025-07-26 15:51:39 [boundedElastic-283] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:51:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[99]毫秒
2025-07-26 15:51:40 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:51:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[87]毫秒
2025-07-26 15:51:41 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:51:41 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-26 15:51:44 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:51:44 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[100]毫秒
2025-07-26 15:51:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:51:54 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[96]毫秒
2025-07-26 15:52:30 [boundedElastic-300] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 15:52:31 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[100]毫秒
2025-07-26 16:17:03 [boundedElastic-323] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-26 16:17:03 [boundedElastic-323] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-26 16:17:03 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[35]毫秒
2025-07-26 16:17:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[492]毫秒
2025-07-26 16:17:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-26 16:17:05 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 16:17:05 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-26 16:17:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[112]毫秒
2025-07-26 16:17:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-26 16:17:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[825]毫秒
2025-07-26 16:17:12 [boundedElastic-323] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 16:17:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[611]毫秒
2025-07-26 16:17:13 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 16:17:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[79]毫秒
2025-07-26 16:17:13 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 16:17:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[45]毫秒
2025-07-26 16:17:14 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 16:17:14 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-26 16:20:11 [boundedElastic-338] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 16:20:12 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[858]毫秒
2025-07-26 16:22:09 [boundedElastic-302] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["20"]}]
2025-07-26 16:22:09 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[332]毫秒
2025-07-26 16:57:25 [boundedElastic-343] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-26 16:57:25 [boundedElastic-363] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-26 16:57:25 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[57]毫秒
2025-07-26 16:57:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[333]毫秒
2025-07-26 16:57:25 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-26 16:57:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[129]毫秒
2025-07-26 16:57:26 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 16:57:26 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-26 16:57:31 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-26 16:57:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[978]毫秒
2025-07-26 16:57:33 [boundedElastic-361] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 16:57:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[621]毫秒
2025-07-26 16:57:34 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 16:57:34 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[91]毫秒
2025-07-26 16:57:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 16:57:34 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[51]毫秒
2025-07-26 16:57:34 [boundedElastic-361] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 16:57:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 16:57:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[2]毫秒
2025-07-26 16:57:35 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/getDataNames],无参数
2025-07-26 16:57:35 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[362]毫秒
2025-07-26 16:57:35 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/getDataNames],耗时:[267]毫秒
2025-07-26 16:57:35 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 16:57:35 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/list],耗时:[222]毫秒
2025-07-26 16:57:50 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /tool/gen/1948279596500647937],无参数
2025-07-26 16:57:50 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-26 16:57:51 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[473]毫秒
2025-07-26 16:57:51 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /tool/gen/1948279596500647937],耗时:[517]毫秒
2025-07-26 16:57:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/type/optionselect],无参数
2025-07-26 16:57:51 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/type/optionselect],耗时:[64]毫秒
2025-07-26 17:18:53 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 17:18:53 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 17:18:53 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-26 20:08:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-26 20:08:21 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 28580 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-26 20:08:21 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-26 20:08:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-26 20:08:21 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-26 20:08:25 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-26 20:08:26 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-26 20:08:26 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-26 20:08:27 [redisson-netty-3-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-26 20:08:27 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-26 20:08:31 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway 192.168.124.4:8080 register finished
2025-07-26 20:08:32 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-26 20:08:32 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 13.676 seconds (process running for 14.634)
2025-07-26 20:08:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-26 20:08:32 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-26 20:08:55 [boundedElastic-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-26 20:08:55 [boundedElastic-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-26 20:08:56 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1127]毫秒
2025-07-26 20:08:57 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1355]毫秒
2025-07-26 20:08:57 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-26 20:08:57 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 20:08:57 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[13]毫秒
2025-07-26 20:08:57 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[314]毫秒
2025-07-26 20:09:03 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-26 20:09:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1541]毫秒
2025-07-26 20:09:06 [boundedElastic-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 20:09:07 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1638]毫秒
2025-07-26 20:09:08 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 20:09:08 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[99]毫秒
2025-07-26 20:09:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 20:09:08 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[48]毫秒
2025-07-26 20:09:08 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 20:09:08 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[6]毫秒
2025-07-26 20:09:31 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:09:31 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[110]毫秒
2025-07-26 20:10:06 [boundedElastic-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 20:10:07 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[732]毫秒
2025-07-26 20:10:07 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 20:10:07 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[86]毫秒
2025-07-26 20:10:07 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 20:10:07 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-26 20:10:07 [boundedElastic-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 20:10:07 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[30]毫秒
2025-07-26 20:10:07 [boundedElastic-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 20:10:07 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:10:07 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[54]毫秒
2025-07-26 20:10:08 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[1145]毫秒
2025-07-26 20:13:03 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:13:03 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[96]毫秒
2025-07-26 20:13:18 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949013630241034242","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":187,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":false,"sortOrder":0,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0,"align":"ce]
2025-07-26 20:13:19 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1308]毫秒
2025-07-26 20:13:19 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:13:19 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-26 20:13:29 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:13:30 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[93]毫秒
2025-07-26 20:13:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949080585224388610","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":187,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible":false,"sortOrder":0,"align":"center","sortable":true},{"columnName":"closeBalance","columnLabel":"期末余额","fixed":false,"visible":false,"sortOrder":0,"align":"ce]
2025-07-26 20:13:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1395]毫秒
2025-07-26 20:13:35 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:13:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[81]毫秒
2025-07-26 20:13:39 [boundedElastic-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949080650340958210","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":187,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":167.00001525878906,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible]
2025-07-26 20:13:40 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1200]毫秒
2025-07-26 20:13:40 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:13:40 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[76]毫秒
2025-07-26 20:13:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 20:13:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[649]毫秒
2025-07-26 20:13:44 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 20:13:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[83]毫秒
2025-07-26 20:13:44 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 20:13:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-07-26 20:13:44 [boundedElastic-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 20:13:44 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-26 20:13:44 [boundedElastic-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 20:13:44 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:13:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[46]毫秒
2025-07-26 20:13:45 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[494]毫秒
2025-07-26 20:13:50 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949080673900363777","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":254.99998474121094,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":167,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible]
2025-07-26 20:13:51 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1232]毫秒
2025-07-26 20:13:51 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:13:51 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[50]毫秒
2025-07-26 20:13:54 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949080718854914050","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":200.99998474121094,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":167,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible]
2025-07-26 20:13:55 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1440]毫秒
2025-07-26 20:13:55 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:13:55 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[66]毫秒
2025-07-26 20:14:00 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1],无参数
2025-07-26 20:14:01 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1],耗时:[714]毫秒
2025-07-26 20:14:10 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1947219921831301122],无参数
2025-07-26 20:14:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1947219921831301122],耗时:[673]毫秒
2025-07-26 20:14:21 [boundedElastic-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/1947219922154262529],无参数
2025-07-26 20:14:22 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/1947219922154262529],耗时:[397]毫秒
2025-07-26 20:15:34 [boundedElastic-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:15:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[244]毫秒
2025-07-26 20:18:18 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-26 20:18:18 [boundedElastic-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-26 20:18:18 [boundedElastic-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:18:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-26 20:18:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-26 20:18:19 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-26 20:18:19 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[587]毫秒
2025-07-26 20:18:20 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1603]毫秒
2025-07-26 20:18:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-26 20:18:20 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[46]毫秒
2025-07-26 20:18:25 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderDetail/1946455957600002050],无参数
2025-07-26 20:18:25 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1946455957600002050],无参数
2025-07-26 20:18:25 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-26 20:18:26 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[90]毫秒
2025-07-26 20:18:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/getApprovalProcess/1946455957600002050],耗时:[412]毫秒
2025-07-26 20:18:26 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1946455957600002050],参数类型[param],参数:[{"businessType":["0"]}]
2025-07-26 20:18:27 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1946455957600002050],耗时:[303]毫秒
2025-07-26 20:18:28 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderDetail/1946455957600002050],耗时:[2380]毫秒
2025-07-26 20:18:34 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-26 20:18:34 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[88]毫秒
2025-07-26 20:18:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:18:35 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[462]毫秒
2025-07-26 20:18:35 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 20:18:35 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[193]毫秒
2025-07-26 20:18:48 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-26 20:18:48 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[81]毫秒
2025-07-26 20:19:05 [boundedElastic-21] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:19:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[580]毫秒
2025-07-26 20:19:06 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1934261959020875778"],"beginDate":["2024-07-26"],"endDate":["2025-07-26"],"dateRange":["last1year"]}]
2025-07-26 20:19:06 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/getStatisticsData],耗时:[208]毫秒
2025-07-26 20:19:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:19:09 [boundedElastic-21] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"]}]
2025-07-26 20:19:09 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:19:09 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[54]毫秒
2025-07-26 20:19:09 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[187]毫秒
2025-07-26 20:19:09 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[390]毫秒
2025-07-26 20:19:14 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-26 20:19:14 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:19:14 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-26 20:19:14 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-26 20:19:14 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[122]毫秒
2025-07-26 20:19:14 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[523]毫秒
2025-07-26 20:19:15 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[616]毫秒
2025-07-26 20:19:15 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[793]毫秒
2025-07-26 20:19:18 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:19:18 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:19:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getTotalProperty],耗时:[179]毫秒
2025-07-26 20:19:18 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"]}]
2025-07-26 20:19:18 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[43]毫秒
2025-07-26 20:19:18 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[405]毫秒
2025-07-26 20:19:21 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/rmb_purpose],无参数
2025-07-26 20:19:21 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:19:21 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/rmb_purpose],耗时:[80]毫秒
2025-07-26 20:19:22 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[449]毫秒
2025-07-26 20:19:22 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-26 20:19:22 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbFlow/list],耗时:[201]毫秒
2025-07-26 20:19:28 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-26 20:19:29 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[436]毫秒
2025-07-26 20:19:29 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1934261959020875778"],"beginDate":["2024-07-26"],"endDate":["2025-07-26"],"dateRange":["last1year"]}]
2025-07-26 20:19:29 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbFlow/getStatisticsData],耗时:[166]毫秒
2025-07-26 20:19:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-26 20:19:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/list],耗时:[304]毫秒
2025-07-26 20:20:59 [boundedElastic-25] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949080736684900353","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":194.99998474121094,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":167,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible]
2025-07-26 20:21:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1251]毫秒
2025-07-26 20:21:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:21:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-26 20:21:04 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949082519301849090","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":580.9999847412109,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":167,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible"]
2025-07-26 20:21:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1189]毫秒
2025-07-26 20:21:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:21:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-26 20:21:08 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949082539904270338","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":219.99998474121094,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":238,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":231,"align":"center","sortable":true},{"columnName":"expenseAmount","columnLabel":"本月支出金额","fixed":false,"visible":true,"sortOrder":4,"width":167,"align":"center","sortable":true},{"columnName":"openBalance","columnLabel":"期初余额","fixed":false,"visible]
2025-07-26 20:21:09 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1275]毫秒
2025-07-26 20:21:09 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:21:09 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[42]毫秒
2025-07-26 20:21:49 [boundedElastic-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-26 20:21:49 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-26 20:22:18 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-26 20:22:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[82]毫秒
2025-07-26 20:44:34 [boundedElastic-38] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:44:34 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[223]毫秒
2025-07-26 20:44:44 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949088373515587585","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":226,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":309,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":187,"align":"center","sortable":true},{"columnName":"incomeAmount","columnLabel":"本月收入金额","fixed":false,"visible":true,"sortOrder":4,"width":106,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":5,]
2025-07-26 20:44:46 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1310]毫秒
2025-07-26 20:44:46 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:44:46 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-26 20:51:22 [boundedElastic-48] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:51:22 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[99]毫秒
2025-07-26 20:51:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1949088760503046146","configName":"usdReconciliationTable","columns":[{"columnName":"id","columnLabel":"参考号","fixed":true,"visible":true,"sortOrder":1,"width":226,"align":"center","sortable":true},{"columnName":"transportName","columnLabel":"主账户","fixed":true,"visible":true,"sortOrder":2,"width":309,"align":"center","sortable":true},{"columnName":"shippingName","columnLabel":"使用机构","fixed":true,"visible":true,"sortOrder":3,"width":187,"align":"center","sortable":true},{"columnName":"incomeAmount","columnLabel":"本月收入金额","fixed":false,"visible":true,"sortOrder":4,"width":156,"align":"center","sortable":true},{"columnName":"period","columnLabel":"对账周期","fixed":false,"visible":true,"sortOrder":5,]
2025-07-26 20:51:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1353]毫秒
2025-07-26 20:51:28 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:51:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[77]毫秒
2025-07-26 20:53:30 [boundedElastic-50] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-26 20:53:31 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[919]毫秒
2025-07-26 20:53:31 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-26 20:53:31 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[134]毫秒
2025-07-26 20:53:31 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-26 20:53:31 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[52]毫秒
2025-07-26 20:53:32 [boundedElastic-50] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-26 20:53:32 [boundedElastic-50] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"],"params[timeRange]":[""]}]
2025-07-26 20:53:32 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:53:32 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[80]毫秒
2025-07-26 20:53:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[179]毫秒
2025-07-26 20:53:33 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:53:33 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/list],耗时:[1051]毫秒
2025-07-26 20:53:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[56]毫秒
2025-07-26 20:53:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:53:40 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[132]毫秒
2025-07-26 20:53:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:53:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-26 20:53:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:53:58 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[98]毫秒
2025-07-26 20:54:30 [boundedElastic-45] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:54:30 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[98]毫秒
2025-07-26 20:54:36 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:54:36 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-26 20:54:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:54:48 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[107]毫秒
2025-07-26 20:56:21 [boundedElastic-56] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:56:21 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[124]毫秒
2025-07-26 20:56:41 [boundedElastic-50] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-26 20:56:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:56:46 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[113]毫秒
2025-07-26 20:56:47 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[6264]毫秒
2025-07-26 20:57:50 [boundedElastic-60] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:57:50 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-26 20:57:57 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:57:57 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[97]毫秒
2025-07-26 20:58:15 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:58:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[105]毫秒
2025-07-26 20:58:25 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:58:25 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:58:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[106]毫秒
2025-07-26 20:58:25 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-26 20:58:36 [boundedElastic-42] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:58:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[89]毫秒
2025-07-26 20:59:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:59:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[107]毫秒
2025-07-26 20:59:56 [boundedElastic-59] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 20:59:56 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[93]毫秒
2025-07-26 21:00:01 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 21:00:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-26 21:00:38 [boundedElastic-59] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-26 21:00:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[88]毫秒
2025-07-26 21:01:13 [boundedElastic-57] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-26 21:01:15 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[1649]毫秒
2025-07-26 21:01:27 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdReconciliation/download/1],无参数
2025-07-26 21:01:28 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdReconciliation/download/1],耗时:[1182]毫秒
2025-07-26 21:13:08 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-26 21:13:08 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-26 21:13:08 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
