2025-07-17 08:46:10 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-17 08:46:10 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 26928 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-17 08:46:10 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-17 08:46:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-17 08:46:10 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-17 08:46:13 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-17 08:46:13 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-17 08:46:13 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-17 08:46:14 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-17 08:46:14 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-17 08:46:17 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway 192.168.124.3:8080 register finished
2025-07-17 08:46:17 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-17 08:46:18 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 9.479 seconds (process running for 10.392)
2025-07-17 08:46:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-17 08:46:18 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-17 08:46:52 [boundedElastic-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 08:46:52 [boundedElastic-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 08:46:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1024]毫秒
2025-07-17 08:46:53 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1207]毫秒
2025-07-17 08:46:53 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 08:46:53 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 08:46:53 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[12]毫秒
2025-07-17 08:46:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[307]毫秒
2025-07-17 08:46:56 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 08:46:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1520]毫秒
2025-07-17 08:46:58 [boundedElastic-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 08:47:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1516]毫秒
2025-07-17 08:47:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 08:47:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[110]毫秒
2025-07-17 08:47:01 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 08:47:01 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[48]毫秒
2025-07-17 08:47:01 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 08:47:01 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[14]毫秒
2025-07-17 08:47:59 [boundedElastic-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-17 08:47:59 [boundedElastic-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-17 08:47:59 [boundedElastic-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-17 08:47:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[93]毫秒
2025-07-17 08:47:59 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-17 08:47:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[29]毫秒
2025-07-17 08:47:59 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[866]毫秒
2025-07-17 08:48:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[1244]毫秒
2025-07-17 08:48:00 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-17 08:48:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[29]毫秒
2025-07-17 08:48:00 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 08:48:00 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-17 08:48:00 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-17 08:48:00 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[28]毫秒
2025-07-17 08:48:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[262]毫秒
2025-07-17 08:48:01 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[683]毫秒
2025-07-17 08:52:17 [boundedElastic-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 08:52:17 [boundedElastic-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-17 08:52:17 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[81]毫秒
2025-07-17 08:52:17 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-17 08:52:17 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[322]毫秒
2025-07-17 08:52:18 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[696]毫秒
2025-07-17 08:53:02 [boundedElastic-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 08:53:02 [boundedElastic-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-17 08:53:02 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[85]毫秒
2025-07-17 08:53:03 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-17 08:53:03 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[319]毫秒
2025-07-17 08:53:03 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[758]毫秒
2025-07-17 08:53:21 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 08:53:21 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-17 08:53:21 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[69]毫秒
2025-07-17 08:53:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-17 08:53:21 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[352]毫秒
2025-07-17 08:53:22 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[633]毫秒
2025-07-17 08:56:11 [boundedElastic-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 08:56:11 [boundedElastic-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 08:56:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[52]毫秒
2025-07-17 08:56:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[160]毫秒
2025-07-17 08:56:14 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 08:56:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1376]毫秒
2025-07-17 08:56:15 [boundedElastic-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 08:56:18 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[2399]毫秒
2025-07-17 08:56:18 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 08:56:18 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[121]毫秒
2025-07-17 08:56:18 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 08:56:18 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-17 08:56:20 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 08:56:20 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-17 08:56:20 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_todo_status],无参数
2025-07-17 08:56:20 [boundedElastic-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 08:56:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_status],无参数
2025-07-17 08:56:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_todo_status],耗时:[54]毫秒
2025-07-17 08:56:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_status],耗时:[47]毫秒
2025-07-17 08:56:20 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/list],耗时:[276]毫秒
2025-07-17 08:56:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"shippingId":["1934261052682113025"]}]
2025-07-17 08:56:36 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 08:56:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"shippingId":["1934261052682113025"]}]
2025-07-17 08:56:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[83]毫秒
2025-07-17 08:56:36 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[336]毫秒
2025-07-17 08:56:37 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[986]毫秒
2025-07-17 08:56:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/profile],无参数
2025-07-17 08:56:45 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/social/list],无参数
2025-07-17 08:56:45 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/online],无参数
2025-07-17 08:56:45 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/social/list],耗时:[70]毫秒
2025-07-17 08:56:45 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[228]毫秒
2025-07-17 08:56:45 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/online],耗时:[277]毫秒
2025-07-17 08:56:49 [boundedElastic-17] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 08:56:49 [boundedElastic-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 08:56:49 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[60]毫秒
2025-07-17 08:56:49 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[379]毫秒
2025-07-17 08:56:50 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 08:56:50 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 08:56:50 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-17 08:56:50 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[96]毫秒
2025-07-17 08:56:56 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 08:56:57 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[934]毫秒
2025-07-17 08:56:57 [boundedElastic-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 08:56:57 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[635]毫秒
2025-07-17 08:56:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 08:56:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[130]毫秒
2025-07-17 08:56:58 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 08:56:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[51]毫秒
2025-07-17 08:56:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 08:56:58 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-17 08:57:03 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 08:57:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 08:57:03 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[27]毫秒
2025-07-17 08:57:04 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[547]毫秒
2025-07-17 08:57:05 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 08:57:05 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 08:57:05 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-17 08:57:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[102]毫秒
2025-07-17 08:57:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 08:57:10 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[874]毫秒
2025-07-17 08:57:10 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 08:57:11 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[813]毫秒
2025-07-17 08:57:11 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 08:57:11 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[96]毫秒
2025-07-17 08:57:11 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 08:57:11 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[35]毫秒
2025-07-17 08:57:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 08:57:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-17 08:57:13 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 08:57:13 [boundedElastic-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 08:57:13 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 08:57:13 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[62]毫秒
2025-07-17 08:57:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[419]毫秒
2025-07-17 08:57:14 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[808]毫秒
2025-07-17 08:57:15 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 08:57:15 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/list],耗时:[475]毫秒
2025-07-17 08:57:22 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/rmb_purpose],无参数
2025-07-17 08:57:22 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 08:57:22 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/rmb_purpose],耗时:[53]毫秒
2025-07-17 08:57:23 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[364]毫秒
2025-07-17 08:57:23 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 08:57:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbFlow/list],耗时:[200]毫秒
2025-07-17 08:57:28 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 08:57:28 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[380]毫秒
2025-07-17 08:57:29 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 08:57:29 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[283]毫秒
2025-07-17 08:57:29 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 08:57:30 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[301]毫秒
2025-07-17 09:30:39 [boundedElastic-32] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 09:30:39 [boundedElastic-46] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 09:30:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[234]毫秒
2025-07-17 09:30:40 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[492]毫秒
2025-07-17 09:30:40 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 09:30:40 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 09:30:40 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[19]毫秒
2025-07-17 09:30:40 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[190]毫秒
2025-07-17 10:04:10 [boundedElastic-85] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 10:04:10 [boundedElastic-83] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 10:04:10 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[62]毫秒
2025-07-17 10:04:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[401]毫秒
2025-07-17 10:04:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 10:04:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[6]毫秒
2025-07-17 10:04:11 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 10:04:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[139]毫秒
2025-07-17 11:16:14 [boundedElastic-144] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 11:16:14 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[390]毫秒
2025-07-17 11:16:14 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 11:16:15 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[137]毫秒
2025-07-17 11:16:18 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 11:16:19 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[878]毫秒
2025-07-17 11:16:19 [boundedElastic-144] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 11:16:19 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[657]毫秒
2025-07-17 11:16:20 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 11:16:20 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[138]毫秒
2025-07-17 11:16:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 11:16:20 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[41]毫秒
2025-07-17 11:16:21 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 11:16:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-17 11:16:23 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 11:16:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-17 11:16:23 [boundedElastic-141] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 11:16:23 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[94]毫秒
2025-07-17 11:16:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-17 11:16:23 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-17 11:16:23 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdWorkorder/list],耗时:[319]毫秒
2025-07-17 11:16:23 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-17 11:16:23 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[61]毫秒
2025-07-17 11:16:23 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[502]毫秒
2025-07-17 11:16:24 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 11:16:24 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 11:16:24 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 11:16:24 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 11:16:24 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[59]毫秒
2025-07-17 11:16:24 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[123]毫秒
2025-07-17 11:16:25 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[357]毫秒
2025-07-17 11:16:25 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[664]毫秒
2025-07-17 11:20:15 [boundedElastic-139] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 11:20:15 [boundedElastic-140] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 11:20:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[1441]毫秒
2025-07-17 11:20:18 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[2106]毫秒
2025-07-17 11:20:21 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 11:20:22 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[440]毫秒
2025-07-17 11:20:22 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 11:20:22 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[260]毫秒
2025-07-17 11:20:22 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 11:20:23 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[364]毫秒
2025-07-17 11:20:46 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945385012276293634","grantType":"1","channels":[{"channelId":1,"channelAmount":0.12}]}]
2025-07-17 11:20:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/updateGrantType],耗时:[4657]毫秒
2025-07-17 11:22:11 [boundedElastic-133] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945385012276293634","grantType":"1","channels":[{"channelId":1,"channelAmount":50},{"channelId":2,"channelAmount":40},{"channelId":3,"channelAmount":10}]}]
2025-07-17 11:22:15 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/updateGrantType],耗时:[3745]毫秒
2025-07-17 11:22:26 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 11:22:26 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/list],耗时:[478]毫秒
2025-07-17 11:22:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 11:22:29 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/list],耗时:[427]毫秒
2025-07-17 11:23:42 [boundedElastic-144] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 11:23:43 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[873]毫秒
2025-07-17 11:23:43 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 11:23:43 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[153]毫秒
2025-07-17 11:23:43 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 11:23:43 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[52]毫秒
2025-07-17 11:23:44 [boundedElastic-144] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 11:23:44 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 11:23:44 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 11:23:44 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 11:23:44 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[48]毫秒
2025-07-17 11:23:44 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[66]毫秒
2025-07-17 11:23:44 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 11:23:44 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[456]毫秒
2025-07-17 11:23:45 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[736]毫秒
2025-07-17 11:23:45 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[834]毫秒
2025-07-17 11:23:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 11:24:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[509]毫秒
2025-07-17 11:24:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 11:24:00 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[219]毫秒
2025-07-17 11:24:00 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 11:24:01 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[328]毫秒
2025-07-17 11:24:59 [boundedElastic-147] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 11:24:59 [boundedElastic-133] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 11:24:59 [boundedElastic-119] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 11:24:59 [boundedElastic-119] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 11:24:59 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[87]毫秒
2025-07-17 11:24:59 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[411]毫秒
2025-07-17 11:24:59 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[585]毫秒
2025-07-17 11:24:59 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[735]毫秒
2025-07-17 11:25:08 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 11:25:08 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 11:25:08 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 11:25:08 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 11:25:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[74]毫秒
2025-07-17 11:25:09 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[394]毫秒
2025-07-17 11:25:09 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[535]毫秒
2025-07-17 11:25:09 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[705]毫秒
2025-07-17 11:25:39 [boundedElastic-148] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 11:25:39 [boundedElastic-149] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 11:25:39 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 11:25:39 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 11:25:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[99]毫秒
2025-07-17 11:25:40 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[397]毫秒
2025-07-17 11:25:40 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[625]毫秒
2025-07-17 11:25:40 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[762]毫秒
2025-07-17 11:25:41 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 11:25:42 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[310]毫秒
2025-07-17 11:25:42 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 11:25:42 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[230]毫秒
2025-07-17 11:25:42 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 11:25:43 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[280]毫秒
2025-07-17 11:26:19 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945385012276293634","grantType":"2","channels":[{"channelId":"1934638490754846722","channelAmount":50},{"channelId":"1934638605565530114","channelAmount":40},{"channelId":"1934638650209701889","channelAmount":10}]}]
2025-07-17 11:26:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945385012276293634","grantType":"2","channels":[{"channelId":"1934638490754846722","channelAmount":50},{"channelId":"1934638605565530114","channelAmount":40},{"channelId":"1934638650209701889","channelAmount":10}]}]
2025-07-17 11:26:40 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/updateGrantType],耗时:[21136]毫秒
2025-07-17 11:26:40 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/updateGrantType],耗时:[7373]毫秒
2025-07-17 11:27:00 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945385012276293634","grantType":"2","channels":[{"channelId":"1934638490754846722","channelAmount":50},{"channelId":"1934638605565530114","channelAmount":40},{"channelId":"1934638650209701889","channelAmount":10}]}]
2025-07-17 11:27:10 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945385012276293634","grantType":"2","channels":[{"channelId":"1934638490754846722","channelAmount":50},{"channelId":"1934638605565530114","channelAmount":40},{"channelId":"1934638650209701889","channelAmount":10}]}]
2025-07-17 11:27:16 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/updateGrantType],耗时:[15971]毫秒
2025-07-17 11:27:16 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/updateGrantType],耗时:[5859]毫秒
2025-07-17 11:28:56 [boundedElastic-119] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945385012276293634","grantType":"2","channels":[{"channelId":"1934638490754846722","channelAmount":50},{"channelId":"1934638605565530114","channelAmount":40},{"channelId":"1934638650209701889","channelAmount":10}]}]
2025-07-17 11:29:03 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/updateGrantType],耗时:[7503]毫秒
2025-07-17 11:29:55 [boundedElastic-136] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 11:29:55 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 11:29:55 [boundedElastic-144] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 11:29:55 [boundedElastic-147] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 11:29:55 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[80]毫秒
2025-07-17 11:29:56 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[383]毫秒
2025-07-17 11:29:56 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[536]毫秒
2025-07-17 11:29:56 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[695]毫秒
2025-07-17 11:29:57 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 11:29:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[273]毫秒
2025-07-17 11:29:57 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 11:29:57 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[210]毫秒
2025-07-17 11:29:58 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 11:29:58 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[277]毫秒
2025-07-17 11:30:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945371377764233218],无参数
2025-07-17 11:30:05 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945371377764233218],耗时:[361]毫秒
2025-07-17 11:30:06 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945371377764233218],无参数
2025-07-17 11:30:06 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945371377764233218],耗时:[268]毫秒
2025-07-17 11:30:06 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945371377764233218],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 11:30:06 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945371377764233218],耗时:[253]毫秒
2025-07-17 11:30:09 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945367202070360066],无参数
2025-07-17 11:30:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945367202070360066],耗时:[280]毫秒
2025-07-17 11:30:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945367202070360066],无参数
2025-07-17 11:30:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945367202070360066],耗时:[300]毫秒
2025-07-17 11:30:10 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945367202070360066],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 11:30:10 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945367202070360066],耗时:[485]毫秒
2025-07-17 11:30:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945007984226656257],无参数
2025-07-17 11:30:13 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945007984226656257],耗时:[451]毫秒
2025-07-17 11:30:13 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945007984226656257],无参数
2025-07-17 11:30:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945007984226656257],耗时:[261]毫秒
2025-07-17 11:30:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945007984226656257],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 11:30:14 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945007984226656257],耗时:[266]毫秒
2025-07-17 13:30:45 [boundedElastic-157] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 13:30:45 [boundedElastic-139] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 13:30:45 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[128]毫秒
2025-07-17 13:30:46 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[624]毫秒
2025-07-17 13:30:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 13:30:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 13:30:46 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[10]毫秒
2025-07-17 13:30:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[222]毫秒
2025-07-17 13:33:56 [boundedElastic-157] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 13:33:56 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[296]毫秒
2025-07-17 13:33:56 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 13:33:56 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[191]毫秒
2025-07-17 13:34:00 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 13:34:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[66]毫秒
2025-07-17 13:34:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 13:34:01 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[97]毫秒
2025-07-17 13:34:03 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 13:34:04 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[955]毫秒
2025-07-17 13:34:05 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 13:34:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[714]毫秒
2025-07-17 13:34:05 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 13:34:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[92]毫秒
2025-07-17 13:34:06 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 13:34:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[41]毫秒
2025-07-17 13:34:06 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 13:34:06 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-17 13:34:10 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:34:11 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[922]毫秒
2025-07-17 13:34:11 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/list],参数类型[param],参数:[{"transportId":["1934261959020875778"],"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 13:34:12 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/list],耗时:[255]毫秒
2025-07-17 13:34:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:34:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:34:12 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:34:12 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:34:12 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[67]毫秒
2025-07-17 13:34:12 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[292]毫秒
2025-07-17 13:34:13 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[716]毫秒
2025-07-17 13:34:13 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[880]毫秒
2025-07-17 13:34:18 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:34:18 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[367]毫秒
2025-07-17 13:34:18 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:34:19 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[233]毫秒
2025-07-17 13:34:19 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:34:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[299]毫秒
2025-07-17 13:39:04 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:39:04 [boundedElastic-165] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:39:04 [boundedElastic-166] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:39:04 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[197]毫秒
2025-07-17 13:39:04 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:39:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[647]毫秒
2025-07-17 13:39:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[878]毫秒
2025-07-17 13:39:05 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[917]毫秒
2025-07-17 13:39:36 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:39:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[419]毫秒
2025-07-17 13:39:36 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:39:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[211]毫秒
2025-07-17 13:39:37 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:39:37 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[286]毫秒
2025-07-17 13:40:32 [boundedElastic-169] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:40:32 [boundedElastic-170] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:40:32 [boundedElastic-162] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:40:32 [boundedElastic-167] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:40:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[74]毫秒
2025-07-17 13:40:32 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[399]毫秒
2025-07-17 13:40:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[631]毫秒
2025-07-17 13:40:33 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[792]毫秒
2025-07-17 13:41:11 [boundedElastic-172] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:41:11 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[419]毫秒
2025-07-17 13:41:11 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:41:11 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[206]毫秒
2025-07-17 13:41:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:41:17 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[377]毫秒
2025-07-17 13:43:55 [boundedElastic-173] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:43:55 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:43:55 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:43:55 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:43:55 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[142]毫秒
2025-07-17 13:43:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[371]毫秒
2025-07-17 13:43:55 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[628]毫秒
2025-07-17 13:43:55 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[703]毫秒
2025-07-17 13:44:10 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:44:10 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[336]毫秒
2025-07-17 13:44:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:44:11 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[198]毫秒
2025-07-17 13:44:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:44:11 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[280]毫秒
2025-07-17 13:44:42 [boundedElastic-174] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:44:42 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:44:42 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:44:42 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:44:42 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[76]毫秒
2025-07-17 13:44:42 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[358]毫秒
2025-07-17 13:44:42 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[584]毫秒
2025-07-17 13:44:42 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[745]毫秒
2025-07-17 13:45:24 [boundedElastic-141] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:45:25 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[398]毫秒
2025-07-17 13:45:25 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:45:25 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[200]毫秒
2025-07-17 13:45:25 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:45:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[294]毫秒
2025-07-17 13:48:22 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:48:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[397]毫秒
2025-07-17 13:48:22 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:48:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[199]毫秒
2025-07-17 13:48:23 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:48:23 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[269]毫秒
2025-07-17 13:49:44 [boundedElastic-183] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:49:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[427]毫秒
2025-07-17 13:49:45 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:49:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[248]毫秒
2025-07-17 13:49:46 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:49:46 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[307]毫秒
2025-07-17 13:50:07 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 13:50:08 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[788]毫秒
2025-07-17 13:50:08 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 13:50:08 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[113]毫秒
2025-07-17 13:50:08 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 13:50:08 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[51]毫秒
2025-07-17 13:50:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:50:09 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:50:09 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:50:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[32]毫秒
2025-07-17 13:50:09 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 13:50:09 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:50:09 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[172]毫秒
2025-07-17 13:50:09 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[417]毫秒
2025-07-17 13:50:09 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[789]毫秒
2025-07-17 13:50:10 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[844]毫秒
2025-07-17 13:50:42 [boundedElastic-183] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:50:42 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[422]毫秒
2025-07-17 13:50:42 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:50:43 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[375]毫秒
2025-07-17 13:50:43 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:50:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[365]毫秒
2025-07-17 13:51:30 [boundedElastic-179] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 13:51:31 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[745]毫秒
2025-07-17 13:51:31 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 13:51:31 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[105]毫秒
2025-07-17 13:51:31 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 13:51:31 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[39]毫秒
2025-07-17 13:51:32 [boundedElastic-179] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 13:51:32 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[43]毫秒
2025-07-17 13:51:32 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:51:32 [boundedElastic-179] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:51:32 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:51:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:51:32 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[50]毫秒
2025-07-17 13:51:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[357]毫秒
2025-07-17 13:51:33 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[505]毫秒
2025-07-17 13:51:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[746]毫秒
2025-07-17 13:51:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:51:35 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[344]毫秒
2025-07-17 13:51:35 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:51:36 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[272]毫秒
2025-07-17 13:51:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:51:36 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[285]毫秒
2025-07-17 13:53:16 [boundedElastic-181] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:53:16 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:53:16 [boundedElastic-183] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:53:16 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:53:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[79]毫秒
2025-07-17 13:53:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[396]毫秒
2025-07-17 13:53:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[597]毫秒
2025-07-17 13:53:17 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[801]毫秒
2025-07-17 13:53:18 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:53:19 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[287]毫秒
2025-07-17 13:53:19 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:53:19 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[195]毫秒
2025-07-17 13:53:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:53:20 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[326]毫秒
2025-07-17 13:53:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:53:34 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:53:34 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:53:34 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:53:34 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[76]毫秒
2025-07-17 13:53:34 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[343]毫秒
2025-07-17 13:53:34 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[512]毫秒
2025-07-17 13:53:35 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[637]毫秒
2025-07-17 13:53:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:53:45 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[365]毫秒
2025-07-17 13:53:45 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:53:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[196]毫秒
2025-07-17 13:53:46 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:53:46 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[303]毫秒
2025-07-17 13:54:11 [boundedElastic-182] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 13:54:11 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[790]毫秒
2025-07-17 13:54:12 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 13:54:12 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[100]毫秒
2025-07-17 13:54:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 13:54:12 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-17 13:54:12 [boundedElastic-182] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 13:54:12 [boundedElastic-182] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:54:12 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:54:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:54:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[33]毫秒
2025-07-17 13:54:12 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:54:12 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[28]毫秒
2025-07-17 13:54:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[419]毫秒
2025-07-17 13:54:13 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[563]毫秒
2025-07-17 13:54:13 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[686]毫秒
2025-07-17 13:54:14 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:54:14 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[286]毫秒
2025-07-17 13:54:14 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:54:15 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[194]毫秒
2025-07-17 13:54:15 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:54:15 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[280]毫秒
2025-07-17 13:54:32 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:54:32 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:54:32 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:54:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:54:32 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[75]毫秒
2025-07-17 13:54:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[384]毫秒
2025-07-17 13:54:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[541]毫秒
2025-07-17 13:54:33 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[691]毫秒
2025-07-17 13:54:34 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:54:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[392]毫秒
2025-07-17 13:54:35 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:54:35 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[227]毫秒
2025-07-17 13:54:35 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:54:35 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[289]毫秒
2025-07-17 13:55:05 [boundedElastic-187] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:55:05 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:55:05 [boundedElastic-190] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:55:05 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:55:05 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-17 13:55:05 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[395]毫秒
2025-07-17 13:55:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[581]毫秒
2025-07-17 13:55:06 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[792]毫秒
2025-07-17 13:55:12 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:55:12 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[376]毫秒
2025-07-17 13:55:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:55:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[202]毫秒
2025-07-17 13:55:13 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:55:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[336]毫秒
2025-07-17 13:55:36 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:55:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:55:36 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:55:36 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:55:36 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-17 13:55:37 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[400]毫秒
2025-07-17 13:55:37 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[578]毫秒
2025-07-17 13:55:37 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[781]毫秒
2025-07-17 13:55:38 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:55:38 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[285]毫秒
2025-07-17 13:55:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:55:39 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[221]毫秒
2025-07-17 13:55:39 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:55:39 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[268]毫秒
2025-07-17 13:56:07 [boundedElastic-182] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:56:07 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:56:07 [boundedElastic-185] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:56:07 [boundedElastic-187] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:56:07 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-17 13:56:08 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[364]毫秒
2025-07-17 13:56:08 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[492]毫秒
2025-07-17 13:56:08 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[729]毫秒
2025-07-17 13:56:12 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:56:12 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[273]毫秒
2025-07-17 13:56:12 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:56:12 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[200]毫秒
2025-07-17 13:56:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:56:13 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[374]毫秒
2025-07-17 13:57:50 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:57:50 [boundedElastic-187] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:57:50 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:57:50 [boundedElastic-187] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:57:50 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[129]毫秒
2025-07-17 13:57:50 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[483]毫秒
2025-07-17 13:57:50 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[612]毫秒
2025-07-17 13:57:51 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[825]毫秒
2025-07-17 13:57:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 13:57:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[276]毫秒
2025-07-17 13:57:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 13:57:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[202]毫秒
2025-07-17 13:57:53 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 13:57:53 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[309]毫秒
2025-07-17 13:58:43 [boundedElastic-181] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:58:43 [boundedElastic-178] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:58:43 [boundedElastic-184] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:58:43 [boundedElastic-191] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:58:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[84]毫秒
2025-07-17 13:58:43 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[401]毫秒
2025-07-17 13:58:44 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[738]毫秒
2025-07-17 13:58:44 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[917]毫秒
2025-07-17 13:59:09 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 13:59:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 13:59:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 13:59:09 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 13:59:09 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[106]毫秒
2025-07-17 13:59:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[596]毫秒
2025-07-17 13:59:10 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[866]毫秒
2025-07-17 13:59:10 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[951]毫秒
2025-07-17 14:00:44 [boundedElastic-195] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 14:00:45 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[480]毫秒
2025-07-17 14:00:45 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 14:00:45 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[187]毫秒
2025-07-17 14:00:46 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:00:46 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[285]毫秒
2025-07-17 14:01:02 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945385012276293634","grantType":"2","channels":[{"channelId":"1934638605565530114","channelAmount":100}]}]
2025-07-17 14:01:08 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/updateGrantType],耗时:[5972]毫秒
2025-07-17 14:02:51 [boundedElastic-187] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/process],参数类型[json],参数:[{"workorderId":"1945385011085111297","remark":"","details":[{"detailId":"1945385012276293634","grantType":"2","channelAmount":0}]}]
2025-07-17 14:02:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/process],耗时:[1359]毫秒
2025-07-17 14:02:53 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 14:02:53 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[825]毫秒
2025-07-17 14:02:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 14:02:58 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 14:02:58 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[333]毫秒
2025-07-17 14:02:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[630]毫秒
2025-07-17 14:02:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 14:03:00 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[289]毫秒
2025-07-17 14:03:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 14:03:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[222]毫秒
2025-07-17 14:03:00 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:03:01 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[287]毫秒
2025-07-17 14:03:13 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 14:03:14 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[337]毫秒
2025-07-17 14:03:14 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 14:03:14 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[186]毫秒
2025-07-17 14:03:14 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:03:14 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[244]毫秒
2025-07-17 14:04:33 [boundedElastic-199] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 14:04:34 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[430]毫秒
2025-07-17 14:04:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 14:04:34 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[204]毫秒
2025-07-17 14:04:34 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:04:35 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[275]毫秒
2025-07-17 14:09:19 [boundedElastic-184] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 14:09:19 [boundedElastic-202] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 14:09:19 [boundedElastic-201] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 14:09:19 [boundedElastic-201] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 14:09:19 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[142]毫秒
2025-07-17 14:09:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[337]毫秒
2025-07-17 14:09:19 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[566]毫秒
2025-07-17 14:09:19 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[709]毫秒
2025-07-17 14:09:22 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 14:09:22 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[270]毫秒
2025-07-17 14:09:22 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 14:09:22 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[194]毫秒
2025-07-17 14:09:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:09:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[317]毫秒
2025-07-17 14:09:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945371377764233218],无参数
2025-07-17 14:09:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945371377764233218],耗时:[368]毫秒
2025-07-17 14:09:29 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945371377764233218],无参数
2025-07-17 14:09:29 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945371377764233218],耗时:[230]毫秒
2025-07-17 14:09:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945371377764233218],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:09:30 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945371377764233218],耗时:[258]毫秒
2025-07-17 14:09:45 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945381029344243713","grantType":"2","channels":[{"channelId":"1934638490754846722","channelAmount":100}]}]
2025-07-17 14:09:48 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/updateGrantType],耗时:[3251]毫秒
2025-07-17 14:09:53 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/process],参数类型[json],参数:[{"workorderId":"1945371377764233218","remark":"","details":[{"detailId":"1945381029344243713","grantType":"2","channelAmount":0}]}]
2025-07-17 14:09:54 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/process],耗时:[1009]毫秒
2025-07-17 14:09:54 [boundedElastic-199] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 14:09:55 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[675]毫秒
2025-07-17 14:09:59 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945371377764233218],无参数
2025-07-17 14:09:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945371377764233218],耗时:[368]毫秒
2025-07-17 14:09:59 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945371377764233218],无参数
2025-07-17 14:09:59 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945371377764233218],耗时:[203]毫秒
2025-07-17 14:10:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945371377764233218],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:10:00 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945371377764233218],耗时:[257]毫秒
2025-07-17 14:10:14 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-17 14:10:14 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-17 14:10:15 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-17 14:31:37 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-17 14:31:37 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 25788 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-17 14:31:37 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-17 14:31:37 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-17 14:31:37 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-17 14:31:40 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-17 14:31:40 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-17 14:31:40 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-17 14:31:41 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-17 14:31:41 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-17 14:31:43 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway 192.168.124.3:8080 register finished
2025-07-17 14:31:44 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-17 14:31:44 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 8.877 seconds (process running for 9.692)
2025-07-17 14:31:44 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-17 14:31:44 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-17 14:32:30 [boundedElastic-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 14:32:32 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1857]毫秒
2025-07-17 14:32:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 14:32:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[116]毫秒
2025-07-17 14:32:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 14:32:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[47]毫秒
2025-07-17 14:32:33 [boundedElastic-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 14:32:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[331]毫秒
2025-07-17 14:32:37 [boundedElastic-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 14:32:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getTotalProperty],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 14:32:38 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["2"]}]
2025-07-17 14:32:38 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[42]毫秒
2025-07-17 14:32:38 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[1061]毫秒
2025-07-17 14:32:38 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 14:32:38 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/usdFlow/getStatisticsData],参数类型[param],参数:[{"transportId":["1934261959020875778"],"beginDate":["2024-07-17"],"endDate":["2025-07-17"],"dateRange":["last1year"]}]
2025-07-17 14:32:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getTotalProperty],耗时:[204]毫秒
2025-07-17 14:32:38 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdFlow/getStatisticsData],耗时:[210]毫秒
2025-07-17 14:32:39 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/usdConfig/getPropertyList],耗时:[391]毫秒
2025-07-17 14:32:47 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/rmb_purpose],无参数
2025-07-17 14:32:47 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 14:32:47 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/rmb_purpose],耗时:[59]毫秒
2025-07-17 14:32:47 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[415]毫秒
2025-07-17 14:32:47 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbFlow/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 14:32:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbFlow/list],耗时:[304]毫秒
2025-07-17 14:32:49 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 14:32:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 14:32:50 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-17 14:32:50 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 14:32:50 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 14:32:50 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[312]毫秒
2025-07-17 14:32:51 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[1210]毫秒
2025-07-17 14:32:51 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[1413]毫秒
2025-07-17 14:34:36 [boundedElastic-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945367202070360066],无参数
2025-07-17 14:34:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945367202070360066],耗时:[613]毫秒
2025-07-17 14:34:37 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945367202070360066],无参数
2025-07-17 14:34:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945367202070360066],耗时:[247]毫秒
2025-07-17 14:34:38 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945367202070360066],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:34:38 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945367202070360066],耗时:[383]毫秒
2025-07-17 14:38:45 [boundedElastic-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorderDetail/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945367202955358209","grantType":"1","channels":[{"channelId":"1934638490754846722","channelAmount":100}]}]
2025-07-17 14:38:45 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorderDetail/updateGrantType],耗时:[37]毫秒
2025-07-17 14:38:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorderDetail/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945367202955358209","grantType":"1","channels":[{"channelId":"1934638490754846722","channelAmount":100}]}]
2025-07-17 14:38:52 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorderDetail/updateGrantType],耗时:[25]毫秒
2025-07-17 14:43:45 [boundedElastic-21] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorderDetail/updateGrantType],参数类型[json],参数:[{"rmbdetailId":"1945367202955358209","grantType":"1","channels":[{"channelId":"1934638490754846722","channelAmount":100}]}]
2025-07-17 14:43:45 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorderDetail/updateGrantType],耗时:[442]毫秒
2025-07-17 14:44:21 [boundedElastic-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 14:44:21 [boundedElastic-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbConfig/getPropertyList],参数类型[param],参数:[{"transportId":["1934261959020875778"]}]
2025-07-17 14:44:21 [boundedElastic-17] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["rmbWorkorderTable"]}]
2025-07-17 14:44:21 [boundedElastic-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/channel/RmbList],无参数
2025-07-17 14:44:21 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[79]毫秒
2025-07-17 14:44:22 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbConfig/getPropertyList],耗时:[1017]毫秒
2025-07-17 14:44:23 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/channel/RmbList],耗时:[1837]毫秒
2025-07-17 14:44:23 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[2085]毫秒
2025-07-17 14:44:24 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],无参数
2025-07-17 14:44:24 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945385011085111297],耗时:[328]毫秒
2025-07-17 14:44:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],无参数
2025-07-17 14:44:25 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945385011085111297],耗时:[212]毫秒
2025-07-17 14:44:25 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:44:26 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945385011085111297],耗时:[344]毫秒
2025-07-17 14:44:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorderDetail/1945007984226656257],无参数
2025-07-17 14:44:29 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorderDetail/1945007984226656257],耗时:[426]毫秒
2025-07-17 14:44:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945007984226656257],无参数
2025-07-17 14:44:29 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/getApprovalProcess/1945007984226656257],耗时:[201]毫秒
2025-07-17 14:44:29 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/getExamineLog/1945007984226656257],参数类型[param],参数:[{"businessType":["1"]}]
2025-07-17 14:44:30 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/getExamineLog/1945007984226656257],耗时:[296]毫秒
2025-07-17 14:44:53 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorderDetail/updateAuditStatus],参数类型[json],参数:[{"rmbdetailId":"1945326156397449217","grantType":"1","channels":[{"channelId":"1934638490754846722","channelAmount":100}]}]
2025-07-17 14:44:56 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorderDetail/updateAuditStatus],耗时:[3476]毫秒
2025-07-17 14:45:03 [boundedElastic-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /salary/rmbWorkorder/process],参数类型[json],参数:[{"workorderId":"1945007984226656257","remark":"","details":[{"detailId":"1945326156397449217","grantType":"1","channelAmount":0}]}]
2025-07-17 14:45:04 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /salary/rmbWorkorder/process],耗时:[935]毫秒
2025-07-17 14:45:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/rmbWorkorder/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"transportId":["1934261959020875778"]}]
2025-07-17 14:45:04 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/rmbWorkorder/list],耗时:[654]毫秒
2025-07-17 15:27:22 [boundedElastic-59] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 15:27:22 [boundedElastic-55] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 15:27:23 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[1766]毫秒
2025-07-17 15:27:24 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1751]毫秒
2025-07-17 15:27:25 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 15:27:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 15:27:25 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-07-17 15:27:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[926]毫秒
2025-07-17 16:50:51 [boundedElastic-148] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 16:50:51 [boundedElastic-148] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 16:50:51 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[172]毫秒
2025-07-17 16:50:51 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[282]毫秒
2025-07-17 16:52:25 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 16:52:27 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[1757]毫秒
2025-07-17 16:52:27 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 16:52:28 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[675]毫秒
2025-07-17 16:52:28 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 16:52:28 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[126]毫秒
2025-07-17 16:52:29 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 16:52:29 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[54]毫秒
2025-07-17 16:52:29 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 16:52:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[8]毫秒
2025-07-17 16:52:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-17 16:52:33 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-17 16:52:33 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-17 16:52:33 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 16:52:33 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-17 16:52:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[52]毫秒
2025-07-17 16:52:33 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[51]毫秒
2025-07-17 16:52:33 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[63]毫秒
2025-07-17 16:52:34 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[524]毫秒
2025-07-17 16:52:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[2392]毫秒
2025-07-17 16:54:14 [boundedElastic-142] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 16:54:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[737]毫秒
2025-07-17 16:54:15 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 16:54:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[72]毫秒
2025-07-17 16:54:15 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 16:54:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-17 16:54:15 [boundedElastic-142] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 16:54:15 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[57]毫秒
2025-07-17 16:54:15 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-17 16:54:15 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-17 16:54:15 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-17 16:54:15 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-17 16:54:15 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 16:54:15 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[23]毫秒
2025-07-17 16:54:15 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[23]毫秒
2025-07-17 16:54:15 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[23]毫秒
2025-07-17 16:54:16 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[309]毫秒
2025-07-17 16:54:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[766]毫秒
2025-07-17 16:55:07 [boundedElastic-142] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/social/list],无参数
2025-07-17 16:55:07 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/profile],无参数
2025-07-17 16:55:07 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/online],无参数
2025-07-17 16:55:07 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/social/list],耗时:[85]毫秒
2025-07-17 16:55:07 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[196]毫秒
2025-07-17 16:55:07 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/online],耗时:[171]毫秒
2025-07-17 16:55:24 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/user/profile],参数类型[json],参数:[{"userId":"1934259119388987393","tenantId":"","deptId":"1934262875681820673","userName":"xhry","nickName":"青岛新海瑞洋海事服务有限公司","userType":"sys_user","email":"<EMAIL>","phonenumber":"15600000000","sex":"0","avatar":null,"status":"0","loginIp":"0:0:0:0:0:0:0:1","loginDate":"2025-07-17 16:52:27","remark":null,"createTime":"2025-06-15 22:38:06","deptName":"青岛新海瑞洋海事服务有限公司","companyName":"青岛新海瑞洋海事服务有限公司","roles":[{"roleId":"1913482625502998530","roleName":"航运公司管理员","roleKey":"hangyun_admin","roleSort":1,"dataScope":"5","menuCheckStrictly":null,"deptCheckStrictly":null,"status":"0","remark":null,"createTime":null,"flag":false,"superAdmin":false}],"roleIds":null,"postIds":]
2025-07-17 16:55:24 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/user/profile],耗时:[688]毫秒
2025-07-17 16:55:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 16:55:33 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[661]毫秒
2025-07-17 16:55:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 16:55:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[84]毫秒
2025-07-17 16:55:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 16:55:33 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-07-17 16:55:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/profile],无参数
2025-07-17 16:55:33 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/online],无参数
2025-07-17 16:55:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/social/list],无参数
2025-07-17 16:55:33 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/social/list],耗时:[46]毫秒
2025-07-17 16:55:33 [boundedElastic-157] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 16:55:33 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[35]毫秒
2025-07-17 16:55:33 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[145]毫秒
2025-07-17 16:55:33 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/online],耗时:[158]毫秒
2025-07-17 16:56:33 [boundedElastic-159] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 16:56:33 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[648]毫秒
2025-07-17 16:56:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 16:56:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[75]毫秒
2025-07-17 16:56:34 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 16:56:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-17 16:56:34 [boundedElastic-159] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 16:56:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/online],无参数
2025-07-17 16:56:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/social/list],无参数
2025-07-17 16:56:34 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/profile],无参数
2025-07-17 16:56:34 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-17 16:56:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/social/list],耗时:[57]毫秒
2025-07-17 16:56:34 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[116]毫秒
2025-07-17 16:56:34 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/online],耗时:[146]毫秒
2025-07-17 17:03:10 [boundedElastic-158] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 17:03:10 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[666]毫秒
2025-07-17 17:03:10 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 17:03:11 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[69]毫秒
2025-07-17 17:03:11 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 17:03:11 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[35]毫秒
2025-07-17 17:03:11 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /monitor/online],无参数
2025-07-17 17:03:11 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/social/list],无参数
2025-07-17 17:03:11 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/profile],无参数
2025-07-17 17:03:11 [boundedElastic-158] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:03:11 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/social/list],耗时:[27]毫秒
2025-07-17 17:03:11 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[99]毫秒
2025-07-17 17:03:11 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/profile],耗时:[148]毫秒
2025-07-17 17:03:11 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /monitor/online],耗时:[169]毫秒
2025-07-17 17:03:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 17:03:16 [boundedElastic-158] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 17:03:16 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[43]毫秒
2025-07-17 17:03:17 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[422]毫秒
2025-07-17 17:03:17 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 17:03:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:03:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-07-17 17:03:17 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[117]毫秒
2025-07-17 17:03:20 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 17:03:21 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[793]毫秒
2025-07-17 17:03:21 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 17:03:22 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[588]毫秒
2025-07-17 17:03:22 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 17:03:22 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[64]毫秒
2025-07-17 17:03:22 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 17:03:22 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-17 17:03:22 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:03:22 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-17 17:05:36 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-17 17:05:36 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 17:05:36 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 17:05:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[54]毫秒
2025-07-17 17:05:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[102]毫秒
2025-07-17 17:05:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[434]毫秒
2025-07-17 17:06:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 17:06:00 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[102]毫秒
2025-07-17 17:06:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-17 17:06:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[151]毫秒
2025-07-17 17:06:19 [boundedElastic-134] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 17:06:20 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[651]毫秒
2025-07-17 17:06:20 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 17:06:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[64]毫秒
2025-07-17 17:06:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 17:06:20 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[38]毫秒
2025-07-17 17:06:20 [boundedElastic-134] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:06:20 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-17 17:06:20 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 17:06:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[65]毫秒
2025-07-17 17:06:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-17 17:06:21 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[88]毫秒
2025-07-17 17:14:11 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 17:14:11 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 17:14:11 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[32]毫秒
2025-07-17 17:14:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[448]毫秒
2025-07-17 17:14:13 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 17:14:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:14:13 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-17 17:14:13 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[94]毫秒
2025-07-17 17:14:17 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:14:17 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 17:14:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-17 17:14:17 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[113]毫秒
2025-07-17 17:14:20 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 17:14:20 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:14:20 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-17 17:14:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[87]毫秒
2025-07-17 17:14:58 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 17:14:58 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[881]毫秒
2025-07-17 17:14:59 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 17:14:59 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[660]毫秒
2025-07-17 17:15:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 17:15:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[87]毫秒
2025-07-17 17:15:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 17:15:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[41]毫秒
2025-07-17 17:15:00 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:15:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-17 17:15:04 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-17 17:15:04 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-17 17:15:04 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[27]毫秒
2025-07-17 17:15:04 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[334]毫秒
2025-07-17 17:15:05 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:15:05 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 17:15:05 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-17 17:15:06 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[114]毫秒
2025-07-17 17:15:08 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:15:08 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-17 17:15:08 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-17 17:15:09 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[90]毫秒
2025-07-17 17:16:24 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-17 17:16:25 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[855]毫秒
2025-07-17 17:16:25 [boundedElastic-168] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-17 17:16:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[579]毫秒
2025-07-17 17:16:26 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 17:16:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[93]毫秒
2025-07-17 17:16:26 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-17 17:16:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-17 17:16:27 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-17 17:16:27 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-07-17 17:18:05 [boundedElastic-179] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-17 17:18:05 [boundedElastic-170] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-17 17:18:05 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[41]毫秒
2025-07-17 17:18:05 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[48]毫秒
2025-07-17 17:18:05 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-17 17:18:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-17 17:18:05 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 17:18:05 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[24]毫秒
2025-07-17 17:18:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[330]毫秒
2025-07-17 17:18:06 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-17 17:18:06 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-17 17:18:06 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[897]毫秒
2025-07-17 17:18:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[97]毫秒
2025-07-17 17:18:06 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[350]毫秒
2025-07-17 18:30:46 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-17 18:30:46 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-17 18:30:47 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
