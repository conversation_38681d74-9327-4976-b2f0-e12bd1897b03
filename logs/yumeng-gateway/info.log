2025-07-31 10:30:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 10:30:18 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 27508 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-31 10:30:18 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-31 10:30:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-31 10:30:18 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-31 10:30:21 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-31 10:30:21 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-31 10:30:22 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-31 10:30:22 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-31 10:30:22 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-31 11:35:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-07-31 11:35:29 [main] INFO  c.y.gateway.YumengGatewayApplication - Starting YumengGatewayApplication using Java 17.0.10 with PID 30532 (D:\project\yumeng\ship-Integrated-management-api\yumeng-gateway\target\classes started by qingyi in D:\project\yumeng)
2025-07-31 11:35:29 [main] INFO  c.y.gateway.YumengGatewayApplication - The following 1 profile is active: "dev"
2025-07-31 11:35:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=yumeng-gateway.yml, group=DEFAULT_GROUP] success
2025-07-31 11:35:29 [main] INFO  c.a.c.n.c.NacosConfigDataLoader - [Nacos Config] Load config[dataId=application-common.yml, group=DEFAULT_GROUP] success
2025-07-31 11:35:31 [main] INFO  c.y.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-07-31 11:35:31 [main] INFO  c.y.c.r.config.RedisConfiguration - 初始化 redis 配置
2025-07-31 11:35:31 [main] INFO  org.redisson.Version - Redisson 3.37.0
2025-07-31 11:35:32 [redisson-netty-3-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for ***********/***********:6379
2025-07-31 11:35:32 [redisson-netty-3-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for ***********/***********:6379
2025-07-31 11:35:35 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP yumeng-gateway 192.168.124.6:8080 register finished
2025-07-31 11:35:35 [main] INFO  c.a.c.n.d.NacosDiscoveryHeartBeatPublisher - Start nacos heartBeat task scheduler.
2025-07-31 11:35:35 [main] INFO  c.y.gateway.YumengGatewayApplication - Started YumengGatewayApplication in 8.363 seconds (process running for 9.32)
2025-07-31 11:35:35 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=application-common.yml, group=DEFAULT_GROUP
2025-07-31 11:35:35 [main] INFO  c.a.c.n.r.NacosContextRefresher - [Nacos Config] Listening config: dataId=yumeng-gateway.yml, group=DEFAULT_GROUP
2025-07-31 13:42:03 [boundedElastic-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-31 13:42:03 [boundedElastic-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:42:03 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[529]毫秒
2025-07-31 13:42:03 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[721]毫秒
2025-07-31 13:42:08 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-31 13:42:10 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[2323]毫秒
2025-07-31 13:42:11 [boundedElastic-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 13:42:12 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[1689]毫秒
2025-07-31 13:42:13 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 13:42:13 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[118]毫秒
2025-07-31 13:42:13 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 13:42:13 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[54]毫秒
2025-07-31 13:42:14 [boundedElastic-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:42:14 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-31 13:54:45 [boundedElastic-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 13:54:45 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 13:54:45 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[150]毫秒
2025-07-31 13:54:45 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[190]毫秒
2025-07-31 13:54:45 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 13:54:45 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[138]毫秒
2025-07-31 13:55:17 [boundedElastic-23] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-31 13:55:17 [boundedElastic-25] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-31 13:55:17 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[414]毫秒
2025-07-31 13:55:18 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-31 13:55:18 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:55:18 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1239]毫秒
2025-07-31 13:55:18 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-07-31 13:55:18 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[105]毫秒
2025-07-31 13:55:25 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-31 13:55:26 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[834]毫秒
2025-07-31 13:55:26 [boundedElastic-25] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 13:55:27 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[604]毫秒
2025-07-31 13:55:27 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 13:55:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[90]毫秒
2025-07-31 13:55:27 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 13:55:27 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[63]毫秒
2025-07-31 13:55:28 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:55:28 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-31 13:55:36 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:55:36 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-31 13:55:54 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-31 13:55:54 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-07-31 13:55:54 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-31 13:55:54 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[50]毫秒
2025-07-31 13:55:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[51]毫秒
2025-07-31 13:55:54 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[464]毫秒
2025-07-31 13:56:16 [boundedElastic-17] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-31 13:56:17 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[434]毫秒
2025-07-31 13:56:17 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/1950795468602445825],无参数
2025-07-31 13:56:17 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/1950795468602445825],耗时:[294]毫秒
2025-07-31 13:56:23 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/menu],参数类型[json],参数:[{"menuId":"1950795468602445825","menuName":"账户管理","parentId":1,"orderNum":15,"path":"/bankAccount","component":"/system/bankAccount/index","queryParam":null,"isFrame":"1","isCache":"0","menuType":"C","visible":"0","status":"0","perms":"system:bankAccount:list","icon":"","createDept":100,"remark":"","createTime":"2025-07-31 13:47:38","children":[]}]
2025-07-31 13:56:24 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/menu],耗时:[665]毫秒
2025-07-31 13:56:24 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-31 13:56:24 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[390]毫秒
2025-07-31 13:56:34 [boundedElastic-17] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:56:34 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-31 13:56:36 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 13:56:37 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[632]毫秒
2025-07-31 13:56:37 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 13:56:37 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[90]毫秒
2025-07-31 13:56:37 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 13:56:37 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[53]毫秒
2025-07-31 13:56:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:56:37 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-31 13:56:47 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:56:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-07-31 13:56:53 [boundedElastic-29] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-07-31 13:56:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[48]毫秒
2025-07-31 13:56:53 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-31 13:56:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-31 13:56:53 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[23]毫秒
2025-07-31 13:56:53 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[452]毫秒
2025-07-31 13:57:20 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-31 13:57:21 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[570]毫秒
2025-07-31 13:57:21 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/1950795468602445825],无参数
2025-07-31 13:57:22 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/1950795468602445825],耗时:[433]毫秒
2025-07-31 13:57:27 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[PUT /system/menu],参数类型[json],参数:[{"menuId":"1950795468602445825","menuName":"账户管理","parentId":1,"orderNum":15,"path":"/bankAccount","component":"system/bankAccount/index","queryParam":null,"isFrame":"1","isCache":"0","menuType":"C","visible":"0","status":"0","perms":"system:bankAccount:list","icon":"","createDept":100,"remark":"","createTime":"2025-07-31 13:47:38","children":[]}]
2025-07-31 13:57:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[PUT /system/menu],耗时:[490]毫秒
2025-07-31 13:57:28 [boundedElastic-30] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-31 13:57:28 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[372]毫秒
2025-07-31 13:57:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 13:57:34 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[655]毫秒
2025-07-31 13:57:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 13:57:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[92]毫秒
2025-07-31 13:57:34 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 13:57:34 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[50]毫秒
2025-07-31 13:57:35 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/list],无参数
2025-07-31 13:57:35 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_show_hide],无参数
2025-07-31 13:57:35 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-31 13:57:35 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_show_hide],耗时:[33]毫秒
2025-07-31 13:57:35 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[30]毫秒
2025-07-31 13:57:35 [boundedElastic-30] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:57:35 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-31 13:57:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/list],耗时:[345]毫秒
2025-07-31 13:59:46 [boundedElastic-26] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 13:59:46 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[638]毫秒
2025-07-31 13:59:47 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 13:59:47 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[88]毫秒
2025-07-31 13:59:47 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 13:59:47 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[56]毫秒
2025-07-31 13:59:48 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_currency_type],无参数
2025-07-31 13:59:48 [boundedElastic-26] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 13:59:48 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-31 13:59:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_currency_type],耗时:[97]毫秒
2025-07-31 14:01:27 [boundedElastic-26] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:01:29 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[2052]毫秒
2025-07-31 14:01:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:01:32 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[263]毫秒
2025-07-31 14:01:55 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:01:55 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[325]毫秒
2025-07-31 14:02:01 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:02:02 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[889]毫秒
2025-07-31 14:02:02 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:02:02 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[109]毫秒
2025-07-31 14:02:02 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:02:02 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[109]毫秒
2025-07-31 14:02:02 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:02:02 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-07-31 14:02:02 [boundedElastic-22] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:02:02 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_currency_type],无参数
2025-07-31 14:02:02 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_currency_type],耗时:[57]毫秒
2025-07-31 14:02:03 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[269]毫秒
2025-07-31 14:04:37 [boundedElastic-33] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:04:38 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[234]毫秒
2025-07-31 14:04:38 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:04:38 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[152]毫秒
2025-07-31 14:06:22 [boundedElastic-36] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/bankAccount],参数类型[json],参数:[{"alias":"huaqi-xhry","cardNumber":"*****************","cardHolder":"Zhang San","belongShip":"ship-01","depositBank":"青岛银行","depositBranch":"青岛支行","currencyType":"3","swiftCode":"cn123456789","notes":"这是测试银行卡"}]
2025-07-31 14:06:23 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/bankAccount],耗时:[344]毫秒
2025-07-31 14:10:53 [boundedElastic-43] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-31 14:10:53 [boundedElastic-32] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-31 14:10:53 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[38]毫秒
2025-07-31 14:10:54 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[645]毫秒
2025-07-31 14:10:55 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-31 14:10:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:10:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-07-31 14:10:55 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[106]毫秒
2025-07-31 14:10:57 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-31 14:10:58 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[896]毫秒
2025-07-31 14:10:58 [boundedElastic-43] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:10:59 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[722]毫秒
2025-07-31 14:10:59 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:10:59 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[126]毫秒
2025-07-31 14:10:59 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:10:59 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[37]毫秒
2025-07-31 14:11:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:11:00 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-07-31 14:17:58 [boundedElastic-51] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:17:59 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[822]毫秒
2025-07-31 14:17:59 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:17:59 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[137]毫秒
2025-07-31 14:17:59 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:17:59 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[47]毫秒
2025-07-31 14:17:59 [boundedElastic-51] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:17:59 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-31 14:18:05 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:18:06 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[694]毫秒
2025-07-31 14:18:06 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:18:06 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[106]毫秒
2025-07-31 14:18:06 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:18:06 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-31 14:18:06 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:18:06 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-31 14:18:15 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-31 14:18:15 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:18:15 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[56]毫秒
2025-07-31 14:18:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/list],耗时:[593]毫秒
2025-07-31 14:18:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 14:18:16 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:18:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[26]毫秒
2025-07-31 14:18:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[87]毫秒
2025-07-31 14:18:16 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 14:18:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[138]毫秒
2025-07-31 14:19:22 [boundedElastic-32] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:19:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[784]毫秒
2025-07-31 14:19:23 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:19:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[119]毫秒
2025-07-31 14:19:23 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:19:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[42]毫秒
2025-07-31 14:19:23 [boundedElastic-32] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:19:23 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-07-31 14:19:23 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 14:19:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[25]毫秒
2025-07-31 14:19:23 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:19:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[87]毫秒
2025-07-31 14:19:24 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 14:19:24 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[114]毫秒
2025-07-31 14:20:15 [boundedElastic-43] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:20:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[805]毫秒
2025-07-31 14:20:16 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:20:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[106]毫秒
2025-07-31 14:20:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:20:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-07-31 14:20:16 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 14:20:16 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:20:16 [boundedElastic-43] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:20:17 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[49]毫秒
2025-07-31 14:20:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-07-31 14:20:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[131]毫秒
2025-07-31 14:20:17 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 14:20:17 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[121]毫秒
2025-07-31 14:22:29 [boundedElastic-56] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-31 14:22:29 [boundedElastic-57] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-31 14:22:29 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[40]毫秒
2025-07-31 14:22:29 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[125]毫秒
2025-07-31 14:22:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-31 14:22:30 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:22:30 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[6]毫秒
2025-07-31 14:22:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[109]毫秒
2025-07-31 14:22:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-31 14:22:33 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[916]毫秒
2025-07-31 14:22:34 [boundedElastic-56] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:22:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[701]毫秒
2025-07-31 14:22:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:22:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[109]毫秒
2025-07-31 14:22:35 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:22:35 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[40]毫秒
2025-07-31 14:22:35 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:22:35 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-31 14:24:41 [boundedElastic-59] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:24:41 [boundedElastic-58] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_currency_type],无参数
2025-07-31 14:24:41 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_currency_type],耗时:[54]毫秒
2025-07-31 14:24:42 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[442]毫秒
2025-07-31 14:25:56 [boundedElastic-62] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/bankAccount],参数类型[json],参数:[{"alias":"huaqi1","cardNumber":"****************","cardHolder":"Zhang San","belongShip":"ship-01","depositBank":"中国农业银行","depositBranch":"青岛支行","currencyType":"3","swiftCode":"cn123456789","notes":""}]
2025-07-31 14:25:56 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/bankAccount],耗时:[220]毫秒
2025-07-31 14:26:01 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/bankAccount],参数类型[json],参数:[{"alias":"huaqi1","cardNumber":"****************","cardHolder":"Zhang San","belongShip":"ship-01","depositBank":"中国农业银行","depositBranch":"青岛支行","currencyType":"3","swiftCode":"cn123456789","notes":""}]
2025-07-31 14:26:01 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/bankAccount],耗时:[139]毫秒
2025-07-31 14:30:35 [boundedElastic-56] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:30:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[361]毫秒
2025-07-31 14:30:53 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:30:54 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[315]毫秒
2025-07-31 14:31:49 [boundedElastic-64] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:31:49 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[380]毫秒
2025-07-31 14:32:00 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:32:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[314]毫秒
2025-07-31 14:32:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:32:09 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[316]毫秒
2025-07-31 14:33:47 [boundedElastic-34] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:33:47 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[338]毫秒
2025-07-31 14:33:56 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:33:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[317]毫秒
2025-07-31 14:34:14 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:34:14 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[326]毫秒
2025-07-31 14:34:26 [boundedElastic-69] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:34:26 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[334]毫秒
2025-07-31 14:34:28 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:34:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 14:34:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[24]毫秒
2025-07-31 14:34:28 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[89]毫秒
2025-07-31 14:34:28 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 14:34:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[79]毫秒
2025-07-31 14:34:31 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/business_type],无参数
2025-07-31 14:34:31 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-31 14:34:31 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/business_type],耗时:[29]毫秒
2025-07-31 14:34:31 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[25]毫秒
2025-07-31 14:34:31 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],无参数
2025-07-31 14:34:33 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[2362]毫秒
2025-07-31 14:34:37 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_type],无参数
2025-07-31 14:34:37 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_status],无参数
2025-07-31 14:34:37 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_type],耗时:[46]毫秒
2025-07-31 14:34:37 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_status],耗时:[49]毫秒
2025-07-31 14:34:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:34:46 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[8240]毫秒
2025-07-31 14:35:06 [boundedElastic-60] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:35:06 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[325]毫秒
2025-07-31 14:35:55 [boundedElastic-68] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:35:55 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[680]毫秒
2025-07-31 14:35:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:35:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[96]毫秒
2025-07-31 14:35:56 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:35:56 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-31 14:35:56 [boundedElastic-68] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:35:56 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-31 14:35:58 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:35:59 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[605]毫秒
2025-07-31 14:35:59 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:35:59 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[99]毫秒
2025-07-31 14:35:59 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:35:59 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-07-31 14:35:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:35:59 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-31 14:36:08 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:36:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[686]毫秒
2025-07-31 14:36:09 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:36:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[77]毫秒
2025-07-31 14:36:09 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:36:09 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-31 14:36:10 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:36:10 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-31 14:36:10 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bank_account/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:36:10 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/bankaccount_status],无参数
2025-07-31 14:36:10 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bank_account/list],耗时:[9]毫秒
2025-07-31 14:36:10 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/bankaccount_status],耗时:[28]毫秒
2025-07-31 14:38:19 [boundedElastic-34] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:38:20 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[509]毫秒
2025-07-31 14:38:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:38:40 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[905]毫秒
2025-07-31 14:38:41 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:38:41 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[182]毫秒
2025-07-31 14:38:41 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:38:41 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-31 14:38:42 [boundedElastic-34] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:38:42 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:38:42 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/bankaccount_status],无参数
2025-07-31 14:38:42 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[82]毫秒
2025-07-31 14:38:42 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/bankaccount_status],耗时:[38]毫秒
2025-07-31 14:38:43 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[326]毫秒
2025-07-31 14:38:49 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:38:49 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[717]毫秒
2025-07-31 14:38:50 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:38:50 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[96]毫秒
2025-07-31 14:38:50 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:38:50 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[40]毫秒
2025-07-31 14:38:50 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:38:50 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-31 14:38:59 [boundedElastic-62] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:38:59 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[718]毫秒
2025-07-31 14:38:59 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:39:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[128]毫秒
2025-07-31 14:39:00 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:39:00 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-31 14:39:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:39:00 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-31 14:39:11 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 14:39:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[48]毫秒
2025-07-31 14:39:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:39:12 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[86]毫秒
2025-07-31 14:39:12 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 14:39:12 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[90]毫秒
2025-07-31 14:48:28 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:48:28 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[765]毫秒
2025-07-31 14:48:29 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:48:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[590]毫秒
2025-07-31 14:48:29 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:48:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[81]毫秒
2025-07-31 14:48:30 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:48:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[38]毫秒
2025-07-31 14:48:30 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:48:30 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[53]毫秒
2025-07-31 14:49:32 [boundedElastic-77] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:49:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[787]毫秒
2025-07-31 14:49:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:49:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[86]毫秒
2025-07-31 14:49:33 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:49:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[39]毫秒
2025-07-31 14:49:34 [boundedElastic-77] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:49:34 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[60]毫秒
2025-07-31 14:50:28 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:50:28 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[794]毫秒
2025-07-31 14:50:28 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:50:29 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[88]毫秒
2025-07-31 14:50:29 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:50:29 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-31 14:50:29 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:50:29 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-31 14:50:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:50:33 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[701]毫秒
2025-07-31 14:50:33 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:50:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[85]毫秒
2025-07-31 14:50:34 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:50:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[60]毫秒
2025-07-31 14:50:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:50:34 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-31 14:50:34 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:50:35 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[84]毫秒
2025-07-31 14:51:04 [boundedElastic-74] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:51:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[701]毫秒
2025-07-31 14:51:05 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:51:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[94]毫秒
2025-07-31 14:51:05 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:51:05 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[29]毫秒
2025-07-31 14:51:06 [boundedElastic-74] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:51:06 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[30]毫秒
2025-07-31 14:51:06 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:51:06 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[27]毫秒
2025-07-31 14:51:34 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:51:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:51:34 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[66]毫秒
2025-07-31 14:51:34 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[353]毫秒
2025-07-31 14:51:48 [boundedElastic-87] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:51:48 [boundedElastic-88] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:51:48 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-31 14:51:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[359]毫秒
2025-07-31 14:53:51 [boundedElastic-90] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:53:51 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[346]毫秒
2025-07-31 14:53:56 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:53:56 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 14:53:56 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[50]毫秒
2025-07-31 14:53:56 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[99]毫秒
2025-07-31 14:53:56 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 14:53:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[89]毫秒
2025-07-31 14:54:35 [boundedElastic-85] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:54:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[700]毫秒
2025-07-31 14:54:35 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:54:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[88]毫秒
2025-07-31 14:54:36 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:54:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[29]毫秒
2025-07-31 14:54:36 [boundedElastic-85] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:54:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-31 14:54:36 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:54:36 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:54:36 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[45]毫秒
2025-07-31 14:54:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[262]毫秒
2025-07-31 14:54:48 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],无参数
2025-07-31 14:54:48 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[74]毫秒
2025-07-31 14:54:55 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],无参数
2025-07-31 14:54:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[25]毫秒
2025-07-31 14:55:48 [boundedElastic-76] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:55:48 [boundedElastic-85] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:55:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-31 14:55:48 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[316]毫秒
2025-07-31 14:55:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:55:54 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:55:54 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[67]毫秒
2025-07-31 14:55:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[299]毫秒
2025-07-31 14:56:04 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:56:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[314]毫秒
2025-07-31 14:56:15 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],无参数
2025-07-31 14:56:15 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[29]毫秒
2025-07-31 14:56:17 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:56:17 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:56:17 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[46]毫秒
2025-07-31 14:56:17 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[283]毫秒
2025-07-31 14:57:14 [boundedElastic-83] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],无参数
2025-07-31 14:57:14 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[34]毫秒
2025-07-31 14:58:01 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:58:01 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-31 14:58:13 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"align":"center","width":150,"visible":true,"sortOrder":0},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"align":"center","width":180,"visible":true,"sortOrder":1},{"columnName":"cardHolder","columnLabel":"持卡人","align":"center","width":120,"fixed":false,"visible":true,"sortOrder":2},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"align":"center","width":150,"visible":true,"sortOrder":3},{"columnName":"depositBranch","columnLabel":"开户支行","align":"center","width":200,"fixed":false,"visible":false,"sortOrder":999},{"columnName":"currencyType","columnLabel":"币种类]
2025-07-31 14:58:14 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1137]毫秒
2025-07-31 14:58:19 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:58:19 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[110]毫秒
2025-07-31 14:58:29 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:58:30 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[666]毫秒
2025-07-31 14:58:30 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:58:30 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[63]毫秒
2025-07-31 14:58:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:58:30 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-07-31 14:58:31 [boundedElastic-92] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:58:31 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-31 14:58:31 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:58:31 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:58:31 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[61]毫秒
2025-07-31 14:58:31 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[231]毫秒
2025-07-31 14:58:41 [boundedElastic-92] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 14:58:42 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[639]毫秒
2025-07-31 14:58:42 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 14:58:42 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[60]毫秒
2025-07-31 14:58:42 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 14:58:42 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-07-31 14:58:42 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 14:58:42 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-31 14:58:42 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 14:58:42 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:58:42 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[56]毫秒
2025-07-31 14:58:43 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[229]毫秒
2025-07-31 14:59:50 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 14:59:50 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[117]毫秒
2025-07-31 15:00:06 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:00:07 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[111]毫秒
2025-07-31 15:00:21 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950813231195893761","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":3,"width":120,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"swiftCode","columnLabel":"Swift Code","fixed":false,"visible":true,"sortOrder":5,"width":120,]
2025-07-31 15:00:22 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1121]毫秒
2025-07-31 15:00:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:00:23 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[64]毫秒
2025-07-31 15:00:25 [boundedElastic-83] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:00:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[640]毫秒
2025-07-31 15:00:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:00:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[81]毫秒
2025-07-31 15:00:26 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:00:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[24]毫秒
2025-07-31 15:00:26 [boundedElastic-83] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:00:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-31 15:00:26 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:00:26 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:00:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[73]毫秒
2025-07-31 15:00:27 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[259]毫秒
2025-07-31 15:00:36 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950813770742771713","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":3,"width":120,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"swiftCode","columnLabel":"Swift Code","fixed":false,"visible":true,"sortOrder":5,"width":120,]
2025-07-31 15:00:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1089]毫秒
2025-07-31 15:00:37 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:00:37 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[43]毫秒
2025-07-31 15:00:40 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:00:40 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[581]毫秒
2025-07-31 15:00:40 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:00:40 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[60]毫秒
2025-07-31 15:00:41 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:00:41 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-07-31 15:00:41 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:00:41 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-31 15:00:41 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:00:41 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:00:41 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-31 15:00:41 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[230]毫秒
2025-07-31 15:01:28 [boundedElastic-94] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950813832034136066","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":3,"width":120,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"swiftCode","columnLabel":"Swift Code","fixed":false,"visible":true,"sortOrder":5,"width":120,]
2025-07-31 15:01:29 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1107]毫秒
2025-07-31 15:01:29 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:01:29 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[61]毫秒
2025-07-31 15:01:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950814049420718082","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":3,"width":120,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"swiftCode","columnLabel":"Swift Code","fixed":false,"visible":true,"sortOrder":6,"width":120,]
2025-07-31 15:01:38 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1064]毫秒
2025-07-31 15:01:38 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:01:38 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-31 15:01:46 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950814087442083841","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed":false,"visible":true,"sortOrder":5,"width]
2025-07-31 15:01:47 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1081]毫秒
2025-07-31 15:01:47 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:01:47 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-31 15:01:49 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:01:49 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[607]毫秒
2025-07-31 15:01:49 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:01:49 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[84]毫秒
2025-07-31 15:01:49 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:01:49 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-07-31 15:01:50 [boundedElastic-82] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:01:50 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-31 15:01:50 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:01:50 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:01:50 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[43]毫秒
2025-07-31 15:01:50 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[235]毫秒
2025-07-31 15:01:55 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950814124960133122","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed":false,"visible":true,"sortOrder":5,"width]
2025-07-31 15:01:56 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1030]毫秒
2025-07-31 15:01:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:01:57 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[42]毫秒
2025-07-31 15:01:59 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:02:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[595]毫秒
2025-07-31 15:02:00 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:02:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[93]毫秒
2025-07-31 15:02:00 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:02:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[27]毫秒
2025-07-31 15:02:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:02:00 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[22]毫秒
2025-07-31 15:02:00 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:02:00 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:02:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-31 15:02:00 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[264]毫秒
2025-07-31 15:02:03 [boundedElastic-82] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950814164378202114","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed":false,"visible":true,"sortOrder":5,"width]
2025-07-31 15:02:04 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1095]毫秒
2025-07-31 15:02:05 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:02:05 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-31 15:02:07 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:02:08 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[576]毫秒
2025-07-31 15:02:08 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:02:08 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[62]毫秒
2025-07-31 15:02:08 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:02:08 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-07-31 15:02:08 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:02:08 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-31 15:02:08 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:02:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:02:08 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-31 15:02:08 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[225]毫秒
2025-07-31 15:03:32 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:03:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[83]毫秒
2025-07-31 15:03:44 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:03:44 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[88]毫秒
2025-07-31 15:03:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950814197827776513","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:03:49 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1030]毫秒
2025-07-31 15:03:49 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:03:49 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-31 15:04:29 [boundedElastic-94] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:04:29 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-31 15:04:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:04:37 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-31 15:04:52 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:04:52 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[80]毫秒
2025-07-31 15:04:57 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950814636434534401","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:04:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1070]毫秒
2025-07-31 15:04:59 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:04:59 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[46]毫秒
2025-07-31 15:05:02 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:05:02 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[619]毫秒
2025-07-31 15:05:03 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:05:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[108]毫秒
2025-07-31 15:05:03 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:05:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-31 15:05:03 [boundedElastic-98] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:05:03 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-31 15:05:03 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:05:03 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:05:03 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[71]毫秒
2025-07-31 15:05:04 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[309]毫秒
2025-07-31 15:05:06 [boundedElastic-98] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950814929008209922","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:05:07 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1000]毫秒
2025-07-31 15:05:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:05:08 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[67]毫秒
2025-07-31 15:06:13 [boundedElastic-101] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:06:13 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[85]毫秒
2025-07-31 15:06:23 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:06:23 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[84]毫秒
2025-07-31 15:06:35 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950814965469294593","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:06:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1064]毫秒
2025-07-31 15:06:36 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:06:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[44]毫秒
2025-07-31 15:06:44 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:06:44 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[81]毫秒
2025-07-31 15:06:48 [boundedElastic-65] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950815338242256898","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:06:49 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1005]毫秒
2025-07-31 15:06:49 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:06:49 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[66]毫秒
2025-07-31 15:06:51 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:06:51 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[60]毫秒
2025-07-31 15:06:58 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:06:59 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[85]毫秒
2025-07-31 15:07:06 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:07:06 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[88]毫秒
2025-07-31 15:07:20 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950815392831123458","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:07:21 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1100]毫秒
2025-07-31 15:07:21 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:07:21 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[43]毫秒
2025-07-31 15:07:32 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:07:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[732]毫秒
2025-07-31 15:07:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:07:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[80]毫秒
2025-07-31 15:07:33 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:07:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[27]毫秒
2025-07-31 15:07:34 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:07:34 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-31 15:07:34 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:07:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:07:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[70]毫秒
2025-07-31 15:07:34 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[251]毫秒
2025-07-31 15:08:30 [boundedElastic-86] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:08:30 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[84]毫秒
2025-07-31 15:08:51 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950815526621032449","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:08:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1037]毫秒
2025-07-31 15:08:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:08:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-31 15:10:16 [boundedElastic-86] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:10:16 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[111]毫秒
2025-07-31 15:10:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:10:26 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[94]毫秒
2025-07-31 15:10:33 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950815909393215489","configName":"bankAccountTable","columns":"[{\"columnName\":\"cardHolder\",\"columnLabel\":\"持卡人\",\"fixed\":false,\"visible\":true,\"sortOrder\":1,\"width\":120,\"align\":\"center\",\"sortable\":true},{\"columnName\":\"belongName\",\"columnLabel\":\"账户名称\",\"fixed\":true,\"visible\":true,\"sortOrder\":2,\"width\":150,\"align\":\"center\",\"sortable\":true},{\"columnName\":\"cardNumber\",\"columnLabel\":\"银行卡号\",\"fixed\":true,\"visible\":true,\"sortOrder\":3,\"width\":180,\"align\":\"center\",\"sortable\":true},{\"columnName\":\"depositBank\",\"columnLabel\":\"开户银行\",\"fixed\":true,\"visible\":true,\"sortOrder\":4,\"width\":150,\"align\":\"cent]
2025-07-31 15:10:33 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[39]毫秒
2025-07-31 15:10:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:10:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[86]毫秒
2025-07-31 15:21:07 [boundedElastic-119] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:21:08 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[147]毫秒
2025-07-31 15:21:17 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:21:17 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[150]毫秒
2025-07-31 15:27:52 [boundedElastic-116] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:27:53 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[728]毫秒
2025-07-31 15:27:53 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:27:53 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[85]毫秒
2025-07-31 15:27:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:27:53 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[37]毫秒
2025-07-31 15:27:54 [boundedElastic-116] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:27:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[64]毫秒
2025-07-31 15:27:54 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:27:54 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:27:54 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-31 15:27:54 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[333]毫秒
2025-07-31 15:28:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950815909393215489","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:28:04 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1067]毫秒
2025-07-31 15:28:04 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:28:04 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[55]毫秒
2025-07-31 15:29:16 [boundedElastic-117] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:29:16 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[196]毫秒
2025-07-31 15:29:36 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:29:36 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[150]毫秒
2025-07-31 15:30:12 [boundedElastic-123] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950820740312870914","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:30:13 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1144]毫秒
2025-07-31 15:30:13 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:30:13 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[54]毫秒
2025-07-31 15:30:21 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:30:21 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[743]毫秒
2025-07-31 15:30:21 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:30:22 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[117]毫秒
2025-07-31 15:30:22 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:30:22 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[37]毫秒
2025-07-31 15:30:22 [boundedElastic-84] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:30:22 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-07-31 15:30:22 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:30:22 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:30:22 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[87]毫秒
2025-07-31 15:30:23 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[267]毫秒
2025-07-31 15:30:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950821280002355202","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:30:29 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1164]毫秒
2025-07-31 15:30:29 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:30:29 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[55]毫秒
2025-07-31 15:31:06 [boundedElastic-109] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:31:06 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[119]毫秒
2025-07-31 15:31:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:31:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[99]毫秒
2025-07-31 15:31:37 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:31:37 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-31 15:33:22 [boundedElastic-117] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:33:22 [boundedElastic-120] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 15:33:22 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[116]毫秒
2025-07-31 15:33:22 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[229]毫秒
2025-07-31 15:33:23 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 15:33:23 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[117]毫秒
2025-07-31 15:33:25 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:33:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[687]毫秒
2025-07-31 15:33:26 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:33:26 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[106]毫秒
2025-07-31 15:33:26 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:33:26 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-31 15:33:27 [boundedElastic-120] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:33:27 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[66]毫秒
2025-07-31 15:33:27 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:33:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:33:27 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[103]毫秒
2025-07-31 15:33:27 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[275]毫秒
2025-07-31 15:34:02 [boundedElastic-111] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:34:03 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[215]毫秒
2025-07-31 15:34:18 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950821348033966082","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:34:19 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1127]毫秒
2025-07-31 15:34:22 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950821348033966082","configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":1,"width":120,"align":"center","sortable":true},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":2,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":3,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:34:24 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1119]毫秒
2025-07-31 15:34:27 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:34:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[694]毫秒
2025-07-31 15:34:28 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:34:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[113]毫秒
2025-07-31 15:34:28 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:34:28 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-31 15:34:29 [boundedElastic-127] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:34:29 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[30]毫秒
2025-07-31 15:34:29 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:34:29 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:34:29 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[96]毫秒
2025-07-31 15:34:29 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[308]毫秒
2025-07-31 15:35:05 [boundedElastic-128] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:35:06 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[368]毫秒
2025-07-31 15:35:08 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center"},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center"},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":3,"width":120,"align":"center"},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center"},{"columnName":"depositBranch","columnLabel":"开户支行","fixed":false,"visible":true,"sortOrder":5,"width":200,"align":"center"},{"columnName":"currencyT]
2025-07-31 15:35:09 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[887]毫秒
2025-07-31 15:35:14 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:35:15 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[765]毫秒
2025-07-31 15:35:15 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:35:15 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[104]毫秒
2025-07-31 15:35:15 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:35:15 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-31 15:35:16 [boundedElastic-93] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:35:16 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 15:35:16 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:35:16 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[89]毫秒
2025-07-31 15:35:16 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[83]毫秒
2025-07-31 15:35:16 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[352]毫秒
2025-07-31 15:35:33 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950822523953229825","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":3,"width":120,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:35:35 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1251]毫秒
2025-07-31 15:35:45 [boundedElastic-93] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950822523953229825","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":3,"width":120,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":4,"width":150,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 15:35:46 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1134]毫秒
2025-07-31 15:36:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:36:20 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[712]毫秒
2025-07-31 15:36:20 [boundedElastic-128] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:36:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[177]毫秒
2025-07-31 15:36:21 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:36:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[42]毫秒
2025-07-31 15:36:21 [boundedElastic-128] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:36:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[31]毫秒
2025-07-31 15:50:34 [boundedElastic-126] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:50:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[867]毫秒
2025-07-31 15:50:35 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:50:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[110]毫秒
2025-07-31 15:50:35 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:50:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[35]毫秒
2025-07-31 15:50:36 [boundedElastic-126] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:50:36 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[51]毫秒
2025-07-31 15:51:13 [boundedElastic-133] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:51:13 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[726]毫秒
2025-07-31 15:51:13 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:51:14 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[106]毫秒
2025-07-31 15:51:14 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:51:14 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[44]毫秒
2025-07-31 15:51:14 [boundedElastic-133] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:51:14 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-31 15:52:25 [boundedElastic-140] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:52:25 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[724]毫秒
2025-07-31 15:52:25 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:52:26 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[104]毫秒
2025-07-31 15:52:26 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:52:26 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[34]毫秒
2025-07-31 15:52:26 [boundedElastic-140] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:52:26 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-31 15:53:20 [boundedElastic-126] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:53:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[718]毫秒
2025-07-31 15:53:20 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:53:21 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[108]毫秒
2025-07-31 15:53:21 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:53:21 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-31 15:53:21 [boundedElastic-126] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:53:21 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-31 15:53:22 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 15:53:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[616]毫秒
2025-07-31 15:53:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 15:53:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[110]毫秒
2025-07-31 15:53:23 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 15:53:23 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[39]毫秒
2025-07-31 15:53:24 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 15:53:24 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-07-31 15:53:24 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:53:24 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-31 15:53:24 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[38]毫秒
2025-07-31 15:53:24 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[409]毫秒
2025-07-31 15:53:29 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:53:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[345]毫秒
2025-07-31 15:53:57 [boundedElastic-133] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 15:53:57 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[375]毫秒
2025-07-31 15:54:03 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"currencyType":["3"]}]
2025-07-31 15:54:03 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[390]毫秒
2025-07-31 16:07:28 [boundedElastic-153] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:07:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[981]毫秒
2025-07-31 16:07:29 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:07:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[90]毫秒
2025-07-31 16:07:30 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:07:30 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[37]毫秒
2025-07-31 16:07:30 [boundedElastic-153] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:07:30 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[143]毫秒
2025-07-31 16:07:37 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:07:38 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[648]毫秒
2025-07-31 16:07:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:07:38 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[76]毫秒
2025-07-31 16:07:38 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:07:38 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[25]毫秒
2025-07-31 16:07:39 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:07:39 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-31 16:07:52 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:07:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[651]毫秒
2025-07-31 16:07:53 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:07:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[78]毫秒
2025-07-31 16:07:53 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:07:53 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[24]毫秒
2025-07-31 16:07:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:07:54 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-31 16:07:54 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:07:54 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[359]毫秒
2025-07-31 16:08:01 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:08:01 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-31 16:08:01 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:08:01 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[52]毫秒
2025-07-31 16:08:01 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[142]毫秒
2025-07-31 16:08:02 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[671]毫秒
2025-07-31 16:08:03 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-31 16:08:03 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:08:03 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[492]毫秒
2025-07-31 16:08:03 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[548]毫秒
2025-07-31 16:08:04 [boundedElastic-145] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_status],无参数
2025-07-31 16:08:04 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_status],耗时:[25]毫秒
2025-07-31 16:08:04 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_type],无参数
2025-07-31 16:08:04 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:08:04 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_type],耗时:[35]毫秒
2025-07-31 16:08:04 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[255]毫秒
2025-07-31 16:08:39 [boundedElastic-139] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:08:40 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[697]毫秒
2025-07-31 16:08:40 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:08:40 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[92]毫秒
2025-07-31 16:08:40 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:08:40 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-31 16:08:40 [boundedElastic-139] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:08:40 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-31 16:11:38 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:11:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[732]毫秒
2025-07-31 16:11:38 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:11:38 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[95]毫秒
2025-07-31 16:11:39 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:11:39 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-31 16:11:39 [boundedElastic-151] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:11:39 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-31 16:14:46 [boundedElastic-153] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 16:14:46 [boundedElastic-156] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:14:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[62]毫秒
2025-07-31 16:14:47 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[155]毫秒
2025-07-31 16:14:47 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 16:14:47 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[99]毫秒
2025-07-31 16:14:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/business_type],无参数
2025-07-31 16:14:48 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],无参数
2025-07-31 16:14:48 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-31 16:14:48 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/business_type],耗时:[25]毫秒
2025-07-31 16:14:48 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[27]毫秒
2025-07-31 16:14:49 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[1016]毫秒
2025-07-31 16:14:50 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_type],无参数
2025-07-31 16:14:50 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_status],无参数
2025-07-31 16:14:50 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_status],耗时:[49]毫秒
2025-07-31 16:14:50 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_type],耗时:[57]毫秒
2025-07-31 16:14:51 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:14:51 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[266]毫秒
2025-07-31 16:14:52 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-31 16:14:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/post/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:14:52 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[392]毫秒
2025-07-31 16:14:52 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/post/list],耗时:[359]毫秒
2025-07-31 16:14:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-31 16:14:54 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[98]毫秒
2025-07-31 16:14:55 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-31 16:14:55 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[55]毫秒
2025-07-31 16:14:55 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdWorkorderTable"]}]
2025-07-31 16:14:55 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[48]毫秒
2025-07-31 16:14:56 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/listByType],参数类型[param],参数:[{"type":["4"],"isDistributors":["1"]}]
2025-07-31 16:14:56 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/listByType],耗时:[67]毫秒
2025-07-31 16:14:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["usdReconciliationTable"]}]
2025-07-31 16:14:57 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[84]毫秒
2025-07-31 16:17:00 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /warehouse/warehouse/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:17:02 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderInfo/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:17:02 [boundedElastic-156] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_todo_status],无参数
2025-07-31 16:17:02 [boundedElastic-150] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/examine_status],无参数
2025-07-31 16:17:02 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_todo_status],耗时:[34]毫秒
2025-07-31 16:17:02 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/examine_status],耗时:[26]毫秒
2025-07-31 16:17:02 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /warehouse/warehouse/list],耗时:[2704]毫秒
2025-07-31 16:17:03 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderInfo/list],耗时:[1553]毫秒
2025-07-31 16:17:15 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 16:17:15 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:17:15 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[51]毫秒
2025-07-31 16:17:15 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[134]毫秒
2025-07-31 16:17:16 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 16:17:16 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[90]毫秒
2025-07-31 16:18:01 [boundedElastic-149] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:18:01 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[670]毫秒
2025-07-31 16:18:01 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:18:01 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[68]毫秒
2025-07-31 16:18:01 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:18:02 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[31]毫秒
2025-07-31 16:18:02 [boundedElastic-149] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:18:02 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[48]毫秒
2025-07-31 16:19:23 [boundedElastic-161] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:19:23 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[680]毫秒
2025-07-31 16:19:23 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:19:23 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[70]毫秒
2025-07-31 16:19:23 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:19:24 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-31 16:19:24 [boundedElastic-161] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:19:24 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[29]毫秒
2025-07-31 16:20:01 [boundedElastic-153] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:20:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[707]毫秒
2025-07-31 16:20:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:20:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[102]毫秒
2025-07-31 16:20:02 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:20:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[49]毫秒
2025-07-31 16:20:02 [boundedElastic-153] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:20:02 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-31 16:20:46 [boundedElastic-149] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:20:46 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[750]毫秒
2025-07-31 16:20:48 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:20:49 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[632]毫秒
2025-07-31 16:20:49 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:20:49 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[90]毫秒
2025-07-31 16:20:49 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:20:49 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[28]毫秒
2025-07-31 16:20:50 [boundedElastic-154] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:20:50 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[30]毫秒
2025-07-31 16:21:01 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:21:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[679]毫秒
2025-07-31 16:21:02 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:21:02 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[103]毫秒
2025-07-31 16:21:02 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:21:03 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-31 16:21:03 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:21:03 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[102]毫秒
2025-07-31 16:22:35 [boundedElastic-166] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:22:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[804]毫秒
2025-07-31 16:22:36 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:22:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[103]毫秒
2025-07-31 16:22:36 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:22:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[36]毫秒
2025-07-31 16:22:43 [boundedElastic-166] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:22:43 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[40]毫秒
2025-07-31 16:22:43 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:22:43 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:22:43 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[94]毫秒
2025-07-31 16:22:43 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[338]毫秒
2025-07-31 16:22:50 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:22:50 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[74]毫秒
2025-07-31 16:23:19 [boundedElastic-153] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:23:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[710]毫秒
2025-07-31 16:23:20 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:23:20 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[97]毫秒
2025-07-31 16:23:20 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:23:20 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[43]毫秒
2025-07-31 16:23:21 [boundedElastic-153] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:23:21 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[26]毫秒
2025-07-31 16:23:21 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:23:21 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:23:21 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[25]毫秒
2025-07-31 16:23:21 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[235]毫秒
2025-07-31 16:23:26 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:23:26 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[67]毫秒
2025-07-31 16:23:31 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"configName":"bankAccountTable","columns":[{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":4,"width":120,"align":"center"},{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center"},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center"},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":3,"width":150,"align":"center"},{"columnName":"depositBranch","columnLabel":"开户支行","fixed":false,"visible":true,"sortOrder":5,"width":200,"align":"center"},{"columnName":"swiftCode","columnLabel":"Swift Code","fixed":false,]
2025-07-31 16:23:32 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[982]毫秒
2025-07-31 16:23:32 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:23:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[81]毫秒
2025-07-31 16:23:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950834697727651841","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":3,"width":150,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":4,"width":120,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed":false,"visible":true,"sortOrder":5,"width]
2025-07-31 16:23:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[995]毫秒
2025-07-31 16:23:36 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:23:37 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[53]毫秒
2025-07-31 16:23:38 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950834717302468610","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":3,"width":150,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":4,"width":120,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed":false,"visible":true,"sortOrder":5,"width]
2025-07-31 16:23:39 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[959]毫秒
2025-07-31 16:23:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:23:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[50]毫秒
2025-07-31 16:23:47 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950834730334171137","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":180,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":3,"width":150,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":4,"width":120,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed]
2025-07-31 16:23:48 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1042]毫秒
2025-07-31 16:23:48 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:23:48 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-31 16:24:11 [boundedElastic-164] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"currencyType":["2"],"isDefault":["2"]}]
2025-07-31 16:24:12 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[362]毫秒
2025-07-31 16:27:44 [boundedElastic-166] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/bankAccount],参数类型[json],参数:[{"belongName":"张三","cardNumber":"****************","cardHolder":"ZHANG SAN","depositBank":"青岛银行","depositBranch":"青岛银行","currencyType":"2","swiftCode":"HLFXGB21K92","alias":"花旗银行","belongShip":"ship-01","reservedPhone":"***********","isDefault":"0","notes":"","accountType":"1","ifBelive":"1","isMyself":"2"}]
2025-07-31 16:27:45 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/bankAccount],耗时:[1122]毫秒
2025-07-31 16:27:45 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"currencyType":["2"],"isDefault":["2"]}]
2025-07-31 16:27:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[235]毫秒
2025-07-31 16:27:48 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"currencyType":["2"],"isDefault":["2"]}]
2025-07-31 16:27:48 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[257]毫秒
2025-07-31 16:28:11 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"],"currencyType":["2"],"isDefault":["2"]}]
2025-07-31 16:28:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[323]毫秒
2025-07-31 16:28:18 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:28:18 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[387]毫秒
2025-07-31 16:28:24 [boundedElastic-176] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950834764278673410","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":285,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":3,"width":150,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":4,"width":120,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed":false,"visible":true,"sortOrder":5,"width]
2025-07-31 16:28:25 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1038]毫秒
2025-07-31 16:28:26 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:28:26 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[52]毫秒
2025-07-31 16:28:28 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:28:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[586]毫秒
2025-07-31 16:28:29 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:28:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[66]毫秒
2025-07-31 16:28:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:28:29 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[58]毫秒
2025-07-31 16:28:30 [boundedElastic-176] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:28:30 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[27]毫秒
2025-07-31 16:28:30 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:28:30 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:28:30 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[46]毫秒
2025-07-31 16:28:30 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[312]毫秒
2025-07-31 16:28:37 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/tableConfig/saveConfig],参数类型[json],参数:[{"id":"1950835928508411906","configName":"bankAccountTable","columns":[{"columnName":"belongName","columnLabel":"账户名称","fixed":true,"visible":true,"sortOrder":1,"width":150,"align":"center","sortable":true},{"columnName":"cardNumber","columnLabel":"银行卡号","fixed":true,"visible":true,"sortOrder":2,"width":285,"align":"center","sortable":true},{"columnName":"depositBank","columnLabel":"开户银行","fixed":true,"visible":true,"sortOrder":3,"width":150,"align":"center","sortable":true},{"columnName":"cardHolder","columnLabel":"持卡人","fixed":false,"visible":true,"sortOrder":4,"width":120,"align":"center","sortable":true},{"columnName":"depositBranch","columnLabel":"开户支行","fixed":false,"visible":true,"sortOrder":5,"width]
2025-07-31 16:28:38 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/tableConfig/saveConfig],耗时:[1013]毫秒
2025-07-31 16:28:38 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:28:38 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-31 16:30:28 [boundedElastic-178] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:30:28 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:30:28 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[100]毫秒
2025-07-31 16:30:28 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[363]毫秒
2025-07-31 16:30:55 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:30:55 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:30:55 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[89]毫秒
2025-07-31 16:30:55 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[344]毫秒
2025-07-31 16:31:00 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:31:00 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-31 16:32:24 [boundedElastic-176] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:32:24 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[95]毫秒
2025-07-31 16:32:27 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/1950835763181531137],无参数
2025-07-31 16:32:27 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/1950835763181531137],耗时:[266]毫秒
2025-07-31 16:34:07 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_normal_disable],无参数
2025-07-31 16:34:07 [boundedElastic-172] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/role/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:34:07 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_normal_disable],耗时:[104]毫秒
2025-07-31 16:34:07 [boundedElastic-172] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:34:07 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[162]毫秒
2025-07-31 16:34:08 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/role/list],耗时:[630]毫秒
2025-07-31 16:34:17 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:34:17 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[96]毫秒
2025-07-31 16:34:31 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:34:31 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/company_type],无参数
2025-07-31 16:34:31 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/company_type],耗时:[45]毫秒
2025-07-31 16:34:31 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[108]毫秒
2025-07-31 16:34:31 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info/apply/status],无参数
2025-07-31 16:34:31 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info/apply/status],耗时:[98]毫秒
2025-07-31 16:34:32 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],无参数
2025-07-31 16:34:32 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/business_type],无参数
2025-07-31 16:34:32 [boundedElastic-165] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /salary/workorderConfig/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:34:32 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/business_type],耗时:[28]毫秒
2025-07-31 16:34:33 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /salary/workorderConfig/list],耗时:[447]毫秒
2025-07-31 16:34:35 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/notice/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:34:35 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_type],无参数
2025-07-31 16:34:35 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_notice_status],无参数
2025-07-31 16:34:35 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_type],耗时:[25]毫秒
2025-07-31 16:34:35 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_notice_status],耗时:[27]毫秒
2025-07-31 16:34:35 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/notice/list],耗时:[234]毫秒
2025-07-31 16:34:35 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[3022]毫秒
2025-07-31 16:34:45 [boundedElastic-175] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/config/configKey/sys.user.initPassword],无参数
2025-07-31 16:34:45 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dict/data/type/sys_user_sex],无参数
2025-07-31 16:34:45 [boundedElastic-166] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/deptTree],无参数
2025-07-31 16:34:45 [boundedElastic-165] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:34:45 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dict/data/type/sys_user_sex],耗时:[45]毫秒
2025-07-31 16:34:45 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/config/configKey/sys.user.initPassword],耗时:[81]毫秒
2025-07-31 16:34:46 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/deptTree],耗时:[407]毫秒
2025-07-31 16:34:46 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/list],耗时:[938]毫秒
2025-07-31 16:34:51 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:34:51 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[90]毫秒
2025-07-31 16:35:50 [boundedElastic-175] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/bankAccount],参数类型[json],参数:[{"belongName":"张三","cardNumber":"****************111","cardHolder":"ZHANG SAN","depositBank":"青岛银行","depositBranch":"青岛银行","currencyType":"1","swiftCode":"","alias":"青岛银行","belongShip":"青岛银行","reservedPhone":"***********","isDefault":"0","notes":"青岛银行","accountType":"1","ifBelive":"1","isMyself":"2"}]
2025-07-31 16:35:51 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/bankAccount],耗时:[600]毫秒
2025-07-31 16:35:51 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:35:51 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[262]毫秒
2025-07-31 16:36:19 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[DELETE /system/bankAccount/1950835763181531137,1950837801286119425],无参数
2025-07-31 16:36:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[DELETE /system/bankAccount/1950835763181531137,1950837801286119425],耗时:[481]毫秒
2025-07-31 16:36:19 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:36:20 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[247]毫秒
2025-07-31 16:37:16 [boundedElastic-166] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:37:16 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[422]毫秒
2025-07-31 16:38:17 [boundedElastic-172] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:38:17 [boundedElastic-174] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:38:17 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[91]毫秒
2025-07-31 16:38:17 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[346]毫秒
2025-07-31 16:38:28 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:38:28 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:38:28 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[87]毫秒
2025-07-31 16:38:28 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[333]毫秒
2025-07-31 16:39:17 [boundedElastic-174] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/bankAccount/export],无参数
2025-07-31 16:39:20 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/bankAccount/export],耗时:[3024]毫秒
2025-07-31 16:39:52 [boundedElastic-156] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/bankAccount/export],无参数
2025-07-31 16:39:53 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/bankAccount/export],耗时:[542]毫秒
2025-07-31 16:40:28 [boundedElastic-169] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /system/bankAccount/export],无参数
2025-07-31 16:40:29 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /system/bankAccount/export],耗时:[624]毫秒
2025-07-31 16:46:28 [boundedElastic-177] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:46:28 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[368]毫秒
2025-07-31 16:46:40 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:46:41 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[377]毫秒
2025-07-31 16:46:55 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:46:55 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[94]毫秒
2025-07-31 16:52:23 [boundedElastic-190] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/1950837801286119425],无参数
2025-07-31 16:52:23 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/1950837801286119425],耗时:[359]毫秒
2025-07-31 16:59:04 [boundedElastic-181] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:59:04 [boundedElastic-197] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:59:04 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[95]毫秒
2025-07-31 16:59:04 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[404]毫秒
2025-07-31 16:59:19 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 16:59:19 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 16:59:19 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[72]毫秒
2025-07-31 16:59:19 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[364]毫秒
2025-07-31 16:59:35 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:59:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[718]毫秒
2025-07-31 16:59:36 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:59:36 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[105]毫秒
2025-07-31 16:59:36 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:59:36 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[32]毫秒
2025-07-31 16:59:37 [boundedElastic-184] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:59:37 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[139]毫秒
2025-07-31 16:59:51 [boundedElastic-187] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 16:59:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[683]毫秒
2025-07-31 16:59:52 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 16:59:52 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[104]毫秒
2025-07-31 16:59:52 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 16:59:52 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-31 16:59:53 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 16:59:53 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[23]毫秒
2025-07-31 17:01:26 [boundedElastic-198] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 17:01:27 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[760]毫秒
2025-07-31 17:01:27 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 17:01:27 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[104]毫秒
2025-07-31 17:01:28 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 17:01:28 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[39]毫秒
2025-07-31 17:01:28 [boundedElastic-198] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 17:01:28 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-31 17:01:28 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 17:01:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 17:01:28 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[50]毫秒
2025-07-31 17:01:29 [reactor-http-nio-4] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[274]毫秒
2025-07-31 17:14:30 [boundedElastic-175] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 17:14:31 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[803]毫秒
2025-07-31 17:14:31 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 17:14:31 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[97]毫秒
2025-07-31 17:14:31 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 17:14:31 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[33]毫秒
2025-07-31 17:14:32 [boundedElastic-175] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 17:14:32 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[44]毫秒
2025-07-31 17:14:32 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 17:14:32 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 17:14:32 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[69]毫秒
2025-07-31 17:14:33 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[309]毫秒
2025-07-31 17:19:45 [boundedElastic-211] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 17:19:45 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[658]毫秒
2025-07-31 17:19:45 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 17:19:45 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[73]毫秒
2025-07-31 17:19:45 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 17:19:45 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[27]毫秒
2025-07-31 17:19:46 [boundedElastic-211] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 17:19:46 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[28]毫秒
2025-07-31 17:19:46 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 17:19:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 17:19:46 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[48]毫秒
2025-07-31 17:19:46 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[266]毫秒
2025-07-31 17:23:11 [boundedElastic-219] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 17:23:11 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[693]毫秒
2025-07-31 17:23:12 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 17:23:12 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[89]毫秒
2025-07-31 17:23:12 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 17:23:12 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[26]毫秒
2025-07-31 17:23:13 [boundedElastic-219] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 17:23:13 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-31 17:23:13 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 17:23:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 17:23:13 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[40]毫秒
2025-07-31 17:23:13 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[271]毫秒
2025-07-31 17:23:41 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 17:23:42 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[645]毫秒
2025-07-31 17:23:42 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 17:23:42 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[59]毫秒
2025-07-31 17:23:42 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 17:23:42 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-31 17:23:43 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 17:23:43 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[25]毫秒
2025-07-31 17:23:43 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 17:23:43 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 17:23:43 [reactor-http-nio-10] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[22]毫秒
2025-07-31 17:23:43 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[237]毫秒
2025-07-31 17:29:08 [boundedElastic-227] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 17:29:08 [boundedElastic-205] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 17:29:08 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[63]毫秒
2025-07-31 17:29:08 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[332]毫秒
2025-07-31 17:29:34 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 17:29:34 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 17:29:34 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[85]毫秒
2025-07-31 17:29:35 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[358]毫秒
2025-07-31 17:29:42 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 17:29:43 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[691]毫秒
2025-07-31 17:29:43 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 17:29:43 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[74]毫秒
2025-07-31 17:29:43 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 17:29:43 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[30]毫秒
2025-07-31 17:29:44 [boundedElastic-224] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 17:29:44 [reactor-http-nio-5] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[24]毫秒
2025-07-31 17:29:44 [boundedElastic-208] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 17:29:44 [boundedElastic-224] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 17:29:44 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[49]毫秒
2025-07-31 17:29:44 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[263]毫秒
2025-07-31 17:30:10 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/1950835763181531137],无参数
2025-07-31 17:30:11 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/1950835763181531137],耗时:[251]毫秒
2025-07-31 17:35:58 [boundedElastic-210] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 17:35:59 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 17:35:59 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[64]毫秒
2025-07-31 17:35:59 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[368]毫秒
2025-07-31 19:01:52 [boundedElastic-265] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-07-31 19:01:52 [boundedElastic-267] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-07-31 19:01:53 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[386]毫秒
2025-07-31 19:01:54 [reactor-http-nio-6] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[2216]毫秒
2025-07-31 19:01:54 [reactor-http-nio-8] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-31 19:01:54 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 19:01:54 [reactor-http-nio-9] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[7]毫秒
2025-07-31 19:01:55 [reactor-http-nio-7] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[156]毫秒
2025-07-31 19:40:15 [boundedElastic-282] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-31 19:40:16 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[469]毫秒
2025-07-31 19:40:16 [reactor-http-nio-12] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-07-31 19:40:16 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[124]毫秒
2025-07-31 19:40:20 [reactor-http-nio-13] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[encrypt]
2025-07-31 19:40:21 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[936]毫秒
2025-07-31 19:40:21 [boundedElastic-295] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-07-31 19:40:22 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[779]毫秒
2025-07-31 19:40:23 [reactor-http-nio-16] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/company_info],无参数
2025-07-31 19:40:23 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/company_info],耗时:[107]毫秒
2025-07-31 19:40:23 [reactor-http-nio-1] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-07-31 19:40:23 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[44]毫秒
2025-07-31 19:40:23 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-07-31 19:40:23 [reactor-http-nio-11] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[3]毫秒
2025-07-31 19:46:40 [boundedElastic-292] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 19:46:41 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 19:46:41 [reactor-http-nio-15] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[76]毫秒
2025-07-31 19:46:41 [reactor-http-nio-14] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[454]毫秒
2025-07-31 19:48:31 [boundedElastic-302] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 19:48:31 [boundedElastic-298] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 19:48:31 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[104]毫秒
2025-07-31 19:48:32 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[406]毫秒
2025-07-31 19:49:08 [boundedElastic-309] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/tableConfig/getConfigInfo],参数类型[param],参数:[{"configName":["bankAccountTable"]}]
2025-07-31 19:49:08 [boundedElastic-308] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/bankAccount/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-07-31 19:49:08 [reactor-http-nio-2] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/tableConfig/getConfigInfo],耗时:[68]毫秒
2025-07-31 19:49:08 [reactor-http-nio-3] INFO  c.ym.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/bankAccount/list],耗时:[366]毫秒
2025-07-31 22:03:21 [SpringApplicationShutdownHook] INFO  c.y.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-31 22:03:21 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-31 22:03:21 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
