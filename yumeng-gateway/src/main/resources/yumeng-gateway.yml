# 安全配置
security:
  # 不校验白名单
  ignore:
    whites:
      - /auth/code
      - /auth/logout
      - /auth/login
      - /auth/binding/*
      - /auth/social/callback
      - /auth/register
      - /auth/tenant/list
      - /resource/sms/code
      - /resource/sse/close
      - /*/v3/api-docs
      - /*/error
      - /csrf
      - /auth/email/code
      - /auth/reset-password

spring:
  cloud:
    # 网关配置
    gateway:
      # 打印请求日志(自定义)
      requestLog: true
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: yumeng-auth
          uri: lb://yumeng-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # 代码生成
        - id: yumeng-gen
          uri: lb://yumeng-gen
          predicates:
            - Path=/tool/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: yumeng-system
          uri: lb://yumeng-system
          predicates:
            - Path=/system/**,/monitor/**
          filters:
            - StripPrefix=1
        # 资源服务
        - id: yumeng-resource
          uri: lb://yumeng-resource
          predicates:
            - Path=/resource/**
          filters:
            - StripPrefix=1
        # workflow服务
        - id: yumeng-workflow
          uri: lb://yumeng-workflow
          predicates:
            - Path=/workflow/**
          filters:
            - StripPrefix=1
        # 演示服务
        - id: yumeng-demo
          uri: lb://yumeng-demo
          predicates:
            - Path=/demo/**
          filters:
            - StripPrefix=1
        # MQ演示服务
        - id: yumeng-test-mq
          uri: lb://yumeng-test-mq
          predicates:
            - Path=/test-mq/**
          filters:
            - StripPrefix=1
        # 工资结算服务
        - id: yumeng-warehouse-management
          uri: lb://yumeng-warehouse-management
          predicates:
            - Path=/warehouseManagement/**
          filters:
            - StripPrefix=1
        # 仓库管理服务
        - id: yumeng-salary-settlement
          uri: lb://yumeng-salary-settlement
          predicates:
            - Path=/salarySettlement/**
          filters:
            - StripPrefix=1
    # sentinel 配置
    sentinel:
      filter:
        enabled: false
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${spring.cloud.nacos.server-addr}
            dataId: sentinel-${spring.application.name}.json
            groupId: ${spring.cloud.nacos.config.group}
            username: ${spring.cloud.nacos.username}
            password: ${spring.cloud.nacos.password}
            namespace: ${spring.profiles.active}
            data-type: json
            rule-type: gw-flow
