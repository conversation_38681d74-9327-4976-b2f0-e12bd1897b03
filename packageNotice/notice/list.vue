<template>
	<view class="container">
		<u-list
			@loadmore="loadMore"
			:loading="loading"
			:finished="!hasMore"
			:error="error"
			:immediate-check="true"
		>
			<u-list-item v-for="(item, index) in noticeList" :key="index">
				<view class="notice-card" @click="handleNoticeClick(item)">
					<view class="notice-header">
						<view class="notice-type" :class="item.noticeType === '1' ? 'notice' : 'announcement'">
							{{item.noticeType === '1' ? '通知' : '公告'}}
						</view>
						<text v-if="item.noticeType === '1'" class="notice-status" :class="item.status === '0' ? 'unread' : ''">
							{{item.status === '0' ? '未读' : '已读'}}
						</text>
					</view>
					<view class="notice-title">{{item.noticeTitle}}</view>
					<view class="notice-info">
						<text class="sender">{{item.createByName}}</text>
						<text class="time">{{formatTime(item.createTime)}}</text>
					</view>
				</view>
			</u-list-item>
			<view v-if="!loading && !error && total === 0" class="empty-state">
				<text>暂无通知</text>
			</view>
		</u-list>
		<u-modal
			:show="showDetailModal"
			:title="detailTitle"
			:show-confirm-button="true"
			:show-cancel-button="false"
			confirm-text="确定"
			:close-on-click-overlay="true"
			@confirm="showDetailModal = false"
			@close="showDetailModal = false"
		>
			<view class="slot-content">
				<rich-text :nodes="detailContent"></rich-text>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import { getNoticeList, markNoticeAsRead } from '@/api/system/notice'
	
	export default {
		data() {
			return {
				noticeList: [],
				loading: false,
				error: false,
				pageNum: 1,
				pageSize: 10,
				hasMore: true,
				total: 0,
				showDetailModal: false,
				detailTitle: '',
				detailContent: '',
			}
		},
		onLoad() {
			this.getNoticeList()
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.pageNum = 1
			this.getNoticeList().then(() => {
				uni.stopPullDownRefresh()
			})
		},
		methods: {
			// 获取通知列表
			async getNoticeList(isLoadMore = false) {
				if (!isLoadMore) {
					this.loading = true
				}
				this.error = false
				
				try {
					const res = await getNoticeList({ 
						pageNum: this.pageNum, 
						pageSize: this.pageSize 
					})
					if (res.code === 200) {
						if (isLoadMore) {
							this.noticeList = [...this.noticeList, ...(res.rows || [])]
						} else {
							this.noticeList = res.rows || []
						}
						this.total = res.total || 0
						this.hasMore = (res.rows || []).length === this.pageSize
					} else {
						this.error = true
					}
				} catch (e) {
					console.error('获取通知列表失败:', e)
					this.error = true
				} finally {
					this.loading = false
				}
			},
			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loading) {
					this.pageNum++
					this.getNoticeList(true)
				}
			},
			// 处理通知点击
			async handleNoticeClick(notice) {
				// 显示通知详情弹框
				this.detailTitle = notice.noticeTitle
				this.detailContent = [
					`<div style="line-height: 1.8;">`,
					`<div><b>任务类型：</b>${notice.taskType || '无'}</div>`,
					`<div><b>提示消息：</b>${notice.noticeTitle || '无'}</div>`,
					`<div><b>提交时间：</b>${notice.createTime || '无'}</div>`,
					`<div><b>提交人：</b>${notice.createByName || '无'}</div>`,
					`<div><b>目标人：</b>${notice.targetUser || '无'}</div>`,
					`<div><b>完成时间：</b>${notice.completeTime || '无'}</div>`,
					`<div><b>备注：</b>${notice.remark || '无'}</div>`,
					`</div>`
				].join('')
				this.showDetailModal = true

				// 如果是通知且未读，则标记为已读
				if (notice.noticeType === '1' && notice.status === '0') {
					try {
						const res = await markNoticeAsRead(notice.noticeId)
						if (res.code === 200) {
							notice.status = '1'
						}
					} catch (e) {
						console.error('标记通知已读失败:', e)
					}
				}
			},
			// 格式化时间
			formatTime(time) {
				if (!time) return ''
				const date = new Date(time)
				const now = new Date()
				const diff = now - date
				
				// 如果是今天
				if (diff < 24 * 60 * 60 * 1000) {
					const hours = date.getHours().toString().padStart(2, '0')
					const minutes = date.getMinutes().toString().padStart(2, '0')
					return `${hours}:${minutes}`
				}
				
				// 如果是昨天
				if (diff < 48 * 60 * 60 * 1000) {
					return '昨天'
				}
				
				// 如果是今年
				if (date.getFullYear() === now.getFullYear()) {
					const month = (date.getMonth() + 1).toString().padStart(2, '0')
					const day = date.getDate().toString().padStart(2, '0')
					return `${month}-${day}`
				}
				
				// 其他情况显示完整日期
				return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
			}
		}
	}
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f8f9fa;
		min-height: 100vh;
	}
	
	.notice-card {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 24rpx;
		margin: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.notice-card:active {
		background-color: #f9f9f9;
	}
	
	.notice-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}
	
	.notice-type {
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		font-size: 22rpx;
		font-weight: normal;
	}
	
	.notice-type.notice {
		background-color: rgba(76, 175, 80, 0.1);
		color: #4caf50;
	}
	
	.notice-type.announcement {
		background-color: rgba(255, 152, 0, 0.1);
		color: #ff9800;
	}
	
	.notice-status {
		padding: 2rpx 8rpx;
		border-radius: 4rpx;
		font-size: 20rpx;
		font-weight: normal;
	}
	
	.notice-status.unread {
		background-color: #ff9800;
		color: #fff;
	}
	
	.notice-title {
		font-size: 32rpx;
		color: #333;
		font-weight: 500;
		margin-bottom: 16rpx;
	}
	
	.notice-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 24rpx;
		color: #999;
	}
	
	.slot-content {
		padding: 20rpx;
	}
	
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx;
		color: #999;
		font-size: 28rpx;
	}
</style> 