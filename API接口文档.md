# 船舶综合管理系统 API接口文档

## 接口概述

### 基础信息
- **API基础地址**: `http://localhost:8080`
- **API版本**: v1.0
- **认证方式**: <PERSON><PERSON> (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 统一响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {},
  "timestamp": 1691234567890
}
```

### 状态码说明
| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证授权接口

### 用户登录
**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "admin123",
  "code": "1234",
  "uuid": "verification-uuid"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 7200,
    "user_info": {
      "userId": 1,
      "userName": "admin",
      "nickName": "管理员",
      "email": "<EMAIL>",
      "roles": ["admin"],
      "permissions": ["*:*:*"]
    }
  }
}
```

### 用户登出
**接口地址**: `POST /auth/logout`

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "退出成功"
}
```

### 获取用户信息
**接口地址**: `GET /auth/getInfo`

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "user": {
      "userId": 1,
      "userName": "admin",
      "nickName": "管理员",
      "email": "<EMAIL>",
      "phonenumber": "15888888888",
      "sex": "1",
      "avatar": "",
      "dept": {
        "deptId": 103,
        "deptName": "研发部门"
      },
      "roles": [
        {
          "roleId": 1,
          "roleName": "超级管理员",
          "roleKey": "admin"
        }
      ]
    },
    "roles": ["admin"],
    "permissions": ["*:*:*"]
  }
}
```

### 刷新Token
**接口地址**: `POST /auth/refresh`

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "刷新成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 7200
  }
}
```

## 系统管理接口

### 用户管理

#### 获取用户列表
**接口地址**: `GET /system/user/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| userName | string | 否 | 用户名 |
| phonenumber | string | 否 | 手机号 |
| status | string | 否 | 状态(0正常 1停用) |
| deptId | long | 否 | 部门ID |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "userId": 1,
      "userName": "admin",
      "nickName": "管理员",
      "email": "<EMAIL>",
      "phonenumber": "15888888888",
      "sex": "1",
      "status": "0",
      "createTime": "2023-01-01 00:00:00",
      "dept": {
        "deptId": 103,
        "deptName": "研发部门"
      }
    }
  ],
  "total": 1
}
```

#### 获取用户详情
**接口地址**: `GET /system/user/{userId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | long | 是 | 用户ID |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1,
    "userName": "admin",
    "nickName": "管理员",
    "email": "<EMAIL>",
    "phonenumber": "15888888888",
    "sex": "1",
    "status": "0",
    "deptId": 103,
    "roleIds": [1],
    "postIds": [1]
  }
}
```

#### 新增用户
**接口地址**: `POST /system/user`

**请求参数**:
```json
{
  "userName": "testuser",
  "nickName": "测试用户",
  "email": "<EMAIL>",
  "phonenumber": "15999999999",
  "sex": "0",
  "status": "0",
  "deptId": 103,
  "roleIds": [2],
  "postIds": [2],
  "password": "123456"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "新增成功"
}
```

#### 修改用户
**接口地址**: `PUT /system/user`

**请求参数**:
```json
{
  "userId": 2,
  "userName": "testuser",
  "nickName": "测试用户修改",
  "email": "<EMAIL>",
  "phonenumber": "15999999999",
  "sex": "0",
  "status": "0",
  "deptId": 103,
  "roleIds": [2],
  "postIds": [2]
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "修改成功"
}
```

#### 删除用户
**接口地址**: `DELETE /system/user/{userIds}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userIds | string | 是 | 用户ID，多个用逗号分隔 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "删除成功"
}
```

### 角色管理

#### 获取角色列表
**接口地址**: `GET /system/role/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| roleName | string | 否 | 角色名称 |
| roleKey | string | 否 | 角色权限字符串 |
| status | string | 否 | 状态(0正常 1停用) |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "roleId": 1,
      "roleName": "超级管理员",
      "roleKey": "admin",
      "roleSort": 1,
      "dataScope": "1",
      "status": "0",
      "createTime": "2023-01-01 00:00:00"
    }
  ],
  "total": 1
}
```

#### 新增角色
**接口地址**: `POST /system/role`

**请求参数**:
```json
{
  "roleName": "测试角色",
  "roleKey": "test",
  "roleSort": 3,
  "dataScope": "2",
  "status": "0",
  "menuIds": [1, 2, 3],
  "remark": "测试角色"
}
```

### 部门管理

#### 获取部门列表
**接口地址**: `GET /system/dept/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| deptName | string | 否 | 部门名称 |
| status | string | 否 | 状态(0正常 1停用) |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "deptId": 100,
      "parentId": 0,
      "ancestors": "0",
      "deptName": "若依科技",
      "orderNum": 0,
      "leader": "若依",
      "phone": "15888888888",
      "email": "<EMAIL>",
      "status": "0",
      "children": [
        {
          "deptId": 101,
          "parentId": 100,
          "ancestors": "0,100",
          "deptName": "深圳总公司",
          "orderNum": 1,
          "leader": "若依",
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0"
        }
      ]
    }
  ]
}
```

## 工作流接口

### 流程定义管理

#### 获取流程定义列表
**接口地址**: `GET /workflow/definition/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| name | string | 否 | 流程名称 |
| key | string | 否 | 流程标识 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": "Process_1:1:4",
      "name": "请假流程",
      "key": "leave_process",
      "version": 1,
      "deploymentId": "1",
      "suspended": false,
      "deploymentTime": "2023-01-01 00:00:00"
    }
  ],
  "total": 1
}
```

#### 启动流程实例
**接口地址**: `POST /workflow/process/start`

**请求参数**:
```json
{
  "processDefinitionKey": "leave_process",
  "businessKey": "LEAVE_001",
  "variables": {
    "applicant": "张三",
    "leaveType": "事假",
    "leaveDays": 2,
    "reason": "家中有事"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "流程启动成功",
  "data": {
    "processInstanceId": "5001",
    "businessKey": "LEAVE_001"
  }
}
```

### 任务管理

#### 获取待办任务列表
**接口地址**: `GET /workflow/task/todo`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| processDefinitionKey | string | 否 | 流程定义标识 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "taskId": "5005",
      "taskName": "部门经理审批",
      "processInstanceId": "5001",
      "processDefinitionKey": "leave_process",
      "businessKey": "LEAVE_001",
      "assignee": "manager",
      "createTime": "2023-01-01 09:00:00",
      "variables": {
        "applicant": "张三",
        "leaveType": "事假",
        "leaveDays": 2
      }
    }
  ],
  "total": 1
}
```

#### 完成任务
**接口地址**: `POST /workflow/task/complete`

**请求参数**:
```json
{
  "taskId": "5005",
  "variables": {
    "approved": true,
    "comment": "同意请假申请"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "任务完成成功"
}
```

## AI聊天接口

### 会话管理

#### 创建聊天会话
**接口地址**: `POST /ai/session/create`

**请求参数**:
```json
{
  "sessionName": "技术咨询",
  "modelName": "gpt-3.5-turbo"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "会话创建成功",
  "data": {
    "sessionId": 1001,
    "sessionName": "技术咨询",
    "modelName": "gpt-3.5-turbo"
  }
}
```

#### 获取会话列表
**接口地址**: `GET /ai/session/list`

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "sessionId": 1001,
      "sessionName": "技术咨询",
      "modelName": "gpt-3.5-turbo",
      "status": "0",
      "createTime": "2023-01-01 10:00:00"
    }
  ]
}
```

### 消息管理

#### 发送聊天消息
**接口地址**: `POST /ai/chat/send`

**请求参数**:
```json
{
  "sessionId": 1001,
  "content": "请介绍一下Spring Boot框架",
  "modelName": "gpt-3.5-turbo"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "发送成功",
  "data": {
    "messageId": 2001,
    "content": "Spring Boot是一个基于Spring框架的快速开发框架...",
    "role": "assistant",
    "tokens": 150
  }
}
```

#### 获取聊天历史
**接口地址**: `GET /ai/chat/history`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sessionId | long | 是 | 会话ID |
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认20 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "messageId": 2000,
      "sessionId": 1001,
      "role": "user",
      "content": "请介绍一下Spring Boot框架",
      "createTime": "2023-01-01 10:00:00"
    },
    {
      "messageId": 2001,
      "sessionId": 1001,
      "role": "assistant",
      "content": "Spring Boot是一个基于Spring框架的快速开发框架...",
      "tokens": 150,
      "createTime": "2023-01-01 10:00:05"
    }
  ],
  "total": 2
}
```

## 薪资结算接口

### 人民币工单管理

#### 获取人民币工单列表
**接口地址**: `GET /salary/rmb/workorder/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| transportId | long | 否 | 航运公司ID |
| shippingId | long | 否 | 船舶公司ID |
| workorderStatus | string | 否 | 工单状态 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "transportId": 1001,
      "shippingId": 2001,
      "createrId": 1,
      "processId": 2,
      "salary": 50000.00,
      "taxes": 5000.00,
      "dispatchFee": 2000.00,
      "securityFee": 1500.00,
      "workorderStatus": "PENDING",
      "createTime": "2023-01-01 10:00:00"
    }
  ],
  "total": 1
}
```

#### 创建人民币工单
**接口地址**: `POST /salary/rmb/workorder`

**请求参数**:
```json
{
  "transportId": 1001,
  "shippingId": 2001,
  "workorderStatus": "DRAFT",
  "remark": "2023年1月份薪资代发",
  "details": [
    {
      "employeeName": "张三",
      "idCard": "110101199001011234",
      "bankCard": "6222021234567890123",
      "salaryAmount": 8000.00,
      "taxAmount": 800.00
    },
    {
      "employeeName": "李四",
      "idCard": "110101199002021234",
      "bankCard": "6222021234567890124",
      "salaryAmount": 9000.00,
      "taxAmount": 900.00
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "工单创建成功",
  "data": {
    "workorderId": 1
  }
}
```

#### 获取人民币工单详情
**接口地址**: `GET /salary/rmb/workorder/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 工单ID |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "transportId": 1001,
    "shippingId": 2001,
    "createrId": 1,
    "processId": 2,
    "salary": 50000.00,
    "taxes": 5000.00,
    "dispatchFee": 2000.00,
    "securityFee": 1500.00,
    "workorderStatus": "PENDING",
    "remark": "2023年1月份薪资代发",
    "createTime": "2023-01-01 10:00:00",
    "details": [
      {
        "id": 1,
        "workorderId": 1,
        "employeeName": "张三",
        "idCard": "110101199001011234",
        "bankCard": "6222021234567890123",
        "salaryAmount": 8000.00,
        "taxAmount": 800.00
      }
    ]
  }
}
```

### 美金工单管理

#### 获取美金工单列表
**接口地址**: `GET /salary/usd/workorder/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| transportId | long | 否 | 航运公司ID |
| shippingId | long | 否 | 船舶公司ID |
| workorderStatus | string | 否 | 工单状态 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "transportId": 1001,
      "shippingId": 2001,
      "createrId": 1,
      "processId": 2,
      "salary": 10000.00,
      "commission": 500.00,
      "freeze": 1000.00,
      "usedCredit": 2000.00,
      "workorderStatus": "APPROVED",
      "createTime": "2023-01-01 10:00:00"
    }
  ],
  "total": 1
}
```

#### 创建美金工单
**接口地址**: `POST /salary/usd/workorder`

**请求参数**:
```json
{
  "transportId": 1001,
  "shippingId": 2001,
  "salary": 10000.00,
  "commission": 500.00,
  "freeze": 1000.00,
  "usedCredit": 2000.00,
  "workorderStatus": "DRAFT",
  "remark": "2023年1月份美金薪资代发"
}
```

## 仓库管理接口

### 仓库信息管理

#### 获取仓库列表
**接口地址**: `GET /warehouse/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | int | 否 | 页码，默认1 |
| pageSize | int | 否 | 每页数量，默认10 |
| warehouseName | string | 否 | 仓库名称 |
| warehouseCode | string | 否 | 仓库编码 |
| status | string | 否 | 状态(0正常 1停用) |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "warehouseName": "上海港仓库",
      "warehouseCode": "SH001",
      "address": "上海市浦东新区港口大道123号",
      "keeperId": 10,
      "keeperName": "王五",
      "contactPhone": "13800138000",
      "status": "0",
      "remark": "主要存储集装箱货物",
      "createTime": "2023-01-01 10:00:00"
    }
  ],
  "total": 1
}
```

#### 获取仓库详情
**接口地址**: `GET /warehouse/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 仓库ID |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "warehouseName": "上海港仓库",
    "warehouseCode": "SH001",
    "address": "上海市浦东新区港口大道123号",
    "keeperId": 10,
    "keeperName": "王五",
    "contactPhone": "13800138000",
    "status": "0",
    "remark": "主要存储集装箱货物",
    "createTime": "2023-01-01 10:00:00",
    "updateTime": "2023-01-01 10:00:00"
  }
}
```

#### 新增仓库
**接口地址**: `POST /warehouse`

**请求参数**:
```json
{
  "warehouseName": "深圳港仓库",
  "warehouseCode": "SZ001",
  "address": "深圳市南山区港口路456号",
  "keeperId": 11,
  "contactPhone": "13900139000",
  "status": "0",
  "remark": "新建仓库"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "新增成功"
}
```

#### 修改仓库
**接口地址**: `PUT /warehouse`

**请求参数**:
```json
{
  "id": 1,
  "warehouseName": "上海港仓库(更新)",
  "warehouseCode": "SH001",
  "address": "上海市浦东新区港口大道123号",
  "keeperId": 10,
  "contactPhone": "13800138000",
  "status": "0",
  "remark": "主要存储集装箱货物，已更新"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "修改成功"
}
```

#### 删除仓库
**接口地址**: `DELETE /warehouse/{ids}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | string | 是 | 仓库ID，多个用逗号分隔 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "删除成功"
}
```

## 错误码说明

### 业务错误码
| 错误码 | 说明 |
|--------|------|
| 10001 | 用户名或密码错误 |
| 10002 | 验证码错误 |
| 10003 | 用户已被禁用 |
| 10004 | Token已过期 |
| 10005 | 权限不足 |
| 20001 | 工单状态不允许此操作 |
| 20002 | 薪资金额不能为负数 |
| 20003 | 员工信息不完整 |
| 30001 | 仓库编码已存在 |
| 30002 | 仓库管理员不存在 |
| 40001 | 流程定义不存在 |
| 40002 | 任务已被处理 |
| 50001 | AI模型不可用 |
| 50002 | 会话不存在 |

## 接口调用示例

### JavaScript示例
```javascript
// 登录
const login = async (username, password) => {
  const response = await fetch('/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username,
      password
    })
  });

  const result = await response.json();
  if (result.code === 200) {
    localStorage.setItem('token', result.data.access_token);
    return result.data;
  } else {
    throw new Error(result.msg);
  }
};

// 获取用户列表
const getUserList = async (params) => {
  const token = localStorage.getItem('token');
  const queryString = new URLSearchParams(params).toString();

  const response = await fetch(`/system/user/list?${queryString}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

### Java示例
```java
// 使用RestTemplate调用接口
@Service
public class ApiService {

    @Autowired
    private RestTemplate restTemplate;

    public LoginResult login(String username, String password) {
        String url = "http://localhost:8080/auth/login";

        Map<String, String> request = new HashMap<>();
        request.put("username", username);
        request.put("password", password);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, String>> entity = new HttpEntity<>(request, headers);

        ResponseEntity<ApiResponse<LoginResult>> response = restTemplate.exchange(
            url, HttpMethod.POST, entity,
            new ParameterizedTypeReference<ApiResponse<LoginResult>>() {}
        );

        return response.getBody().getData();
    }
}
```

---

*API接口文档最后更新时间: 2025-08-01*
