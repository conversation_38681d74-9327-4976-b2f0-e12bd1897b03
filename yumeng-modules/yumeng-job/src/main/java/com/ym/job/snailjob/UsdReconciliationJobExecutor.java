package com.ym.job.snailjob;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.log.SnailJobLog;
import com.ym.salary.api.RemoteUsdReconciliationService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;

/**
 * 美元对账单自动生成任务
 * 每月1号自动汇总上月美元流水数据，生成对账单
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Component
@RequiredArgsConstructor
@JobExecutor(name = "usdReconciliationJobExecutor")
public class UsdReconciliationJobExecutor {

    @DubboReference
    private RemoteUsdReconciliationService remoteUsdReconciliationService;

    /**
     * 执行任务
     * 汇总上月美元流水数据，生成对账单
     *
     * @param jobArgs 任务参数
     * @return 执行结果
     */
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        SnailJobLog.LOCAL.info("开始执行美元对账单自动生成任务...");
        
        try {
            // 1. 获取上个月的起始日期和结束日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -1); // 上个月
            calendar.set(Calendar.DAY_OF_MONTH, 1); // 月初
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startDate = calendar.getTime();
            
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.MILLISECOND, -1);
            Date endDate = calendar.getTime();
            
            SnailJobLog.LOCAL.info("对账周期：{} 至 {}", startDate, endDate);
            
            // 2. 通过远程调用生成美元对账单
            Map<String, Integer> result = remoteUsdReconciliationService.generateUsdReconciliation(startDate, endDate);
            
            int successCount = result.getOrDefault("success", 0);
            int failCount = result.getOrDefault("fail", 0);
            
            String message = String.format("美元对账单自动生成任务完成，成功：%d，失败：%d", successCount, failCount);
            SnailJobLog.LOCAL.info(message);
            return ExecuteResult.success(message);
            
        } catch (Exception e) {
            SnailJobLog.LOCAL.error("美元对账单自动生成任务异常：" + e.getMessage(), e);
            return ExecuteResult.failure("美元对账单自动生成任务异常：" + e.getMessage());
        }
    }
} 