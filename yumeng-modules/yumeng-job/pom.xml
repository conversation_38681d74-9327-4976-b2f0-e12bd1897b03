<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ym</groupId>
        <artifactId>yumeng-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yumeng-job</artifactId>

    <description>
        yumeng-job 任务调度模块
    </description>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ym</groupId>
                <artifactId>yumeng-ship-bom</artifactId>
                <version>1.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-nacos</artifactId>
        </dependency>

        <!-- yumeng Common Log -->
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-dict</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-tenant</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ym</groupId>
                    <artifactId>yumeng-common-mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-security</artifactId>
        </dependency>

        <!-- yumeng Api System -->
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-api-system</artifactId>
        </dependency>

        <!-- yumeng Api Salary -->
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-ship-salary</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.2.11</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
