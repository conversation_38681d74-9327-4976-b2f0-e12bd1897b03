<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ym</groupId>
        <artifactId>ship-Integrated-management-api</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>yumeng-system</module>
        <module>yumeng-gen</module>
        <module>yumeng-job</module>
        <module>yumeng-resource</module>
        <module>yumeng-workflow</module>
    </modules>

    <artifactId>yumeng-modules</artifactId>
    <packaging>pom</packaging>

    <description>
        yumeng-modules业务模块
    </description>

    <dependencies>
        <!-- 自定义负载均衡(多团队开发使用) -->
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-loadbalancer</artifactId>
        </dependency>

        <!-- ELK 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.ym</groupId>-->
<!--            <artifactId>yumeng-common-logstash</artifactId>-->
<!--        </dependency>-->

        <!-- skywalking 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.ym</groupId>-->
<!--            <artifactId>yumeng-common-skylog</artifactId>-->
<!--        </dependency>-->

        <!-- prometheus 监控 -->
<!--        <dependency>-->
<!--            <groupId>com.ym</groupId>-->
<!--            <artifactId>yumeng-common-prometheus</artifactId>-->
<!--        </dependency>-->

    </dependencies>
</project>
