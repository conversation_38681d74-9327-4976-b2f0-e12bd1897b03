package com.ym.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import com.ym.common.sensitive.annotation.Sensitive;
import com.ym.common.sensitive.core.SensitiveStrategy;
import com.ym.system.domain.CompanyInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 公司信息视图对象 company_info
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CompanyInfo.class)
public class CompanyInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "公司名称")
    private String companyName;

    /**
     * 公司证件类型（1统一信用代码 2营业执照号）
     */
    @ExcelProperty(value = "公司证件类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "company_cert_type")
    private String companyCertType;

    /**
     * 公司证件号码
     */
    @ExcelProperty(value = "公司证件号码")
    private String companyCertNo;

    /**
     * 成立时间
     */
    @ExcelProperty(value = "成立时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date establishTime;

    /**
     * 公司地址
     */
    @ExcelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 登记机关
     */
    @ExcelProperty(value = "登记机关")
    private String registrationAuthority;

    /**
     * 邮箱地址
     */
    @ExcelProperty(value = "邮箱地址")
    private String email;

    /**
     * 法人姓名
     */
    @ExcelProperty(value = "法人姓名")
    private String legalPerson;

    /**
     * 法人证件类型（1居民身份证 2护照）
     */
    @ExcelProperty(value = "法人证件类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "legal_cert_type")
    private String legalCertType;

    /**
     * 法人证件号码
     */
    @ExcelProperty(value = "法人证件号码")
    private String legalCertNo;

    /**
     * 企业类型（1船舶管理类 2航运服务类 3平台运维类）
     */
    @ExcelProperty(value = "企业类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "company_type")
    private String companyType;

    /**
     * 实际地址是否与注册地一致（1是 2否）
     */
    @ExcelProperty(value = "实际地址是否与注册地一致", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "address_consistent")
    private String addressConsistent;

    /**
     * 实际经营地址
     */
    @ExcelProperty(value = "实际经营地址")
    private String actualAddress;

    /**
     * 公司关联用户id
     */
    @ExcelProperty(value = "公司关联用户id")
    private Long userId;

    /**
     * 阿里云oss_id
     */
//    @Sensitive(strategy = SensitiveStrategy.BANK_CARD, perms = "salary:company_info:edit,salary:company_approval:list")
    @ExcelProperty(value = "阿里云oss_id")
    private String fileId;

    /**
     * 申请状态当前状态 (0待审核1审核通过2审核不通过3停用)
     */
    @ExcelProperty(value = "申请状态当前状态 (0待审核1审核通过2审核不通过3停用)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "company_application_satus")
    private Long applicationStatus;

    /**
     * 关联部门id
     */
    @ExcelProperty(value = "关联部门id")
    private Long relatedDept;

    @ExcelProperty(value = "删除标志")
    private String deptName;


}
