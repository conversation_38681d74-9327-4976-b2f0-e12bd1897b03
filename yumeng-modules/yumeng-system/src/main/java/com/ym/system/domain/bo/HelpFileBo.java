package com.ym.system.domain.bo;

import com.ym.system.domain.HelpFile;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 帮助文件业务对象 help_file
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = HelpFile.class, reverseConvertGenerate = false)
public class HelpFileBo extends BaseEntity {

    /**
     * 文件唯一ID
     */
    @NotNull(message = "文件唯一ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 文件标题
     */
    @NotBlank(message = "文件标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 文件描述
     */
    @NotBlank(message = "文件描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 所属分区ID(meau菜单表ID)
     */
    @NotNull(message = "所属分区ID(meau菜单表ID)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long partitionId;

    /**
     * 文件存储路径
     */
    private Long fileId;

    /**
     * 封面图片ID
     */
    @NotNull(message = "封面图片ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long  coverUrlId;
    /**
     * 封面文件url
     */
    private String coverUrl;

    /**
     * 文件类型
     */
    @NotBlank(message = "文件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileType;


}
