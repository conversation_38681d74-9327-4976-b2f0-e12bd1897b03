package com.ym.system.domain.vo;

import com.ym.system.domain.BankBin;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 银行标识码信息视图对象 bank_bin
 *
 * <AUTHOR>
 * @date 2025-05-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BankBin.class)
public class BankBinVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 银行标识码
     */
    @ExcelProperty(value = "银行标识码")
    private String binCode;

    /**
     * 开户银行名称
     */
    @ExcelProperty(value = "银行名称")
    private String bankName;

    /**
     * 开户支行名称
     */
    @ExcelProperty(value = "开户支行名称")
    private String branchName;


}
