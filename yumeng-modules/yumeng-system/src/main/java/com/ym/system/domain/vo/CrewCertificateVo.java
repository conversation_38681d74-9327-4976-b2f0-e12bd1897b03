package com.ym.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 船员证书信息视图对象
 */
@Data
public class CrewCertificateVo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 船员id
     */
    private Long crewId;

    /**
     * 证书类型
     */
    private String certificateType;

    /**
     * 证书号码
     */
    private String certificateNo;

    /**
     * 身份证号码
     */
    private String identityNo;

    /**
     * 姓名
     */
    private String fullName;

    /**
     * 签发机关
     */
    private String issuingAuthority;

    /**
     * 签发日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date issueDate;

    /**
     * 有效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;

    /**
     * 证书状态 1、有效 2 失效
     */
    private String certificateStatus;

    /**
     * 培训项目名称
     */
    private String trainingProgram;

    /**
     * 证书类别(航区类别：如无限航区)
     */
    private String areaType;

    /**
     * 证书等级
     */
    private String certificateLevel;

    /**
     * 证书职务
     */
    private String certificatePosition;

    /**
     * 证书印刷号
     */
    private String printingNumber;

    /**
     * 工作部门
     */
    private String department;

    /**
     * 职责限制
     */
    private String responsibility;

    /**
     * 体检机构
     */
    private String examinationInstitution;

    /**
     * 主检医师
     */
    private String doctor;

    /**
     * 授权海事机关
     */
    private String authorizedAgency;
} 