package com.ym.system.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 实名认证
 */
@Data
public class CertificationVo {

    /**
     * 认证姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 身份证正面照片
     */
    private String idCardFront;

    /**
     * 身份证背面照片
     */
    private String idCardBack;
    /**
     * 证件签发机关
     */
    private String issuingAuthority;
}
