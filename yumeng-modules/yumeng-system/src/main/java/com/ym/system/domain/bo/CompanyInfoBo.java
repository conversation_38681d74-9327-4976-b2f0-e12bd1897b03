package com.ym.system.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.system.domain.CompanyInfo;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 公司信息业务对象 company_info
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CompanyInfo.class, reverseConvertGenerate = false)
public class CompanyInfoBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyName;

    /**
     * 公司证件类型（1统一信用代码 2营业执照号）
     */
    @NotBlank(message = "公司证件类型（1统一信用代码 2营业执照号）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCertType;

    /**
     * 公司证件号码
     */
    @NotBlank(message = "公司证件号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCertNo;

    /**
     * 成立时间
     */
    @NotNull(message = "成立时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date establishTime;

    /**
     * 公司地址
     */
    @NotBlank(message = "公司地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyAddress;

    /**
     * 登记机关
     */
    @NotBlank(message = "登记机关不能为空", groups = { AddGroup.class, EditGroup.class })
    private String registrationAuthority;

    /**
     * 邮箱地址
     */
    @NotBlank(message = "邮箱地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String email;

    /**
     * 法人姓名
     */
    @NotBlank(message = "法人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String legalPerson;

    /**
     * 法人证件类型（1居民身份证 2护照）
     */
    @NotBlank(message = "法人证件类型（1居民身份证 2护照）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String legalCertType;

    /**
     * 法人证件号码
     */
    @NotBlank(message = "法人证件号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String legalCertNo;

    /**
     * 企业类型（1船舶管理类 2航运服务类 3平台运维类）
     */
    @NotBlank(message = "企业类型（1船舶管理类 2航运服务类 3平台运维类）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyType;

    /**
     * 实际地址是否与注册地一致（1是 2否）
     */
    @NotBlank(message = "实际地址是否与注册地一致（1是 2否）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String addressConsistent;

    /**
     * 实际经营地址
     */
    @NotBlank(message = "实际经营地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actualAddress;

    /**
     * 公司关联用户id
     */
    private Long userId;

    /**
     * 阿里云oss_id
     */
    private String fileId;

    /**
     * 申请状态当前状态 (0待审核1审核通过2审核不通过3停用)
     */
    private Long applicationStatus;

    /**
     * 审批意见
     */
    private String approvalComment;

    /**
     * 是否通知用户
     */
    private Boolean notifyUser;
    /**
     * 关联部门id
     */
    private Long relatedDept;
    /**
     * 部门名称
     */
    private String deptName;
}
