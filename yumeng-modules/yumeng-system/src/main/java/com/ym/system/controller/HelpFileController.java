package com.ym.system.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.ym.common.idempotent.annotation.RepeatSubmit;
import com.ym.common.log.annotation.Log;
import com.ym.common.web.core.BaseController;
import com.ym.common.mybatis.core.page.PageQuery;
import com.ym.common.core.domain.R;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.log.enums.BusinessType;
import com.ym.common.excel.utils.ExcelUtil;
import com.ym.system.domain.vo.HelpFileVo;
import com.ym.system.domain.bo.HelpFileBo;
import com.ym.system.service.IHelpFileService;
import com.ym.common.mybatis.core.page.TableDataInfo;

/**
 * 帮助文件
 * 前端访问路由地址为:/system/helpFile
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/helpFile")
public class HelpFileController extends BaseController {

    private final IHelpFileService helpFileService;

    /**
     * 查询帮助文件列表
     */
    //@SaCheckPermission("system:helpFile:list")
    @GetMapping("/list")
    public TableDataInfo<HelpFileVo> list(HelpFileBo bo, PageQuery pageQuery) {
        return helpFileService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出帮助文件列表
     */
    @SaCheckPermission("system:helpFile:export")
    @Log(title = "帮助文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HelpFileBo bo, HttpServletResponse response) {
        List<HelpFileVo> list = helpFileService.queryList(bo);
        ExcelUtil.exportExcel(list, "帮助文件", HelpFileVo.class, response);
    }

    /**
     * 获取帮助文件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:helpFile:query")
    @GetMapping("/{id}")
    public R<HelpFileVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(helpFileService.queryById(id));
    }

    /**
     * 新增帮助文件
     */
    @SaCheckPermission("system:helpFile:add")
    @Log(title = "帮助文件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody HelpFileBo bo) {
        return toAjax(helpFileService.insertByBo(bo));
    }

    /**
     * 修改帮助文件
     */
    @SaCheckPermission("system:helpFile:edit")
    @Log(title = "帮助文件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody HelpFileBo bo) {
        return toAjax(helpFileService.updateByBo(bo));
    }

    /**
     * 删除帮助文件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:helpFile:remove")
    @Log(title = "帮助文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(helpFileService.deleteWithValidByIds(List.of(ids), true));
    }
    @GetMapping("pdf/base64/{id}")
    public String getPdfBase64(@PathVariable Long id) {
        return helpFileService.getPdfBase64(id);
    }

}
