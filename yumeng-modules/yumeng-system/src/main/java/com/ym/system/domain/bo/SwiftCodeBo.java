package com.ym.system.domain.bo;

import com.ym.system.domain.SwiftCode;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * SWIFT编码业务对象 swift_code
 *
 * <AUTHOR>
 * @date 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SwiftCode.class, reverseConvertGenerate = false)
public class SwiftCodeBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 银行名称
     */
    @NotBlank(message = "银行名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bankName;
    /**
     * 银行支行名称
     */
    @NotBlank(message = "银行支行名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String branchName;

    /**
     * 完整码
     */
    @NotBlank(message = "完整码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String wholeCode;

    /**
     * 银行编码
     */
    @NotBlank(message = "银行编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bankCode;

    /**
     * 国家编码
     */
    @NotBlank(message = "国家编码 不能为空", groups = { AddGroup.class, EditGroup.class })
    private String countryCode;

    /**
     * 地区编码
     */
    @NotBlank(message = "地区编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String areaCode;

    /**
     * 补码
     */
    @NotBlank(message = "补码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String complement;


}
