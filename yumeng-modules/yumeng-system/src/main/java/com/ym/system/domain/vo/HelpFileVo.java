package com.ym.system.domain.vo;

import com.ym.system.domain.HelpFile;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 帮助文件视图对象 help_file
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = HelpFile.class)
public class HelpFileVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件唯一ID
     */
    @ExcelProperty(value = "文件唯一ID")
    private Long id;

    /**
     * 文件标题
     */
    @ExcelProperty(value = "文件标题")
    private String title;

    /**
     * 文件描述
     */
    @ExcelProperty(value = "文件描述")
    private String description;

    /**
     * 所属分区ID(meau菜单表ID)
     */
    @ExcelProperty(value = "所属分区ID(meau菜单表ID)")
    private Long partitionId;

    @ExcelProperty(value = "所属分区名称")
    private String partitionName;

    /**
     * 文件存储路径
     */
    @ExcelProperty(value = "文件存储路径")
    private Long fileId;

    @ExcelProperty(value = "文件访问路径")
    private String fileUrl;
    /**
     * 封面图片ID
     */
    @ExcelProperty(value = "封面图片ID")
    private Long  coverUrlId;
    /**
     * 封面文件url
     */
    @ExcelProperty(value = "封面文件url")
    private String coverUrl;

    /**
     * 文件类型
     */
    @ExcelProperty(value = "文件类型")
    private String fileType;


}
