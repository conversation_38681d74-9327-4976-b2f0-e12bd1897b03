package com.ym.system.domain.vo;

import com.ym.common.mybatis.core.domain.BaseEntity;

/**
 * 企业认领用户请求VO
 *
 * <AUTHOR>
 */
public class SysClaimUserVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;

    /** 角色ID数组 */
    private Long[] roleIds;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long[] getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(Long[] roleIds) {
        this.roleIds = roleIds;
    }
}
