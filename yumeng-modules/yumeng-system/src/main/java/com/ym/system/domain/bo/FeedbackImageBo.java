package com.ym.system.domain.bo;

import com.ym.system.domain.FeedbackImage;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 反馈图片业务对象 feedback_image
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FeedbackImage.class, reverseConvertGenerate = false)
public class FeedbackImageBo extends BaseEntity {

    /**
     * 图片ID
     */
    @NotNull(message = "图片ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 关联反馈ID
     */
    @NotNull(message = "关联反馈ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long feedbackId;

    /**
     * 图片存储id
     */
    @NotNull(message = "图片存储id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long imageUrlId;

    /**
     * 排序序号 (0-2)
     */
    private Integer sortOrder;
} 