package com.ym.system.domain.vo;

import com.ym.system.domain.FeedbackImage;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 反馈图片视图对象 feedback_image
 *
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FeedbackImage.class)
public class FeedbackImageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 图片ID
     */
    @ExcelProperty(value = "图片ID")
    private Long id;

    /**
     * 关联反馈ID
     */
    @ExcelProperty(value = "关联反馈ID")
    private Long feedbackId;

    /**
     * 图片存储id
     */
    @ExcelProperty(value = "图片存储id")
    private Long imageUrlId;

    /**
     * 排序序号 (0-2)
     */
    @ExcelProperty(value = "排序序号")
    private Integer sortOrder;
} 