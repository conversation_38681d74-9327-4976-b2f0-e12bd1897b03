package com.ym.system.domain.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.ym.system.domain.TableColumnConfig;
import com.ym.system.domain.UserTableConfig;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 动态格配置视图对象 user_table_config
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserTableConfig.class)
public class UserTableConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 配置名称
     */
    @ExcelProperty(value = "配置名称")
    private String configName;

    /**
     * 表格宽度，如100%, 1200px等
     */
    @ExcelProperty(value = "表格宽度，如100%, 1200px等")
    private String tableWidth;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 表格列配置
     */
    private List<TableColumnConfig> columns;


}
