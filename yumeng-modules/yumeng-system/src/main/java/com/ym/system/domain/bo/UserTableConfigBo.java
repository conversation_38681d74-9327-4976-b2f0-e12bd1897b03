package com.ym.system.domain.bo;

import com.ym.system.domain.TableColumnConfig;
import com.ym.system.domain.UserTableConfig;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.List;

/**
 * 动态格配置业务对象 user_table_config
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserTableConfig.class, reverseConvertGenerate = false)
public class UserTableConfigBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 配置名称
     */
    @NotBlank(message = "配置名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String configName;

    /**
     * 表格宽度，如100%, 1200px等
     */
    private String tableWidth;

    /**
     * 备注
     */
    private String remark;

    /**
     * 表格列配置
     */
    private List<TableColumnConfig> columns;
}
