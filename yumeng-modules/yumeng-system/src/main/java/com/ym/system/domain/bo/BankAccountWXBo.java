package com.ym.system.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.common.excel.convert.ExcelDictConvert;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.system.domain.BankAccount;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 银行账户业务对象 bank_account
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BankAccount.class, reverseConvertGenerate = false)
public class BankAccountWXBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 所属id：船员id 或 公司id
     */
    @NotNull(message = "所属人不能为空")
    private Long belongId;

    /**
     * 所属名称：公司账户名或持卡人姓名
     */
    @NotBlank(message = "所属姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String belongName;

    /**
     * 银行账户类型 1、公司  2、个人
     */
    @NotBlank(message = "银行账户类型不能为空")
    private String accountType;

    /**
     * 是否可信账户  1、可信 2、不可信 可信标准，第一次打款成功后为可信
     */
    @NotBlank(message = "是否可信账户不能为空")
    private String ifBelive;

    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cardNumber;

    /**
     * 持卡人拼音
     */
    @NotBlank(message = "持卡人拼音不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cardHolder;

    /**
     * 开户银行
     */
    @NotBlank(message = "开户银行不能为空", groups = {AddGroup.class, EditGroup.class})
    private String depositBank;

    /**
     * 开户支行
     */
    @NotBlank(message = "开户支行不能为空", groups = {AddGroup.class, EditGroup.class})
    private String depositBranch;

    /**
     * 支持币种类型  1、人民币   2、外币  3、外币/人民币都支持
     */
    @NotBlank(message = "支持币种类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String currencyType;

    /**
     * 预留手机号
     */
    @NotBlank(message = "预留手机号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String reservedPhone;

    /**
     * 与账户持有者关系例如：本人 、夫妻 、子女 、父母、其它
     */
    @NotBlank(message = "与账户持有者关系不能为空", groups = {AddGroup.class, EditGroup.class})
    private String holderRelation;

    /**
     * 是否是默认卡
     */
    private String isDefault;
    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * 是否本人 1、是 2、否
     */
    @NotBlank(message = "是否是本人不能为空",  groups = {AddGroup.class, EditGroup.class})
    private String isMyself;
    /**
     * 备注
     */
    private String notes;

}
