package com.ym.system.domain.bo;

import com.ym.system.domain.TableColumnConfig;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 动态格列配置业务对象 table_column_config
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TableColumnConfig.class, reverseConvertGenerate = false)
public class TableColumnConfigBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 关联user_table_config.id
     */
    @NotNull(message = "关联user_table_config.id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long configId;

    /**
     * 列名
     */
    @NotBlank(message = "列名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String columnName;

    /**
     * 列显示名称
     */
    @NotBlank(message = "列显示名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String columnLabel;

    /**
     * 是否可见
     */
    @NotNull(message = "是否可见不能为空", groups = { AddGroup.class, EditGroup.class })
    private Boolean visible;

    /**
     * 列宽度
     */
    @NotBlank(message = "列宽度不能为空", groups = { AddGroup.class, EditGroup.class })
    private String width;

    /**
     * 排序顺序
     */
    @NotNull(message = "排序顺序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer sortOrder;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
