package com.ym.system.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.system.domain.UserplusInfo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用户附加信息视图对象 userplus_info
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UserplusInfo.class)
public class UserplusInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 真实姓名
     */
    @ExcelProperty(value = "真实姓名")
    private String fullName;

    /**
     * 身高
     */
    @ExcelProperty(value = "身高")
    private String height;

    /**
     * 体重
     */
    @ExcelProperty(value = "体重")
    private String weight;

    /**
     * 血型 1、A型   2、B型   3、AB型   来自维护个血型字典， 添加到血型字典表里；
     */
    @ExcelProperty(value = "血型 1、A型   2、B型   3、AB型   来自维护个血型字典， 添加到血型字典表里；", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "blood_type")
    private String bloodType;

    /**
     * 国籍
     */
    @ExcelProperty(value = "国籍")
    private String nationality;

    /**
     * 民族
     */
    @ExcelProperty(value = "民族")
    private String ethnicity;

    /**
     * 证件类型  1、居民身份证   2、护照
     */
    @ExcelProperty(value = "证件类型  1、居民身份证   2、护照", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "legal_cert_type")
    private String certificateType;

    /**
     * 证件号码  1、身份证号    2、护照号
     */
    @ExcelProperty(value = "证件号码  1、身份证号    2、护照号")
    private String certificateNumber;

    /**
     * 出生日期
     */
    @ExcelProperty(value = "出生日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 户籍地--省
     */
    @ExcelProperty(value = "户籍地--省")
    private String province;

    /**
     * 户籍地-- 市
     */
    @ExcelProperty(value = "户籍地-- 市")
    private String city;

    /**
     * 户籍地 -- 县
     */
    @ExcelProperty(value = "户籍地 -- 县")
    private String countryTown;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 电子签名，保存电子签名图片的地址，
     */
    @ExcelProperty(value = "电子签名，保存电子签名图片的地址，")
    private Long signature;


    /**
     * 电子签名，保存电子签名时间，
     */
    @ExcelProperty(value = "电子签名，保存电子签名图片的时间，")
    private Date signatureTime;


    /**
     * 个人操作口令（6位数字）
     */
    @ExcelProperty(value = "个人操作口令", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "6=位数字")
    private String operationPassword;

    /**
     * 联络人姓名
     */
    @ExcelProperty(value = "联络人姓名")
    private String contactName;

    /**
     * 联络人电话
     */
    @ExcelProperty(value = "联络人电话")
    private String contactPhone;

    /**
     * 联络人与本人关系  ，例如：朋友 、 夫妻  、子女   、父母   、其它
     */
    @ExcelProperty(value = "联络人与本人关系  ，例如：朋友 、 夫妻  、子女   、父母   、其它")
    private String contactAddress;

    /**
     * 证件正面
     */
    @ExcelProperty(value = "证件正面")
    private Long frontUrlId;
    /**
     * 证件背面
     */
    @ExcelProperty(value = "证件背面")
    private Long backUrlId;
    /**
     * 证件签发机关
     */
    @ExcelProperty(value = "签发机关")
    private String issuingAuthority;
    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date  idExpiration;

    /**
     * 身份证有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idStart;
    /**
     * 是否认证  1、是   0、否
     */
    @ExcelProperty(value = "是否认证  1、是   0、否", converter = ExcelDictConvert.class)
    private String isCert;

}
