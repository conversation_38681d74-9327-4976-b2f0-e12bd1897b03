package com.ym.system.domain.vo;

import com.ym.system.domain.BankAccount;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 银行账户视图对象 bank_account
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BankAccount.class)
public class BankAccountVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 所属id：船员id 或 公司id
     */
    @ExcelProperty(value = "所属id：船员id 或 公司id")
    private Long belongId;

    /**
     * 所属名称：公司账户名或持卡人姓名
     */
    @ExcelProperty(value = "所属名称：公司账户名或持卡人姓名")
    private String belongName;

    /**
     * 银行账户类型 1、公司  2、个人
     */
    @ExcelProperty(value = "银行账户类型 1、公司  2、个人", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "account_type")
    private String accountType;

    /**
     * 是否可信账户  1、可信 2、不可信 可信标准，第一次打款成功后为可信
     */
    @ExcelProperty(value = "是否可信账户  1、可信 2、不可信 可信标准，第一次打款成功后为可信", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "if_belive_account")
    private String ifBelive;

    /**
     * 银行卡号
     */
    @ExcelProperty(value = "银行卡号")
    private String cardNumber;

    /**
     * 持卡人拼音
     */
    @ExcelProperty(value = "持卡人拼音")
    private String cardHolder;

    /**
     * 账户别名
     */
    @ExcelProperty(value = "账户别名")
    private String alias;

    /**
     * 所属船舶
     */
    @ExcelProperty(value = "所属船舶")
    private String belongShip;

    /**
     * 开户银行
     */
    @ExcelProperty(value = "开户银行")
    private String depositBank;

    /**
     * 开户支行
     */
    @ExcelProperty(value = "开户支行")
    private String depositBranch;

    /**
     * 支持币种类型  1、人民币   2、外币  3、外币/人民币都支持
     */
    @ExcelProperty(value = "支持币种类型  1、人民币   2、外币  3、外币/人民币都支持", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "currency_type")
    private String currencyType;

    /**
     * Swift代码
     */
    @ExcelProperty(value = "Swift代码")
    private String swiftCode;

    /**
     * 预留手机号
     */
    @ExcelProperty(value = "预留手机号")
    private String reservedPhone;

    /**
     * 与账户持有者关系例如：本人 、夫妻 、子女 、父母、其它
     */
    @ExcelProperty(value = "与账户持有者关系例如：本人 、夫妻 、子女 、父母、其它")
    private String holderRelation;

    /**
     * 是否是默认卡
     */
    @ExcelProperty(value = "是否是默认卡", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_default_account")
    private String isDefault;

    /**
     * 是否本人 1、是 2、否
     */
    @ExcelProperty(value = "是否本人")
    private String isMyself;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String notes;


}
