package com.ym.system.domain.bo;

import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import com.ym.system.domain.ApprovalRecord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审批记录业务对象 approval_record
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ApprovalRecord.class, reverseConvertGenerate = false)
public class ApprovalRecordBo extends ApprovalRecord {

    /**
     * 公司ID
     */
    @NotNull(message = "公司ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long companyId;

    /**
     * 审批意见
     */
    @NotBlank(message = "审批意见不能为空", groups = { AddGroup.class, EditGroup.class })
    private String approvalComment;

    /**
     * 是否通知用户
     */
    private Boolean notifyUser;
}
