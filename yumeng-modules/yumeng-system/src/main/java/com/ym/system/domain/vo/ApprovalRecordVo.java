package com.ym.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.system.domain.ApprovalRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 审批记录视图对象 approval_record
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ApprovalRecord.class)
public class ApprovalRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 公司ID
     */
    @ExcelProperty(value = "公司ID")
    private Long companyId;

    /**
     * 审批人ID
     */
    @ExcelProperty(value = "审批人ID")
    private Long approverId;

    /**
     * 申请提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "申请提交时间")
    private Date applyTime;

    /**
     * 审批意见
     */
    @ExcelProperty(value = "审批意见")
    private String approvalComment;

    /**
     * 审批时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "审批时间")
    private Date approvalTime;

    /**
     * 审批人姓名
     */
    @ExcelProperty(value = "审批人姓名")
    private String approverName;

}
