package com.ym.system.domain.bo;

import com.ym.system.domain.UserplusInfo;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户附加信息业务对象 userplus_info
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UserplusInfo.class, reverseConvertGenerate = false)
public class UserplusInfoBo extends BaseEntity {

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { EditGroup.class })
    private Long userId;

    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fullName;

    /**
     * 身高
     */
    @NotNull(message = "身高不能为空", groups = { AddGroup.class, EditGroup.class })
    private String height;

    /**
     * 体重
     */
    @NotNull(message = "体重不能为空", groups = { AddGroup.class, EditGroup.class })
    private String weight;

    /**
     * 血型 1、A型   2、B型   3、AB型   来自维护个血型字典， 添加到血型字典表里；
     */
    @NotBlank(message = "血型 1、A型   2、B型   3、AB型   来自维护个血型字典， 添加到血型字典表里；不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bloodType;

    /**
     * 国籍
     */
    @NotBlank(message = "国籍不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nationality;

    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ethnicity;

    /**
     * 证件类型  1、居民身份证   2、护照
     */
    @NotBlank(message = "证件类型  1、居民身份证   2、护照不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateType;

    /**
     * 证件号码  1、身份证号    2、护照号
     */
    @NotBlank(message = "证件号码  1、身份证号    2、护照号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateNumber;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 户籍地--省
     */
    @NotBlank(message = "户籍地--省不能为空", groups = { AddGroup.class, EditGroup.class })
    private String province;

    /**
     * 户籍地-- 市
     */
    @NotBlank(message = "户籍地-- 市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String city;

    /**
     * 户籍地 -- 县
     */
    @NotBlank(message = "户籍地 -- 县不能为空", groups = { AddGroup.class, EditGroup.class })
    private String countryTown;

    /**
     * 地址
     */
    @NotBlank(message = "地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address;

    /**
     * 电子签名图片id
     */
    private Long signature;

    /**
     * 电子签名，保存电子签名时间，
     */
    private Date signatureTime;
    /**
     * 个人操作口令（6位数字）
     */
    @NotBlank(message = "个人操作口令（6位数字）不能为空", groups = { AddGroup.class})
    private String operationPassword;

    /**
     * 联络人姓名
     */
    @NotBlank(message = "联络人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contactName;

    /**
     * 联络人电话
     */
    @NotBlank(message = "联络人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contactPhone;

    /**
     * 联络人与本人关系  ，例如：朋友 、 夫妻  、子女   、父母   、其它
     */
    @NotBlank(message = "联络人与本人关系不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contactAddress;

    /**
     * 证件正面
     */
    @NotBlank(message = "身份证正面地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long frontUrlId;
    /**
     * 证件背面
     */
    @NotBlank(message = "身份证背面地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long backUrlId;

    /**
     * 证件签发机关
     */
    private String issuingAuthority;
    /**
     * 过期时间
     */
    private Date idExpiration;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idStart;
    /**
     * 是否认证  1、是   0、否
     */
    @NotBlank(message = "是否认证  1、是   0、否不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isCert;


}
