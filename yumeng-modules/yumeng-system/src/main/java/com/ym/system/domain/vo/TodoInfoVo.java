package com.ym.system.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.system.domain.TodoInfo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 待办任务视图对象 todo_info
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TodoInfo.class)
public class TodoInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 提交人ID
     */
    @ExcelProperty(value = "提交人ID")
    private Long userId;

    /**
     * 提交人名称
     */
    @ExcelProperty(value = "提交人名称")
    private String userName;

    /**
     * 目标人ID
     */
    @ExcelProperty(value = "目标人ID")
    private Long examineUserId;

    /**
     * 目标人名称
     */
    @ExcelProperty(value = "目标人名称")
    private String examineUserName;

    /**
     * 业务ID
     */
    @ExcelProperty(value = "业务ID")
    private Long businessId;

    /**
     * 业务类型(S、PSI 、、TASK)
     */
    @ExcelProperty(value = "业务类型(S、PSI 、、TASK)")
    private Long businessType;

    /**
     * 0未办、1已办
     */
    @ExcelProperty(value = "0未办、1已办")
    private Long status;

    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 已办时间
     */
    @ExcelProperty(value = "已办时间")
    private Date finishTime;

    /**
     * 提示消息
     */
    @ExcelProperty(value = "提示消息")
    private String msg;


}
