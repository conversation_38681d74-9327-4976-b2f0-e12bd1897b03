package com.ym.system.domain.bo;

import com.ym.system.domain.TodoInfo;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 待办任务业务对象 todo_info
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TodoInfo.class, reverseConvertGenerate = false)
public class TodoInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 提交人ID
     */
    @NotNull(message = "提交人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 提交人名称
     */
    @NotBlank(message = "提交人名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 目标人ID
     */
    @NotNull(message = "目标人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long examineUserId;

    /**
     * 目标人名称
     */
    @NotBlank(message = "目标人名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String examineUserName;

    /**
     * 业务ID
     */
    @NotNull(message = "业务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long businessId;

    /**
     * 业务类型(S、PSI 、、TASK)
     */
    @NotNull(message = "业务类型(S、PSI 、、TASK)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long businessType;

    /**
     * 0未办、1已办
     */
    @NotNull(message = "0未办、1已办不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * 提交时间
     */
    @NotNull(message = "提交时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date submitTime;

    /**
     * 已办时间
     */
    @NotNull(message = "已办时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date finishTime;

    /**
     * 提示消息
     */
    @NotBlank(message = "提示消息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String msg;


}
