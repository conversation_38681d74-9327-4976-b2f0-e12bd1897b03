package com.ym.system.domain.vo;

import com.ym.system.domain.SwiftCode;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * SWIFT编码视图对象 swift_code
 *
 * <AUTHOR>
 * @date 2025-05-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SwiftCode.class)
public class SwiftCodeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 银行名称
     */
    @ExcelProperty(value = "银行名称")
    private String bankName;
    @ExcelProperty(value = "银行支行名称")
    private String branchName;

    /**
     * 完整码
     */
    @ExcelProperty(value = "完整码")
    private String wholeCode;

    /**
     * 银行编码
     */
    @ExcelProperty(value = "银行编码")
    private String bankCode;

    /**
     * 国家编码
     */
    @ExcelProperty(value = "国家编码 ")
    private String countryCode;

    /**
     * 地区编码
     */
    @ExcelProperty(value = "地区编码")
    private String areaCode;

    /**
     * 补码
     */
    @ExcelProperty(value = "补码")
    private String complement;


}
