package com.ym.system.domain.bo;

import com.ym.system.domain.EmpRelation;
import com.ym.common.mybatis.core.domain.BaseEntity;
import com.ym.common.core.validate.AddGroup;
import com.ym.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用工关系业务对象 emp_relation
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = EmpRelation.class, reverseConvertGenerate = false)
public class EmpRelationBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 所属机构id，可以时公司id，也可以是公司下部门id
     */
    @NotNull(message = "所属机构id，可以时公司id，也可以是公司下部门id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long institutionId;

    /**
     * 起始时间
     */
    @NotNull(message = "起始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    /**
     * 角色（可能多个，用逗号隔开），放1个或多个角色的名字
     */
    @NotBlank(message = "角色（可能多个，用逗号隔开），放1个或多个角色的名字不能为空", groups = { AddGroup.class, EditGroup.class })
    private String roles;

    /**
     * 状态   1、启用（入职）    2、停用（离职）
     */
    @NotBlank(message = "状态   1、启用（入职）    2、停用（离职）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String state;


}
