package com.ym.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.checkerframework.checker.formatter.qual.Format;

import java.util.Date;

/**
 * 认证图片上传返回VO
 */
@Data
public class CertificationImageVo {

    /**
     * 图片ID
     */
    private Long id;

    /**
     * 图片URL
     */
    private String url;

    /**
     * 姓名(OCR识别结果)
     */
    private String name;

    /**
     * 身份证号(OCR识别结果)
     */
    private String idCard;
    /**
     * 证件签发机关
     */
    private String issuingAuthority;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idExpiration;

    /**
     *  身份证开始时间(OCR识别结果)
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date idStart;
    /**
     * 是否是永久性证件0 是 1 否
     */
    private String isPermanent;
}
