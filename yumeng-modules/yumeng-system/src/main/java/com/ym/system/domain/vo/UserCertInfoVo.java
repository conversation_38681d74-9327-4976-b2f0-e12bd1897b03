package com.ym.system.domain.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 用户证件信息响应对象
 *
 * <AUTHOR>
 */
@Data
public class UserCertInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 证件类型
     */
    private String certType;

    /**
     * 证件号码
     */
    private String certNo;

    /**
     * 职务
     */
    private String job;

    /**
     * 关联账户列表
     */
    private List<UserBankAccountVo> accounts;

    /**
     * 用户关联银行账户信息
     */
    @Data
    public static class UserBankAccountVo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 账户ID
         */
        private String accountId;

        /**
         * 账户名称
         */
        private String accountName;

        /**
         * 持卡人拼音
         */
        private String accountPinyin;

        /**
         * 银行账号
         */
        private String accountNo;

        /**
         * 开户银行
         */
        private String bankName;

        /**
         * 开户支行
         */
        private String depositBranch;

        /**
         * SWIFT代码
         */
        private String swiftCode;
    }
}
