package com.ym.system.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ym.common.core.exception.ServiceException;
import com.ym.common.core.utils.StringUtils;
import com.ym.common.satoken.utils.LoginHelper;
import com.ym.system.api.RemoteCompanyInfoService;
import com.ym.system.domain.CompanyInfo;
import com.ym.system.domain.SysUser;
import com.ym.system.domain.vo.CompanyInfoVo;
import com.ym.system.domain.vo.SysDeptVo;
import com.ym.system.mapper.CompanyInfoMapper;
import com.ym.system.mapper.SysDeptMapper;
import com.ym.system.mapper.SysUserMapper;
import com.ym.system.service.ICompanyInfoService;
import com.ym.system.service.ISysDeptService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteCompanyInfoServiceImpl implements RemoteCompanyInfoService {

    private final ICompanyInfoService companyInfoService;
    private final CompanyInfoMapper companyInfoMapper;
    private final SysDeptMapper sysDeptMapper;
    private final SysUserMapper sysUserMapper;
    /**
     * 获取公司名称根据公司ID
     * @param transportId  公司ID
     * @return 公司名称
     */
    @Override
    public String getTransportName(Long transportId) {
        CompanyInfoVo companyInfo = companyInfoService.queryById(transportId);
        return companyInfo.getCompanyName();
    }

    /**
     * 获取船舶公司类型
     *
     * @param deptId
     * @return
     */
    @Override
    public String getTransportCompanyType(Long deptId) {
        if (LoginHelper.isSuperAdmin()){
            return null;
        }
        CompanyInfo companyInfo = companyInfoService.getCompanyNameByDeptId(deptId);
        if (companyInfo == null){
            return null;
        }
        return companyInfo.getCompanyType();
    }

    /**
     * @param deptId
     * @return
     */
    @Override
    public String getCompanyNameByDeptId(Long deptId) {
        CompanyInfo companyInfo = companyInfoService.getCompanyNameByDeptId(deptId);
        return companyInfo.getCompanyName();
    }

    /**
     * 根据公司ID获取公司部门ID
     * @param companyId 公司ID
     * @return 公司部门ID
     */
    @Override
    public Long getShippingDeptId(Long companyId) {
        CompanyInfoVo companyInfo = companyInfoService.queryById(companyId);
        return companyInfo.getRelatedDept();
    }

    /**
     * 获取公司类型
     */
    @Override
    public String getCompanyTypeByUserId(Long userId) {
        Long deptId = sysUserMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserId, userId)).getDeptId();
        return this.getTransportCompanyType(deptId);
    }

}
