package com.ym.system.domain.vo;

import com.ym.system.domain.TableColumnConfig;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 动态格列配置视图对象 table_column_config
 *
 * <AUTHOR>
 * @date 2025-06-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TableColumnConfig.class)
public class TableColumnConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 关联user_table_config.id
     */
    @ExcelProperty(value = "关联user_table_config.id")
    private Long configId;

    /**
     * 列名
     */
    @ExcelProperty(value = "列名")
    private String columnName;

    /**
     * 列显示名称
     */
    @ExcelProperty(value = "列显示名称")
    private String columnLabel;

    /**
     * 是否可见
     */
    @ExcelProperty(value = "是否可见")
    private Boolean visible;

    /**
     * 列宽度
     */
    @ExcelProperty(value = "列宽度")
    private String width;

    /**
     * 排序顺序
     */
    @ExcelProperty(value = "排序顺序")
    private Integer sortOrder;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
