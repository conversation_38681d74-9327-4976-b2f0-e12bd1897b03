package com.ym.system.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ym.system.domain.EmpRelation;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ym.common.excel.annotation.ExcelDictFormat;
import com.ym.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用工关系视图对象 emp_relation
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = EmpRelation.class)
public class EmpRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 所属机构id，可以时公司id，也可以是公司下部门id
     */
    @ExcelProperty(value = "所属机构id，可以时公司id，也可以是公司下部门id")
    private Long institutionId;

    /**
     * 起始时间
     */
    @ExcelProperty(value = "起始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 角色（可能多个，用逗号隔开），放1个或多个角色的名字
     */
    @ExcelProperty(value = "角色", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "可=能多个，用逗号隔开")
    private String roles;

    /**
     * 状态   1、启用（入职）    2、停用（离职）
     */
    @ExcelProperty(value = "状态   1、启用", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "入=职")
    private String state;


}
