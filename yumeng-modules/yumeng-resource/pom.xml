<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ym</groupId>
        <artifactId>yumeng-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>yumeng-resource</artifactId>

    <description>
        yumeng-resource资源服务
    </description>

    <dependencies>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-common-sse</artifactId>
        </dependency>

        <!-- yumeng Api System -->
        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ym</groupId>
            <artifactId>yumeng-api-resource</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.2.11</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
