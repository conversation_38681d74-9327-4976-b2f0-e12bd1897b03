<template>
	<view class="container">
		<view class="card-form">
			<u-form :model="form" ref="uForm" :border-bottom="true">
				<!-- 持卡人姓名 -->
				<u-form-item label="持卡人姓名" prop="belongName" border-bottom required>
					<u-input v-model="form.belongName" placeholder="请输入持卡人姓名"
						@blur="handleNameBlur"/>
				</u-form-item>
				<!-- 持卡人拼音 -->
				<u-form-item label="持卡人拼音" prop="cardHolder" border-bottom required>
					<u-input v-model="form.cardHolder" placeholder="持卡人姓名拼音"/>
				</u-form-item>
				<!-- 银行卡号 -->
				<u-form-item label="银行卡号" prop="cardNumber" border-bottom required>
					<view class="input-with-tip">
						<u-input v-model="form.cardNumber" placeholder="请输入银行卡号（中间不要存在空格）"
							type="number" maxlength="19" @blur="handleCardNumberBlur" style="width: calc(100% - 40rpx);"/>
						<u-icon name="question-circle" size="18" color="#909399" class="tip-icon" @click="showBankCardTip"></u-icon>
					</view>
				</u-form-item>

				<!-- 开户银行 -->
				<u-form-item label="开户银行" prop="depositBank" border-bottom required>
					<u-input v-model="form.depositBank" placeholder="请输入开户银行" />
				</u-form-item>

				<!-- 开户支行 -->
				<u-form-item label="开户支行" prop="depositBranch" border-bottom required>
					<u-input v-model="form.depositBranch" placeholder="请输入开户支行" @blur="handleBranchBlur" />
				</u-form-item>

				<!-- 卡类型 -->
				<u-form-item label="卡类型" prop="isMyself" border-bottom required>
					<view class="radio-group-container">
						<u-radio-group v-model="form.isMyself" @change="handleCardTypeChange">
							<u-radio name="1">本人卡</u-radio>
							<view style="display: inline-block; width: 30rpx;"></view>
							<u-radio name="2">亲属卡</u-radio>
						</u-radio-group>
					</view>
				</u-form-item>

				<!-- 支持币种类型 -->
				<u-form-item label="币种类型" prop="currencyType" border-bottom required>
					<view class="radio-group-container">
						<u-radio-group v-model="form.currencyType" @change="handleCurrencyChange">
							<view v-for="(item, index) in currencyOptions" :key="index" class="radio-option-wrapper">
								<u-radio
									:name="item.value"
									:disabled="item.disabled"
								>
									{{item.label}}
								</u-radio>
							</view>
						</u-radio-group>
					</view>
				</u-form-item>

				<!-- Swift Code (美金账户显示) -->
				<u-form-item v-if="showSwiftCode" label="Swift Code" prop="swiftCode" border-bottom required>
					<u-input v-model="form.swiftCode" placeholder="请输入Swift Code" />
				</u-form-item>

				<!-- 预留手机号 -->
				<u-form-item label="银行预留手机号" prop="reservedPhone" border-bottom required>
					<u-input v-model="form.reservedPhone" placeholder="请输入银行预留手机号"
						type="number" maxlength="11"/>
				</u-form-item>

				<!-- 与账户持有者关系 -->
				<u-form-item label="持卡人关系" prop="holderRelation" border-bottom required v-if="form.isMyself === '2'">
					<u-input v-model="form.holderRelation" placeholder="请输入与账户持有者关系，如:本人"/>
				</u-form-item>
			</u-form>

			<view class="btn-wrap">
				<u-button type="primary" @click="submitForm">提交</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import toPinyin from '@/utils/toPinyin'
	import { addBankCard, updateBankCard, getBankCardInfo, queryBankInfo, querySwiftCodeByBank } from '@/api/system/bankCard'
	import { mapGetters } from 'vuex'

	export default {
		data() {
			return {
				isEdit: false, // 是否是编辑模式
				cardId: '', // 编辑时的卡ID
				form: {
					belongName: '',
					accountType: '2', // 始终为个人账户
					cardHolder: '',
					cardNumber: '',
					depositBank: '',
					depositBranch: '',
					currencyType: '1',  // 默认人民币
					swiftCode: '',
					reservedPhone: '',
					holderRelation: '',
					isDefault: '0', // 默认卡状态
					isMyself: '1' // 1:本人卡 2:亲属卡
				},
				showSwiftCode: false,
				currencyOptions: [
					{
						label: '人民币',
						value: '1'
					},
					{
						label: '美金',
						value: '2'
					},
					{
						label: '人民币/美金',
						value: '3'
					}
				],
				// 表单验证规则
				rules: {
					belongName: [
						{ required: true, message: '请输入持卡人姓名', trigger: 'blur' }
					],
					cardHolder: [
						{ required: true, message: '持卡人拼音不能为空', trigger: 'blur' }
					],
					cardNumber: [
						{ required: true, message: '请输入银行卡号', trigger: 'blur' },
						{ min: 10, max: 19, message: '银行卡号长度不正确', trigger: 'blur' }
					],
					depositBank: [
						{ required: true, message: '请输入开户银行', trigger: 'blur' }
					],
					depositBranch: [
						{ required: true, message: '请输入开户支行', trigger: 'blur' }
					],
					currencyType: [
						{ required: true, message: '请选择币种类型', trigger: 'change' }
					],
					swiftCode: [
						{
							required: true,
							message: '请输入Swift Code',
							trigger: 'blur',
							// 仅当显示Swift code时验证
							validator: (rule, value, callback) => {
								if (this.showSwiftCode && !value) {
									return false;
								}
								return true;
							}
						}
					],
					reservedPhone: [
						{ required: false, message: '请输入预留手机号', trigger: 'blur' },
						{
							validator: (rule, value, callback) => {
								return /^1[3-9]\d{9}$/.test(value);
							},
							message: '请输入正确的手机号码',
							trigger: 'blur'
						}
					],
					holderRelation: [
						{ required: true, message: '请输入与账户持有者关系', trigger: ['blur', 'change'] }
					]
				}
			}
		},
		computed: {
			...mapGetters([
				'certName'
			])
		},
		onLoad(options) {
			// 如果传入了cardId,说明是编辑模式
			if (options.cardId) {
				this.isEdit = true;
				this.cardId = options.cardId;
				this.getCardInfo();
			} else {
				//如果是本人卡则设置姓名
				if (this.form.isMyself === '1') {
					this.form.belongName = this.certName;
					this.form.holderRelation = '本人';
					// 触发姓名拼音转换
					this.handleNameBlur();
				}
			}

			// 设置页面标题
			uni.setNavigationBarTitle({
				title: this.isEdit ? '修改银行卡' : '添加银行卡'
			});
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			// 显示银行卡提示信息
			showBankCardTip() {
				uni.showToast({
					title: '银行卡号中间不要存在空格',
					icon: 'none',
					duration: 2000
				});
			},
			// 获取银行卡信息
			getCardInfo() {
				// 调用API获取银行卡信息
				getBankCardInfo(this.cardId).then(res => {
					if (res.code === 200) {
						// 填充表单数据
						Object.keys(this.form).forEach(key => {
							if (res.data[key] !== undefined) {
								this.form[key] = res.data[key];
							}
						});

						// 更新Swift Code显示状态
						this.handleCurrencyChange(this.form.currencyType);
					} else {
						this.$modal.showToast(res.msg);
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				}).catch(err => {
					uni.showToast({
						title: err.msg || '获取银行卡信息失败',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				});
			},
			// 姓名输入后自动填充拼音
			handleNameBlur() {
				if (this.form.belongName) {
					// 分割姓和名
					const surname = this.form.belongName.charAt(0); // 取第一个字作为姓
					const givenName = this.form.belongName.slice(1); // 剩余部分作为名

					// 分别转换姓和名的拼音
					const surnamePinyin = toPinyin.chineseToPinYin(surname);
					const givenNamePinyin = toPinyin.chineseToPinYin(givenName);

					// 处理姓的拼音
					const surnameArr = surnamePinyin.match(/[A-Z][a-z]+/g) || [];
					const surnameStr = surnameArr.join('');

					// 处理名的拼音
					const givenNameArr = givenNamePinyin.match(/[A-Z][a-z]+/g) || [];
					const givenNameStr = givenNameArr.join('');

					// 用空格连接姓和名并转大写
					this.form.cardHolder = `${surnameStr} ${givenNameStr}`.toUpperCase();
				}
			},
			// 币种类型变更
			handleCurrencyChange(value) {
				// 当选择美金账户或美金/人民币账户时，显示Swift Code字段
				this.showSwiftCode = value === '2' || value === '3';
				// 触发holderRelation的验证
				this.$refs.uForm.validateField('holderRelation');
			},
			// 卡类型变更
			handleCardTypeChange(value) {
				if (value === '1') {
					// 本人卡,自动设置关系为本人和姓名
					this.form.holderRelation = '本人';
					this.form.belongName = this.certName;
					// 触发姓名拼音转换
					this.handleNameBlur();
				} else {
					// 亲属卡,清空关系和姓名
					this.form.holderRelation = '';
					this.form.belongName = '';
					this.form.cardHolder = '';
				}
			},
			// 银行卡号输入完成后查询银行信息
			async handleCardNumberBlur() {
				if (!this.form.cardNumber) return;

				// 验证卡号长度
				if (this.form.cardNumber.length < 6) {
					this.$modal.showToast('请输入正确的银行卡号');
					return;
				}

				try {
					// 调用接口查询银行信息
					const res = await queryBankInfo(this.form.cardNumber);
					// 处理查询结果
					if (res.code === 200) {
						if(res.data != this.form.depositBank ){
							this.form.swiftCode = ''
						}
						this.form.depositBank = res.data || '';
					}
				} catch (err) {
					this.$modal.showToast(err.msg || '查询银行信息失败，请重试');
				}
			},

			// 开户支行输入完成后查询Swift Code
			async handleBranchBlur() {
				// 只有当需要显示Swift Code且开户行和开户支行都已填写时才查询
				if (!this.showSwiftCode || !this.form.depositBank || !this.form.depositBranch) {
					return;
				}

				try {
					// 调用接口查询Swift Code
					const res = await querySwiftCodeByBank(this.form.depositBank, this.form.depositBranch);
					// 处理查询结果
					if (res.code === 200 && res.data) {
						this.form.swiftCode = res.data;
					}
				} catch (err) {
				}
			},
			// 提交表单
			submitForm() {
				this.$refs.uForm.validate().then(valid => {
					if (valid) {
						// 检查必填项
						if (this.showSwiftCode && !this.form.swiftCode) {
							return this.$modal.showToast('请输入Swift Code');
						}

						// 检查holderRelation
						if (this.form.isMyself === '2' && !this.form.holderRelation) {
							//console.error('holderRelation为空:', this.form);
							return this.$modal.showToast('与账户持有者关系不能为空');
						}

						// 添加拼音提示弹框
						uni.showModal({
							title: '温馨提示',
							content: '请注意：持卡人拼音可能存在多音字，请以护照上的拼音为准。确认无误后继续提交。',
							confirmText: '确认提交',
							cancelText: '返回修改',
							success: (res) => {
								if (res.confirm) {
									// 用户点击确认，继续提交
									this.$modal.loading(this.isEdit ? '保存中...' : '提交中...');

									// 准备提交的数据
									const submitData = {
										...this.form,
										isMyself: this.form.isMyself // 确保isMyself字段被包含
									};
									if (this.isEdit) {
										submitData.id = this.cardId;
									}

									// 调用API提交数据
									const apiCall = this.isEdit ? updateBankCard(submitData) : addBankCard(submitData);
									apiCall.then(res => {
										if (res.code === 200) {
											this.$modal.showToast(this.isEdit ? '修改成功' : '添加成功');
											setTimeout(() => {
												uni.navigateBack();
											}, 1500);
										}
									});
								}
							}
						});
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}

	.card-form {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
	}

	.u-form-item {
		padding: 10rpx 0;
	}

	.u-form-item__body {
		display: flex !important;
		flex-direction: column !important;
		align-items: flex-start !important;
	}

	.u-form-item__body__left {
		width: 100% !important;
		margin-bottom: 10rpx !important;
	}

	.u-form-item__body__right {
		width: 100% !important;
	}

	.u-input {
		width: 100% !important;
	}

	.input-with-tip {
		display: flex;
		align-items: center;
		width: 100%;
	}
	
	.tip-icon {
		margin-left: 10rpx;
		display: flex;
		align-items: center;
	}

	.btn-wrap {
		margin-top: 60rpx;
		padding: 0 30rpx;
	}
	
	/* 单选按钮样式 */
	.radio-group-container {
		width: 100%;
	}
	
	.radio-option-wrapper {
		display: inline-block;
		margin-right: 30rpx;
		margin-bottom: 10rpx;
	}
</style>
