<template>
	<view class="container">
		<view class="header">
			<view class="title">我的银行卡</view>
			<view class="add-btn" @click="handleToAdd">
				<u-icon name="plus" color="#FFFFFF" size="18"></u-icon>
			</view>
		</view>

		<!-- 银行卡列表 -->
		<view class="card-list">
			<view v-for="(card, index) in cardList" :key="index" class="card-item"
				:class="[card.currencyType === '1' ? 'bg-blue' : (card.currencyType === '2' ? 'bg-red' : 'bg-purple')]">
				<view class="card-top">
					<view class="bank-name">{{card.depositBank}}银行</view>
					<view class="card-owner">{{card.belongName}}</view>
					<view class="account-type">{{card.accountType === '2' ? '个人账户' : '企业账户'}}</view>
					<view class="is-belive">{{card.isBelive === '1' ? '已验证' : '未验证'}}</view>
				</view>
				<view class="delete-btn" @click.stop="handleDelete(card.id)">
					<u-icon name="trash" color="#FFFFFF" size="16"></u-icon>
				</view>
				<view class="edit-btn" @click.stop="handleEdit(card)">
					<u-icon name="edit-pen" color="#FFFFFF" size="16"></u-icon>
				</view>
				<view class="card-number">
					<text class="number-text">{{formatCardNumber(card.cardNumber, card.showNumber)}}</text>
					<view class="eye-icon" @tap.stop="toggleCardNumber(card)">
						<u-icon :name="card.showNumber ? 'eye' : 'eye-off'" color="#FFFFFF" size="16"></u-icon>
					</view>
				</view>
				<view class="card-bottom">
					<view class="currency-type">
						{{card.currencyType === '1' ? '人民币账户' : (card.currencyType === '2' ? '美金账户' : '美金/人民币账户')}}
					</view>
					<view class="default-checkers">
						<!-- 人民币账户或双币账户显示默认人民币选择框 -->
						<view v-if="card.currencyType === '1' || card.currencyType === '3'" class="default-checker">
							<text class="default-label">默认人民币账户</text>
							<checkbox :checked="card.isDefault === '2' || card.isDefault === '3'" @tap.stop="toggleDefault(card, '2')" color="#FFFFFF" />
						</view>
						<!-- 美金账户或双币账户显示默认美金选择框 -->
						<view v-if="card.currencyType === '2' || card.currencyType === '3'" class="default-checker">
							<text class="default-label">默认美金账户</text>
							<checkbox :checked="card.isDefault === '1' || card.isDefault === '3'" @tap.stop="toggleDefault(card, '1')" color="#FFFFFF" />
						</view>
					</view>
				</view>
			</view>

			<!-- 无数据提示 -->
			<view v-if="cardList.length === 0" class="no-data">
				<u-empty mode="list" text="暂无银行卡，请添加"></u-empty>
			</view>
		</view>
	</view>
</template>

<script>
	import { getBankCardList, deleteBankCard, updateBankCardDefault } from '@/api/system/bankCard'
	import { mapGetters } from 'vuex'

	export default {
		data() {
			return {
				cardList: []
			}
		},
		computed: {
			...mapGetters([
				'isCert'
			])
		},
		onShow() {
			if (!this.isCert) {
				// 未认证，弹框提示
				uni.showModal({
					title: '温馨提示',
					content: '请先完成实名认证，才能管理银行卡',
					confirmText: '去认证',
					cancelText: '取消',
					success: (modalRes) => {
						if (modalRes.confirm) {
							uni.redirectTo({
								url: '/packageMine/auth/index'
							})
						} else if (modalRes.cancel) {
							// 取消时返回上一级
							uni.navigateBack()
						}
					}
				})
				return
			}
			// 已认证，加载银行卡列表
			this.getCardList()
		},
		methods: {
			// 获取银行卡列表
			getCardList() {
				getBankCardList().then(res => {
				  if (res.code === 200) {
				    this.cardList = res.data
						// console.log(this.cardList);
				  }
				})

			},
			// 格式化卡号
			formatCardNumber(cardNumber, showNumber) {
				// 保留前4位和后4位，其余用*代替
				if (!cardNumber) return ''
				const length = cardNumber.length
				if (length <= 8) return cardNumber

				if (showNumber) {
					// 显示完整卡号，每4位加一个空格
					return cardNumber.replace(/(\d{4})(?=\d)/g, '$1 ')
				}

				const prefix = cardNumber.substr(0, 4)
				const suffix = cardNumber.substr(length - 4, 4)
				const maskLength = length - 8
				const mask = '**** **** '.substr(0, maskLength)

				return `${prefix} ${mask} ${suffix}`
			},
			// 切换卡号显示状态
			toggleCardNumber(card) {
				this.$set(card, 'showNumber', !card.showNumber)
			},
			// 切换默认卡状态
			toggleDefault(card, defaultType) {
				// 验证默认类型是否合法
				if (!this.isValidDefaultType(card.currencyType, defaultType)) {
					uni.showToast({
						title: '该卡不支持此默认类型',
						icon: 'none'
					});
					return;
				}

				// 如果当前已经是该类型的默认卡,则取消默认
				const newDefault = card.isDefault === defaultType ? '0' : defaultType;

				// 更新卡片默认状态
				this.cardList.forEach(item => {
					if (item.id === card.id) {
						item.isDefault = newDefault;
					} else if (newDefault !== '0') {
						// 如果设置了新的默认卡,其他同类型的卡需要取消默认
						if (this.shouldCancelDefault(item, newDefault)) {
							item.isDefault = '0';
						}
					}
				});

				// 调用API更新默认卡状态
				updateBankCardDefault(card.id, newDefault).then(res => {
					if (res.code === 200) {
						uni.showToast({
							title: newDefault === '0' ? '已取消默认' : '设置成功',
							icon: 'success'
						});
						// 重新获取银行卡列表,确保数据同步
						this.getCardList();
					}
				}).catch(err => {
					// 如果请求失败,回滚界面状态
					this.getCardList();
					uni.showToast({
						title: '设置失败',
						icon: 'error'
					});
				});
			},

			// 验证默认类型是否合法
			isValidDefaultType(currencyType, defaultType) {
				// 取消默认总是合法的
				if (defaultType === '0') return true;

				switch (currencyType) {
					case '1': // 仅人民币
						return defaultType === '2';
					case '2': // 仅外币
						return defaultType === '1';
					case '3': // 双币
						return ['1', '2', '3'].includes(defaultType);
					default:
						return false;
				}
			},

			// 判断是否需要取消其他卡的默认状态
			shouldCancelDefault(card, newDefault) {
				// 如果是双币默认
				if (newDefault === '3') {
					return card.currencyType === '3';
				}

				// 如果是人民币默认
				if (newDefault === '2') {
					return (card.currencyType === '1' || card.currencyType === '3') &&
						(card.isDefault === '2' || card.isDefault === '3');
				}

				// 如果是外币默认
				if (newDefault === '1') {
					return (card.currencyType === '2' || card.currencyType === '3') &&
						(card.isDefault === '1' || card.isDefault === '3');
				}

				return false;
			},
			// 跳转到添加银行卡页面
			handleToAdd() {
				uni.navigateTo({
					url: '/packageMine/bankCard/add'
				})
			},
			// 跳转到修改银行卡页面
			handleEdit(card) {
				uni.navigateTo({
					url: `/packageMine/bankCard/add?cardId=${card.id}`
				})
			},
			// 删除银行卡
			handleDelete(cardId) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该银行卡吗？',
					success: (res) => {
						if (res.confirm) {
							deleteBankCard(cardId).then(res => {
							  if (res.code === 200) {
							    this.$modal.showToast('删除成功')
							    this.getCardList()
							  }
							})

						}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.container {
		padding: 20rpx;
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;

		.title {
			font-size: 32rpx;
			font-weight: bold;
		}

		.add-btn {
			width: 60rpx;
			height: 60rpx;
			background-color: #3c96f3;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.card-list {
		margin-top: 20rpx;

		.card-item {
			padding: 30rpx;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			color: #fff;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
			position: relative;

			&.bg-blue {
				background: linear-gradient(to right, #36d1dc, #5b86e5);
			}

			&.bg-red {
				background: linear-gradient(to right, #ff6b6b, #ff8e53);
			}

			&.bg-purple {
				background: linear-gradient(to right, #cc2b5e, #753a88);
			}

			.card-top {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				margin-bottom: 20rpx;
				padding-right: 50rpx;

				.bank-name {
					font-size: 36rpx;
					font-weight: bold;
					margin-right: 20rpx;
				}

				.card-owner {
					font-size: 28rpx;
				}

				.account-type {
					font-size: 24rpx;
					margin-left: 20rpx;
				}

				.is-belive {
					font-size: 24rpx;
					margin-left: 20rpx;
				}
			}

			.delete-btn {
				position: absolute;
				top: 20rpx;
				right: 20rpx;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				padding: 5rpx;
			}

			.edit-btn {
				position: absolute;
				top: 20rpx;
				right: 70rpx;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				padding: 5rpx;
			}

			.card-number {
				font-size: 36rpx;
				letter-spacing: 4rpx;
				margin: 20rpx 0 40rpx;
				position: relative;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.number-text {
					flex: 1;
				}

				.eye-icon {
					width: 40rpx;
					height: 40rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: rgba(255, 255, 255, 0.2);
					border-radius: 50%;
					padding: 5rpx;
					margin-left: 20rpx;
				}
			}

			.card-bottom {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;

				.currency-type {
					font-size: 24rpx;
				}

				.default-checkers {
					display: flex;
					flex-direction: column;
					align-items: flex-end;
					gap: 10rpx;

					.default-checker {
						display: flex;
						align-items: center;
						gap: 10rpx;

						.default-label {
							font-size: 24rpx;
							color: rgba(255, 255, 255, 0.8);
						}
					}
				}
			}
		}

		.no-data {
			padding: 100rpx 0;
		}
	}
</style>
