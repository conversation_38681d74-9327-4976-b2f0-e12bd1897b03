<template>
  <view class="feedback-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">提交反馈</text>
      <text class="page-subtitle">请详细描述您遇到的问题，我们会尽快解决</text>
    </view>

    <view class="content">
      <!-- 问题描述区域 -->
      <view class="section description-section">
        <view class="section-title">
          <view class="title-wrapper">
            <text class="required">*</text>
            <text>问题描述或建议</text>
          </view>
          <view class="char-count">{{ formData.description.length }}/500</view>
        </view>
        <view class="textarea-wrapper">
          <textarea
            v-model="formData.description"
            class="textarea"
            placeholder="请详细描述您遇到的问题或建议，并提供操作步骤和现象..."
            maxlength="500"
            :show-confirm-bar="false"
          />
        </view>
      </view>

      <!-- 图片上传区域 -->
      <view class="section upload-section">
        <view class="section-title">
          <text>上传截图（最多3张）</text>
        </view>
        <view class="upload-container">
          <view
            v-for="(item, index) in imageList"
            :key="index"
            class="image-item"
          >
            <image
              :src="item"
              class="uploaded-image"
              @click="previewImage(index)"
              mode="aspectFill"
            />
            <view class="delete-btn" @click="deleteImage(index)">
              <text class="delete-icon">×</text>
            </view>
          </view>
          <view
            v-if="imageList.length < 3"
            class="upload-btn"
            @click="chooseImage"
          >
            <text class="upload-icon">+</text>
            <text class="upload-text">添加图片</text>
          </view>
        </view>
      </view>

      <!-- 联系方式区域 -->
      <view class="section contact-section">
        <view class="section-title">
          <text>联系方式</text>
        </view>
        
        <view class="contact-form">
          <view class="contact-item">
            <view class="contact-icon">
              <text class="iconfont icon-phone">📱</text>
            </view>
            <view class="contact-content">
              <input
                v-model="formData.phone"
                class="contact-input"
                placeholder="手机号码"
                type="number"
                maxlength="11"
                @blur="validatePhone"
              />
            </view>
          </view>
          <view v-if="errors.phone" class="error-message">{{ errors.phone }}</view>

          <view class="contact-item">
            <view class="contact-icon">
              <text class="iconfont icon-email">✉️</text>
            </view>
            <view class="contact-content">
              <input
                v-model="formData.email"
                class="contact-input"
                placeholder="邮箱"
                type="text"
                @blur="validateEmail"
              />
            </view>
          </view>

          <view class="contact-switch-item">
            <view class="contact-switch-label">是否允许联系</view>
            <view 
              class="contact-switch" 
              :class="{ 'switch-active': formData.allowContact === 1 }"
              @click="toggleContact"
            >
              <view class="switch-handle"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部提交按钮 -->
    <view class="footer">
      <button
        class="submit-btn"
        :class="{ disabled: !canSubmit }"
        :disabled="!canSubmit"
        @click="submitFeedback"
      >
        提交反馈
      </button>
    </view>

    <!-- 加载提示 -->
    <uni-load-more
      v-if="loading"
      status="loading"
      content-text="{ contentText: { contentdown: '上传中...', contentrefresh: '上传中...', contentnomore: '上传完成' } }"
    />
  </view>
</template>

<script>
import { uploadFeedbackImage, submitFeedback } from "@/api/feedback";

export default {
  data() {
    return {
      loading: false,
      uploadingCount: 0, // 正在上传的图片数量
      formData: {
        description: "",
        phone: "",
        email: "",
        allowContact: 0, // 0-不允许，1-允许
      },
      imageList: [], // 显示的图片列表
      ossIdList: [], // 对应的ossId列表
      errors: {
        phone: ""
      }
    };
  },
  computed: {
    canSubmit() {
      return this.formData.description.trim().length > 0;
    },
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 验证手机号
    validatePhone() {
      if (this.formData.phone && !/^1[3-9]\d{9}$/.test(this.formData.phone)) {
        this.errors.phone = "请输入正确的手机号码";
      } else {
        this.errors.phone = "";
      }
    },

    // 验证邮箱
    validateEmail() {
      // 邮箱验证逻辑已在提交时处理
    },

    // 选择图片并立即上传
    chooseImage() {
      const remainCount = 3 - this.imageList.length;
      uni.chooseImage({
        count: remainCount,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          // 先添加到显示列表
          const tempFilePaths = res.tempFilePaths;
          this.imageList = this.imageList.concat(tempFilePaths);

          // 立即上传每张图片
          this.uploadingCount += tempFilePaths.length;
          this.loading = true;

          tempFilePaths.forEach((filePath, index) => {
            this.uploadSingleImage(
              filePath,
              this.imageList.length - tempFilePaths.length + index
            );
          });
        },
        fail: (err) => {
          console.error("选择图片失败:", err);
          uni.showToast({
            title: "选择图片失败",
            icon: "none",
          });
        },
      });
    },

    // 上传单张图片
    uploadSingleImage(filePath, index) {
      // 构造上传数据，与电子签名上传保持一致
      const uploadData = {
        name: 'file',
        filePath: filePath
      }
      
      uploadFeedbackImage(uploadData)
        .then((res) => {
          if (res.code === 200 && res.data) {
            // 保存ossId
            if (!this.ossIdList[index]) {
              // 直接存储返回的data值，因为data就是ossId
              this.ossIdList[index] = res.data;
            }
          } else {
            uni.showToast({
              title: "图片上传失败",
              icon: "none",
            });
            // 移除上传失败的图片
            this.imageList.splice(index, 1);
            this.ossIdList.splice(index, 1);
          }
        })
        .catch((err) => {
          console.error("上传图片失败:", err);
          uni.showToast({
            title: "图片上传失败",
            icon: "none",
          });
          // 移除上传失败的图片
          this.imageList.splice(index, 1);
          this.ossIdList.splice(index, 1);
        })
        .finally(() => {
          this.uploadingCount--;
          if (this.uploadingCount === 0) {
            this.loading = false;
          }
        });
    },

    // 预览图片
    previewImage(index) {
      uni.previewImage({
        urls: this.imageList,
        current: index,
      });
    },

    // 删除图片
    deleteImage(index) {
      uni.showModal({
        title: "提示",
        content: "确定要删除这张图片吗？",
        success: (res) => {
          if (res.confirm) {
            // 同时删除图片和对应的ossId
            this.imageList.splice(index, 1);
            this.ossIdList.splice(index, 1);
          }
        },
      });
    },

    // 切换允许联系状态
    toggleContact() {
      this.formData.allowContact = this.formData.allowContact === 1 ? 0 : 1;
    },
// 提交反馈
    async submitFeedback() {
      if (!this.canSubmit) return;

      // 验证联系方式
      if (this.formData.allowContact === 1) {

        // 验证手机号格式
        if (this.formData.phone && !/^1[3-9]\d{9}$/.test(this.formData.phone)) {
          uni.showToast({
            title: "请输入正确的手机号码",
            icon: "none",
          });
          return;
        }

        // 验证邮箱格式
        if (
          this.formData.email &&
          !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.formData.email)
        ) {
          uni.showToast({
            title: "请输入正确的邮箱地址",
            icon: "none",
          });
          return;
        }
      }

      // 检查是否有图片正在上传
      if (this.uploadingCount > 0) {
        uni.showToast({
          title: "图片上传中，请稍候...",
          icon: "none",
        });
        return;
      }

      this.loading = true;

      // 提交反馈数据，使用已上传的图片ossId
      const feedbackData = {
        description: this.formData.description.trim(),
        imageIds: this.ossIdList,
        phone: this.formData.allowContact === 1 ? this.formData.phone : "",
        email: this.formData.allowContact === 1 ? this.formData.email : "",
        allowContact: Number(this.formData.allowContact), // 确保发送数字类型
        priority: 0, // 设置默认优先级
      };
      console.log('提交数据:', JSON.stringify(feedbackData));
        // 调用反馈API
        const result = await submitFeedback(feedbackData);
        if (result.code === 200) {
          uni.showToast({
            title: "反馈提交成功",
            icon: "success",
          });
          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      
    },
  },
};
</script>

<style lang="scss" scoped>
.feedback-page {
  min-height: 100vh;
  background-color: #f8f9fc;
  display: flex;
  flex-direction: column;
  padding-bottom: 130rpx;
}

.page-header {
  padding: 60rpx 40rpx 20rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #888888;
  line-height: 1.4;
}

.content {
  flex: 1;
  padding: 24rpx 30rpx;
}

.section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  
  .title-wrapper {
    display: flex;
    align-items: center;
    
    .required {
      color: #ff4757;
      margin-right: 8rpx;
      font-weight: bold;
    }
    
    text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }
}

.description-section {
  border-left: 8rpx solid #007aff;
}

.textarea-wrapper {
  background-color: #f8f9fc;
  border-radius: 16rpx;
  padding: 4rpx;
}

.textarea {
  width: 100%;
  min-height: 240rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: transparent;
  box-sizing: border-box;
  line-height: 1.6;
}

.char-count {
  font-size: 24rpx;
  color: #999999;
}

.upload-section {
  border-left: 8rpx solid #36cfc9;
}

.upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.image-item {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 71, 87, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  color: #ffffff;
  font-size: 28rpx;
  line-height: 1;
}

.upload-btn {
  width: 180rpx;
  height: 180rpx;
  border: 2rpx dashed #cccccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fc;
}

.upload-icon {
  font-size: 48rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999999;
}

.contact-section {
  border-left: 8rpx solid #faad14;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fc;
  border-radius: 12rpx;
}

.contact-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-content {
  flex: 1;
}

.contact-input {
  font-size: 28rpx;
  color: #333333;
  width: 100%;
}

.contact-switch-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f8f9fc;
  border-radius: 12rpx;
}

.contact-switch-label {
  font-size: 28rpx;
  color: #333333;
}

.contact-switch {
  position: relative;
  width: 90rpx;
  height: 50rpx;
  background-color: #e0e0e0;
  border-radius: 25rpx;
  transition: all 0.3s ease;
  
  .switch-handle {
    position: absolute;
    top: 5rpx;
    left: 5rpx;
    width: 40rpx;
    height: 40rpx;
    background-color: #ffffff;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }
  
  &.switch-active {
    background-color: #007aff;
    
    .switch-handle {
      left: 45rpx;
    }
  }
}

.error-message {
  color: #ff4757;
  font-size: 24rpx;
  padding-left: 20rpx;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 30rpx calc(24rpx + env(safe-area-inset-bottom));
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  background-image: linear-gradient(135deg, #0099ff, #007aff);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 2rpx;

  &.disabled {
    background-image: none;
    background-color: #cccccc;
    color: #999999;
  }

  &:not(.disabled):active {
    transform: scale(0.98);
    background-image: linear-gradient(135deg, #0080ff, #0066cc);
  }
}
</style>
