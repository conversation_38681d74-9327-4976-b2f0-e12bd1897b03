<template>
  <view class="signature-container">


    <!-- 已有签名显示区域 -->
    <view v-if="hasSignature" class="signature-display">
      <view class="signature-info">
        <text>已设置电子签名(一年仅可修改一次)</text>
        <text class="signature-time">上次签名时间：{{signatureTime}}</text>
      </view>
      <view class="signature-image-container">
        <image :src="signatureUrl" class="signature-image" mode="aspectFit" @load="onSignatureImageLoad"></image>
        <canvas canvas-id="watermarkCanvas" class="watermark-canvas"></canvas>
      </view>
    </view>

    <!-- 签名创建区域 -->
    <view v-if="!hasSignature || canEditSignature" class="signature-create">
      <view v-if="!isFullscreenMode" class="signature-button-container">
        <button class="signature-button" @click="startFullscreenSignature">点击签名</button>
        <view class="signature-info">
          <text v-if="hasSignature">您的签名已超过一年，可以设置新的签名</text>
          <text v-else>点击按钮进行电子签名（保存后不可修改）</text>
        </view>
      </view>
    </view>
    
    <!-- 全屏签名模式 -->
    <view v-if="isFullscreenMode" class="fullscreen-signature">
      <view class="fullscreen-signature-container">
        <canvas
          canvas-id="signatureCanvas"
          class="fullscreen-canvas"
          @touchstart="touchStart"
          @touchmove="touchMove"
          @touchend="touchEnd"
        ></canvas>
      </view>
      <view class="fullscreen-actions">
        <button class="action-button clear" @click="clearCanvas">清除</button>
        <button class="action-button save" @click="saveSignature" :disabled="!canSave">保存</button>
        <button class="action-button cancel" @click="cancelFullscreenSignature">取消</button>
      </view>
    </view>
    
    <!-- 用于旋转处理的隐藏画布 -->
    <view v-if="showRotateCanvas" class="rotate-canvas-container" :style="{width: rotateCanvasWidth + 'px', height: rotateCanvasHeight + 'px'}">
      <canvas canvas-id="rotateCanvas" class="rotate-canvas"></canvas>
    </view>
  </view>
</template>

<script>
import { getUserSignature, uploadSignature } from '@/api/system/signature'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      hasSignature: false,
      signatureUrl: '',
      signatureTime:'',
      context: null,
      points: [],
      canSave: false,
      isDrawing: false,
      isFullscreenMode: false, // 控制全屏签名模式
      showRotateCanvas: false, // 控制旋转画布显示
      rotateCanvasWidth: 0,    // 旋转画布宽度
      rotateCanvasHeight: 0    // 旋转画布高度
    }
  },
  computed: {
    ...mapGetters([
      'isCert',
      'certName'
    ]),
    canEditSignature() {
      if (!this.signatureTime) return true
      const currentTime = new Date()
      const signatureDate = new Date(this.signatureTime)
      const diffTime = currentTime - signatureDate
      const oneYear = 365 * 24 * 60 * 60 * 1000 // 一年的毫秒数
      return diffTime >= oneYear
    }
  },
  onLoad() {
    if (!this.isCert) {
      // 未认证，弹框提示
      uni.showModal({
        title: '温馨提示',
        content: '请先完成实名认证，才能设置电子签名',
        confirmText: '去认证',
        cancelText: '取消',
        success: (modalRes) => {
          if (modalRes.confirm) {
            uni.redirectTo({
              url: '/packageMine/auth/index'
            })
          } else if (modalRes.cancel) {
            // 取消时返回上一级
            uni.navigateBack()
          }
        }
      })
      return
    }
    // 已认证，继续后续逻辑
    this.checkSignature()
  },
  onReady() {
    // 不再自动初始化画布，改为在全屏模式开启时初始化
  },
  methods: {
    // 查询是否已有签名
    async checkSignature() {
      try {
        const res = await getUserSignature()
        if (res.data) {
          this.hasSignature = true
          this.signatureUrl = res.data.url
          this.signatureTime = res.data.time
          // 确保在获取到签名后重新绘制水印
          this.$nextTick(() => {
            this.drawWatermark()
          })
        }
      } catch (error) {
        console.error('获取签名失败', error)
        this.$modal.showToast('获取签名信息失败')
      }
    },

    // 初始化画布
    initCanvas() {
      this.context = uni.createCanvasContext('signatureCanvas', this)
      this.context.strokeStyle = '#000000'
      this.context.lineWidth = 5
      this.context.lineCap = 'round'
      this.context.lineJoin = 'round'
      
      // 重置绘图状态
      this.points = []
      this.canSave = false
      
      // 监听屏幕方向变化，确保画布适配
      uni.onWindowResize(() => {
        if (this.isFullscreenMode) {
          // 触发画布重绘以适应新尺寸
          setTimeout(() => {
            if (this.points.length > 0) {
              this.redrawCanvas()
            }
          }, 300)
        }
      })
    },
    
    // 重绘画布
    redrawCanvas() {
      if (!this.context || this.points.length === 0) return
      
      this.context.clearRect(0, 0, 5000, 5000)
      
      for (let i = 0; i < this.points.length - 1; i++) {
        const point = this.points[i]
        const nextPoint = this.points[i + 1]
        
        this.context.beginPath()
        this.context.moveTo(point.x, point.y)
        this.context.lineTo(nextPoint.x, nextPoint.y)
        this.context.stroke()
      }
      
      this.context.draw(true)
    },
    
    // 启动全屏签名模式
    startFullscreenSignature() {
      this.isFullscreenMode = true
      
      // 检查并锁定屏幕方向为横屏（如果平台支持）
      try {
        // 尝试锁定屏幕为横屏模式
        if (uni.getSystemInfoSync().platform !== 'devtools') {
          uni.setPageOrientation && uni.setPageOrientation({
            orientation: 'landscape',
            success: () => {
              console.log('设置横屏成功')
            },
            fail: () => {
              console.log('设置横屏失败，请手动旋转设备')
            }
          })
        }
      } catch (e) {
        console.error('设置屏幕方向出错', e)
      }
      
      this.$nextTick(() => {
        this.initCanvas()
      })
    },
    
    // 取消全屏签名
    cancelFullscreenSignature() {
      this.isFullscreenMode = false
      this.points = []
      this.canSave = false
      
      // 恢复屏幕方向为自动（如果平台支持）
      try {
        if (uni.getSystemInfoSync().platform !== 'devtools') {
          uni.setPageOrientation && uni.setPageOrientation({
            orientation: 'auto',
            success: () => {
              console.log('恢复屏幕方向成功')
            },
            fail: () => {
              console.log('恢复屏幕方向失败')
            }
          })
        }
      } catch (e) {
        console.error('恢复屏幕方向出错', e)
      }
    },

    // 触摸开始
    touchStart(e) {
      this.isDrawing = true
      const point = {
        x: e.touches[0].x,
        y: e.touches[0].y
      }
      this.points.push(point)
      this.context.beginPath()
      this.context.moveTo(point.x, point.y)
      this.context.lineTo(point.x, point.y)
      this.context.stroke()
      this.context.draw(true)
    },

    // 触摸移动
    touchMove(e) {
      if (!this.isDrawing) return
      const point = {
        x: e.touches[0].x,
        y: e.touches[0].y
      }
      this.points.push(point)
      this.context.beginPath()
      this.context.moveTo(this.points[this.points.length - 2].x, this.points[this.points.length - 2].y)
      this.context.lineTo(point.x, point.y)
      this.context.stroke()
      this.context.draw(true)
      this.canSave = true
    },

    // 触摸结束
    touchEnd() {
      this.isDrawing = false
    },

    // 清除画布
    clearCanvas() {
      this.context.clearRect(0, 0, 1000, 1000)
      this.context.draw()
      this.points = []
      this.canSave = false
    },

    // 保存签名
    saveSignature() {
      if (!this.canSave) {
        this.$modal.showToast('请先绘制签名')
        return
      }

      this.$modal.confirm('签名保存后将无法修改，确认保存？').then(() => {
        // 保存前恢复屏幕方向为自动（如果平台支持）
        try {
          if (uni.getSystemInfoSync().platform !== 'devtools') {
            uni.setPageOrientation && uni.setPageOrientation({
              orientation: 'auto'
            })
          }
        } catch (e) {
          console.error('恢复屏幕方向出错', e)
        }
        
        // 获取原始画布信息
        const query = uni.createSelectorQuery().in(this)
        query.select('.fullscreen-canvas').boundingClientRect(data => {
          if (!data) {
            this.$modal.showToast('获取画布尺寸失败')
            return
          }
          
          const originWidth = data.width
          const originHeight = data.height
          
          // 创建临时旋转画布
          const rotateCanvasId = 'rotateCanvas'
          
          // 动态添加旋转画布到模板
          this.$set(this, 'showRotateCanvas', true)
          
          this.$nextTick(() => {
            // 初始化旋转画布
            const rotateContext = uni.createCanvasContext(rotateCanvasId, this)
            
            // 设置旋转画布尺寸（宽高对调）
            this.rotateCanvasWidth = originHeight
            this.rotateCanvasHeight = originWidth
            
            // 获取原始画布内容为图片
            uni.canvasToTempFilePath({
              canvasId: 'signatureCanvas',
              success: (res) => {
                // 在旋转画布上绘制旋转后的图像
                rotateContext.save()
                rotateContext.translate(0, originWidth)
                rotateContext.rotate(-90 * Math.PI / 180) // 逆时针旋转90度
                rotateContext.drawImage(res.tempFilePath, 0, 0, originWidth, originHeight)
                rotateContext.restore()
                rotateContext.draw(false, () => {
                  // 旋转完成后，将旋转后的画布保存为图片
                  setTimeout(() => {
                    uni.canvasToTempFilePath({
                      canvasId: rotateCanvasId,
                      success: (rotateRes) => {
                        // 隐藏旋转画布
                        this.$set(this, 'showRotateCanvas', false)
                        
                        // 上传旋转后的图片
                        this.uploadSignatureImage(rotateRes.tempFilePath)
                        // 关闭全屏签名模式
                        this.isFullscreenMode = false
                      },
                      fail: (err) => {
                        // 隐藏旋转画布
                        this.$set(this, 'showRotateCanvas', false)
                        
                        console.error('生成旋转图片失败', err)
                        this.$modal.showToast('签名生成失败')
                      }
                    }, this)
                  }, 300) // 给画布渲染一些时间
                })
              },
              fail: (err) => {
                // 隐藏旋转画布
                this.$set(this, 'showRotateCanvas', false)
                
                console.error('获取原始画布图片失败', err)
                this.$modal.showToast('签名生成失败')
              }
            }, this)
          })
        }).exec()
      }).catch(() => {
        // 用户取消保存时不做任何操作
      })
    },

    // 上传签名图片
    async uploadSignatureImage(filePath) {
      try {
        uni.showLoading({
          title: '正在保存...'
        })
        const res = await uploadSignature({
          name: 'file',
          filePath: filePath
        })
        uni.hideLoading()

        if (res.code === 200) {
          this.$modal.showToast('签名保存成功')
          // 重新获取签名信息
          await this.checkSignature()
        } else {
          this.$modal.showToast('签名保存失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('上传签名失败', error)
        this.$modal.showToast('签名上传失败')
      }
    },

    // 签名图片加载完成后绘制水印
    onSignatureImageLoad(e) {
      this.$nextTick(() => {
        this.drawWatermark()
      })
    },

    // 绘制水印
    drawWatermark() {
      if (!this.certName) return

      const query = uni.createSelectorQuery().in(this)
      query.select('.signature-image-container').boundingClientRect(data => {
        if (!data) return

        const { width, height } = data
        const canvas = uni.createCanvasContext('watermarkCanvas', this)

        // 设置水印样式
        canvas.font = 'normal 20rpx KaiTi'
        canvas.fillStyle = 'rgba(0, 0, 0, 0.3)' // 增加不透明度
        canvas.textAlign = 'center'
        canvas.textBaseline = 'middle'

        // 计算水印间距
        const spacing = 80 // 进一步减小水印之间的间距
        const angle = -Math.PI / 6 // 倾斜角度

        // 绘制重复的水印
        for (let i = -spacing; i < width + spacing; i += spacing) {
          for (let j = -spacing; j < height + spacing; j += spacing) {
            canvas.save()
            canvas.translate(i, j)
            canvas.rotate(angle)
            canvas.fillText(`${this.certName}`, 0, 0)
            canvas.restore()
          }
        }

        canvas.draw()
      }).exec()
    }
  }
}
</script>

<style lang="scss">
.signature-container {
  padding: 20px;
  background-color: #f5f6f7;
  min-height: 100vh;

  .signature-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
  }

  .signature-info {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
  }

  .signature-display {
    background-color: white;
    border-radius: 8rpx;
    padding: 20rpx;

    .signature-info {
      margin-bottom: 15rpx;
      color: #666;
      font-size: 28rpx;

      .signature-time {
        display: block;
        margin-top: 8rpx;
        color: #999;
        font-size: 24rpx;
      }
    }

    .signature-image-container {
      position: relative;
      width: 100%;
      height: 200rpx;
      background-color: #f9f9f9;
      border: 1rpx solid #eaeaea;
      border-radius: 8rpx;
      overflow: hidden;

      .signature-image {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 1;
      }

      .watermark-canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        pointer-events: none; // 确保水印不影响图片点击
      }
    }
  }

  .signature-create {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .signature-button-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .signature-button {
        width: 180rpx;
        height: 180rpx;
        line-height: 180rpx;
        text-align: center;
        background-color: #3c96f3;
        color: white;
        border-radius: 50%;
        margin-bottom: 20rpx;
        font-size: 30rpx;
      }
    }
  }
  
  // 全屏签名样式
    .fullscreen-signature {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: white;
    z-index: 999;
    display: flex;
    /* 改为横向布局 */
    flex-direction: row;
    
    .signature-header {
      /* 左侧提示区域 */
      width: 120rpx;
      writing-mode: vertical-lr; /* 垂直文本方向 */
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f0f0f0;
      
      .signature-tips {
        color: #666;
        font-size: 28rpx;
        text-align: center;
        padding: 20rpx 0;
      }
    }
    
    .fullscreen-signature-container {
      flex: 1;
      height: 100%;
      background-color: #f9f9f9;
      position: relative;
      
      .fullscreen-canvas {
        width: 100%;
        height: 100%;
      }
    }
    
    .fullscreen-actions {
      /* 右侧按钮区域 */
      width: 120rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 15rpx 0;
      background-color: #f5f6f7;
      
      .action-button {
        margin: 8rpx 12rpx;
        height: 42rpx;
        width: 90rpx;
        line-height: 16rpx;
        text-align: center;
        font-size: 16rpx;
        border-radius: 21rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
        
        &:active {
          transform: scale(0.95);
          box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
        }
        
        &.clear {
          background-color: #3c96f3;
          color: white;
          opacity: 0.85;
        }
        
        &.save {
          background-color: #3c96f3;
          color: white;
          font-weight: bold;
          box-shadow: 0 4rpx 12rpx rgba(60, 150, 243, 0.35);
          
          &[disabled] {
            background-color: #cccccc;
            color: #999999;
            box-shadow: none;
          }
        }
        
        &.cancel {
          background-color: #3c96f3;
          color: white;
          opacity: 0.7;
        }
      }
    }
  }
  
  .rotate-canvas-container {
    position: fixed;
    left: -9999px;  /* 放在屏幕外 */
    top: 0;
    z-index: -1;
    overflow: hidden;
    
    .rotate-canvas {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
