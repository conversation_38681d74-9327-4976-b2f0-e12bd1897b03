/**
 * 解析船员证书查询页面HTML数据
 * @param {string} html - 页面HTML内容
 * @returns {Array} 证书数据数组
 */
export function parseCertificateData(html) {
  try {
    
    const certificates = [];

    // 解析海船合格证
    const seaQualRegex = /<table[^>]*>.*?海船合格证.*?<tr[^>]*>.*?<\/tr>.*?<tr[^>]*>.*?<\/tr>(.*?)<\/table>/gs;
    const seaQualMatch = seaQualRegex.exec(html);
    if (seaQualMatch) {
      const rows = seaQualMatch[1].match(/<tr[^>]*>(.*?)<\/tr>/gs) || [];
      rows.forEach((row, index) => {
        try {
          const cells = row.match(/<td[^>]*>(.*?)<\/td>/gs) || [];
          if (cells.length >= 7) {
            const certificate = {
              certificateType: '2',
              certificateNo: extractText(cells[0]),
              identityNo: extractText(cells[1]),
              fullName: extractText(cells[2]),
              issuingAuthority: extractText(cells[3]),
              issueDate: formatDate(extractText(cells[4])),
              effectiveDate: formatDate(extractText(cells[5])),
              certificateStatus: extractText(cells[6]) === '有效' ? '1' : '2'
            };
            certificates.push(certificate);
          }
        } catch (error) {
          
        }
      });
    }

    // 解析海船合格证项目
    const seaQualItemsRegex = /<table[^>]*>.*?海船合格证项目.*?<tr[^>]*>.*?<\/tr>.*?<tr[^>]*>.*?<\/tr>(.*?)<\/table>/gs;
    const seaQualItemsMatch = seaQualItemsRegex.exec(html);
    if (seaQualItemsMatch) {
      const rows = seaQualItemsMatch[1].match(/<tr[^>]*>(.*?)<\/tr>/gs) || [];
      rows.forEach((row, index) => {
        try {
          
          // 预处理行数据，处理多余空格和HTML实体
          let processedRow = row.replace(/&nbsp;/g, ' ')
                               .replace(/\s+/g, ' ')
                               .trim();
          
          // 使用直接文本提取方法
          const trTextContent = processedRow.replace(/<[^>]+>/g, '||').replace(/\|\|\s*\|\|/g, '||').trim();
          const cellTexts = trTextContent.split('||').filter(text => text.trim() !== '');
          
          
          if (cellTexts.length >= 4) {
            const certificate = {
              certificateType: '3',
              trainingProgram: cellTexts[0] || '',
              issuingAuthority: cellTexts[1] || '',
              issueDate: formatDate(cellTexts[2] || ''),
              effectiveDate: cellTexts.length >= 5 ? formatDate(cellTexts[3] || '') : '',
              certificateStatus: cellTexts.length >= 5 ? 
                (cellTexts[4].includes('有效') ? '1' : '2') : 
                (cellTexts[3].includes('有效') ? '1' : '2')
            };
            certificates.push(certificate);
          } else {
            // 备选方法：使用TD标签提取
            const cells = row.match(/<td[^>]*>(.*?)<\/td>/gs) || [];
            if (cells.length >= 4) {
              const certificate = {
                certificateType: '3',
                trainingProgram: extractText(cells[0]),
                issuingAuthority: extractText(cells[1]),
                issueDate: formatDate(extractText(cells[2])),
                effectiveDate: cells.length >= 5 ? formatDate(extractText(cells[3])) : '',
                certificateStatus: cells.length >= 5 ? 
                  (extractText(cells[4]).includes('有效') ? '1' : '2') : 
                  (extractText(cells[3]).includes('有效') ? '1' : '2')
              };
              certificates.push(certificate);
            } else {
              // 如果上述方法都失败，尝试基于title属性
              const titleMatches = row.match(/title=["']([^"']*?)["']/gi) || [];
              const titles = titleMatches.map(match => match.replace(/^title=["']/, '').replace(/["']$/, ''));
              
              if (titles.length >= 1) {
                // 至少可以获取培训项目名称
                let textContent = row.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
                let parts = textContent.split(/\s+/).filter(p => p.trim());
                
                // 根据实际提取到的部分内容灵活构建证书对象
                const certificate = {
                  certificateType: '3',
                  trainingProgram: titles[0] || '',
                  issuingAuthority: parts.length > 1 ? parts[1] || '' : '',
                  issueDate: parts.length > 2 ? formatDate(parts[2] || '') : '',
                  effectiveDate: parts.length > 3 ? formatDate(parts[3] || '') : '',
                  certificateStatus: textContent.includes('有效') ? '1' : '2'
                };
                certificates.push(certificate);
              }
            }
          }
        } catch (error) {
          
        }
      });
    }

    // 解析海船适任证
    // 注意：HTML中的海船适任证表格前有一个句点"."，需要特殊处理
    const shipCompetencyRegex = /<td[^>]*>\s*<b>\s*<font[^>]*>海船适任证<\/font>\s*<\/b>\s*<\/td>/i;
    const tableStartRegex = /<table[^>]*>/gi;
    const tableEndRegex = /<\/table>/gi;
    
    let foundShipComp = false;
    let shipCompetencyTableContent = '';
    
    // 寻找海船适任证表头
    const headMatch = shipCompetencyRegex.exec(html);
    if (headMatch) {
      
      // 定位表头所在位置
      const headIndex = headMatch.index;
      
      // 从表头位置向前寻找最近的表格开始标签
      tableStartRegex.lastIndex = 0;
      let tableStartMatch;
      let lastTableStartIndex = -1;
      
      while ((tableStartMatch = tableStartRegex.exec(html)) !== null) {
        if (tableStartMatch.index < headIndex) {
          lastTableStartIndex = tableStartMatch.index;
        } else {
          break;
        }
      }
      
      // 从表头位置向后寻找最近的表格结束标签
      tableEndRegex.lastIndex = headIndex;
      const tableEndMatch = tableEndRegex.exec(html);
      
      if (lastTableStartIndex !== -1 && tableEndMatch) {
        // 提取整个表格内容
        shipCompetencyTableContent = html.substring(lastTableStartIndex, tableEndMatch.index + tableEndMatch[0].length);
        foundShipComp = true;
      }
    }
    
    if (foundShipComp) {
      // 从表格中提取数据行，跳过表头行
      const rows = shipCompetencyTableContent.match(/<tr\s+class=['"]text-center['"][^>]*>[\s\S]*?<\/tr>/gi) || [];
      
      rows.forEach((row, index) => {
        try {
          
          // 直接提取单元格文本进行处理
          // 这是处理海船适任证的最可靠方法
          const trTextContent = row.replace(/<[^>]+>/g, '||').replace(/\|\|\s*\|\|/g, '||').trim();
          const cellTexts = trTextContent.split('||').filter(text => text.trim() !== '');
          
          
          if (cellTexts.length >= 7) {
            // 构建证书对象
            let certificate = {
              certificateType: '4',
              certificateNo: cellTexts[0] || '',
              areaType: cellTexts[1] || '',
              certificateLevel: cellTexts[2] || '',
              certificatePosition: cellTexts[3] || '',
              issuingAuthority: cellTexts[4] || '',
              issueDate: formatDate(cellTexts[5] || ''),
              effectiveDate: formatDate(cellTexts[6] || ''),
              certificateStatus: (cellTexts[7] || '').includes('有效') ? '1' : '2'
            };
            
            // 特殊处理乙类适任证书的情况
            if (certificate.areaType === 'Y' && row.includes('乙类适任证书')) {
              certificate.areaType = '乙类适任证书';
            }
            
            certificates.push(certificate);
          } else {
            // 如果直接文本提取方法失败，尝试使用正则提取单元格
            const cellsMatch = row.match(/<td[^>]*>([\s\S]*?)<\/td>/gi) || [];
            
            // 如果能提取到合适数量的单元格
            if (cellsMatch.length >= 7) {
              // 分别处理每个单元格
              const certificateNo = extractCellContent(cellsMatch[0]);
              const areaTypeRaw = extractCellContent(cellsMatch[1]);
              const certificateLevel = extractCellContent(cellsMatch[2]);
              const certificatePosition = extractCellContent(cellsMatch[3]);
              const issuingAuthority = extractCellContent(cellsMatch[4]);
              const issueDate = formatDate(extractCellContent(cellsMatch[5]));
              const effectiveDate = formatDate(extractCellContent(cellsMatch[6]));
              
              // 特殊处理证书类别
              let areaType = areaTypeRaw;
              if (areaType === 'Y' && row.includes('乙类适任证书')) {
                areaType = '乙类适任证书';
              }
              
              // 证书状态可能在第7个单元格或被包含在前面的单元格中
              let certificateStatus = '';
              if (cellsMatch.length >= 8) {
                certificateStatus = extractCellContent(cellsMatch[7]);
              } else {
                // 尝试从第6个单元格后面的内容提取
                const statusMatch = row.match(/<\/td>\s*<td[^>]*>([\s\S]*?)(?:<\/td>|$)/gi);
                if (statusMatch && statusMatch.length > 0) {
                  certificateStatus = extractText(statusMatch[statusMatch.length - 1]);
                }
              }
              const certificate = {
                certificateType: '4',
                certificateNo,
                areaType,
                certificateLevel,
                certificatePosition,
                issuingAuthority,
                issueDate,
                effectiveDate,
                certificateStatus: certificateStatus.includes('有效') ? '1' : '2'
              };
              
              certificates.push(certificate);
            } else {
              // 如果常规方法失败，回退到基于title属性的方法
              const titleMatches = row.match(/title=["']([^"']*?)["']/gi) || [];
              const titles = titleMatches.map(match => match.replace(/^title=["']/, '').replace(/["']$/, ''));
              
              if (titles.length >= 8) {
                // 特殊处理证书类别
                let areaType = titles[1] || '';
                if (areaType === 'Y' && row.includes('乙类适任证书')) {
                  areaType = '乙类适任证书';
                }
                
                const certificate = {
                  certificateType: '4',
                  certificateNo: titles[0] || '',
                  areaType: areaType,
                  certificateLevel: titles[2] || '',
                  certificatePosition: titles[3] || '',
                  issuingAuthority: titles[4] || '',
                  issueDate: formatDate(titles[5] || ''),
                  effectiveDate: formatDate(titles[6] || ''),
                  certificateStatus: titles[7] === '有效' ? '1' : '2'
                };
                certificates.push(certificate);
              } else {
                // 最后的备选方法 - 基于纯文本内容
                let textContent = row.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
                let parts = textContent.split(/\s+/);
                
                if (parts.length >= 8) {
                  // 特殊处理证书类别
                  let areaType = parts[1] || '';
                  if (areaType === 'Y' && parts.includes('乙类适任证书')) {
                    areaType = '乙类适任证书';
                  }
                  
                  const certificate = {
                    certificateType: '4',
                    certificateNo: parts[0] || '',
                    areaType: areaType,
                    certificateLevel: parts[2] || '',
                    certificatePosition: parts[3] || '',
                    issuingAuthority: parts[4] || '',
                    issueDate: formatDate(parts[5] || ''),
                    effectiveDate: formatDate(parts[6] || ''),
                    certificateStatus: parts[7] === '有效' ? '1' : '2'
                  };
                  certificates.push(certificate);
                }
              }
            }
          }
        } catch (error) {
          
        }
      });
    }

    // 解析健康证书
    const healthRegex = /<table[^>]*>.*?健康证书.*?<tr[^>]*>.*?<\/tr>.*?<tr[^>]*>.*?<\/tr>(.*?)<\/table>/gs;
    const healthMatch = healthRegex.exec(html);
    if (healthMatch) {
      const rows = healthMatch[1].match(/<tr[^>]*>(.*?)<\/tr>/gs) || [];
      rows.forEach((row, index) => {
        try {
          const cells = row.match(/<td[^>]*>(.*?)<\/td>/gs) || [];
          if (cells.length >= 10) {
            const certificate = {
              certificateType: '5',
              certificateNo: extractText(cells[0]),
              printingNumber: extractText(cells[1]),
              department: extractText(cells[2]),
              responsibility: extractText(cells[3]),
              examinationInstitution: extractText(cells[4]),
              doctor: extractText(cells[5]),
              issueDate: formatDate(extractText(cells[6])),
              effectiveDate: formatDate(extractText(cells[7])),
              authorizedAgency: extractText(cells[8]),
              certificateStatus: extractText(cells[9]) === '有效' ? '1' : '2'
            };
            certificates.push(certificate);
          }
        } catch (error) {
          
        }
      });
    }

    return certificates;

  } catch (error) {
    return [];
  }
}

/**
 * 从HTML标签中提取文本内容
 * @param {string} html - HTML标签
 * @returns {string} 提取的文本内容
 */
function extractText(html) {
  if (!html) return '';
  return html.replace(/<[^>]+>/g, '').replace(/\s+/g, ' ').trim();
}

/**
 * 格式化日期
 * @param {string} dateStr - 日期字符串
 * @returns {string} 格式化后的日期字符串 YYYY-MM-DD
 */
function formatDate(dateStr) {
  if (!dateStr || dateStr.trim() === '') return '';
  
  // 去除多余空格
  const trimmedDateStr = dateStr.trim();
  
  // 已经是标准格式的情况
  if (/^\d{4}-\d{2}-\d{2}$/.test(trimmedDateStr)) {
    return trimmedDateStr;
  }
  
  // 处理其他常见格式
  try {
    // 尝试使用Date对象解析
    const date = new Date(trimmedDateStr);
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    
    // 尝试解析其他格式，如 DD/MM/YYYY 或 MM/DD/YYYY
    const dateRegex = /(\d{1,4})[\/\-\.](\d{1,2})[\/\-\.](\d{1,4})/;
    const match = trimmedDateStr.match(dateRegex);
    if (match) {
      let [_, part1, part2, part3] = match;
      
      // 根据部分长度判断是年月日的哪一部分
      if (part1.length === 4) { // YYYY-MM-DD
        return `${part1}-${part2.padStart(2, '0')}-${part3.padStart(2, '0')}`;
      } else if (part3.length === 4) { // DD-MM-YYYY 或 MM-DD-YYYY
        return `${part3}-${part1.padStart(2, '0')}-${part2.padStart(2, '0')}`;
      }
    }
    
    return trimmedDateStr;
  } catch (error) {
    return trimmedDateStr; // 出错时原样返回
  }
}

/**
 * 提取单元格内容，优先使用文本内容，如果为空则使用title属性
 */
function extractCellContent(cellHtml) {
  if (!cellHtml) return '';
  
  // 提取title属性
  const titleMatch = cellHtml.match(/title=["']([^"']*?)["']/i);
  const titleValue = titleMatch ? titleMatch[1].trim() : '';
  
  // 提取文本内容
  const textContent = extractText(cellHtml);
  
  // 优先返回非空的文本内容，否则返回title值
  return textContent || titleValue;
} 