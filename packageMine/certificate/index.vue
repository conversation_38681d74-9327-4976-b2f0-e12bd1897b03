<template>
  <view class="container">
    <view class="header">
      <view class="title">证件管理</view>
    </view>

    <!-- 查询表单 -->
    <view class="query-form">
      <view class="form-item">
        <text class="label">身份证号：</text>
        <input
          type="text"
          v-model="idCardInput"
          class="input"
          placeholder="请输入身份证号"
        />
      </view>

      <view class="form-item">
        <text class="label">验证码：</text>
        <view class="verify-code">
          <input
            type="text"
            v-model="formData.verifyCode"
            class="input"
            placeholder="请输入验证码"
          />
          <image
            :src="verifyCodeBase64"
            class="verify-image"
            @click="refreshVerifyCode"
            mode="aspectFit"
          ></image>
        </view>
      </view>

      <view class="form-item">
        <text class="label">证件类型：</text>
        <radio-group class="radio-group" @change="handleCertTypeChange">
          <label class="radio">
            <radio value="H" :checked="formData.certType === 'H'" />海船船员证书
          </label>
        </radio-group>
      </view>

      <button type="primary" @click="handleQuery" class="query-btn">
        查询
      </button>
    </view>

    <!-- 查询结果 -->
    <view class="result-section" v-if="showResult">
      <view class="result-title">查询结果</view>

      <view v-if="loading" class="loading">
        <view class="loading-icon"></view>
        <text>数据加载中...</text>
      </view>

      <view v-else-if="Object.keys(certList).length === 0" class="no-data">
        <view class="empty-icon"></view>
        <text>暂无证件信息</text>
      </view>

      <view v-else>
        <view v-for="(certs, type) in certList" :key="type" class="cert-group">
          <view class="cert-group-title">{{ getTypeName(type) }}</view>
          <view class="table-container">
            <view class="table-header">
              <view v-for="(label, key) in getTableHeaders(type)" :key="key" class="table-cell" :data-key="key">
                {{ label }}
              </view>
            </view>
            <view v-for="(cert, idx) in certs" :key="idx" class="table-row">
              <view v-for="(label, key) in getTableHeaders(type)" :key="key" class="table-cell" :data-key="key">
                <template v-if="key === 'certificateStatus'">
                  {{ cert[key] === '1' ? '有效' : '失效' }}
                </template>
                <template v-else>
                  {{ cert[key] || '-' }}
                </template>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { submitCertificate } from "@/api/system/certificate";
import { mapGetters} from "vuex";
import { parseCertificateData } from './certificateParser.js';

export default {
  data() {
    return {
      formData: {
        verifyCode: "",
        certType: "H", // 默认海船船员证书
      },
      verifyCodeUrl: "",
      verifyCodeBase64: "",
      loading: false,
      showResult: false,
      certList: {},
      sessionId: "",
      idCardInput: "", // 用于输入框v-model 
      
    };
  },
  computed: {
    ...mapGetters(["isCert", "idCard"])
  },
  onLoad() {
    if (!this.isCert) {
      // 未认证，弹框提示
      uni.showModal({
        title: "温馨提示",
        content: "请先完成实名认证，才能管理证件",
        confirmText: "去认证",
        cancelText: "取消",
        success: (modalRes) => {
          if (modalRes.confirm) {
            uni.redirectTo({
              url: "/packageMine/auth/index",
            });
          } else if (modalRes.cancel) {
            // 取消时返回上一级
            uni.navigateBack();
          }
        },
      });
      return;
    }
    // 自动填充身份证号
    if (this.idCard) {
      this.idCardInput = this.idCard;
    }
    // 获取验证码
    this.getVerifyCode();
  },
  methods: {
    // 获取验证码
    async getVerifyCode() {
      const res = await uni.request({
        url: "https://www.dashbin.net:8899/prod-api/certificate/getValidateImage.action",
        method: "GET",
        header: {
          "content-type": "image/jpeg",
        },
        responseType: "arraybuffer",
      });

      // 转换图片数据为base64
      const base64 = uni.arrayBufferToBase64(res[1].data);
      this.verifyCodeBase64 = "data:image/jpeg;base64," + base64;
    },

    // 刷新验证码
    refreshVerifyCode() {
      this.getVerifyCode();
    },

    // 处理证件类型变更
    handleCertTypeChange(e) {
      this.formData.certType = e.detail.value;
    },

    // 处理查询
    async handleQuery() {
      if (!this.idCardInput) {
        uni.showToast({
          title: "请输入身份证号",
          icon: "none",
        });
        return;
      }
      if (!this.formData.verifyCode) {
        uni.showToast({
          title: "请输入验证码",
          icon: "none",
        });
        return;
      }
      this.loading = true;
      this.showResult = true;

      try {
        // 构建请求参数
        const params = {
          "lycxQO.gg_sfzhm": this.idCardInput,
          yanzhm: this.formData.verifyCode,
          "lycxQO.applScope": this.formData.certType,
        };

        // 发送请求
        const res = await uni.request({
          url: "https://www.dashbin.net:8899/prod-api/certificate/certQuery.action",
          method: "POST",
          data: params,
          header: {
            "content-type": "application/x-www-form-urlencoded",
            Cookie: `JSESSIONID=${this.sessionId}`,
            Accept:
              "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            Connection: "keep-alive",
            Host: "cyxx.msa.gov.cn",
            Referer: "https://cyxx.msa.gov.cn/crew_qey/qry/certQuery.action",
            Origin: "https://cyxx.msa.gov.cn",
          },
        });

        // 处理返回结果
        if (res[1].data) {
          const certs = parseCertificateData(res[1].data);
          //console.log('解析到的所有证书:', certs);
          
          // 按证书类型分组
          const groupMap = {};
          certs.forEach(cert => {
            const type = cert.certificateType;
            if (!groupMap[type]) groupMap[type] = [];
            groupMap[type].push(cert);
          });
          //console.log('分组后的证书数据:', groupMap);
          this.certList = groupMap;

          // 直接提交到后端
          if (certs.length > 0) {
            try {
              const submitRes = await submitCertificate(certs);
              if (submitRes.code === 200) {
                uni.showToast({
                  title: "数据已同步到系统",
                  icon: "success",
                });
              } else {
                uni.showToast({
                  title: submitRes.msg || "同步失败",
                  icon: "none",
                });
              }
            } catch (err) {
              console.error('提交证书数据失败:', err);
              uni.showToast({
                title: "同步失败，请稍后重试",
                icon: "none",
              });
            }
          }
        } else {
          uni.showToast({
            title: "查询失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error('查询证书失败:', error);
        uni.showToast({
          title: "查询失败，请稍后重试",
          icon: "none",
        });
      } finally {
        this.loading = false;
        // 自动刷新验证码
        this.refreshVerifyCode();
      }
    },

    // 获取表格头部
    getTableHeaders(type) {
      const headers = {
        '2': { // 海船合格证
          certificateNo: '证书编号',
          identityNo: '身份证号',
          fullName: '姓名',
          issuingAuthority: '签发机关',
          issueDate: '签发日期',
          effectiveDate: '有效期至',
          certificateStatus: '证书状态'
        },
        '3': { // 海船合格证项目
          trainingProgram: '培训项目',
          issuingAuthority: '签发机关',
          issueDate: '签发日期',
          effectiveDate: '有效期至',
          certificateStatus: '证书状态'
        },
        '4': { // 海船适任证
          certificateNo: '证书编号',
          areaType: '证书类别',
          certificateLevel: '证书等级',
          certificatePosition: '证书职务',
          issuingAuthority: '签发机关',
          issueDate: '签发日期',
          effectiveDate: '有效期至',
          certificateStatus: '证书状态'
        },
        '5': { // 健康证书
          certificateNo: '证书编号',
          printingNumber: '证书印刷号',
          department: '工作部门',
          responsibility: '职责限制',
          examinationInstitution: '体检机构',
          doctor: '主检医师',
          issueDate: '签发日期',
          effectiveDate: '有效期至',
          authorizedAgency: '授权海事机关',
          certificateStatus: '证书状态'
        }
      };
      return headers[type] || {};
    },

    getTypeName(type) {
      // 证书类型映射
      const map = {
        '1': '海员证',
        '2': '海船合格证',
        '3': '海船合格证项目',
        '4': '海船适任证',
        '5': '健康证书',
        '6': '船上厨师和膳食服务辅助人员培训证书'
      };
      return map[type] || '其他证书';
    },
    getFieldLabel(key) {
      // 字段名映射
      const map = {
        certificateNo: '证书编号',
        identityNo: '身份证号',
        fullName: '姓名',
        issuingAuthority: '签发机关',
        issueDate: '签发日期',
        effectiveDate: '有效期至',
        certificateStatus: '证书状态',
        trainingProgram: '培训项目',
        areaType: '证书类别',
        certificateLevel: '证书等级',
        certificatePosition: '证书职务',
        printingNumber: '证书印刷号',
        department: '工作部门',
        responsibility: '职责限制',
        examinationInstitution: '体检机构',
        doctor: '主检医师',
        authorizedAgency: '授权海事机关',
        certificateType: '证书类型',
        certificateName: '证书名称'
      };
      return map[key] || key;
    },
  },
};
</script>

<style lang="scss">
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;

  .title {
    font-size: 32rpx;
    font-weight: bold;
  }
}

.query-form {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .form-item {
    margin-bottom: 20rpx;

    .label {
      display: block;
      margin-bottom: 10rpx;
      color: #333;
      font-size: 28rpx;
    }

    .input {
      width: 100%;
      height: 80rpx;
      border: 1px solid #ddd;
      border-radius: 8rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
    }

    .verify-code {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .input {
        flex: 1;
      }

      .verify-image {
        width: 200rpx;
        height: 80rpx;
        border: 1px solid #ddd;
        border-radius: 8rpx;
      }
    }

    .radio-group {
      display: flex;
      gap: 20rpx;

      .radio {
        font-size: 28rpx;
      }
    }
  }

  .query-btn {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #3c96f3;
    color: #fff;
    font-size: 28rpx;
    border-radius: 8rpx;
    margin-top: 20rpx;
  }
}

.result-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;

  .result-title {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;

    .loading-icon {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    text {
      margin-top: 20rpx;
      color: #999;
      font-size: 28rpx;
    }
  }

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;

    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      background-color: #f5f5f5;
      border-radius: 50%;
      margin-bottom: 20rpx;
    }

    text {
      color: #999;
      font-size: 28rpx;
    }
  }

  .table-container {
    width: 100%;
    overflow-x: auto;
    background: #fff;
    border-radius: 8rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .table-header {
      display: flex;
      background: #f5f7fa;
      border-bottom: 2rpx solid #ebeef5;
      position: sticky;
      top: 0;
      z-index: 1;

      .table-cell {
        flex: 1;
        min-width: 160rpx;
        padding: 24rpx 20rpx;
        font-weight: bold;
        color: #606266;
        font-size: 26rpx;
        text-align: center;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;

        // 身份证号列宽
        &[data-key="identityNo"] {
          min-width: 320rpx;
        }

        // 证书编号列宽
        &[data-key="certificateNo"] {
          min-width: 280rpx;
        }

        // 签发机关列宽
        &[data-key="issuingAuthority"] {
          min-width: 240rpx;
        }

        // 培训项目列宽
        &[data-key="trainingProgram"] {
          min-width: 240rpx;
        }

        // 证书类别列宽
        &[data-key="areaType"] {
          min-width: 200rpx;
        }

        // 证书等级列宽
        &[data-key="certificateLevel"] {
          min-width: 200rpx;
        }

        // 证书职务列宽
        &[data-key="certificatePosition"] {
          min-width: 200rpx;
        }

        // 体检机构列宽
        &[data-key="examinationInstitution"] {
          min-width: 280rpx;
        }

        // 主检医师列宽
        &[data-key="doctor"] {
          min-width: 240rpx;
        }
      }
    }

    .table-row {
      display: flex;
      border-bottom: 1rpx solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      .table-cell {
        flex: 1;
        min-width: 160rpx;
        padding: 24rpx 20rpx;
        color: #606266;
        font-size: 26rpx;
        text-align: center;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;

        // 身份证号列宽
        &[data-key="identityNo"] {
          min-width: 320rpx;
        }

        // 证书编号列宽
        &[data-key="certificateNo"] {
          min-width: 280rpx;
        }

        // 签发机关列宽
        &[data-key="issuingAuthority"] {
          min-width: 240rpx;
        }

        // 培训项目列宽
        &[data-key="trainingProgram"] {
          min-width: 240rpx;
        }

        // 证书类别列宽
        &[data-key="areaType"] {
          min-width: 200rpx;
        }

        // 证书等级列宽
        &[data-key="certificateLevel"] {
          min-width: 200rpx;
        }

        // 证书职务列宽
        &[data-key="certificatePosition"] {
          min-width: 200rpx;
        }

        // 体检机构列宽
        &[data-key="examinationInstitution"] {
          min-width: 280rpx;
        }

        // 主检医师列宽
        &[data-key="doctor"] {
          min-width: 240rpx;
        }
      }
    }
  }

  .cert-group {
    margin-bottom: 50rpx;

    .cert-group-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #303133;
      margin-bottom: 30rpx;
      padding-left: 20rpx;
      border-left: 8rpx solid #3c96f3;
    }
  }
}
</style>
