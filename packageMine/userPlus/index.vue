<template>
  <view class="container">
    <view class="example">
      <u-form :model="form" ref="uForm" :errorType="['message']">
        <u-form-item label="真实姓名" prop="fullName" labelWidth="80px" required>
          <u-input v-model="form.fullName" placeholder="请输入真实姓名" />
        </u-form-item>

        <u-form-item label="身高(cm)" prop="height" labelWidth="80px" required>
          <u-input v-model="form.height" placeholder="请输入身高" type="number" />
        </u-form-item>

        <u-form-item label="体重(kg)" prop="weight" labelWidth="80px" required>
          <u-input v-model="form.weight" placeholder="请输入体重" type="number" />
        </u-form-item>

        <u-form-item label="血型" prop="bloodType" labelWidth="80px" required>
          <picker :value="bloodTypeIndex" :range="bloodTypeOptions" @change="onBloodTypeChange">
            <view class="picker-view">
              <text v-if="form.bloodTypeName">{{form.bloodTypeName}}</text>
              <text v-else class="placeholder">请选择血型</text>
              <text class="arrow">▼</text>
            </view>
          </picker>
        </u-form-item>

        <u-form-item label="国籍" prop="nationality" labelWidth="80px" required>
          <picker :value="nationalityIndex" :range="nationalityOptions" @change="onNationalityChange">
            <view class="picker-view">
              <text v-if="form.nationality">{{form.nationality}}</text>
              <text v-else class="placeholder">请选择国籍</text>
              <text class="arrow">▼</text>
            </view>
          </picker>
        </u-form-item>

        <u-form-item label="民族" prop="ethnicity" labelWidth="80px" required>
          <picker :value="ethnicityIndex" :range="ethnicityOptions" @change="onEthnicityChange">
            <view class="picker-view">
              <text v-if="form.ethnicity">{{form.ethnicity}}</text>
              <text v-else class="placeholder">请选择民族</text>
              <text class="arrow">▼</text>
            </view>
          </picker>
        </u-form-item>

        <u-form-item label="出生日期" prop="birthday" labelWidth="80px" required>
          <picker mode="date" :value="form.birthdayFormat" @change="onDateChange">
            <view class="picker-view">
              <text v-if="form.birthdayFormat">{{form.birthdayFormat}}</text>
              <text v-else class="placeholder">请选择出生日期</text>
              <text class="arrow">▼</text>
            </view>
          </picker>
        </u-form-item>

        <view class="section-title">户籍信息</view>
        <u-form-item label="省市区" prop="province" labelWidth="80px" required>
          <picker mode="region" :value="regionValue" @change="bindRegionChange">
            <view class="picker-view">
              <text v-if="regionText">{{regionText}}</text>
              <text v-else class="placeholder">请选择省市区</text>
              <text class="arrow">▼</text>
            </view>
          </picker>
        </u-form-item>

        <u-form-item label="详细地址" prop="address" labelWidth="80px" required>
          <u-input v-model="form.address" placeholder="请输入详细地址" />
        </u-form-item>

        <view class="section-title">操作安全</view>
        <u-form-item label="敏感信息保护密码" prop="operationPassword" labelWidth="80px" required>
          <view v-if="hasPassword" class="code-row">
            <input class="code-input" value="******" disabled />
            <button class="code-btn" @click="handleForgetPassword">忘记密码</button>
          </view>
          <view v-else class="password-input-container">
            <u-input
              v-model="form.operationPassword"
              placeholder="请输入6位数字敏感信息保护密码"
              :type="passwordVisible ? 'text' : 'password'"
              maxlength="6"
            />
            <view class="eye-icon" @tap="togglePasswordVisibility">
              <u-icon :name="passwordVisible ? 'eye' : 'eye-off'" size="24" color="#909399"></u-icon>
            </view>
          </view>
        </u-form-item>

        <u-form-item v-if="!hasPassword" label="确认密码" prop="confirmPassword" labelWidth="80px" required>
          <view class="password-input-container">
            <u-input
              v-model="form.confirmPassword"
              placeholder="请再次输入6位数字敏感信息保护密码"
              :type="confirmPasswordVisible ? 'text' : 'password'"
              maxlength="6"
            />
            <view class="eye-icon" @tap="toggleConfirmPasswordVisibility">
              <u-icon :name="confirmPasswordVisible ? 'eye' : 'eye-off'" size="24" color="#909399"></u-icon>
            </view>
          </view>
        </u-form-item>

        <view class="section-title">紧急联络人</view>
        <u-form-item label="联络人姓名" prop="contactName" labelWidth="80px" required>
          <u-input v-model="form.contactName" placeholder="请输入联络人姓名" />
        </u-form-item>

        <u-form-item label="联络人电话" prop="contactPhone" labelWidth="80px" required>
          <u-input v-model="form.contactPhone" placeholder="请输入联络人电话" />
        </u-form-item>

        <u-form-item label="与本人关系" prop="contactAddress" labelWidth="80px" required>
          <u-input v-model="form.contactAddress" placeholder="请输入与本人关系" />
        </u-form-item>

        <u-gap height="30"></u-gap>
        <u-button type="primary" text="保存" @click="submit"></u-button>
      </u-form>
    </view>
    <!-- 忘记密码弹框 -->
    <u-modal :show="showDetailModal" :title="detailTitle" :show-confirm-button="true"
      :show-cancel-button="true" confirm-text="确定" cancel-text="取消"
      :close-on-click-overlay="true" @confirm="handleModalConfirm" @cancel="showDetailModal = false"
      @close="showDetailModal = false">
      <view class="slot-content">
        <view class="dialog-row">
          <u-input v-model="forgetForm.oldPwd" placeholder="请输入旧密码（6位数字）" maxlength="6" type="password" />
        </view>
        <view class="dialog-row">
          <u-input v-model="forgetForm.newPwd" placeholder="请输入新密码（6位数字）" maxlength="6" type="password" />
        </view>
        <view class="dialog-row">
          <u-input v-model="forgetForm.confirmPwd" placeholder="请再次输入新密码" maxlength="6" type="password" />
        </view>
      </view>
    </u-modal>
  </view>
</template>

<script>
import { getUserPlusInfo, updateUserPlusInfo, resetOperationPassword } from '@/api/system/userPlus'
import { mapGetters } from 'vuex'
import { nationalityList } from './nationality.js'
import { ethnicityList } from './ethnicity.js'

export default {
  data() {
    return {
      passwordVisible: false,
      confirmPasswordVisible: false,
      hasPassword: false,
      showDetailModal: false,
      detailTitle: '重置敏感信息保护密码',
      form: {
        userId: 0,
        fullName: '',
        height: '',
        weight: '',
        bloodType: '',
        bloodTypeName: '',
        nationality: '',
        ethnicity: '',
        birthday: '',
        birthdayFormat: '',
        province: '',
        city: '',
        countryTown: '',
        address: '',
        signature: '',
        operationPassword: '',
        contactName: '',
        contactPhone: '',
        contactAddress: '',
        confirmPassword: ''
      },
      // 血型选项
      bloodTypeOptions: ['A型', 'B型', 'AB型', 'O型'],
      bloodTypeValues: ['1', '2', '3', '4'],
      bloodTypeIndex: 0,
      // 国籍选项
      nationalityOptions: nationalityList.map(item => item.label),
      nationalityIndex: 0,
      // 民族选项
      ethnicityOptions: ethnicityList.map(item => item.label),
      ethnicityIndex: 0,
      // 基础表单验证规则
      baseRules: {
        fullName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        height: [
          { required: true, message: '请输入身高', trigger: 'blur' },
          { type: 'number', message: '身高必须是数字', trigger: 'blur', transform: val => Number(val) }
        ],
        weight: [
          { required: true, message: '请输入体重', trigger: 'blur' },
          { type: 'number', message: '体重必须是数字', trigger: 'blur', transform: val => Number(val) }
        ],
        bloodType: [
          { required: true, message: '请选择血型', trigger: 'change' }
        ],
        nationality: [
          { required: true, message: '请输入国籍', trigger: 'blur' }
        ],
        ethnicity: [
          { required: true, message: '请输入民族', trigger: 'blur' }
        ],
        birthday: [
          { required: true, message: '请选择出生日期', trigger: 'change' }
        ],
        province: [
          { required: true, message: '请输入省份', trigger: 'blur' }
        ],
        city: [
          { required: true, message: '请输入城市', trigger: 'blur' }
        ],
        countryTown: [
          { required: true, message: '请输入区县', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '请输入详细地址', trigger: 'blur' }
        ],
        contactName: [
          { required: true, message: '请输入联络人姓名', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入联络人电话', trigger: 'blur' }
        ],
        contactAddress: [
          { required: true, message: '请输入与本人关系', trigger: 'blur' }
        ]
      },
      forgetForm: {
        oldPwd: '',
        newPwd: '',
        confirmPwd: ''
      },
      regionValue: '',
      regionText: ''
    }
  },

  computed: {
    ...mapGetters([
      'isCert'
    ]),
    // 动态表单验证规则
    rules() {
      const rules = { ...this.baseRules }
      // 只有当没有密码时才添加验证规则
      if (!this.hasPassword) {
        // console.log('添加验证规则')
        rules.operationPassword = [
          { required: true, message: '请输入敏感信息保护密码', trigger: 'blur' },
          { len: 6, message: '必须是6位数字敏感信息保护密码', trigger: 'blur' },
          { pattern: /^\d{6}$/, message: '必须是6位数字', trigger: 'blur' }
        ]
        rules.confirmPassword = [
          { required: true, message: '请再次输入敏感信息保护密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== this.form.operationPassword) {
                callback(new Error('两次输入的密码不一致'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      }
      // console.log('当前表单验证规则:', rules)
      return rules
    }
  },

  onLoad() {
    if (!this.isCert) {
      // 未认证，弹框提示
      uni.showModal({
        title: '温馨提示',
        content: '请先完成实名认证，才能完善个人信息',
        confirmText: '去认证',
        cancelText: '取消',
        success: (modalRes) => {
          if (modalRes.confirm) {
            uni.redirectTo({
              url: '/packageMine/auth/index'
            })
          } else if (modalRes.cancel) {
            // 取消时返回上一级
            uni.navigateBack()
          }
        }
      })
      return
    }
    // 已认证，加载个人信息
    this.getUserInfo()
  },

  onReady() {
    this.$refs.uForm.setRules(this.rules)
  },

  methods: {
    // 获取用户附加信息
    getUserInfo() {
      this.$modal.loading('加载中...')
      getUserPlusInfo().then(res => {
        this.$modal.closeLoading()
        if (res.code === 200 && res.data) {
          const data = res.data
          // 设置表单数据
          this.form.userId = data.userId || ''
          this.form.fullName = data.fullName || ''
          this.form.height = data.height || ''
          this.form.weight = data.weight || ''
          this.form.bloodType = data.bloodType || ''
          this.form.bloodTypeName = this.getBloodTypeName(data.bloodType)
          this.form.nationality = data.nationality || ''
          this.form.ethnicity = data.ethnicity || ''
          this.form.birthday = data.birthday || ''
          this.form.birthdayFormat = this.formatBirthday(data.birthday)
          this.form.province = data.province || ''
          this.form.city = data.city || ''
          this.form.countryTown = data.countryTown || ''
          this.form.address = data.address || ''
          this.form.signature = data.signature || ''
          this.form.operationPassword = data.operationPassword || ''
          this.hasPassword = data.operationPassword=='******'
          this.form.contactName = data.contactName || ''
          this.form.contactPhone = data.contactPhone || ''
          this.form.contactAddress = data.contactAddress || ''

          // 初始化省市区选择器
          if(data.province || data.city || data.countryTown) {
            this.regionValue = [data.province || '', data.city || '', data.countryTown || ''];
            this.regionText = (data.province || '') + (data.city || '') + (data.countryTown || '');
          }
          
          // console.log('获取到的用户数据:', this.hasPassword)
          // 重新设置验证规则
          this.$nextTick(() => {
            this.$refs.uForm.setRules(this.rules)
          })
        }
      }).catch(err => {
        //console.error('获取数据失败:', err)
        this.$modal.closeLoading()
      })
    },

    // 获取血型名称
    getBloodTypeName(type) {
      const index = this.bloodTypeValues.indexOf(type)
      if (index !== -1) {
        this.bloodTypeIndex = index
        return this.bloodTypeOptions[index]
      }
      return ''
    },

    // 血型选择确认
    onBloodTypeChange(e) {
      this.form.bloodTypeName = this.bloodTypeOptions[e.detail.value]
      this.form.bloodType = this.bloodTypeValues[e.detail.value]
      this.bloodTypeIndex = e.detail.value
    },

    // 出生日期选择确认
    onDateChange(e) {
      try {
        const dateValue = e.detail.value;
        this.form.birthday = dateValue;
        // 设置显示格式
        this.form.birthdayFormat = dateValue;
      } catch (err) {
        console.error('日期处理错误:', err);
      }
    },

    // 国籍选择确认
    onNationalityChange(e) {
      const index = e.detail.value
      this.form.nationality = this.nationalityOptions[index]
      this.nationalityIndex = index
    },

    // 民族选择确认
    onEthnicityChange(e) {
      const index = e.detail.value
      this.form.ethnicity = this.ethnicityOptions[index]
      this.ethnicityIndex = index
    },

    // 提交表单
    submit() {
      this.$refs.uForm.validate().then(valid => {
        if (valid) {
          this.$modal.loading('保存中...')
          // 构造请求数据
          const submitData = {
            userId: this.form.userId,
            fullName: this.form.fullName,
            height: this.form.height,
            weight: this.form.weight,
            bloodType: this.form.bloodType,
            nationality: this.form.nationality,
            ethnicity: this.form.ethnicity,
            birthday: this.form.birthday,
            province: this.form.province,
            city: this.form.city,
            countryTown: this.form.countryTown,
            address: this.form.address,
            signature: this.form.signature,
            operationPassword: this.form.operationPassword,
            contactName: this.form.contactName,
            contactPhone: this.form.contactPhone,
            contactAddress: this.form.contactAddress
          }

          updateUserPlusInfo(submitData).then(res => {
            this.$modal.closeLoading()
            if (res.code === 200) {
              this.$modal.showToast('保存成功')
              setTimeout(() => {
                uni.navigateBack()
              }, 1500)
            }
          }).catch(err => {
            this.$modal.closeLoading()
            this.$modal.showToast('保存失败，请重试')
          })
        } else {
          // console.log('表单验证失败')
          this.$modal.showToast('请完善表单信息')
        }
      }).catch(errors => {
        // console.log('表单验证失败:', errors)
        this.$modal.showToast('请完善表单信息')
      })
    },

    // 格式化出生日期
    formatBirthday(birthday) {
      if (!birthday) return '';
      try {
        // 兼容iOS的日期格式
        if (typeof birthday === 'string') {
          // 处理格式如 "2025-04-22"
          const parts = birthday.replace(/-/g, '/').split(' ');
          if (parts.length > 0) {
            // 只返回日期部分，格式为 YYYY-MM-DD
            const dateParts = parts[0].split('/');
            if (dateParts.length === 3) {
              return `${dateParts[0]}-${dateParts[1].padStart(2, '0')}-${dateParts[2].padStart(2, '0')}`;
            }
          }
        }

        // 如果上面的处理失败，尝试使用原生Date
        const date = new Date(birthday.replace(/-/g, '/'));
        return this.formatDate(date, 'yyyy-MM-dd');
      } catch (e) {
        console.error('日期格式化错误:', e, birthday);
        return '';
      }
    },

    // 日期格式化函数
    formatDate(date, fmt) {
      if (!date) return '';
      let o = {
        "M+": date.getMonth() + 1, // 月份
        "d+": date.getDate(), // 日
        "h+": date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
        "H+": date.getHours(), // 小时
        "m+": date.getMinutes(), // 分
        "s+": date.getSeconds(), // 秒
        "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
        "S": date.getMilliseconds() // 毫秒
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
      }
      for (let k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
      }
      return fmt;
    },

    // 切换密码可见性
    togglePasswordVisibility() {
      this.passwordVisible = !this.passwordVisible;
    },

    // 切换确认密码可见性
    toggleConfirmPasswordVisibility() {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
    },

    // 处理忘记密码
    handleForgetPassword() {
      // console.log('打开忘记密码弹框')
      this.showDetailModal = true
      this.resetForgetForm()
    },

    // 处理模态框确认按钮点击
    async handleModalConfirm() {
      if (!/^\d{6}$/.test(this.forgetForm.oldPwd)) {
        this.$modal.showToast('旧密码必须为6位数字')
        return
      }
      if (!/^\d{6}$/.test(this.forgetForm.newPwd)) {
        this.$modal.showToast('新密码必须为6位数字')
        return
      }
      if (this.forgetForm.newPwd !== this.forgetForm.confirmPwd) {
        this.$modal.showToast('两次密码输入不一致')
        return
      }
      if (this.forgetForm.oldPwd === this.forgetForm.newPwd) {
        this.$modal.showToast('新密码不能与旧密码相同')
        return
      }
      this.$modal.loading('重置中...')
      // 发送请求到后端
        const res = await resetOperationPassword({
          oldPassword: this.forgetForm.oldPwd,
          newPassword: this.forgetForm.newPwd
        });
        if (res.code === 200) {
          this.$modal.closeLoading();
          this.$modal.showToast('重置成功');
          this.form.operationPassword = '******'; // 显示为已设置密码
          this.hasPassword = true;
          this.showDetailModal = false;
        }
    },

    resetForgetForm() {
      this.forgetForm = { oldPwd: '', newPwd: '', confirmPwd: '' }
    },

    bindRegionChange(e) {
      this.regionValue = e.detail.value;
      this.regionText = e.detail.value[0] + e.detail.value[1] + e.detail.value[2];
      // 更新form中的省市区字段，保持与后端数据结构一致
      this.form.province = e.detail.value[0];
      this.form.city = e.detail.value[1];
      this.form.countryTown = e.detail.value[2];
    }
  }
}
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}

.container {
  width: 100%;
  padding: 0;
}

.example {
  padding: 15px;
  background-color: #fff;
}

.section-title {
  padding: 8px 0;
  margin: 5px 0;
  font-size: 15px;
  color: #303133;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}

.custom-input {
  width: 100%;
  height: 35px;
  background-color: #ffffff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  position: relative;
}

.uni-input {
  height: 35px;
  width: 100%;
  line-height: 35px;
  padding: 0 10px;
  font-size: 14px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.picker-view {
  width: 100%;
  height: 35px;
  line-height: 35px;
  padding: 0 10px;
  font-size: 14px;
  background-color: #ffffff;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #eee;
}

.placeholder {
  color: #999;
}

.arrow {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}

.submit-btn {
  margin-top: 20px;
  width: 100%;
}

.password-input-container {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.eye-icon {
  position: absolute;
  right: 10rpx;
  z-index: 2;
  padding: 10rpx;
}

.forget-password {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;

  .disabled-input {
    width: 60%;

    :deep(.u-input__input) {
      background-color: #f5f5f5;
      color: #999;
    }
  }

  .forget-btn {
    background: linear-gradient(45deg, #2979ff, #5cadff);
    border: none;
    color: #ffffff;
    font-size: 12px;
    padding: 0 8px;
    height: 28px;
    border-radius: 14px;
    box-shadow: 0 2px 6px rgba(41, 121, 255, 0.2);
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 10px;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 1px 3px rgba(41, 121, 255, 0.2);
    }
  }
}

.password-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;

  .password-input {
    flex: 1;
    height: 35px;
    background-color: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 12px;

    .password-text {
      color: #999;
      font-size: 14px;
    }
  }

  .forget-btn {
    background: linear-gradient(45deg, #2979ff, #5cadff);
    border: none;
    color: #ffffff;
    font-size: 12px;
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(41, 121, 255, 0.2);
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 40px;
    width: 40px;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 1px 2px rgba(41, 121, 255, 0.2);
    }
  }
}

.code-row {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 0;
}
.code-input {
  flex: 1;
  height: 36px;
  border: 1px solid #dcdcdc;
  border-radius: 6px 0 0 6px;
  padding: 0 12px;
  font-size: 15px;
  color: #999;
  background: #fff;
  outline: none;
  box-sizing: border-box;
  border-right: none;
}
.code-btn {
  min-width: 160rpx;
  height: 70rpx;
  line-height: 70rpx;
  background: #2979ff;
  color: #fff;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  padding: 0 20rpx;
  transition: all 0.3s ease;
}
.code-btn:active {
  background: #2b85e4;
  color: #fff;
}
.code-btn:disabled {
  background: #a0cfff;
  color: #fff;
  border: none;
  cursor: not-allowed;
}

.slot-content {
  padding: 20rpx;
}

.dialog-row {
  margin-bottom: 14rpx;
}

.code-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.code-input {
  flex: 1;
}

.code-btn {
  min-width: 160rpx;
  height: 70rpx;
  line-height: 70rpx;
  background: #2979ff;
  color: #fff;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  padding: 0 20rpx;
  transition: all 0.3s ease;
}

.code-btn:active {
  background: #2b85e4;
  color: #fff;
}

.code-btn:disabled {
  background: #a0cfff;
  color: #fff;
  border: none;
  cursor: not-allowed;
}

.dialog-btn {
  min-width: 60px;
  height: 32px;
  border: none;
  border-radius: 16px;
  font-size: 15px;
  padding: 0 16px;
  transition: all 0.3s ease;
}

.dialog-btn.confirm {
  background: #2979ff;
  color: #fff;
}

.dialog-btn.confirm:active {
  background: #2b85e4;
  color: #fff;
}

.dialog-btn.cancel {
  background: #2979ff;
  color: #fff;
}

.dialog-btn.cancel:active {
  background: #2b85e4;
  color: #fff;
}
</style>
