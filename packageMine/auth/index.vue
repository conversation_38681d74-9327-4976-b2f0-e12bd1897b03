<template>
  <view class="container">
    <view class="example">
      <!-- 未认证状态 -->
      <template v-if="!isCert">
        <u-form :model="form" ref="uForm" :errorType="['message']">
          <view class="section-title">身份证照片</view>
          <view class="upload-section">
            <view class="upload-item">
              <view class="upload-title">身份证正面</view>
              <view class="upload-box" @tap="chooseImage('front')">
                <image v-if="form.idCardFront" :src="form.cardFrontUrl" mode="aspectFill" class="preview-image"></image>
                <view v-else class="upload-placeholder">
                  <u-icon name="camera" size="36" color="#999"></u-icon>
                  <view class="upload-tips">点击上传</view>
                </view>
              </view>
            </view>

            <view class="upload-item">
              <view class="upload-title">身份证反面</view>
              <view class="upload-box" @tap="chooseImage('back')">
                <image v-if="form.idCardBack" :src="form.cardBackUrl" mode="aspectFill" class="preview-image"></image>
                <view v-else class="upload-placeholder">
                  <u-icon name="camera" size="36" color="#999"></u-icon>
                  <view class="upload-tips">点击上传</view>
                </view>
              </view>
            </view>
          </view>

          <view class="section-title">基本信息</view>
          <u-form-item label="真实姓名" prop="realName" labelWidth="80px">
            <u-input v-model="form.realName" placeholder="请输入真实姓名" />
          </u-form-item>

          <u-form-item label="身份证号" prop="idCard" labelWidth="80px">
            <u-input v-model="form.idCard" placeholder="请输入身份证号码" maxlength="18" />
          </u-form-item>
		  <u-form-item label="签发机关" prop="issuingAuthority" labelWidth="80px">
		    <u-input v-model="form.issuingAuthority" placeholder="身份证签发机关" maxlength="18"  disabled />
		  </u-form-item>
		  <u-form-item label="开始时间" prop="idStart" labelWidth="80px" >
        <u-input v-model="form.idStart" placeholder="开始时间" maxlength="18"  disabled />
		  </u-form-item>
      <u-form-item label="过期时间" prop="idExpiration" labelWidth="80px" >
        <u-input v-model="form.idExpiration" placeholder="过期时间" maxlength="18"  disabled />
		  </u-form-item>
          <u-gap height="30"></u-gap>
          <u-button type="primary" text="提交认证" @click="submit"></u-button>
        </u-form>
      </template>

      <!-- 已认证状态 -->
      <template v-else>
        <view class="status-container">
          <u-icon name="checkmark-circle" size="60" color="#19be6b"></u-icon>
          <view class="status-text">已认证</view>
          <view class="info-list">
            <view class="info-item">
              <text class="label">真实姓名:</text>
              <text class="value">{{certName}}</text>
            </view>
            <view class="info-item">
              <text class="label">身份证号:</text>
              <text class="value">{{idCard}}</text>
            </view>
          </view>
          <u-button
            type="primary"
            text="返回"
            @click="goBack"
            class="back-button"
          ></u-button>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
import { submitAuth, uploadIdCardImage } from '@/api/system/auth'
import { mapGetters } from 'vuex'

export default {
  data() {
    return {
      form: {
        realName: '',
        idCard: '',
        idCardFront: '',
        idCardBack: '',
        faceImage: '',
        cardFrontUrl:'',
        cardBackUrl:'',
        agreement: false,
        idExpiration:'',
        issuingAuthority:'',
        idStart:''
      },
      // 表单验证规则
      rules: {
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '请输入身份证号码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              return this.validateIdCard(value);
            },
            message: '请输入正确的身份证号码',
            trigger: 'blur'
          }
        ]
      }
    }
  },

  computed: {
    ...mapGetters([
      'isCert',// 是否实名认证
      'certName',// 身份证姓名
      'idCard'// 身份证号码
    ])
  },

  mounted() {
    // 使用nextTick确保DOM更新完成
    this.$nextTick(() => {
      if (this.$refs.uForm) {
        this.$refs.uForm.setRules(this.rules);
      }
    });
  },

  methods: {
    // 选择图片
    chooseImage(type) {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['camera', 'album'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          // 显示上传中loading
          this.$modal.loading('上传中...');

          // 上传图片
          uploadIdCardImage(tempFilePath, type).then(res => {
            if (res.code === 200) {
              // 上传成功，更新form中的图片URL
              if (type === 'front') {
                this.form.cardFrontUrl = res.data.url;
                this.form.realName = res.data.name;
                this.form.idCard = res.data.idCard;
				        this.form.idCardFront = res.data.id;
              } else if (type === 'back') {
                this.form.cardBackUrl = res.data.url;
                this.form.idCardBack = res.data.id;
                if(res.data.isPermanent == 0) {
                  this.form.idExpiration = "长期";
                }else{
                  this.form.idExpiration =res.data.idExpiration;
                }
                this.form.issuingAuthority =res.data.issuingAuthority;
                this.form.idStart =res.data.idStart;
              }
              this.$modal.showToast('上传成功');
            }
          });
        }
      });
    },

    // 提交表单
    submit() {
      this.$refs.uForm.validate().then(valid => {
        if (valid) {
          // 检查必须项
          if (!this.form.idCardFront) {
            return this.$modal.showToast('请上传身份证正面照片');
          }
          if (!this.form.idCardBack) {
            return this.$modal.showToast('请上传身份证反面照片');
          }

          this.$modal.loading('提交中...');

          // 构造请求数据 - 直接使用已上传的图片URL
          const submitData = {
            realName: this.form.realName,
            idCard: this.form.idCard,
            idCardFront: this.form.idCardFront,
            idCardBack: this.form.idCardBack,
            idExpiration:this.form.idExpiration,
            issuingAuthority:this.form.issuingAuthority
          };

          // 调用API提交数据
          submitAuth(submitData).then(res => {
            this.$modal.closeLoading();
            if (res.code === 200) {
              // 更新store中的认证状态，传入完整的数据对象
              this.$store.commit('SET_CERT', {
                status: 1,
                name: this.form.realName,    // 使用表单中的姓名
                idCard: this.form.idCard     // 使用表单中的身份证号
              });
              this.$modal.showToast('认证成功');
            }
          });
        } else {
          this.$modal.showToast('请完善表单信息');
        }
      }).catch(errors => {
        this.$modal.showToast('请完善表单信息');
      });
    },

    // 身份证号码验证
    validateIdCard(idCard) {
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      return reg.test(idCard);
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 过期时间选择确认
    onExpirationDateChange(e) {
      try {
        const dateValue = e.detail.value;
        this.form.idExpiration = dateValue;
      } catch (err) {
        console.error('日期处理错误:', err);
      }
    }
  }
};
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}

.container {
  width: 100%;
  padding: 0;
}

.example {
  padding: 15px;
  background-color: #fff;
}

.section-title {
  padding: 8px 0;
  margin: 5px 0;
  font-size: 15px;
  color: #303133;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}

.upload-section {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 10px 0;
}

.upload-item {
  width: 48%;
}

.upload-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.upload-box {
  width: 100%;
  height: 140px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-tips {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.face-verify {
  padding: 10px 0;
}

.face-box {
  height: 180px;
  margin: 0 auto;
}

.agreement-section {
  padding: 15px 0;
  display: flex;
  align-items: center;
}

.agreement-text {
  font-size: 14px;
  color: #606266;
}

.agreement-link {
  font-size: 14px;
  color: #2979ff;
}

.status-container {
  padding: 40px 0 80px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.status-text {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin: 20px 0;
}

.info-list {
  width: 100%;
  margin-top: 20px;
  padding: 0 20px;
}

.info-item {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #eee;

  .label {
    width: 80px;
    font-size: 14px;
    color: #606266;
  }

  .value {
    flex: 1;
    font-size: 14px;
    color: #303133;
  }
}

.back-button {
  margin-top: 30px;
  width: 80%;
}

.picker-view {
  width: 100%;
  height: 35px;
  line-height: 35px;
  padding: 0 10px;
  font-size: 14px;
  background-color: #ffffff;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #eee;
}

.placeholder {
  color: #999;
}

.arrow {
  font-size: 12px;
  color: #666;
  margin-right: 5px;
}
</style>
