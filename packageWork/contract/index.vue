<template>
	<view class="container">
		<view class="header">
			<view class="title">美金合同下载</view>
		</view>

		<!-- 任务提示 -->
		<view v-if="contractInfo.status !== 'normal'" class="task-tips">
			<view class="tips-title">请先完成以下任务</view>
			<view class="task-list">
				<view class="task-item" v-for="(item, index) in contractInfo.tasks" :key="index" @click="handleTaskClick(item)">
					<view class="task-info">
						<text class="task-title">{{item.title}}</text>
						<text class="task-desc">{{item.description}}</text>
					</view>
					<view class="task-arrow">
						<u-icon name="arrow-right" color="#999" size="28"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<view v-else>
			<view class="card">
				<view class="card-title">基本信息</view>
				<view class="info-list">
					<view class="info-item">
						<text class="label">姓名:</text>
						<text class="value">{{contractInfo.fullname}}</text>
					</view>
					<view class="info-item">
						<text class="label">国籍:</text>
						<text class="value">{{contractInfo.nationality}}</text>
					</view>
					<view class="info-item">
						<text class="label">海员证号:</text>
						<text class="value">{{contractInfo.seamanBookNo}}</text>
					</view>
				</view>
			</view>

			<view class="card">
				<view class="card-title">合同信息</view>
				<view class="info-list">
					<view class="info-item">
						<text class="label">职务:</text>
						<input class="input" v-model="contractInfo.rank" placeholder="请输入职务" />
					</view>
					<view class="info-item">
						<text class="label">每月工资总额:</text>
						<input class="input" v-model="contractInfo.total" placeholder="请输入工资总额" />
					</view>
					<view class="info-item">
						<text class="label">合同签订时间:</text>
						<picker mode="date" :value="contractInfo.seaEnteredInto" @change="onDateChange" class="date-picker">
							<view class="picker-view">
								<text v-if="contractInfo.seaEnteredInto">{{contractInfo.seaEnteredInto}}</text>
								<text v-else class="placeholder">请选择签订时间</text>
								<text class="arrow">▼</text>
							</view>
						</picker>
					</view>
				</view>
			</view>

			<!-- 签名区域 -->
			<!-- <view class="card">
				<view class="card-title">电子签名</view>
				 已有签名显示区域
				<view v-if="hasSignature" class="signature-display">
					<view class="signature-info">
						<text>已设置电子签名(一年仅可修改一次)</text>
						<text class="signature-time">上次签名时间：{{signatureTime}}</text>
					</view>
					<view class="signature-image-container">
						<image :src="signatureUrl" class="signature-image" mode="aspectFit" @load="onSignatureImageLoad"></image>
						<canvas canvas-id="watermarkCanvas" class="watermark-canvas"></canvas>
					</view>
				</view>

				 签名创建区域 
				<view v-if="!hasSignature || canEditSignature" class="signature-create">
					<view class="signature-info">
						<text v-if="hasSignature">您的签名已超过一年，可以设置新的签名</text>
						<text v-else>请在下方区域绘制您的电子签名（保存后不可修改）</text>
					</view>
					<view class="signature-pad-container">
						<canvas
							canvas-id="signatureCanvas"
							class="signature-canvas"
							@touchstart="touchStart"
							@touchmove="touchMove"
							@touchend="touchEnd"
						></canvas>
					</view>
					<view class="signature-actions">
						<button class="action-button clear" @click="clearCanvas">清除</button>
						<button class="action-button save" @click="saveSignature" :disabled="!canSave">保存</button>
					</view>
				</view>
			</view> -->

			<view class="btn-wrap">
				<u-button type="primary" @click="handleDownload">下载合同</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import { getContractInfo, downloadContract } from '@/api/work/contract'
	import { getUserSignature, uploadSignature } from '@/api/system/signature'
	import { mapGetters } from 'vuex'

	export default {
		data() {
			return {
				contractInfo: {
					fullname: '',
					nationality: '',
					seamanBookNo: '',
					rank: '',
					total: '',
					seaEnteredInto: '',
					status: 'normal',
					tasks: []
				},
				// 签名相关数据
				hasSignature: false,
				signatureUrl: '',
				signatureTime: '',
				context: null,
				points: [],
				canSave: false,
				isDrawing: false,
				// 日期选择器相关
				showDatePicker: false,
				minDate: new Date(2000, 0, 1).getTime(),
				maxDate: new Date().getTime(),
				watermarkContext: null,
			}
		},
		computed: {
			...mapGetters([
				'isCert',
				'certName'
			]),
			canEditSignature() {
				if (!this.signatureTime) return true
				const currentTime = new Date()
				const signatureDate = new Date(this.signatureTime)
				const diffTime = currentTime - signatureDate
				const oneYear = 365 * 24 * 60 * 60 * 1000 // 一年的毫秒数
				return diffTime >= oneYear
			}
		},
		onLoad() {
			if (!this.isCert) {
				// 未认证，弹框提示
				uni.showModal({
					title: '温馨提示',
					content: '请先完成实名认证，才能设置电子签名',
					confirmText: '去认证',
					cancelText: '取消',
					success: (modalRes) => {
						if (modalRes.confirm) {
							uni.redirectTo({
								url: '/packageMine/auth/index'
							})
						} else if (modalRes.cancel) {
							// 取消时返回上一级
							uni.navigateBack()
						}
					}
				})
				return
			}
			// 已认证，继续后续逻辑
			this.getContractInfo()
			this.checkSignature()
		},
		onReady() {
			if (!this.hasSignature) {
				this.initCanvas()
			}
		},
		methods: {
			// 获取合同信息
			getContractInfo() {
				getContractInfo().then(res => {
					if (res.code === 200) {
						this.contractInfo = {
							...this.contractInfo,
							...res.data
						}
					} else {
						uni.showToast({
							title: res.msg || '获取合同信息失败',
							icon: 'none'
						})
					}
				}).catch(err => {
					console.error('获取合同信息失败:', err)
					uni.showToast({
						title: '获取合同信息失败',
						icon: 'none'
					})
				})
			},
			// 处理任务点击
			handleTaskClick(task) {
				// 如果url为空,说明暂无对应页面
				if (!task.url) {
					uni.showToast({
						title: '该功能暂未开放',
						icon: 'none'
					})
					return
				}

				uni.navigateTo({
					url: task.url
				})
			},
			// 下载合同
			handleDownload() {
				// 检查是否有签名
				if (!this.hasSignature) {
					uni.showToast({
						title: '请先设置电子签名',
						icon: 'none'
					})
					return
				}

				const { rank, total, seaEnteredInto } = this.contractInfo
				if (!rank || !total || !seaEnteredInto) {
					uni.showToast({
						title: '请填写完整的合同信息',
						icon: 'none'
					})
					return
				}

				uni.showLoading({
					title: '加载中...'
				})

				downloadContract({
					rank,
					total,
					seaEnteredInto
				}).then(res => {
					if (res.code === 200) {
						// 将base64转换为临时文件路径
						const base64 = res.data
						const tempFilePath = `data:image/jpeg;base64,${base64}`

						// 先弹出提示框告知用户可以长按保存图片
						uni.showModal({
							title: '提示',
							content: '图片即将打开，您可以长按图片保存到相册',
							confirmText: '打开图片',
							success: (modalRes) => {
								if (modalRes.confirm) {
									// 用户确认后预览图片
									uni.previewImage({
										urls: [tempFilePath],
										current: tempFilePath
									})
								}
							}
						})
					} else {
						uni.showToast({
							title: res.msg || '获取图片失败',
							icon: 'none'
						})
					}
				}).catch(err => {
					uni.showToast({
						title: '获取图片失败',
						icon: 'none'
					})
				}).finally(() => {
					uni.hideLoading()
				})
			},
			// 查询是否已有签名
			async checkSignature() {
				try {
					const res = await getUserSignature()
					if (res.data) {
						this.hasSignature = true
						this.signatureUrl = res.data.url
						this.signatureTime = res.data.time
					}
				} catch (error) {
					this.$modal.showToast(error)
				}
			},

			// 初始化画布
			initCanvas() {
				this.context = uni.createCanvasContext('signatureCanvas', this)
				this.context.strokeStyle = '#000000'
				this.context.lineWidth = 5
				this.context.lineCap = 'round'
				this.context.lineJoin = 'round'
			},

			// 触摸开始
			touchStart(e) {
				this.isDrawing = true
				const point = {
					x: e.touches[0].x,
					y: e.touches[0].y
				}
				this.points.push(point)
				this.context.beginPath()
				this.context.moveTo(point.x, point.y)
				this.context.lineTo(point.x, point.y)
				this.context.stroke()
				this.context.draw(true)
			},

			// 触摸移动
			touchMove(e) {
				if (!this.isDrawing) return
				const point = {
					x: e.touches[0].x,
					y: e.touches[0].y
				}
				this.points.push(point)
				this.context.beginPath()
				this.context.moveTo(this.points[this.points.length - 2].x, this.points[this.points.length - 2].y)
				this.context.lineTo(point.x, point.y)
				this.context.stroke()
				this.context.draw(true)
				this.canSave = true
			},

			// 触摸结束
			touchEnd() {
				this.isDrawing = false
			},

			// 清除画布
			clearCanvas() {
				this.context.clearRect(0, 0, 1000, 1000)
				this.context.draw()
				this.points = []
				this.canSave = false
			},

			// 保存签名
			saveSignature() {
				if (!this.canSave) {
					this.$modal.showToast('请先绘制签名')
					return
				}

				this.$modal.confirm('签名保存后将无法修改，确认保存？').then(() => {
					uni.canvasToTempFilePath({
						canvasId: 'signatureCanvas',
						success: (res) => {
							this.uploadSignatureImage(res.tempFilePath)
						},
						fail: (err) => {
							console.error('生成图片失败', err)
							this.$modal.showToast('签名生成失败')
						}
					}, this)
				})
			},

			// 上传签名图片
			async uploadSignatureImage(filePath) {
				try {
					uni.showLoading({
						title: '正在保存...'
					})
					const res = await uploadSignature({
						name: 'file',
						filePath: filePath
					})
					uni.hideLoading()

					if (res.code === 200) {
						this.$modal.showToast('签名保存成功')
						// 重新获取签名信息
						await this.checkSignature()
					} else {
						this.$modal.showToast('签名保存失败')
					}
				} catch (error) {
					uni.hideLoading()
					console.error('上传签名失败', error)
					this.$modal.showToast('签名上传失败')
				}
			},

			// 日期选择确认
			onDateChange(e) {
				this.contractInfo.seaEnteredInto = e.detail.value
			},

			// 签名图片加载完成后绘制水印
			onSignatureImageLoad(e) {
				this.$nextTick(() => {
					this.drawWatermark()
				})
			},

			// 绘制水印
			drawWatermark() {
				if (!this.certName) return

				const query = uni.createSelectorQuery().in(this)
				query.select('.signature-image-container').boundingClientRect(data => {
					if (!data) return

					const { width, height } = data
					const canvas = uni.createCanvasContext('watermarkCanvas', this)

					// 设置水印样式
					canvas.font = 'normal 20rpx KaiTi'
					canvas.fillStyle = 'rgba(0, 0, 0, 0.3)' // 增加不透明度
					canvas.textAlign = 'center'
					canvas.textBaseline = 'middle'

					// 计算水印间距
					const spacing = 80 // 进一步减小水印之间的间距
					const angle = -Math.PI / 6 // 倾斜角度

					// 绘制重复的水印
					for (let i = -spacing; i < width + spacing; i += spacing) {
						for (let j = -spacing; j < height + spacing; j += spacing) {
							canvas.save()
							canvas.translate(i, j)
							canvas.rotate(angle)
							canvas.fillText(`${this.certName}`, 0, 0)
							canvas.restore()
						}
					}

					canvas.draw()
				}).exec()
			},
		}
	}
</script>

<style lang="scss">
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20rpx;
	}

	.header {
		margin-bottom: 30rpx;

		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.task-tips {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;

		.tips-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
		}

		.task-list {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;

			.task-item {
				width: 48%;
				margin-bottom: 20rpx;
				padding: 20rpx;
				background: #f8f8f8;
				border-radius: 12rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				&:last-child {
					margin-right: auto;
				}

				.task-info {
					flex: 1;

					.task-title {
						font-size: 30rpx;
						color: #333;
						margin-bottom: 10rpx;
					}

					.task-desc {
						font-size: 26rpx;
						color: #999;
					}
				}

				.task-arrow {
					margin-left: 10rpx;
				}
			}
		}
	}

	.card {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);

		.card-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
			padding-bottom: 20rpx;
			border-bottom: 1rpx solid #eee;
		}

		.info-list {
			.info-item {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				.label {
					width: 240rpx;
					font-size: 28rpx;
					color: #666;
				}

				.value {
					flex: 1;
					font-size: 28rpx;
					color: #333;
				}

				.input {
					flex: 1;
					height: 60rpx;
					background-color: #f8f8f8;
					border-radius: 8rpx;
					padding: 0 20rpx;
					font-size: 28rpx;
					color: #333;
				}
			}
		}
	}

	.signature-display {
		background-color: white;
		border-radius: 8rpx;
		padding: 20rpx;

		.signature-info {
			margin-bottom: 15rpx;
			color: #666;
			font-size: 28rpx;

			.signature-time {
				display: block;
				margin-top: 8rpx;
				color: #999;
				font-size: 24rpx;
			}
		}

		.signature-image-container {
			position: relative;
			width: 100%;
			height: 200rpx;
			background-color: #f9f9f9;
			border: 1rpx solid #eaeaea;
			border-radius: 8rpx;
			overflow: hidden;

			.signature-image {
				width: 100%;
				height: 100%;
				position: relative;
				z-index: 1;
			}

			.watermark-canvas {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 2;
				pointer-events: none; // 确保水印不影响图片点击
			}
		}
	}

	.signature-create {
		background-color: white;
		border-radius: 8rpx;
		padding: 20rpx;

		.signature-info {
			margin-bottom: 15rpx;
			color: #666;
			font-size: 28rpx;
		}

		.signature-pad-container {
			width: 100%;
			height: 400rpx;
			border: 1rpx solid #ddd;
			border-radius: 8rpx;
			margin-bottom: 20rpx;
			background-color: #f9f9f9;
			overflow: hidden;

			.signature-canvas {
				width: 100%;
				height: 100%;
			}
		}

		.signature-actions {
			display: flex;
			justify-content: space-between;

			.action-button {
				flex: 1;
				margin: 0 10rpx;
				height: 80rpx;
				line-height: 80rpx;
				font-size: 28rpx;
				border-radius: 8rpx;

				&.clear {
					background-color: #f5f5f5;
					color: #666;
				}

				&.save {
					background-color: #3c96f3;
					color: white;

					&[disabled] {
						background-color: #cccccc;
						color: #999999;
					}
				}
			}
		}
	}

	.btn-wrap {
		padding: 30rpx;
	}

	.date-picker {
		flex: 1;
		width: 100%;
	}

	.picker-view {
		height: 60rpx;
		line-height: 60rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.placeholder {
		color: #999;
	}

	.arrow {
		font-size: 24rpx;
		color: #666;
		margin-right: 5rpx;
	}
</style>
