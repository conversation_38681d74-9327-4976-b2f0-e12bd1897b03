/*
 Navicat Premium Dump SQL

 Source Server         : ***********
 Source Server Type    : MySQL
 Source Server Version : 80039 (8.0.39)
 Source Host           : ***********:3307
 Source Schema         : yumeng-config

 Target Server Type    : MySQL
 Target Server Version : 80039 (8.0.39)
 File Encoding         : 65001

 Date: 13/04/2025 12:23:44
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for config_info
-- ----------------------------
DROP TABLE IF EXISTS `config_info`;
CREATE TABLE `config_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'group_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  `c_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'configuration description',
  `c_use` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'configuration usage',
  `effect` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '配置生效的描述',
  `type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '配置的类型',
  `c_schema` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT '配置的模式',
  `encrypted_data_key` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '密钥',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfo_datagrouptenant` (`data_id`,`group_id`,`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=116 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='config_info';

-- ----------------------------
-- Records of config_info
-- ----------------------------
BEGIN;
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (1, 'application-common.yml', 'DEFAULT_GROUP', 'server:\n  # undertow 配置\n  undertow:\n    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的\n    max-http-post-size: -1\n    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理\n    # 每块buffer的空间大小,越小的空间被利用越充分\n    buffer-size: 512\n    # 是否分配的直接内存\n    direct-buffers: true\n    threads:\n      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程\n      io: 8\n      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载\n      worker: 256\n\ndubbo:\n  application:\n    # 关闭qos端口避免单机多生产者端口冲突 如需使用自行开启\n    qos-enable: false\n  protocol:\n    # 如需使用 Triple 3.0 新协议 可查看官方文档\n    # 使用 dubbo 协议通信\n    name: dubbo\n    # dubbo 协议端口(-1表示自增端口,从20880开始)\n    port: -1\n    # 指定dubbo协议注册ip\n    # host: *************\n  # 消费者相关配置\n  consumer:\n    # 超时时间\n    timeout: 3000\n  # 自定义配置\n  custom:\n    # 全局请求log\n    request-log: true\n    # info 基础信息 param 参数信息 full 全部\n    log-level: info\n\nspring:\n  threads:\n    # 开启虚拟线程 仅jdk21可用\n    virtual:\n      enabled: false\n  # 资源信息\n  messages:\n    # 国际化资源文件路径\n    basename: i18n/messages\n  servlet:\n    multipart:\n      # 整个请求大小限制\n      max-request-size: 20MB\n      # 上传单个文件大小限制\n      max-file-size: 10MB\n  mvc:\n    # 设置静态资源路径 防止所有请求都去查静态资源\n    static-path-pattern: /static/**\n    format:\n      date-time: yyyy-MM-dd HH:mm:ss\n  #jackson配置\n  jackson:\n    # 日期格式化\n    date-format: yyyy-MM-dd HH:mm:ss\n    serialization:\n      # 格式化输出\n      INDENT_OUTPUT: false\n      # 忽略无法转换的对象\n      fail_on_empty_beans: false\n    deserialization:\n      # 允许对象忽略json中不存在的属性\n      fail_on_unknown_properties: false\n  cloud:\n    nacos:\n      discovery:\n        metadata:\n          # admin 监控账号密码\n          username: yumeng\n          userpassword: yumeng@2025!\n    # sentinel 配置\n    sentinel:\n      # sentinel 开关\n      enabled: true\n      transport:\n        # dashboard控制台服务名 用于服务发现\n        # 如无此配置将默认使用下方 dashboard 配置直接注册\n        server-name: yumeng-sentinel-dashboard\n        # 客户端指定注册的ip 用于多网卡ip不稳点使用\n        # client-ip:\n        # 控制台地址 从1.3.0开始使用 server-name 注册\n        # dashboard: localhost:8718\n\n    bus:\n      id: ${spring.application.name}\n      base-packages: com.ym.**.event\n  # 消息总线 也可以使用 kafka 参考 spring-cloud-bus 用法\n  rabbitmq:\n    host: localhost\n    port: 5672\n    username: yumeng\n    password: yumeng@2025!\n\n  # redis通用配置 子服务可以自行配置进行覆盖\n  data:\n    redis:\n      host: ***********\n      port: 6379\n      # redis 密码必须配置\n      password: yumeng@2025!\n      database: 5\n      # 需要使用数字\n      timeout: 10000\n      ssl.enabled: false\n\n# redisson 配置\nredisson:\n  # redis key前缀\n  keyPrefix:\n  # 线程池数量\n  threads: 4\n  # Netty线程池数量\n  nettyThreads: 8\n  # 单节点配置\n  singleServerConfig:\n    # 客户端名称\n    clientName: ${spring.application.name}\n    # 最小空闲连接数\n    connectionMinimumIdleSize: 8\n    # 连接池大小\n    connectionPoolSize: 32\n    # 连接空闲超时，单位：毫秒\n    idleConnectionTimeout: 10000\n    # 命令等待超时，单位：毫秒\n    timeout: 3000\n    # 发布和订阅连接池大小\n    subscriptionConnectionPoolSize: 50\n\n# 分布式锁 lock4j 全局配置\nlock4j:\n  # 获取分布式锁超时时间，默认为 3000 毫秒\n  acquire-timeout: 3000\n  # 分布式锁的超时时间，默认为 30 秒\n  expire: 30000\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n  endpoint:\n    health:\n      show-details: ALWAYS\n    logfile:\n      external-file: ./logs/${spring.application.name}/console.log\n\n# 日志配置\nlogging:\n  level:\n    org.springframework: warn\n    org.apache.dubbo: warn\n    com.alibaba.nacos: warn\n    com.alibaba.cloud.sentinel: warn\n    org.mybatis.spring.mapper: error\n    org.apache.dubbo.config: error\n    # 临时处理 spring 调整日志级别导致启动警告问题 不影响使用等待 alibaba 适配\n    org.springframework.context.support.PostProcessorRegistrationDelegate: error\n  config: classpath:logback-plus.xml\n\n# Sa-Token配置\nsa-token:\n  # token名称 (同时也是cookie名称)\n  token-name: Authorization\n  # 开启内网服务调用鉴权(不允许越过gateway访问内网服务 保障服务安全)\n  check-same-token: true\n  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)\n  is-concurrent: true\n  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)\n  is-share: false\n  # jwt秘钥\n  jwt-secret-key: abcdefghijklmnopqrstuvwxyz\n\n# MyBatisPlus配置\n# https://baomidou.com/config/\nmybatis-plus:\n  # 多包名使用 例如 org.dromara.**.mapper,org.xxx.**.mapper\n  mapperPackage: com.ym.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ym.**.domain\n  global-config:\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      # 如需改为自增 需要将数据库表全部设置为自增\n      idType: ASSIGN_ID\n\n# 数据加密\nmybatis-encryptor:\n  # 是否开启加密\n  enable: false\n  # 默认加密算法\n  algorithm: BASE64\n  # 编码方式 BASE64/HEX。默认BASE64\n  encode: BASE64\n  # 安全秘钥 对称算法的秘钥 如：AES，SM4\n  password:\n  # 公私钥 非对称算法的公私钥 如：SM2，RSA\n  publicKey:\n  privateKey:\n\n# api接口加密\napi-decrypt:\n  # 是否开启全局接口加密\n  enabled: true\n  # AES 加密头标识\n  headerFlag: encrypt-key\n  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=\n  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJnNwrj4hi/y3CCJu868ghCG5dUj8wZK++RNlTLcXoMmdZWEQ/u02RgD5LyLAXGjLOjbMtC+/J9qofpSGTKSx/MCAwEAAQ==\n  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==\n  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=\n\n# 防止XSS攻击\nxss:\n  enabled: true\n  excludeUrls:\n    - /system/notice\n    - /workflow/model/save\n    - /workflow/model/editModelXml\n\n# 接口文档配置\nspringdoc:\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  #  swagger-ui:\n  #    # 持久化认证数据\n  #    persistAuthorization: true\n  info:\n    # 标题\n    title: \'标题：育蒙微服务权限管理系统_接口文档\'\n    # 描述\n    description: \'描述：育蒙微服务权限管理系统\'\n    # 版本\n    version: 1.0.0\n    # 作者信息\n    contact:\n      name: Luo Ming\n      email: <EMAIL>\n      url: http://***********/luoming/yumeng-cloud-api.git\n  components:\n    # 鉴权方式配置\n    security-schemes:\n      apiKey:\n        type: APIKEY\n        in: HEADER\n        name: ${sa-token.token-name}\n\n# seata配置\nseata:\n  # 是否启用\n  enabled: true\n  # Seata 应用编号，默认为应用名\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n\n# 多租户配置\ntenant:\n  # 是否开启\n  enable: false\n  # 排除表\n  excludes:\n    - sys_menu\n    - sys_tenant\n    - sys_tenant_package\n    - sys_role_dept\n    - sys_role_menu\n    - sys_user_post\n    - sys_user_role\n    - sys_client\n    - sys_oss_config\n', '4b04c660e38092ec94e5a3fbd040cb72', '2022-01-09 15:18:55', '2025-04-07 12:22:56', 'nacos', '***************', '', 'dev', '通用配置基础配置', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (2, 'datasource.yml', 'DEFAULT_GROUP', 'datasource:\n  system-master:\n    # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562\n    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能\n    url: ********************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  gen:\n    url: ********************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  job:\n    url: jdbc:mysql://***********:3307/yumeng-job?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true\n    username: root\n    password: qloong2019\n  workflow:\n    url: jdbc:mysql://***********:3307/yumeng-workflow?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true\n    username: root\n    password: qloong2019\n  salary:\n    url: jdbc:mysql://***********:3307/yumeng-salary?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true\n    username: root\n    password: qloong2019\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content\n    dynamic:\n      # 性能分析插件(有性能损耗 不建议生产环境使用)\n      p6spy: true\n      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n      seata: ${seata.enabled}\n      # 严格模式 匹配不到数据源则报错\n      strict: true\n      hikari:\n        # 最大连接池数量\n        maxPoolSize: 20\n        # 最小空闲线程数量\n        minIdle: 10\n        # 配置获取连接等待超时的时间\n        connectionTimeout: 30000\n        # 校验超时时间\n        validationTimeout: 5000\n        # 空闲连接存活最大时间，默认10分钟\n        idleTimeout: 600000\n        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟\n        maxLifetime: 1800000\n        # 多久检查一次连接的活性\n        keepaliveTime: 30000\n', 'b41469ec9d3dd417986947fe64ad37a0', '2022-01-09 15:19:07', '2025-04-12 09:21:39', 'nacos', '************', '', 'dev', '数据源配置', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (3, 'yumeng-gateway.yml', 'DEFAULT_GROUP', '# 安全配置\nsecurity:\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/code\n      - /auth/logout\n      - /auth/login\n      - /auth/binding/*\n      - /auth/social/callback\n      - /auth/register\n      - /auth/tenant/list\n      - /resource/sms/code\n      - /resource/sse/close\n      - /*/v3/api-docs\n      - /*/error\n      - /csrf\n\nspring:\n  cloud:\n    # 网关配置\n    gateway:\n      # 打印请求日志(自定义)\n      requestLog: true\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: yumeng-auth\n          uri: lb://yumeng-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            - StripPrefix=1\n        # 代码生成\n        - id: yumeng-gen\n          uri: lb://yumeng-gen\n          predicates:\n            - Path=/tool/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: yumeng-system\n          uri: lb://yumeng-system\n          predicates:\n            - Path=/system/**,/monitor/**\n          filters:\n            - StripPrefix=1\n        # 资源服务\n        - id: yumeng-resource\n          uri: lb://yumeng-resource\n          predicates:\n            - Path=/resource/**\n          filters:\n            - StripPrefix=1\n        # workflow服务\n        - id: yumeng-workflow\n          uri: lb://yumeng-workflow\n          predicates:\n            - Path=/workflow/**\n          filters:\n            - StripPrefix=1\n        # 演示服务\n        - id: yumeng-demo\n          uri: lb://yumeng-demo\n          predicates:\n            - Path=/demo/**\n          filters:\n            - StripPrefix=1\n        # MQ演示服务\n        - id: yumeng-test-mq\n          uri: lb://yumeng-test-mq\n          predicates:\n            - Path=/test-mq/**\n          filters:\n            - StripPrefix=1\n        # 工资结算\n        - id: yumeng-salary-settlement\n          uri: lb://yumeng-salary-settlement\n          predicates:\n            - Path=/salary/**\n          filters:\n            - StripPrefix=1\n    # sentinel 配置\n    sentinel:\n      filter:\n        enabled: false\n      # nacos配置持久化\n      datasource:\n        ds1:\n          nacos:\n            server-addr: ${spring.cloud.nacos.server-addr}\n            dataId: sentinel-${spring.application.name}.json\n            groupId: ${spring.cloud.nacos.config.group}\n            username: ${spring.cloud.nacos.username}\n            password: ${spring.cloud.nacos.password}\n            namespace: ${spring.profiles.active}\n            data-type: json\n            rule-type: gw-flow\n', 'ff81786a3111f9b07edc6c8be1fdba46', '2022-01-09 15:19:43', '2025-04-12 11:58:48', 'nacos', '***************', '', 'dev', '网关模块', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (4, 'yumeng-auth.yml', 'DEFAULT_GROUP', '# 安全配置\nsecurity:\n  # 验证码\n  captcha:\n    # 是否开启验证码\n    enabled: true\n    # 验证码类型 math 数组计算 char 字符验证\n    type: MATH\n    # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰\n    category: CIRCLE\n    # 数字验证码位数\n    numberLength: 1\n    # 字符验证码长度\n    charLength: 4\n\n# 用户配置\nuser:\n  password:\n    # 密码最大错误次数\n    maxRetryCount: 5\n    # 密码锁定时间（默认10分钟）\n    lockTime: 10\n\n# 三方授权\njustauth:\n  # 前端外网访问地址\n  address: http://localhost:80\n  type:\n    maxkey:\n      # maxkey 服务器地址\n      # 注意 如下均配置均不需要修改 maxkey 已经内置好了数据\n      server-url: http://sso.maxkey.top\n      client-id: 876892492581044224\n      client-secret: x1Y5MTMwNzIwMjMxNTM4NDc3Mzche8\n      redirect-uri: ${justauth.address}/social-callback?source=maxkey\n    topiam:\n      # topiam 服务器地址\n      server-url: http://127.0.0.1:1989/api/v1/authorize/y0q************spq***********8ol\n      client-id: 449c4*********937************759\n      client-secret: ac7***********1e0************28d\n      redirect-uri: ${justauth.address}/social-callback?source=topiam\n      scopes: [ openid, email, phone, profile ]\n    qq:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=qq\n      union-id: false\n    weibo:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=weibo\n    gitee:\n      client-id: 91436b7940090d09c72c7daf85b959cfd5f215d67eea73acbf61b6b590751a98\n      client-secret: 02c6fcfd70342980cd8dd2f2c06c1a350645d76c754d7a264c4e125f9ba915ac\n      redirect-uri: ${justauth.address}/social-callback?source=gitee\n    dingtalk:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=dingtalk\n    baidu:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=baidu\n    csdn:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=csdn\n    coding:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=coding\n      coding-group-name: xx\n    oschina:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=oschina\n    alipay_wallet:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=alipay_wallet\n      alipay-public-key: MIIB**************DAQAB\n    wechat_open:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=wechat_open\n    wechat_mp:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=wechat_mp\n    wechat_enterprise:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=wechat_enterprise\n      agent-id: 1000002\n    gitlab:\n      client-id: 10**********6\n      client-secret: 1f7d08**********5b7**********29e\n      redirect-uri: ${justauth.address}/social-callback?source=gitlab\n', '********************************', '2022-01-09 15:19:43', '2025-01-17 15:05:51', 'nacos', '*************', '', 'dev', '认证中心', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (5, 'yumeng-monitor.yml', 'DEFAULT_GROUP', '# 监控中心配置\nspring:\n  security:\n    user:\n      name: yumeng\n      password: yumeng@2025!\n  boot:\n    admin:\n      ui:\n        title: yumeng-cloud服务监控中心\n      discovery:\n        # seata 不具有健康检查的能力 防止报错排除掉\n        ignored-services: yumeng-seata-server\n', '4a13c56975da651c50d832bed59cf9d1', '2022-01-09 15:20:18', '2025-04-07 13:36:11', 'nacos', '***************', '', 'dev', '监控中心', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (6, 'yumeng-system.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.system-master.url}\n          username: ${datasource.system-master.username}\n          password: ${datasource.system-master.password}\n#        oracle:\n#          type: ${spring.datasource.type}\n#          driverClassName: oracle.jdbc.OracleDriver\n#          url: ${datasource.system-oracle.url}\n#          username: ${datasource.system-oracle.username}\n#          password: ${datasource.system-oracle.password}\n#        postgres:\n#          type: ${spring.datasource.type}\n#          driverClassName: org.postgresql.Driver\n#          url: ${datasource.system-postgres.url}\n#          username: ${datasource.system-postgres.username}\n#          password: ${datasource.system-postgres.password}\n', 'f59259409ff41d30e5276bf4afbcaa3d', '2022-01-09 15:20:18', '2025-01-17 15:07:39', 'nacos', '*************', '', 'dev', '系统模块', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (7, 'yumeng-gen.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      seata: false\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.salary.url}\n          username: ${datasource.salary.username}\n          password: ${datasource.salary.password}\n# 代码生成\ngen:\n  # 作者\n  author: luoming\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ym.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '1837954c5a6ee60eab55b1e95abd9bbd', '2022-01-09 15:20:18', '2025-04-12 12:38:25', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', '代码生成', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (8, 'yumeng-job.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      seata: false\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.job.url}\n          username: ${datasource.job.username}\n          password: ${datasource.job.password}\n\nsnail-job:\n  enabled: true\n  # 需要在 SnailJob 后台组管理创建对应名称的组,然后创建任务的时候选择对应的组,才能正确分派任务\n  group: \"yumeng_group\"\n  #  SnailJob 接入验证令牌\n  token: \"SJ_cKqBTPzCsWA3VyuCfFoccmuIEGXjr5KT\"\n  server:\n    # 从 nacos 获取服务\n    server-name: yumeng-snailjob-server\n    # 服务名优先 ip垫底\n    host: 127.0.0.1\n    port: 17888\n  # 详见 script/sql/ry_job.sql `sj_namespace` 表\n  namespace: ${spring.profiles.active}\n  # 随主应用端口飘逸\n  port: 2${server.port}\n  # 客户端ip指定\n  host:\n', '1c0e2fd23f5975d64bfce1ea42373d2c', '2022-01-09 15:20:18', '2025-01-17 15:06:28', 'nacos', '*************', '', 'dev', '定时任务', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (9, 'yumeng-resource.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.system-master.url}\n          username: ${datasource.system-master.username}\n          password: ${datasource.system-master.password}\n#        oracle:\n#          type: ${spring.datasource.type}\n#          driverClassName: oracle.jdbc.OracleDriver\n#          url: ${datasource.system-oracle.url}\n#          username: ${datasource.system-oracle.username}\n#          password: ${datasource.system-oracle.password}\n#        postgres:\n#          type: ${spring.datasource.type}\n#          driverClassName: org.postgresql.Driver\n#          url: ${datasource.system-postgres.url}\n#          username: ${datasource.system-postgres.username}\n#          password: ${datasource.system-postgres.password}\n\n# 默认/推荐使用sse推送\nsse:\n  enabled: true\n  path: /sse\n\nwebsocket:\n  # 如果关闭 需要和前端开关一起关闭\n  enabled: false\n  # 路径\n  path: /websocket\n  # 设置访问源地址\n  allowedOrigins: \'*\'\n\nmail:\n  enabled: false\n  host: smtp.163.com\n  port: 465\n  # 是否需要用户名密码验证\n  auth: true\n  # 发送方，遵循RFC-822标准\n  from: <EMAIL>\n  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）\n  user: <EMAIL>\n  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）\n  pass: xxxxxxxxxx\n  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。\n  starttlsEnable: true\n  # 使用SSL安全连接\n  sslEnable: true\n  # SMTP超时时长，单位毫秒，缺省值不超时\n  timeout: 0\n  # Socket连接超时值，单位毫秒，缺省值不超时\n  connectionTimeout: 0\n\n# sms 短信 支持 阿里云 腾讯云 云片 等等各式各样的短信服务商\n# https://sms4j.com/doc3/ 差异配置文档地址 支持单厂商多配置，可以配置多个同时使用\nsms:\n  # 配置源类型用于标定配置来源(interface,yaml)\n  config-type: yaml\n  # 用于标定yml中的配置是否开启短信拦截，接口配置不受此限制\n  restricted: true\n  # 短信拦截限制单手机号每分钟最大发送，只对开启了拦截的配置有效\n  minute-max: 1\n  # 短信拦截限制单手机号每日最大发送量，只对开启了拦截的配置有效\n  account-max: 30\n  # 以下配置来自于 org.dromara.sms4j.provider.config.BaseConfig类中\n  blends:\n    # 唯一ID 用于发送短信寻找具体配置 随便定义别用中文即可\n    # 可以同时存在两个相同厂商 例如: ali1 ali2 两个不同的阿里短信账号 也可用于区分租户\n    config1:\n      # 框架定义的厂商名称标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分\n      supplier: alibaba\n      # 有些称为accessKey有些称之为apiKey，也有称为sdkKey或者appId。\n      access-key-id: 您的accessKey\n      # 称为accessSecret有些称之为apiSecret\n      access-key-secret: 您的accessKeySecret\n      signature: 您的短信签名\n      sdk-app-id: 您的sdkAppId\n    config2:\n      # 厂商标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分\n      supplier: tencent\n      access-key-id: 您的accessKey\n      access-key-secret: 您的accessKeySecret\n      signature: 您的短信签名\n      sdk-app-id: 您的sdkAppId\n', 'efb16000eb230328fa2128ea56558fd2', '2022-01-09 15:20:35', '2025-01-17 15:06:49', 'nacos', '*************', '', 'dev', '文件服务', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (10, 'yumeng-workflow.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.workflow.url}\n          username: ${datasource.workflow.username}\n          password: ${datasource.workflow.password}\n#        oracle:\n#          type: ${spring.datasource.type}\n#          driverClassName: oracle.jdbc.OracleDriver\n#          url: ${datasource.system-oracle.url}\n#          username: ${datasource.system-oracle.username}\n#          password: ${datasource.system-oracle.password}\n#        postgres:\n#          type: ${spring.datasource.type}\n#          driverClassName: org.postgresql.Driver\n#          url: ${datasource.system-postgres.url}\n#          username: ${datasource.system-postgres.username}\n#          password: ${datasource.system-postgres.password}\n\n# flowable配置\nflowable:\n  # 开关 用于启动/停用工作流\n  enabled: true\n  process.enabled: ${flowable.enabled}\n  eventregistry.enabled: ${flowable.enabled}\n  # 关闭定时任务JOB\n  async-executor-activate: false\n  # 将databaseSchemaUpdate设置为true。当Flowable发现库与数据库表结构不一致时，会自动将数据库表结构升级至新版本。\n  database-schema-update: true\n  activity-font-name: 宋体\n  label-font-name: 宋体\n  annotation-font-name: 宋体\n  # 关闭各个模块生成表，目前只使用工作流基础表\n  idm:\n    enabled: false\n  cmmn:\n    enabled: false\n  dmn:\n    enabled: false\n  app:\n    enabled: false\n', '95b566892b838da618fbfdaf1ecffd0a', '2022-01-09 15:20:35', '2025-01-17 15:07:53', 'nacos', '*************', '', 'dev', '工作流服务', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (11, 'sentinel-yumeng-gateway.json', 'DEFAULT_GROUP', '[\n  {\n    \"resource\": \"yumeng-auth\",\n    \"count\": 500,\n    \"grade\": 1,\n    \"limitApp\": \"default\",\n    \"strategy\": 0,\n    \"controlBehavior\": 0\n  },\n  {\n    \"resource\": \"yumeng-system\",\n    \"count\": 1000,\n    \"grade\": 1,\n    \"limitApp\": \"default\",\n    \"strategy\": 0,\n    \"controlBehavior\": 0\n  },\n  {\n    \"resource\": \"yumeng-resource\",\n    \"count\": 500,\n    \"grade\": 1,\n    \"limitApp\": \"default\",\n    \"strategy\": 0,\n    \"controlBehavior\": 0\n  }\n]\n', 'e190a59a0ba66e016199515576a5d6b1', '2022-01-09 15:21:02', '2025-01-17 15:05:33', 'nacos', '*************', '', 'dev', '限流策略', '', '', 'json', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (12, 'seata-server.properties', 'DEFAULT_GROUP', 'service.vgroupMapping.yumeng-auth-group=default\nservice.vgroupMapping.yumeng-system-group=default\nservice.vgroupMapping.yumeng-resource-group=default\nservice.vgroupMapping.yumeng-workflow-group=default\nservice.vgroupMapping.yumeng-salary-settlement-group=default\n\nservice.enableDegrade=false\nservice.disableGlobalTransaction=false\n\n#Transaction storage configuration, only for the server. The file, DB, and redis configuration values are optional.\nstore.mode=db\nstore.lock.mode=db\nstore.session.mode=db\n#Used for password encryption\n#store.publicKey=\n\n#These configurations are required if the `store mode` is `db`. If `store.mode,store.lock.mode,store.session.mode` are not equal to `db`, you can remove the configuration block.\nstore.db.datasource=hikari\nstore.db.dbType=mysql\nstore.db.driverClassName=com.mysql.cj.jdbc.Driver\nstore.db.url=jdbc:mysql://***********:3307/yumeng-seata?useUnicode=true&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true\nstore.db.user=root\nstore.db.password=qloong2019\nstore.db.minConn=5\nstore.db.maxConn=30\nstore.db.globalTable=global_table\nstore.db.branchTable=branch_table\nstore.db.distributedLockTable=distributed_lock\nstore.db.queryLimit=100\nstore.db.lockTable=lock_table\nstore.db.maxWait=5000\n\n# redis 模式 store.mode=redis 开启 (控制台查询功能有限,不影响实际执行功能)\n# store.redis.host=127.0.0.1\n# store.redis.port=6379\n# 最大连接数\n# store.redis.maxConn=10\n# 最小连接数\n# store.redis.minConn=1\n# store.redis.database=0\n# store.redis.password=\n# store.redis.queryLimit=100\n\n#Transaction rule configuration, only for the server\nserver.recovery.committingRetryPeriod=1000\nserver.recovery.asynCommittingRetryPeriod=1000\nserver.recovery.rollbackingRetryPeriod=1000\nserver.recovery.timeoutRetryPeriod=1000\nserver.maxCommitRetryTimeout=-1\nserver.maxRollbackRetryTimeout=-1\nserver.rollbackRetryTimeoutUnlockEnable=false\nserver.distributedLockExpireTime=10000\nserver.xaerNotaRetryTimeout=60000\nserver.session.branchAsyncQueueSize=5000\nserver.session.enableBranchAsyncRemove=false\n\n#Transaction rule configuration, only for the client\nclient.rm.asyncCommitBufferLimit=10000\nclient.rm.lock.retryInterval=10\nclient.rm.lock.retryTimes=30\nclient.rm.lock.retryPolicyBranchRollbackOnConflict=true\nclient.rm.reportRetryCount=5\nclient.rm.tableMetaCheckEnable=true\nclient.rm.tableMetaCheckerInterval=60000\nclient.rm.sqlParserType=druid\nclient.rm.reportSuccessEnable=false\nclient.rm.sagaBranchRegisterEnable=false\nclient.rm.sagaJsonParser=fastjson\nclient.rm.tccActionInterceptorOrder=-2147482648\nclient.tm.commitRetryCount=5\nclient.tm.rollbackRetryCount=5\nclient.tm.defaultGlobalTransactionTimeout=60000\nclient.tm.degradeCheck=false\nclient.tm.degradeCheckAllowTimes=10\nclient.tm.degradeCheckPeriod=2000\nclient.tm.interceptorOrder=-2147482648\nclient.undo.dataValidation=true\nclient.undo.logSerialization=jackson\nclient.undo.onlyCareUpdateColumns=true\nserver.undo.logSaveDays=7\nserver.undo.logDeletePeriod=86400000\nclient.undo.logTable=undo_log\nclient.undo.compress.enable=true\nclient.undo.compress.type=zip\nclient.undo.compress.threshold=64k\n\n#For TCC transaction mode\ntcc.fence.logTableName=tcc_fence_log\ntcc.fence.cleanPeriod=1h\n\n#Log rule configuration, for client and server\nlog.exceptionRate=100\n\n#Metrics configuration, only for the server\nmetrics.enabled=false\nmetrics.registryType=compact\nmetrics.exporterList=prometheus\nmetrics.exporterPrometheusPort=9898\n\n#For details about configuration items, see https://seata.io/zh-cn/docs/user/configurations.html\n#Transport configuration, for client and server\ntransport.type=TCP\ntransport.server=NIO\ntransport.heartbeat=true\ntransport.enableTmClientBatchSendRequest=false\ntransport.enableRmClientBatchSendRequest=true\ntransport.enableTcServerBatchSendResponse=false\ntransport.rpcRmRequestTimeout=30000\ntransport.rpcTmRequestTimeout=30000\ntransport.rpcTcRequestTimeout=30000\ntransport.threadFactory.bossThreadPrefix=NettyBoss\ntransport.threadFactory.workerThreadPrefix=NettyServerNIOWorker\ntransport.threadFactory.serverExecutorThreadPrefix=NettyServerBizHandler\ntransport.threadFactory.shareBossWorker=false\ntransport.threadFactory.clientSelectorThreadPrefix=NettyClientSelector\ntransport.threadFactory.clientSelectorThreadSize=1\ntransport.threadFactory.clientWorkerThreadPrefix=NettyClientWorkerThread\ntransport.threadFactory.bossThreadSize=1\ntransport.threadFactory.workerThreadSize=default\ntransport.shutdown.wait=3\ntransport.serialization=seata\ntransport.compressor=none\n', 'cbb0d9fd782a2dd58a465b199fe3da4e', '2022-01-09 15:21:02', '2025-04-12 09:45:51', 'nacos', '0:0:0:0:0:0:0:1', '', 'dev', 'seata配置文件', '', '', 'properties', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (13, 'yumeng-sentinel-dashboard.yml', 'DEFAULT_GROUP', 'spring:\n  mvc:\n    pathmatch:\n      # 修复 sentinel 控制台未适配 springboot 2.6 新路由方式\n      matching-strategy: ANT_PATH_MATCHER\n\nserver:\n  servlet:\n    encoding:\n      force: true\n      charset: UTF-8\n      enabled: true\n    session:\n      cookie:\n        name: sentinel_dashboard_cookie\n\nlogging:\n  level:\n    org.springframework.web: INFO\n\nauth:\n  enabled: true\n  filter:\n    exclude-urls: /,/auth/login,/auth/logout,/registry/machine,/version,/actuator,/actuator/**\n    exclude-url-suffixes: htm,html,js,css,map,ico,ttf,woff,png\n  username: sentinel\n  password: sentinel\n', '59055747c62f08cd2c38a6016d4b9227', '2022-01-09 15:21:02', '2025-01-17 15:07:12', 'nacos', '*************', '', 'dev', 'sentinel控制台配置文件', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (14, 'yumeng-snailjob-server.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    driver-class-name: com.mysql.cj.jdbc.Driver\n    url: ${datasource.job.url}\n    username: ${datasource.job.username}\n    password: ${datasource.job.password}\n    hikari:\n      connection-timeout: 30000\n      validation-timeout: 5000\n      minimum-idle: 10\n      maximum-pool-size: 20\n      idle-timeout: 600000\n      max-lifetime: 900000\n      keepaliveTime: 30000\n  cloud:\n    nacos:\n      discovery:\n        metadata:\n          # 解决 er 服务有 context-path 无法监控问题\n          management.context-path: ${server.servlet.context-path}/actuator\n          # 监控账号密码\n          username: yumeng\n          userpassword: ym@18513311526\n\n# snail-job 服务端配置\nsnail-job:\n  # 拉取重试数据的每批次的大小\n  retry-pull-page-size: 1000\n  # 拉取重试数据的每批次的大小\n  job-pull-page-size: 1000\n  # 服务端 netty 端口\n  netty-port: 17888\n  # 重试和死信表的分区总数\n  total-partition: 2\n  # 一个客户端每秒最多接收的重试数量指令\n  limiter: 1000\n  # 号段模式下步长配置\n  step: 100\n  # 日志保存时间(单位: day)\n  log-storage: 90\n  # 回调配置\n  callback:\n    #回调最大执行次数\n    max-count: 288\n    #间隔时间\n    trigger-interval: 900\n  retry-max-pull-count: 10\n', '36ae96030d5c6b81afdab36c679db3e6', '2022-01-09 15:21:02', '2025-01-17 15:07:27', 'nacos', '*************', '', 'dev', 'SJ定时任务控制台', '', '', 'yaml', '', '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (101, 'application-common.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '通用配置基础配置', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (102, 'datasource.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '数据源配置', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (103, 'yumeng-gateway.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '网关模块', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (104, 'yumeng-auth.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '认证中心', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (105, 'yumeng-monitor.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '监控中心', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (106, 'yumeng-system.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '系统模块', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (107, 'yumeng-gen.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '代码生成', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (108, 'yumeng-job.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '定时任务', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (109, 'yumeng-resource.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '文件服务', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (110, 'yumeng-workflow.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '工作流服务', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (111, 'sentinel-yumeng-gateway.json', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:23:00', '2022-01-09 15:23:00', NULL, '0:0:0:0:0:0:0:1', '', 'prod', '限流策略', NULL, NULL, 'json', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (112, 'seata-server.properties', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:21:02', '2022-01-09 15:21:02', NULL, '0:0:0:0:0:0:0:1', '', 'prod', 'seata配置文件', NULL, NULL, 'properties', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (113, 'yumeng-sentinel-dashboard.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:21:02', '2022-01-09 15:21:02', NULL, '0:0:0:0:0:0:0:1', '', 'prod', 'sentinel控制台配置文件', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (114, 'yumeng-snailjob-server.yml', 'DEFAULT_GROUP', '# 将项目路径：config/下对应文件中内容复制到此处', '2944a25cb97926efcaa43b3ad7a64cf0', '2022-01-09 15:21:02', '2022-01-09 15:21:02', NULL, '0:0:0:0:0:0:0:1', '', 'prod', 'SJ定时任务控制台', NULL, NULL, 'yaml', NULL, '');
INSERT INTO `config_info` (`id`, `data_id`, `group_id`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `app_name`, `tenant_id`, `c_desc`, `c_use`, `effect`, `type`, `c_schema`, `encrypted_data_key`) VALUES (115, 'yumeng-salary-settlement.yml', 'DEFAULT_GROUP', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.salary.url}\n          username: ${datasource.salary.username}\n          password: ${datasource.salary.password}', 'ddda19b5166f143e244f28b5d581c58d', '2025-04-12 08:25:45', '2025-04-12 09:27:13', 'nacos', '************', '', 'dev', '工资结算服务配置文件', '', '', 'yaml', '', '');
COMMIT;

-- ----------------------------
-- Table structure for config_info_aggr
-- ----------------------------
DROP TABLE IF EXISTS `config_info_aggr`;
CREATE TABLE `config_info_aggr` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `datum_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'datum_id',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '内容',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfoaggr_datagrouptenantdatum` (`data_id`,`group_id`,`tenant_id`,`datum_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='增加租户字段';

-- ----------------------------
-- Records of config_info_aggr
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for config_info_beta
-- ----------------------------
DROP TABLE IF EXISTS `config_info_beta`;
CREATE TABLE `config_info_beta` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `beta_ips` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'betaIps',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '密钥',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfobeta_datagrouptenant` (`data_id`,`group_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='config_info_beta';

-- ----------------------------
-- Records of config_info_beta
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for config_info_tag
-- ----------------------------
DROP TABLE IF EXISTS `config_info_tag`;
CREATE TABLE `config_info_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_id',
  `tag_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_configinfotag_datagrouptenanttag` (`data_id`,`group_id`,`tenant_id`,`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='config_info_tag';

-- ----------------------------
-- Records of config_info_tag
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for config_tags_relation
-- ----------------------------
DROP TABLE IF EXISTS `config_tags_relation`;
CREATE TABLE `config_tags_relation` (
  `id` bigint NOT NULL COMMENT 'id',
  `tag_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'tag_name',
  `tag_type` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'tag_type',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_id',
  `nid` bigint NOT NULL AUTO_INCREMENT COMMENT 'nid, 自增长标识',
  PRIMARY KEY (`nid`),
  UNIQUE KEY `uk_configtagrelation_configidtag` (`id`,`tag_name`,`tag_type`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='config_tag_relation';

-- ----------------------------
-- Records of config_tags_relation
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for group_capacity
-- ----------------------------
DROP TABLE IF EXISTS `group_capacity`;
CREATE TABLE `group_capacity` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Group ID，空字符表示整个集群',
  `quota` int unsigned NOT NULL DEFAULT '0' COMMENT '配额，0表示使用默认值',
  `usage` int unsigned NOT NULL DEFAULT '0' COMMENT '使用量',
  `max_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int unsigned NOT NULL DEFAULT '0' COMMENT '聚合子配置最大个数，，0表示使用默认值',
  `max_aggr_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int unsigned NOT NULL DEFAULT '0' COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='集群、各Group容量信息表';

-- ----------------------------
-- Records of group_capacity
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for his_config_info
-- ----------------------------
DROP TABLE IF EXISTS `his_config_info`;
CREATE TABLE `his_config_info` (
  `id` bigint unsigned NOT NULL COMMENT 'id',
  `nid` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'nid, 自增标识',
  `data_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'data_id',
  `group_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'group_id',
  `app_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'app_name',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'content',
  `md5` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'md5',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `src_user` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'source user',
  `src_ip` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'source ip',
  `op_type` char(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'operation type',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT '租户字段',
  `encrypted_data_key` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '密钥',
  PRIMARY KEY (`nid`),
  KEY `idx_gmt_create` (`gmt_create`),
  KEY `idx_gmt_modified` (`gmt_modified`),
  KEY `idx_did` (`data_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='多租户改造';

-- ----------------------------
-- Records of his_config_info
-- ----------------------------
BEGIN;
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (1, 1, 'application-common.yml', 'DEFAULT_GROUP', '', 'server:\n  # undertow 配置\n  undertow:\n    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的\n    max-http-post-size: -1\n    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理\n    # 每块buffer的空间大小,越小的空间被利用越充分\n    buffer-size: 512\n    # 是否分配的直接内存\n    direct-buffers: true\n    threads:\n      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程\n      io: 8\n      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载\n      worker: 256\n\ndubbo:\n  application:\n    # 关闭qos端口避免单机多生产者端口冲突 如需使用自行开启\n    qos-enable: false\n  protocol:\n    # 如需使用 Triple 3.0 新协议 可查看官方文档\n    # 使用 dubbo 协议通信\n    name: dubbo\n    # dubbo 协议端口(-1表示自增端口,从20880开始)\n    port: -1\n    # 指定dubbo协议注册ip\n    # host: *************\n  # 消费者相关配置\n  consumer:\n    # 超时时间\n    timeout: 3000\n  # 自定义配置\n  custom:\n    # 全局请求log\n    request-log: true\n    # info 基础信息 param 参数信息 full 全部\n    log-level: info\n\nspring:\n  threads:\n    # 开启虚拟线程 仅jdk21可用\n    virtual:\n      enabled: false\n  # 资源信息\n  messages:\n    # 国际化资源文件路径\n    basename: i18n/messages\n  servlet:\n    multipart:\n      # 整个请求大小限制\n      max-request-size: 20MB\n      # 上传单个文件大小限制\n      max-file-size: 10MB\n  mvc:\n    # 设置静态资源路径 防止所有请求都去查静态资源\n    static-path-pattern: /static/**\n    format:\n      date-time: yyyy-MM-dd HH:mm:ss\n  #jackson配置\n  jackson:\n    # 日期格式化\n    date-format: yyyy-MM-dd HH:mm:ss\n    serialization:\n      # 格式化输出\n      INDENT_OUTPUT: false\n      # 忽略无法转换的对象\n      fail_on_empty_beans: false\n    deserialization:\n      # 允许对象忽略json中不存在的属性\n      fail_on_unknown_properties: false\n  cloud:\n    nacos:\n      discovery:\n        metadata:\n          # admin 监控账号密码\n          username: yumeng\n          userpassword: ym@18513311526\n    # sentinel 配置\n    sentinel:\n      # sentinel 开关\n      enabled: true\n      transport:\n        # dashboard控制台服务名 用于服务发现\n        # 如无此配置将默认使用下方 dashboard 配置直接注册\n        server-name: yumeng-sentinel-dashboard\n        # 客户端指定注册的ip 用于多网卡ip不稳点使用\n        # client-ip:\n        # 控制台地址 从1.3.0开始使用 server-name 注册\n        # dashboard: localhost:8718\n\n    bus:\n      id: ${spring.application.name}\n      base-packages: com.ym.**.event\n  # 消息总线 也可以使用 kafka 参考 spring-cloud-bus 用法\n  rabbitmq:\n    host: localhost\n    port: 5672\n    username: yumeng\n    password: ym@18513311526\n\n  # redis通用配置 子服务可以自行配置进行覆盖\n  data:\n    redis:\n      host: 127.0.0.1\n      port: 6379\n      # redis 密码必须配置\n      password: libarayredis\n      database: 0\n      # 需要使用数字\n      timeout: 10000\n      ssl.enabled: false\n\n# redisson 配置\nredisson:\n  # redis key前缀\n  keyPrefix:\n  # 线程池数量\n  threads: 4\n  # Netty线程池数量\n  nettyThreads: 8\n  # 单节点配置\n  singleServerConfig:\n    # 客户端名称\n    clientName: ${spring.application.name}\n    # 最小空闲连接数\n    connectionMinimumIdleSize: 8\n    # 连接池大小\n    connectionPoolSize: 32\n    # 连接空闲超时，单位：毫秒\n    idleConnectionTimeout: 10000\n    # 命令等待超时，单位：毫秒\n    timeout: 3000\n    # 发布和订阅连接池大小\n    subscriptionConnectionPoolSize: 50\n\n# 分布式锁 lock4j 全局配置\nlock4j:\n  # 获取分布式锁超时时间，默认为 3000 毫秒\n  acquire-timeout: 3000\n  # 分布式锁的超时时间，默认为 30 秒\n  expire: 30000\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n  endpoint:\n    health:\n      show-details: ALWAYS\n    logfile:\n      external-file: ./logs/${spring.application.name}/console.log\n\n# 日志配置\nlogging:\n  level:\n    org.springframework: warn\n    org.apache.dubbo: warn\n    com.alibaba.nacos: warn\n    com.alibaba.cloud.sentinel: warn\n    org.mybatis.spring.mapper: error\n    org.apache.dubbo.config: error\n    # 临时处理 spring 调整日志级别导致启动警告问题 不影响使用等待 alibaba 适配\n    org.springframework.context.support.PostProcessorRegistrationDelegate: error\n  config: classpath:logback-plus.xml\n\n# Sa-Token配置\nsa-token:\n  # token名称 (同时也是cookie名称)\n  token-name: Authorization\n  # 开启内网服务调用鉴权(不允许越过gateway访问内网服务 保障服务安全)\n  check-same-token: true\n  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)\n  is-concurrent: true\n  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)\n  is-share: false\n  # jwt秘钥\n  jwt-secret-key: abcdefghijklmnopqrstuvwxyz\n\n# MyBatisPlus配置\n# https://baomidou.com/config/\nmybatis-plus:\n  # 多包名使用 例如 org.dromara.**.mapper,org.xxx.**.mapper\n  mapperPackage: com.ym.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ym.**.domain\n  global-config:\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      # 如需改为自增 需要将数据库表全部设置为自增\n      idType: ASSIGN_ID\n\n# 数据加密\nmybatis-encryptor:\n  # 是否开启加密\n  enable: false\n  # 默认加密算法\n  algorithm: BASE64\n  # 编码方式 BASE64/HEX。默认BASE64\n  encode: BASE64\n  # 安全秘钥 对称算法的秘钥 如：AES，SM4\n  password:\n  # 公私钥 非对称算法的公私钥 如：SM2，RSA\n  publicKey:\n  privateKey:\n\n# api接口加密\napi-decrypt:\n  # 是否开启全局接口加密\n  enabled: true\n  # AES 加密头标识\n  headerFlag: encrypt-key\n  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=\n  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJnNwrj4hi/y3CCJu868ghCG5dUj8wZK++RNlTLcXoMmdZWEQ/u02RgD5LyLAXGjLOjbMtC+/J9qofpSGTKSx/MCAwEAAQ==\n  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==\n  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=\n\n# 防止XSS攻击\nxss:\n  enabled: true\n  excludeUrls:\n    - /system/notice\n    - /workflow/model/save\n    - /workflow/model/editModelXml\n\n# 接口文档配置\nspringdoc:\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  #  swagger-ui:\n  #    # 持久化认证数据\n  #    persistAuthorization: true\n  info:\n    # 标题\n    title: \'标题：育蒙微服务权限管理系统_接口文档\'\n    # 描述\n    description: \'描述：育蒙微服务权限管理系统\'\n    # 版本\n    version: 1.0.0\n    # 作者信息\n    contact:\n      name: Luo Ming\n      email: <EMAIL>\n      url: http://************/luoming/yumeng-cloud-api.git\n  components:\n    # 鉴权方式配置\n    security-schemes:\n      apiKey:\n        type: APIKEY\n        in: HEADER\n        name: ${sa-token.token-name}\n\n# seata配置\nseata:\n  # 是否启用\n  enabled: true\n  # Seata 应用编号，默认为应用名\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n\n# 多租户配置\ntenant:\n  # 是否开启\n  enable: true\n  # 排除表\n  excludes:\n    - sys_menu\n    - sys_tenant\n    - sys_tenant_package\n    - sys_role_dept\n    - sys_role_menu\n    - sys_user_post\n    - sys_user_role\n    - sys_client\n    - sys_oss_config\n', '94fb11e6c93baa1a2da892468efb5b8e', '2025-04-02 21:15:28', '2025-04-02 13:15:28', 'nacos', '0:0:0:0:0:0:0:1', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (1, 2, 'application-common.yml', 'DEFAULT_GROUP', '', 'server:\n  # undertow 配置\n  undertow:\n    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的\n    max-http-post-size: -1\n    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理\n    # 每块buffer的空间大小,越小的空间被利用越充分\n    buffer-size: 512\n    # 是否分配的直接内存\n    direct-buffers: true\n    threads:\n      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程\n      io: 8\n      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载\n      worker: 256\n\ndubbo:\n  application:\n    # 关闭qos端口避免单机多生产者端口冲突 如需使用自行开启\n    qos-enable: false\n  protocol:\n    # 如需使用 Triple 3.0 新协议 可查看官方文档\n    # 使用 dubbo 协议通信\n    name: dubbo\n    # dubbo 协议端口(-1表示自增端口,从20880开始)\n    port: -1\n    # 指定dubbo协议注册ip\n    # host: *************\n  # 消费者相关配置\n  consumer:\n    # 超时时间\n    timeout: 3000\n  # 自定义配置\n  custom:\n    # 全局请求log\n    request-log: true\n    # info 基础信息 param 参数信息 full 全部\n    log-level: info\n\nspring:\n  threads:\n    # 开启虚拟线程 仅jdk21可用\n    virtual:\n      enabled: false\n  # 资源信息\n  messages:\n    # 国际化资源文件路径\n    basename: i18n/messages\n  servlet:\n    multipart:\n      # 整个请求大小限制\n      max-request-size: 20MB\n      # 上传单个文件大小限制\n      max-file-size: 10MB\n  mvc:\n    # 设置静态资源路径 防止所有请求都去查静态资源\n    static-path-pattern: /static/**\n    format:\n      date-time: yyyy-MM-dd HH:mm:ss\n  #jackson配置\n  jackson:\n    # 日期格式化\n    date-format: yyyy-MM-dd HH:mm:ss\n    serialization:\n      # 格式化输出\n      INDENT_OUTPUT: false\n      # 忽略无法转换的对象\n      fail_on_empty_beans: false\n    deserialization:\n      # 允许对象忽略json中不存在的属性\n      fail_on_unknown_properties: false\n  cloud:\n    nacos:\n      discovery:\n        metadata:\n          # admin 监控账号密码\n          username: yumeng\n          userpassword: ym@18513311526\n    # sentinel 配置\n    sentinel:\n      # sentinel 开关\n      enabled: true\n      transport:\n        # dashboard控制台服务名 用于服务发现\n        # 如无此配置将默认使用下方 dashboard 配置直接注册\n        server-name: yumeng-sentinel-dashboard\n        # 客户端指定注册的ip 用于多网卡ip不稳点使用\n        # client-ip:\n        # 控制台地址 从1.3.0开始使用 server-name 注册\n        # dashboard: localhost:8718\n\n    bus:\n      id: ${spring.application.name}\n      base-packages: com.ym.**.event\n  # 消息总线 也可以使用 kafka 参考 spring-cloud-bus 用法\n  rabbitmq:\n    host: localhost\n    port: 5672\n    username: yumeng\n    password: ym@18513311526\n\n  # redis通用配置 子服务可以自行配置进行覆盖\n  data:\n    redis:\n      host: 127.0.0.1\n      port: 6379\n      # redis 密码必须配置\n      password: libarayredis\n      database: 0\n      # 需要使用数字\n      timeout: 10000\n      ssl.enabled: false\n\n# redisson 配置\nredisson:\n  # redis key前缀\n  keyPrefix:\n  # 线程池数量\n  threads: 4\n  # Netty线程池数量\n  nettyThreads: 8\n  # 单节点配置\n  singleServerConfig:\n    # 客户端名称\n    clientName: ${spring.application.name}\n    # 最小空闲连接数\n    connectionMinimumIdleSize: 8\n    # 连接池大小\n    connectionPoolSize: 32\n    # 连接空闲超时，单位：毫秒\n    idleConnectionTimeout: 10000\n    # 命令等待超时，单位：毫秒\n    timeout: 3000\n    # 发布和订阅连接池大小\n    subscriptionConnectionPoolSize: 50\n\n# 分布式锁 lock4j 全局配置\nlock4j:\n  # 获取分布式锁超时时间，默认为 3000 毫秒\n  acquire-timeout: 3000\n  # 分布式锁的超时时间，默认为 30 秒\n  expire: 30000\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n  endpoint:\n    health:\n      show-details: ALWAYS\n    logfile:\n      external-file: ./logs/${spring.application.name}/console.log\n\n# 日志配置\nlogging:\n  level:\n    org.springframework: warn\n    org.apache.dubbo: warn\n    com.alibaba.nacos: warn\n    com.alibaba.cloud.sentinel: warn\n    org.mybatis.spring.mapper: error\n    org.apache.dubbo.config: error\n    # 临时处理 spring 调整日志级别导致启动警告问题 不影响使用等待 alibaba 适配\n    org.springframework.context.support.PostProcessorRegistrationDelegate: error\n  config: classpath:logback-plus.xml\n\n# Sa-Token配置\nsa-token:\n  # token名称 (同时也是cookie名称)\n  token-name: Authorization\n  # 开启内网服务调用鉴权(不允许越过gateway访问内网服务 保障服务安全)\n  check-same-token: true\n  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)\n  is-concurrent: true\n  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)\n  is-share: false\n  # jwt秘钥\n  jwt-secret-key: abcdefghijklmnopqrstuvwxyz\n\n# MyBatisPlus配置\n# https://baomidou.com/config/\nmybatis-plus:\n  # 多包名使用 例如 org.dromara.**.mapper,org.xxx.**.mapper\n  mapperPackage: com.ym.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ym.**.domain\n  global-config:\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      # 如需改为自增 需要将数据库表全部设置为自增\n      idType: ASSIGN_ID\n\n# 数据加密\nmybatis-encryptor:\n  # 是否开启加密\n  enable: false\n  # 默认加密算法\n  algorithm: BASE64\n  # 编码方式 BASE64/HEX。默认BASE64\n  encode: BASE64\n  # 安全秘钥 对称算法的秘钥 如：AES，SM4\n  password:\n  # 公私钥 非对称算法的公私钥 如：SM2，RSA\n  publicKey:\n  privateKey:\n\n# api接口加密\napi-decrypt:\n  # 是否开启全局接口加密\n  enabled: true\n  # AES 加密头标识\n  headerFlag: encrypt-key\n  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=\n  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJnNwrj4hi/y3CCJu868ghCG5dUj8wZK++RNlTLcXoMmdZWEQ/u02RgD5LyLAXGjLOjbMtC+/J9qofpSGTKSx/MCAwEAAQ==\n  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==\n  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=\n\n# 防止XSS攻击\nxss:\n  enabled: true\n  excludeUrls:\n    - /system/notice\n    - /workflow/model/save\n    - /workflow/model/editModelXml\n\n# 接口文档配置\nspringdoc:\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  #  swagger-ui:\n  #    # 持久化认证数据\n  #    persistAuthorization: true\n  info:\n    # 标题\n    title: \'标题：育蒙微服务权限管理系统_接口文档\'\n    # 描述\n    description: \'描述：育蒙微服务权限管理系统\'\n    # 版本\n    version: 1.0.0\n    # 作者信息\n    contact:\n      name: Luo Ming\n      email: <EMAIL>\n      url: http://************/luoming/yumeng-cloud-api.git\n  components:\n    # 鉴权方式配置\n    security-schemes:\n      apiKey:\n        type: APIKEY\n        in: HEADER\n        name: ${sa-token.token-name}\n\n# seata配置\nseata:\n  # 是否启用\n  enabled: true\n  # Seata 应用编号，默认为应用名\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n\n# 多租户配置\ntenant:\n  # 是否开启\n  enable: false\n  # 排除表\n  excludes:\n    - sys_menu\n    - sys_tenant\n    - sys_tenant_package\n    - sys_role_dept\n    - sys_role_menu\n    - sys_user_post\n    - sys_user_role\n    - sys_client\n    - sys_oss_config\n', '5439c681dfed015650b22d9ad0be9f84', '2025-04-06 22:06:32', '2025-04-06 14:06:33', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (2, 3, 'datasource.yml', 'DEFAULT_GROUP', '', 'datasource:\n  system-master:\n    # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562\n    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能\n    url: ******************************************************************************************************************************************************************************************************************    username: root\n    password: Lm19941117@\n  gen:\n    url: ******************************************************************************************************************************************************************************************************************    username: root\n    password: Lm19941117@\n  job:\n    url: ****************************************************************************************************************************************************************************************************************    username: root\n    password: Lm19941117@\n  workflow:\n    url: **************************************************************************************************************************************************************************************************************************************************    username: root\n    password: Lm19941117@\n#  system-oracle:\n#    url: ***************************************#    username: ROOT\n#    password: password\n#  system-postgres:\n#    url: ********************************************************************************************************************************************#    username: root\n#    password: password\n\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content\n    dynamic:\n      # 性能分析插件(有性能损耗 不建议生产环境使用)\n      p6spy: true\n      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n      seata: ${seata.enabled}\n      # 严格模式 匹配不到数据源则报错\n      strict: true\n      hikari:\n        # 最大连接池数量\n        maxPoolSize: 20\n        # 最小空闲线程数量\n        minIdle: 10\n        # 配置获取连接等待超时的时间\n        connectionTimeout: 30000\n        # 校验超时时间\n        validationTimeout: 5000\n        # 空闲连接存活最大时间，默认10分钟\n        idleTimeout: 600000\n        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟\n        maxLifetime: 1800000\n        # 多久检查一次连接的活性\n        keepaliveTime: 30000\n', '87492315b755ebbab209ce81b7f721cf', '2025-04-06 22:07:34', '2025-04-06 14:07:34', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (12, 4, 'seata-server.properties', 'DEFAULT_GROUP', '', 'service.vgroupMapping.yumeng-auth-group=default\nservice.vgroupMapping.yumeng-system-group=default\nservice.vgroupMapping.yumeng-resource-group=default\nservice.vgroupMapping.yumeng-workflow-group=default\n\nservice.enableDegrade=false\nservice.disableGlobalTransaction=false\n\n#Transaction storage configuration, only for the server. The file, DB, and redis configuration values are optional.\nstore.mode=db\nstore.lock.mode=db\nstore.session.mode=db\n#Used for password encryption\n#store.publicKey=\n\n#These configurations are required if the `store mode` is `db`. If `store.mode,store.lock.mode,store.session.mode` are not equal to `db`, you can remove the configuration block.\nstore.db.datasource=hikari\nstore.db.dbType=mysql\nstore.db.driverClassName=com.mysql.cj.jdbc.Driver\nstore.db.url=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************# redis 模式 store.mode=redis 开启 (控制台查询功能有限,不影响实际执行功能)\n# store.redis.host=127.0.0.1\n# store.redis.port=6379\n# 最大连接数\n# store.redis.maxConn=10\n# 最小连接数\n# store.redis.minConn=1\n# store.redis.database=0\n# store.redis.password=\n# store.redis.queryLimit=100\n\n#Transaction rule configuration, only for the server\nserver.recovery.committingRetryPeriod=1000\nserver.recovery.asynCommittingRetryPeriod=1000\nserver.recovery.rollbackingRetryPeriod=1000\nserver.recovery.timeoutRetryPeriod=1000\nserver.maxCommitRetryTimeout=-1\nserver.maxRollbackRetryTimeout=-1\nserver.rollbackRetryTimeoutUnlockEnable=false\nserver.distributedLockExpireTime=10000\nserver.xaerNotaRetryTimeout=60000\nserver.session.branchAsyncQueueSize=5000\nserver.session.enableBranchAsyncRemove=false\n\n#Transaction rule configuration, only for the client\nclient.rm.asyncCommitBufferLimit=10000\nclient.rm.lock.retryInterval=10\nclient.rm.lock.retryTimes=30\nclient.rm.lock.retryPolicyBranchRollbackOnConflict=true\nclient.rm.reportRetryCount=5\nclient.rm.tableMetaCheckEnable=true\nclient.rm.tableMetaCheckerInterval=60000\nclient.rm.sqlParserType=druid\nclient.rm.reportSuccessEnable=false\nclient.rm.sagaBranchRegisterEnable=false\nclient.rm.sagaJsonParser=fastjson\nclient.rm.tccActionInterceptorOrder=-2147482648\nclient.tm.commitRetryCount=5\nclient.tm.rollbackRetryCount=5\nclient.tm.defaultGlobalTransactionTimeout=60000\nclient.tm.degradeCheck=false\nclient.tm.degradeCheckAllowTimes=10\nclient.tm.degradeCheckPeriod=2000\nclient.tm.interceptorOrder=-2147482648\nclient.undo.dataValidation=true\nclient.undo.logSerialization=jackson\nclient.undo.onlyCareUpdateColumns=true\nserver.undo.logSaveDays=7\nserver.undo.logDeletePeriod=86400000\nclient.undo.logTable=undo_log\nclient.undo.compress.enable=true\nclient.undo.compress.type=zip\nclient.undo.compress.threshold=64k\n\n#For TCC transaction mode\ntcc.fence.logTableName=tcc_fence_log\ntcc.fence.cleanPeriod=1h\n\n#Log rule configuration, for client and server\nlog.exceptionRate=100\n\n#Metrics configuration, only for the server\nmetrics.enabled=false\nmetrics.registryType=compact\nmetrics.exporterList=prometheus\nmetrics.exporterPrometheusPort=9898\n\n#For details about configuration items, see https://seata.io/zh-cn/docs/user/configurations.html\n#Transport configuration, for client and server\ntransport.type=TCP\ntransport.server=NIO\ntransport.heartbeat=true\ntransport.enableTmClientBatchSendRequest=false\ntransport.enableRmClientBatchSendRequest=true\ntransport.enableTcServerBatchSendResponse=false\ntransport.rpcRmRequestTimeout=30000\ntransport.rpcTmRequestTimeout=30000\ntransport.rpcTcRequestTimeout=30000\ntransport.threadFactory.bossThreadPrefix=NettyBoss\ntransport.threadFactory.workerThreadPrefix=NettyServerNIOWorker\ntransport.threadFactory.serverExecutorThreadPrefix=NettyServerBizHandler\ntransport.threadFactory.shareBossWorker=false\ntransport.threadFactory.clientSelectorThreadPrefix=NettyClientSelector\ntransport.threadFactory.clientSelectorThreadSize=1\ntransport.threadFactory.clientWorkerThreadPrefix=NettyClientWorkerThread\ntransport.threadFactory.bossThreadSize=1\ntransport.threadFactory.workerThreadSize=default\ntransport.shutdown.wait=3\ntransport.serialization=seata\ntransport.compressor=none\n', '9a6ed721fe3196ac8e81af4ef9395030', '2025-04-06 22:20:41', '2025-04-06 14:20:41', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (12, 5, 'seata-server.properties', 'DEFAULT_GROUP', '', 'service.vgroupMapping.yumeng-auth-group=default\nservice.vgroupMapping.yumeng-system-group=default\nservice.vgroupMapping.yumeng-resource-group=default\nservice.vgroupMapping.yumeng-workflow-group=default\n\nservice.enableDegrade=false\nservice.disableGlobalTransaction=false\n\n#Transaction storage configuration, only for the server. The file, DB, and redis configuration values are optional.\nstore.mode=db\nstore.lock.mode=db\nstore.session.mode=db\n#Used for password encryption\n#store.publicKey=\n\n#These configurations are required if the `store mode` is `db`. If `store.mode,store.lock.mode,store.session.mode` are not equal to `db`, you can remove the configuration block.\nstore.db.datasource=hikari\nstore.db.dbType=mysql\nstore.db.driverClassName=com.mysql.cj.jdbc.Driver\nstore.db.url=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************# redis 模式 store.mode=redis 开启 (控制台查询功能有限,不影响实际执行功能)\n# store.redis.host=127.0.0.1\n# store.redis.port=6379\n# 最大连接数\n# store.redis.maxConn=10\n# 最小连接数\n# store.redis.minConn=1\n# store.redis.database=0\n# store.redis.password=\n# store.redis.queryLimit=100\n\n#Transaction rule configuration, only for the server\nserver.recovery.committingRetryPeriod=1000\nserver.recovery.asynCommittingRetryPeriod=1000\nserver.recovery.rollbackingRetryPeriod=1000\nserver.recovery.timeoutRetryPeriod=1000\nserver.maxCommitRetryTimeout=-1\nserver.maxRollbackRetryTimeout=-1\nserver.rollbackRetryTimeoutUnlockEnable=false\nserver.distributedLockExpireTime=10000\nserver.xaerNotaRetryTimeout=60000\nserver.session.branchAsyncQueueSize=5000\nserver.session.enableBranchAsyncRemove=false\n\n#Transaction rule configuration, only for the client\nclient.rm.asyncCommitBufferLimit=10000\nclient.rm.lock.retryInterval=10\nclient.rm.lock.retryTimes=30\nclient.rm.lock.retryPolicyBranchRollbackOnConflict=true\nclient.rm.reportRetryCount=5\nclient.rm.tableMetaCheckEnable=true\nclient.rm.tableMetaCheckerInterval=60000\nclient.rm.sqlParserType=druid\nclient.rm.reportSuccessEnable=false\nclient.rm.sagaBranchRegisterEnable=false\nclient.rm.sagaJsonParser=fastjson\nclient.rm.tccActionInterceptorOrder=-2147482648\nclient.tm.commitRetryCount=5\nclient.tm.rollbackRetryCount=5\nclient.tm.defaultGlobalTransactionTimeout=60000\nclient.tm.degradeCheck=false\nclient.tm.degradeCheckAllowTimes=10\nclient.tm.degradeCheckPeriod=2000\nclient.tm.interceptorOrder=-2147482648\nclient.undo.dataValidation=true\nclient.undo.logSerialization=jackson\nclient.undo.onlyCareUpdateColumns=true\nserver.undo.logSaveDays=7\nserver.undo.logDeletePeriod=86400000\nclient.undo.logTable=undo_log\nclient.undo.compress.enable=true\nclient.undo.compress.type=zip\nclient.undo.compress.threshold=64k\n\n#For TCC transaction mode\ntcc.fence.logTableName=tcc_fence_log\ntcc.fence.cleanPeriod=1h\n\n#Log rule configuration, for client and server\nlog.exceptionRate=100\n\n#Metrics configuration, only for the server\nmetrics.enabled=false\nmetrics.registryType=compact\nmetrics.exporterList=prometheus\nmetrics.exporterPrometheusPort=9898\n\n#For details about configuration items, see https://seata.io/zh-cn/docs/user/configurations.html\n#Transport configuration, for client and server\ntransport.type=TCP\ntransport.server=NIO\ntransport.heartbeat=true\ntransport.enableTmClientBatchSendRequest=false\ntransport.enableRmClientBatchSendRequest=true\ntransport.enableTcServerBatchSendResponse=false\ntransport.rpcRmRequestTimeout=30000\ntransport.rpcTmRequestTimeout=30000\ntransport.rpcTcRequestTimeout=30000\ntransport.threadFactory.bossThreadPrefix=NettyBoss\ntransport.threadFactory.workerThreadPrefix=NettyServerNIOWorker\ntransport.threadFactory.serverExecutorThreadPrefix=NettyServerBizHandler\ntransport.threadFactory.shareBossWorker=false\ntransport.threadFactory.clientSelectorThreadPrefix=NettyClientSelector\ntransport.threadFactory.clientSelectorThreadSize=1\ntransport.threadFactory.clientWorkerThreadPrefix=NettyClientWorkerThread\ntransport.threadFactory.bossThreadSize=1\ntransport.threadFactory.workerThreadSize=default\ntransport.shutdown.wait=3\ntransport.serialization=seata\ntransport.compressor=none\n', '3248562049da7f934af178ba4f70997f', '2025-04-06 22:22:54', '2025-04-06 14:22:55', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (2, 6, 'datasource.yml', 'DEFAULT_GROUP', '', 'datasource:\n  system-master:\n    # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562\n    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能\n    url: ******************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  gen:\n    url: ******************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  job:\n    url: ****************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  workflow:\n    url: **************************************************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n#  system-oracle:\n#    url: ***************************************#    username: ROOT\n#    password: password\n#  system-postgres:\n#    url: ********************************************************************************************************************************************#    username: root\n#    password: password\n\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content\n    dynamic:\n      # 性能分析插件(有性能损耗 不建议生产环境使用)\n      p6spy: true\n      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n      seata: ${seata.enabled}\n      # 严格模式 匹配不到数据源则报错\n      strict: true\n      hikari:\n        # 最大连接池数量\n        maxPoolSize: 20\n        # 最小空闲线程数量\n        minIdle: 10\n        # 配置获取连接等待超时的时间\n        connectionTimeout: 30000\n        # 校验超时时间\n        validationTimeout: 5000\n        # 空闲连接存活最大时间，默认10分钟\n        idleTimeout: 600000\n        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟\n        maxLifetime: 1800000\n        # 多久检查一次连接的活性\n        keepaliveTime: 30000\n', 'bca50572efac94ec0e4f7603485a6528', '2025-04-06 22:36:27', '2025-04-06 14:36:27', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (1, 7, 'application-common.yml', 'DEFAULT_GROUP', '', 'server:\n  # undertow 配置\n  undertow:\n    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的\n    max-http-post-size: -1\n    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理\n    # 每块buffer的空间大小,越小的空间被利用越充分\n    buffer-size: 512\n    # 是否分配的直接内存\n    direct-buffers: true\n    threads:\n      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程\n      io: 8\n      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载\n      worker: 256\n\ndubbo:\n  application:\n    # 关闭qos端口避免单机多生产者端口冲突 如需使用自行开启\n    qos-enable: false\n  protocol:\n    # 如需使用 Triple 3.0 新协议 可查看官方文档\n    # 使用 dubbo 协议通信\n    name: dubbo\n    # dubbo 协议端口(-1表示自增端口,从20880开始)\n    port: -1\n    # 指定dubbo协议注册ip\n    # host: *************\n  # 消费者相关配置\n  consumer:\n    # 超时时间\n    timeout: 3000\n  # 自定义配置\n  custom:\n    # 全局请求log\n    request-log: true\n    # info 基础信息 param 参数信息 full 全部\n    log-level: info\n\nspring:\n  threads:\n    # 开启虚拟线程 仅jdk21可用\n    virtual:\n      enabled: false\n  # 资源信息\n  messages:\n    # 国际化资源文件路径\n    basename: i18n/messages\n  servlet:\n    multipart:\n      # 整个请求大小限制\n      max-request-size: 20MB\n      # 上传单个文件大小限制\n      max-file-size: 10MB\n  mvc:\n    # 设置静态资源路径 防止所有请求都去查静态资源\n    static-path-pattern: /static/**\n    format:\n      date-time: yyyy-MM-dd HH:mm:ss\n  #jackson配置\n  jackson:\n    # 日期格式化\n    date-format: yyyy-MM-dd HH:mm:ss\n    serialization:\n      # 格式化输出\n      INDENT_OUTPUT: false\n      # 忽略无法转换的对象\n      fail_on_empty_beans: false\n    deserialization:\n      # 允许对象忽略json中不存在的属性\n      fail_on_unknown_properties: false\n  cloud:\n    nacos:\n      discovery:\n        metadata:\n          # admin 监控账号密码\n          username: yumeng\n          userpassword: yumeng@2025!\n    # sentinel 配置\n    sentinel:\n      # sentinel 开关\n      enabled: true\n      transport:\n        # dashboard控制台服务名 用于服务发现\n        # 如无此配置将默认使用下方 dashboard 配置直接注册\n        server-name: yumeng-sentinel-dashboard\n        # 客户端指定注册的ip 用于多网卡ip不稳点使用\n        # client-ip:\n        # 控制台地址 从1.3.0开始使用 server-name 注册\n        # dashboard: localhost:8718\n\n    bus:\n      id: ${spring.application.name}\n      base-packages: com.ym.**.event\n  # 消息总线 也可以使用 kafka 参考 spring-cloud-bus 用法\n  rabbitmq:\n    host: localhost\n    port: 5672\n    username: yumeng\n    password: yumeng@2025!\n\n  # redis通用配置 子服务可以自行配置进行覆盖\n  data:\n    redis:\n      host: 127.0.0.1\n      port: 6379\n      # redis 密码必须配置\n      password: yumeng@2025!\n      database: 0\n      # 需要使用数字\n      timeout: 10000\n      ssl.enabled: false\n\n# redisson 配置\nredisson:\n  # redis key前缀\n  keyPrefix:\n  # 线程池数量\n  threads: 4\n  # Netty线程池数量\n  nettyThreads: 8\n  # 单节点配置\n  singleServerConfig:\n    # 客户端名称\n    clientName: ${spring.application.name}\n    # 最小空闲连接数\n    connectionMinimumIdleSize: 8\n    # 连接池大小\n    connectionPoolSize: 32\n    # 连接空闲超时，单位：毫秒\n    idleConnectionTimeout: 10000\n    # 命令等待超时，单位：毫秒\n    timeout: 3000\n    # 发布和订阅连接池大小\n    subscriptionConnectionPoolSize: 50\n\n# 分布式锁 lock4j 全局配置\nlock4j:\n  # 获取分布式锁超时时间，默认为 3000 毫秒\n  acquire-timeout: 3000\n  # 分布式锁的超时时间，默认为 30 秒\n  expire: 30000\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n  endpoint:\n    health:\n      show-details: ALWAYS\n    logfile:\n      external-file: ./logs/${spring.application.name}/console.log\n\n# 日志配置\nlogging:\n  level:\n    org.springframework: warn\n    org.apache.dubbo: warn\n    com.alibaba.nacos: warn\n    com.alibaba.cloud.sentinel: warn\n    org.mybatis.spring.mapper: error\n    org.apache.dubbo.config: error\n    # 临时处理 spring 调整日志级别导致启动警告问题 不影响使用等待 alibaba 适配\n    org.springframework.context.support.PostProcessorRegistrationDelegate: error\n  config: classpath:logback-plus.xml\n\n# Sa-Token配置\nsa-token:\n  # token名称 (同时也是cookie名称)\n  token-name: Authorization\n  # 开启内网服务调用鉴权(不允许越过gateway访问内网服务 保障服务安全)\n  check-same-token: true\n  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)\n  is-concurrent: true\n  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)\n  is-share: false\n  # jwt秘钥\n  jwt-secret-key: abcdefghijklmnopqrstuvwxyz\n\n# MyBatisPlus配置\n# https://baomidou.com/config/\nmybatis-plus:\n  # 多包名使用 例如 org.dromara.**.mapper,org.xxx.**.mapper\n  mapperPackage: com.ym.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ym.**.domain\n  global-config:\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      # 如需改为自增 需要将数据库表全部设置为自增\n      idType: ASSIGN_ID\n\n# 数据加密\nmybatis-encryptor:\n  # 是否开启加密\n  enable: false\n  # 默认加密算法\n  algorithm: BASE64\n  # 编码方式 BASE64/HEX。默认BASE64\n  encode: BASE64\n  # 安全秘钥 对称算法的秘钥 如：AES，SM4\n  password:\n  # 公私钥 非对称算法的公私钥 如：SM2，RSA\n  publicKey:\n  privateKey:\n\n# api接口加密\napi-decrypt:\n  # 是否开启全局接口加密\n  enabled: true\n  # AES 加密头标识\n  headerFlag: encrypt-key\n  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=\n  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJnNwrj4hi/y3CCJu868ghCG5dUj8wZK++RNlTLcXoMmdZWEQ/u02RgD5LyLAXGjLOjbMtC+/J9qofpSGTKSx/MCAwEAAQ==\n  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==\n  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=\n\n# 防止XSS攻击\nxss:\n  enabled: true\n  excludeUrls:\n    - /system/notice\n    - /workflow/model/save\n    - /workflow/model/editModelXml\n\n# 接口文档配置\nspringdoc:\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  #  swagger-ui:\n  #    # 持久化认证数据\n  #    persistAuthorization: true\n  info:\n    # 标题\n    title: \'标题：育蒙微服务权限管理系统_接口文档\'\n    # 描述\n    description: \'描述：育蒙微服务权限管理系统\'\n    # 版本\n    version: 1.0.0\n    # 作者信息\n    contact:\n      name: Luo Ming\n      email: <EMAIL>\n      url: http://***********/luoming/yumeng-cloud-api.git\n  components:\n    # 鉴权方式配置\n    security-schemes:\n      apiKey:\n        type: APIKEY\n        in: HEADER\n        name: ${sa-token.token-name}\n\n# seata配置\nseata:\n  # 是否启用\n  enabled: true\n  # Seata 应用编号，默认为应用名\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n\n# 多租户配置\ntenant:\n  # 是否开启\n  enable: false\n  # 排除表\n  excludes:\n    - sys_menu\n    - sys_tenant\n    - sys_tenant_package\n    - sys_role_dept\n    - sys_role_menu\n    - sys_user_post\n    - sys_user_role\n    - sys_client\n    - sys_oss_config\n', '7c0dc7a0584444109c3d247df928d17a', '2025-04-07 20:18:50', '2025-04-07 12:18:50', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (1, 8, 'application-common.yml', 'DEFAULT_GROUP', '', 'server:\n  # undertow 配置\n  undertow:\n    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的\n    max-http-post-size: -1\n    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理\n    # 每块buffer的空间大小,越小的空间被利用越充分\n    buffer-size: 512\n    # 是否分配的直接内存\n    direct-buffers: true\n    threads:\n      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程\n      io: 8\n      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载\n      worker: 256\n\ndubbo:\n  application:\n    # 关闭qos端口避免单机多生产者端口冲突 如需使用自行开启\n    qos-enable: false\n  protocol:\n    # 如需使用 Triple 3.0 新协议 可查看官方文档\n    # 使用 dubbo 协议通信\n    name: dubbo\n    # dubbo 协议端口(-1表示自增端口,从20880开始)\n    port: -1\n    # 指定dubbo协议注册ip\n    # host: *************\n  # 消费者相关配置\n  consumer:\n    # 超时时间\n    timeout: 3000\n  # 自定义配置\n  custom:\n    # 全局请求log\n    request-log: true\n    # info 基础信息 param 参数信息 full 全部\n    log-level: info\n\nspring:\n  threads:\n    # 开启虚拟线程 仅jdk21可用\n    virtual:\n      enabled: false\n  # 资源信息\n  messages:\n    # 国际化资源文件路径\n    basename: i18n/messages\n  servlet:\n    multipart:\n      # 整个请求大小限制\n      max-request-size: 20MB\n      # 上传单个文件大小限制\n      max-file-size: 10MB\n  mvc:\n    # 设置静态资源路径 防止所有请求都去查静态资源\n    static-path-pattern: /static/**\n    format:\n      date-time: yyyy-MM-dd HH:mm:ss\n  #jackson配置\n  jackson:\n    # 日期格式化\n    date-format: yyyy-MM-dd HH:mm:ss\n    serialization:\n      # 格式化输出\n      INDENT_OUTPUT: false\n      # 忽略无法转换的对象\n      fail_on_empty_beans: false\n    deserialization:\n      # 允许对象忽略json中不存在的属性\n      fail_on_unknown_properties: false\n  cloud:\n    nacos:\n      discovery:\n        metadata:\n          # admin 监控账号密码\n          username: yumeng\n          userpassword: yumeng@2025!\n    # sentinel 配置\n    sentinel:\n      # sentinel 开关\n      enabled: true\n      transport:\n        # dashboard控制台服务名 用于服务发现\n        # 如无此配置将默认使用下方 dashboard 配置直接注册\n        server-name: yumeng-sentinel-dashboard\n        # 客户端指定注册的ip 用于多网卡ip不稳点使用\n        # client-ip:\n        # 控制台地址 从1.3.0开始使用 server-name 注册\n        # dashboard: localhost:8718\n\n    bus:\n      id: ${spring.application.name}\n      base-packages: com.ym.**.event\n  # 消息总线 也可以使用 kafka 参考 spring-cloud-bus 用法\n  rabbitmq:\n    host: localhost\n    port: 5672\n    username: yumeng\n    password: yumeng@2025!\n\n  # redis通用配置 子服务可以自行配置进行覆盖\n  data:\n    redis:\n      host: ***********\n      port: 6379\n      # redis 密码必须配置\n      password: yumeng@2025!\n      database: 0\n      # 需要使用数字\n      timeout: 10000\n      ssl.enabled: false\n\n# redisson 配置\nredisson:\n  # redis key前缀\n  keyPrefix:\n  # 线程池数量\n  threads: 4\n  # Netty线程池数量\n  nettyThreads: 8\n  # 单节点配置\n  singleServerConfig:\n    # 客户端名称\n    clientName: ${spring.application.name}\n    # 最小空闲连接数\n    connectionMinimumIdleSize: 8\n    # 连接池大小\n    connectionPoolSize: 32\n    # 连接空闲超时，单位：毫秒\n    idleConnectionTimeout: 10000\n    # 命令等待超时，单位：毫秒\n    timeout: 3000\n    # 发布和订阅连接池大小\n    subscriptionConnectionPoolSize: 50\n\n# 分布式锁 lock4j 全局配置\nlock4j:\n  # 获取分布式锁超时时间，默认为 3000 毫秒\n  acquire-timeout: 3000\n  # 分布式锁的超时时间，默认为 30 秒\n  expire: 30000\n\n# 暴露监控端点\nmanagement:\n  endpoints:\n    web:\n      exposure:\n        include: \'*\'\n  endpoint:\n    health:\n      show-details: ALWAYS\n    logfile:\n      external-file: ./logs/${spring.application.name}/console.log\n\n# 日志配置\nlogging:\n  level:\n    org.springframework: warn\n    org.apache.dubbo: warn\n    com.alibaba.nacos: warn\n    com.alibaba.cloud.sentinel: warn\n    org.mybatis.spring.mapper: error\n    org.apache.dubbo.config: error\n    # 临时处理 spring 调整日志级别导致启动警告问题 不影响使用等待 alibaba 适配\n    org.springframework.context.support.PostProcessorRegistrationDelegate: error\n  config: classpath:logback-plus.xml\n\n# Sa-Token配置\nsa-token:\n  # token名称 (同时也是cookie名称)\n  token-name: Authorization\n  # 开启内网服务调用鉴权(不允许越过gateway访问内网服务 保障服务安全)\n  check-same-token: true\n  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)\n  is-concurrent: true\n  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)\n  is-share: false\n  # jwt秘钥\n  jwt-secret-key: abcdefghijklmnopqrstuvwxyz\n\n# MyBatisPlus配置\n# https://baomidou.com/config/\nmybatis-plus:\n  # 多包名使用 例如 org.dromara.**.mapper,org.xxx.**.mapper\n  mapperPackage: com.ym.**.mapper\n  # 对应的 XML 文件位置\n  mapperLocations: classpath*:mapper/**/*Mapper.xml\n  # 实体扫描，多个package用逗号或者分号分隔\n  typeAliasesPackage: com.ym.**.domain\n  global-config:\n    dbConfig:\n      # 主键类型\n      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID\n      # 如需改为自增 需要将数据库表全部设置为自增\n      idType: ASSIGN_ID\n\n# 数据加密\nmybatis-encryptor:\n  # 是否开启加密\n  enable: false\n  # 默认加密算法\n  algorithm: BASE64\n  # 编码方式 BASE64/HEX。默认BASE64\n  encode: BASE64\n  # 安全秘钥 对称算法的秘钥 如：AES，SM4\n  password:\n  # 公私钥 非对称算法的公私钥 如：SM2，RSA\n  publicKey:\n  privateKey:\n\n# api接口加密\napi-decrypt:\n  # 是否开启全局接口加密\n  enabled: true\n  # AES 加密头标识\n  headerFlag: encrypt-key\n  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=\n  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJnNwrj4hi/y3CCJu868ghCG5dUj8wZK++RNlTLcXoMmdZWEQ/u02RgD5LyLAXGjLOjbMtC+/J9qofpSGTKSx/MCAwEAAQ==\n  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换\n  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==\n  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKNPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gAkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWowcSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99EcvDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthhYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3UP8iWi1Qw0Y=\n\n# 防止XSS攻击\nxss:\n  enabled: true\n  excludeUrls:\n    - /system/notice\n    - /workflow/model/save\n    - /workflow/model/editModelXml\n\n# 接口文档配置\nspringdoc:\n  api-docs:\n    # 是否开启接口文档\n    enabled: true\n  #  swagger-ui:\n  #    # 持久化认证数据\n  #    persistAuthorization: true\n  info:\n    # 标题\n    title: \'标题：育蒙微服务权限管理系统_接口文档\'\n    # 描述\n    description: \'描述：育蒙微服务权限管理系统\'\n    # 版本\n    version: 1.0.0\n    # 作者信息\n    contact:\n      name: Luo Ming\n      email: <EMAIL>\n      url: http://***********/luoming/yumeng-cloud-api.git\n  components:\n    # 鉴权方式配置\n    security-schemes:\n      apiKey:\n        type: APIKEY\n        in: HEADER\n        name: ${sa-token.token-name}\n\n# seata配置\nseata:\n  # 是否启用\n  enabled: true\n  # Seata 应用编号，默认为应用名\n  application-id: ${spring.application.name}\n  # Seata 事务组编号，用于 TC 集群名\n  tx-service-group: ${spring.application.name}-group\n\n# 多租户配置\ntenant:\n  # 是否开启\n  enable: false\n  # 排除表\n  excludes:\n    - sys_menu\n    - sys_tenant\n    - sys_tenant_package\n    - sys_role_dept\n    - sys_role_menu\n    - sys_user_post\n    - sys_user_role\n    - sys_client\n    - sys_oss_config\n', '6f1fd1e0d8068a6eaa09569695a4ab2e', '2025-04-07 20:22:55', '2025-04-07 12:22:56', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (2, 9, 'datasource.yml', 'DEFAULT_GROUP', '', 'datasource:\n  system-master:\n    # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562\n    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能\n    url: ******************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  gen:\n    url: ******************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  job:\n    url: ****************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  workflow:\n    url: **************************************************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n#  system-oracle:\n#    url: ***************************************#    username: ROOT\n#    password: password\n#  system-postgres:\n#    url: ********************************************************************************************************************************************#    username: root\n#    password: password\n\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content\n    dynamic:\n      # 性能分析插件(有性能损耗 不建议生产环境使用)\n      p6spy: true\n      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n      seata: ${seata.enabled}\n      # 严格模式 匹配不到数据源则报错\n      strict: true\n      hikari:\n        # 最大连接池数量\n        maxPoolSize: 20\n        # 最小空闲线程数量\n        minIdle: 10\n        # 配置获取连接等待超时的时间\n        connectionTimeout: 30000\n        # 校验超时时间\n        validationTimeout: 5000\n        # 空闲连接存活最大时间，默认10分钟\n        idleTimeout: 600000\n        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟\n        maxLifetime: 1800000\n        # 多久检查一次连接的活性\n        keepaliveTime: 30000\n', '74c5331b691733e528b2ed0f3d0a3c5e', '2025-04-07 20:25:40', '2025-04-07 12:25:40', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (12, 10, 'seata-server.properties', 'DEFAULT_GROUP', '', 'service.vgroupMapping.yumeng-auth-group=default\nservice.vgroupMapping.yumeng-system-group=default\nservice.vgroupMapping.yumeng-resource-group=default\nservice.vgroupMapping.yumeng-workflow-group=default\n\nservice.enableDegrade=false\nservice.disableGlobalTransaction=false\n\n#Transaction storage configuration, only for the server. The file, DB, and redis configuration values are optional.\nstore.mode=db\nstore.lock.mode=db\nstore.session.mode=db\n#Used for password encryption\n#store.publicKey=\n\n#These configurations are required if the `store mode` is `db`. If `store.mode,store.lock.mode,store.session.mode` are not equal to `db`, you can remove the configuration block.\nstore.db.datasource=hikari\nstore.db.dbType=mysql\nstore.db.driverClassName=com.mysql.cj.jdbc.Driver\nstore.db.url=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************# redis 模式 store.mode=redis 开启 (控制台查询功能有限,不影响实际执行功能)\n# store.redis.host=127.0.0.1\n# store.redis.port=6379\n# 最大连接数\n# store.redis.maxConn=10\n# 最小连接数\n# store.redis.minConn=1\n# store.redis.database=0\n# store.redis.password=\n# store.redis.queryLimit=100\n\n#Transaction rule configuration, only for the server\nserver.recovery.committingRetryPeriod=1000\nserver.recovery.asynCommittingRetryPeriod=1000\nserver.recovery.rollbackingRetryPeriod=1000\nserver.recovery.timeoutRetryPeriod=1000\nserver.maxCommitRetryTimeout=-1\nserver.maxRollbackRetryTimeout=-1\nserver.rollbackRetryTimeoutUnlockEnable=false\nserver.distributedLockExpireTime=10000\nserver.xaerNotaRetryTimeout=60000\nserver.session.branchAsyncQueueSize=5000\nserver.session.enableBranchAsyncRemove=false\n\n#Transaction rule configuration, only for the client\nclient.rm.asyncCommitBufferLimit=10000\nclient.rm.lock.retryInterval=10\nclient.rm.lock.retryTimes=30\nclient.rm.lock.retryPolicyBranchRollbackOnConflict=true\nclient.rm.reportRetryCount=5\nclient.rm.tableMetaCheckEnable=true\nclient.rm.tableMetaCheckerInterval=60000\nclient.rm.sqlParserType=druid\nclient.rm.reportSuccessEnable=false\nclient.rm.sagaBranchRegisterEnable=false\nclient.rm.sagaJsonParser=fastjson\nclient.rm.tccActionInterceptorOrder=-2147482648\nclient.tm.commitRetryCount=5\nclient.tm.rollbackRetryCount=5\nclient.tm.defaultGlobalTransactionTimeout=60000\nclient.tm.degradeCheck=false\nclient.tm.degradeCheckAllowTimes=10\nclient.tm.degradeCheckPeriod=2000\nclient.tm.interceptorOrder=-2147482648\nclient.undo.dataValidation=true\nclient.undo.logSerialization=jackson\nclient.undo.onlyCareUpdateColumns=true\nserver.undo.logSaveDays=7\nserver.undo.logDeletePeriod=86400000\nclient.undo.logTable=undo_log\nclient.undo.compress.enable=true\nclient.undo.compress.type=zip\nclient.undo.compress.threshold=64k\n\n#For TCC transaction mode\ntcc.fence.logTableName=tcc_fence_log\ntcc.fence.cleanPeriod=1h\n\n#Log rule configuration, for client and server\nlog.exceptionRate=100\n\n#Metrics configuration, only for the server\nmetrics.enabled=false\nmetrics.registryType=compact\nmetrics.exporterList=prometheus\nmetrics.exporterPrometheusPort=9898\n\n#For details about configuration items, see https://seata.io/zh-cn/docs/user/configurations.html\n#Transport configuration, for client and server\ntransport.type=TCP\ntransport.server=NIO\ntransport.heartbeat=true\ntransport.enableTmClientBatchSendRequest=false\ntransport.enableRmClientBatchSendRequest=true\ntransport.enableTcServerBatchSendResponse=false\ntransport.rpcRmRequestTimeout=30000\ntransport.rpcTmRequestTimeout=30000\ntransport.rpcTcRequestTimeout=30000\ntransport.threadFactory.bossThreadPrefix=NettyBoss\ntransport.threadFactory.workerThreadPrefix=NettyServerNIOWorker\ntransport.threadFactory.serverExecutorThreadPrefix=NettyServerBizHandler\ntransport.threadFactory.shareBossWorker=false\ntransport.threadFactory.clientSelectorThreadPrefix=NettyClientSelector\ntransport.threadFactory.clientSelectorThreadSize=1\ntransport.threadFactory.clientWorkerThreadPrefix=NettyClientWorkerThread\ntransport.threadFactory.bossThreadSize=1\ntransport.threadFactory.workerThreadSize=default\ntransport.shutdown.wait=3\ntransport.serialization=seata\ntransport.compressor=none\n', 'f8b0ae82879eec9642b60f3fbd655cfd', '2025-04-07 20:26:39', '2025-04-07 12:26:40', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (5, 11, 'yumeng-monitor.yml', 'DEFAULT_GROUP', '', '# 监控中心配置\nspring:\n  security:\n    user:\n      name: yumeng\n      password: ym@18513311526\n  boot:\n    admin:\n      ui:\n        title: yumeng-cloud服务监控中心\n      discovery:\n        # seata 不具有健康检查的能力 防止报错排除掉\n        ignored-services: yumeng-seata-server\n', 'fbf69209430232991ebe29396d53880a', '2025-04-07 21:36:11', '2025-04-07 13:36:11', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (0, 12, 'yumeng-salary-settlement.yml', 'DEFAULT_GROUP', '', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.salary-master.url}\n          username: ${datasource.salary-master.username}\n          password: ${datasource.salary-master.password}', 'dbce1d95d9d1ed513c2d12a0e0d41943', '2025-04-12 16:25:45', '2025-04-12 08:25:45', 'nacos', '************', 'I', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (2, 13, 'datasource.yml', 'DEFAULT_GROUP', '', 'datasource:\n  system-master:\n    # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562\n    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能\n    url: ********************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  gen:\n    url: ********************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  job:\n    url: jdbc:mysql://***********:3307/yumeng-job?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true\n    username: root\n    password: qloong2019\n  workflow:\n    url: jdbc:mysql://***********:3307/yumeng-workflow?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true\n    username: root\n    password: qloong2019\n#  system-oracle:\n#    url: ***************************************#    username: ROOT\n#    password: password\n#  system-postgres:\n#    url: ********************************************************************************************************************************************#    username: root\n#    password: password\n\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content\n    dynamic:\n      # 性能分析插件(有性能损耗 不建议生产环境使用)\n      p6spy: true\n      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n      seata: ${seata.enabled}\n      # 严格模式 匹配不到数据源则报错\n      strict: true\n      hikari:\n        # 最大连接池数量\n        maxPoolSize: 20\n        # 最小空闲线程数量\n        minIdle: 10\n        # 配置获取连接等待超时的时间\n        connectionTimeout: 30000\n        # 校验超时时间\n        validationTimeout: 5000\n        # 空闲连接存活最大时间，默认10分钟\n        idleTimeout: 600000\n        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟\n        maxLifetime: 1800000\n        # 多久检查一次连接的活性\n        keepaliveTime: 30000\n', 'a6f910045d36e83417e2a6512594d6c1', '2025-04-12 16:56:52', '2025-04-12 08:56:52', 'nacos', '************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (2, 14, 'datasource.yml', 'DEFAULT_GROUP', '', 'datasource:\n  system-master:\n    # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562\n    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能\n    url: ********************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  gen:\n    url: ********************************************************************************************************************************************************************************************************************    username: root\n    password: qloong2019\n  job:\n    url: jdbc:mysql://***********:3307/yumeng-job?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true\n    username: root\n    password: qloong2019\n  workflow:\n    url: jdbc:mysql://***********:3307/yumeng-workflow?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true\n    username: root\n    password: qloong2019\n  salary:\n    url: jdbc:mysql://***********:3307/yumeng-salary?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true\n    username: root\n    password: qloong2019\n#  system-oracle:\n#    url: ***************************************#    username: ROOT\n#    password: password\n#  system-postgres:\n#    url: ********************************************************************************************************************************************#    username: root\n#    password: password\n\nspring:\n  datasource:\n    type: com.zaxxer.hikari.HikariDataSource\n    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content\n    dynamic:\n      # 性能分析插件(有性能损耗 不建议生产环境使用)\n      p6spy: true\n      # 开启seata代理，开启后默认每个数据源都代理，如果某个不需要代理可单独关闭\n      seata: ${seata.enabled}\n      # 严格模式 匹配不到数据源则报错\n      strict: true\n      hikari:\n        # 最大连接池数量\n        maxPoolSize: 20\n        # 最小空闲线程数量\n        minIdle: 10\n        # 配置获取连接等待超时的时间\n        connectionTimeout: 30000\n        # 校验超时时间\n        validationTimeout: 5000\n        # 空闲连接存活最大时间，默认10分钟\n        idleTimeout: 600000\n        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟\n        maxLifetime: 1800000\n        # 多久检查一次连接的活性\n        keepaliveTime: 30000\n', '00673de6671c5b750584d17cb7b54242', '2025-04-12 17:21:39', '2025-04-12 09:21:39', 'nacos', '************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (115, 15, 'yumeng-salary-settlement.yml', 'DEFAULT_GROUP', '', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.salary-master.url}\n          username: ${datasource.salary-master.username}\n          password: ${datasource.salary-master.password}', 'dbce1d95d9d1ed513c2d12a0e0d41943', '2025-04-12 17:27:13', '2025-04-12 09:27:13', 'nacos', '************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (12, 16, 'seata-server.properties', 'DEFAULT_GROUP', '', 'service.vgroupMapping.yumeng-auth-group=default\nservice.vgroupMapping.yumeng-system-group=default\nservice.vgroupMapping.yumeng-resource-group=default\nservice.vgroupMapping.yumeng-workflow-group=default\n\nservice.enableDegrade=false\nservice.disableGlobalTransaction=false\n\n#Transaction storage configuration, only for the server. The file, DB, and redis configuration values are optional.\nstore.mode=db\nstore.lock.mode=db\nstore.session.mode=db\n#Used for password encryption\n#store.publicKey=\n\n#These configurations are required if the `store mode` is `db`. If `store.mode,store.lock.mode,store.session.mode` are not equal to `db`, you can remove the configuration block.\nstore.db.datasource=hikari\nstore.db.dbType=mysql\nstore.db.driverClassName=com.mysql.cj.jdbc.Driver\nstore.db.url=jdbc:mysql://***********:3307/yumeng-seata?useUnicode=true&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true\nstore.db.user=root\nstore.db.password=qloong2019\nstore.db.minConn=5\nstore.db.maxConn=30\nstore.db.globalTable=global_table\nstore.db.branchTable=branch_table\nstore.db.distributedLockTable=distributed_lock\nstore.db.queryLimit=100\nstore.db.lockTable=lock_table\nstore.db.maxWait=5000\n\n# redis 模式 store.mode=redis 开启 (控制台查询功能有限,不影响实际执行功能)\n# store.redis.host=127.0.0.1\n# store.redis.port=6379\n# 最大连接数\n# store.redis.maxConn=10\n# 最小连接数\n# store.redis.minConn=1\n# store.redis.database=0\n# store.redis.password=\n# store.redis.queryLimit=100\n\n#Transaction rule configuration, only for the server\nserver.recovery.committingRetryPeriod=1000\nserver.recovery.asynCommittingRetryPeriod=1000\nserver.recovery.rollbackingRetryPeriod=1000\nserver.recovery.timeoutRetryPeriod=1000\nserver.maxCommitRetryTimeout=-1\nserver.maxRollbackRetryTimeout=-1\nserver.rollbackRetryTimeoutUnlockEnable=false\nserver.distributedLockExpireTime=10000\nserver.xaerNotaRetryTimeout=60000\nserver.session.branchAsyncQueueSize=5000\nserver.session.enableBranchAsyncRemove=false\n\n#Transaction rule configuration, only for the client\nclient.rm.asyncCommitBufferLimit=10000\nclient.rm.lock.retryInterval=10\nclient.rm.lock.retryTimes=30\nclient.rm.lock.retryPolicyBranchRollbackOnConflict=true\nclient.rm.reportRetryCount=5\nclient.rm.tableMetaCheckEnable=true\nclient.rm.tableMetaCheckerInterval=60000\nclient.rm.sqlParserType=druid\nclient.rm.reportSuccessEnable=false\nclient.rm.sagaBranchRegisterEnable=false\nclient.rm.sagaJsonParser=fastjson\nclient.rm.tccActionInterceptorOrder=-2147482648\nclient.tm.commitRetryCount=5\nclient.tm.rollbackRetryCount=5\nclient.tm.defaultGlobalTransactionTimeout=60000\nclient.tm.degradeCheck=false\nclient.tm.degradeCheckAllowTimes=10\nclient.tm.degradeCheckPeriod=2000\nclient.tm.interceptorOrder=-2147482648\nclient.undo.dataValidation=true\nclient.undo.logSerialization=jackson\nclient.undo.onlyCareUpdateColumns=true\nserver.undo.logSaveDays=7\nserver.undo.logDeletePeriod=86400000\nclient.undo.logTable=undo_log\nclient.undo.compress.enable=true\nclient.undo.compress.type=zip\nclient.undo.compress.threshold=64k\n\n#For TCC transaction mode\ntcc.fence.logTableName=tcc_fence_log\ntcc.fence.cleanPeriod=1h\n\n#Log rule configuration, for client and server\nlog.exceptionRate=100\n\n#Metrics configuration, only for the server\nmetrics.enabled=false\nmetrics.registryType=compact\nmetrics.exporterList=prometheus\nmetrics.exporterPrometheusPort=9898\n\n#For details about configuration items, see https://seata.io/zh-cn/docs/user/configurations.html\n#Transport configuration, for client and server\ntransport.type=TCP\ntransport.server=NIO\ntransport.heartbeat=true\ntransport.enableTmClientBatchSendRequest=false\ntransport.enableRmClientBatchSendRequest=true\ntransport.enableTcServerBatchSendResponse=false\ntransport.rpcRmRequestTimeout=30000\ntransport.rpcTmRequestTimeout=30000\ntransport.rpcTcRequestTimeout=30000\ntransport.threadFactory.bossThreadPrefix=NettyBoss\ntransport.threadFactory.workerThreadPrefix=NettyServerNIOWorker\ntransport.threadFactory.serverExecutorThreadPrefix=NettyServerBizHandler\ntransport.threadFactory.shareBossWorker=false\ntransport.threadFactory.clientSelectorThreadPrefix=NettyClientSelector\ntransport.threadFactory.clientSelectorThreadSize=1\ntransport.threadFactory.clientWorkerThreadPrefix=NettyClientWorkerThread\ntransport.threadFactory.bossThreadSize=1\ntransport.threadFactory.workerThreadSize=default\ntransport.shutdown.wait=3\ntransport.serialization=seata\ntransport.compressor=none\n', '568e68116a348bb348eb138dfd6f525f', '2025-04-12 17:39:18', '2025-04-12 09:39:18', 'nacos', '0:0:0:0:0:0:0:1', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (12, 17, 'seata-server.properties', 'DEFAULT_GROUP', '', 'service.vgroupMapping.yumeng-auth-group=default\nservice.vgroupMapping.yumeng-system-group=default\nservice.vgroupMapping.yumeng-resource-group=default\nservice.vgroupMapping.yumeng-workflow-group=default\nservice.vgroupMapping.yumeng-salary-settlement-group=default\n\nservice.enableDegrade=false\nservice.disableGlobalTransaction=false\n\n#Transaction storage configuration, only for the server. The file, DB, and redis configuration values are optional.\nstore.mode=db\nstore.lock.mode=db\nstore.session.mode=db\n#Used for password encryption\n#store.publicKey=\n\n#These configurations are required if the `store mode` is `db`. If `store.mode,store.lock.mode,store.session.mode` are not equal to `db`, you can remove the configuration block.\nstore.db.datasource=hikari\nstore.db.dbType=mysql\nstore.db.driverClassName=com.mysql.cj.jdbc.Driver\nstore.db.url=jdbc:mysql://***********:3307/yumeng-seata?useUnicode=true&rewriteBatchedStatements=true&allowPublicKeyRetrieval=true\nstore.db.user=root\nstore.db.password=qloong2019\nstore.db.minConn=5\nstore.db.maxConn=30\nstore.db.globalTable=global_table\nstore.db.branchTable=branch_table\nstore.db.distributedLockTable=distributed_lock\nstore.db.queryLimit=100\nstore.db.lockTable=lock_table\nstore.db.maxWait=5000\n\n# redis 模式 store.mode=redis 开启 (控制台查询功能有限,不影响实际执行功能)\n# store.redis.host=127.0.0.1\n# store.redis.port=6379\n# 最大连接数\n# store.redis.maxConn=10\n# 最小连接数\n# store.redis.minConn=1\n# store.redis.database=0\n# store.redis.password=\n# store.redis.queryLimit=100\n\n#Transaction rule configuration, only for the server\nserver.recovery.committingRetryPeriod=1000\nserver.recovery.asynCommittingRetryPeriod=1000\nserver.recovery.rollbackingRetryPeriod=1000\nserver.recovery.timeoutRetryPeriod=1000\nserver.maxCommitRetryTimeout=-1\nserver.maxRollbackRetryTimeout=-1\nserver.rollbackRetryTimeoutUnlockEnable=false\nserver.distributedLockExpireTime=10000\nserver.xaerNotaRetryTimeout=60000\nserver.session.branchAsyncQueueSize=5000\nserver.session.enableBranchAsyncRemove=false\n\n#Transaction rule configuration, only for the client\nclient.rm.asyncCommitBufferLimit=10000\nclient.rm.lock.retryInterval=10\nclient.rm.lock.retryTimes=30\nclient.rm.lock.retryPolicyBranchRollbackOnConflict=true\nclient.rm.reportRetryCount=5\nclient.rm.tableMetaCheckEnable=true\nclient.rm.tableMetaCheckerInterval=60000\nclient.rm.sqlParserType=druid\nclient.rm.reportSuccessEnable=false\nclient.rm.sagaBranchRegisterEnable=false\nclient.rm.sagaJsonParser=fastjson\nclient.rm.tccActionInterceptorOrder=-2147482648\nclient.tm.commitRetryCount=5\nclient.tm.rollbackRetryCount=5\nclient.tm.defaultGlobalTransactionTimeout=60000\nclient.tm.degradeCheck=false\nclient.tm.degradeCheckAllowTimes=10\nclient.tm.degradeCheckPeriod=2000\nclient.tm.interceptorOrder=-2147482648\nclient.undo.dataValidation=true\nclient.undo.logSerialization=jackson\nclient.undo.onlyCareUpdateColumns=true\nserver.undo.logSaveDays=7\nserver.undo.logDeletePeriod=86400000\nclient.undo.logTable=undo_log\nclient.undo.compress.enable=true\nclient.undo.compress.type=zip\nclient.undo.compress.threshold=64k\n\n#For TCC transaction mode\ntcc.fence.logTableName=tcc_fence_log\ntcc.fence.cleanPeriod=1h\n\n#Log rule configuration, for client and server\nlog.exceptionRate=100\n\n#Metrics configuration, only for the server\nmetrics.enabled=false\nmetrics.registryType=compact\nmetrics.exporterList=prometheus\nmetrics.exporterPrometheusPort=9898\n\n#For details about configuration items, see https://seata.io/zh-cn/docs/user/configurations.html\n#Transport configuration, for client and server\ntransport.type=TCP\ntransport.server=NIO\ntransport.heartbeat=true\ntransport.enableTmClientBatchSendRequest=false\ntransport.enableRmClientBatchSendRequest=true\ntransport.enableTcServerBatchSendResponse=false\ntransport.rpcRmRequestTimeout=30000\ntransport.rpcTmRequestTimeout=30000\ntransport.rpcTcRequestTimeout=30000\ntransport.threadFactory.bossThreadPrefix=NettyBoss\ntransport.threadFactory.workerThreadPrefix=NettyServerNIOWorker\ntransport.threadFactory.serverExecutorThreadPrefix=NettyServerBizHandler\ntransport.threadFactory.shareBossWorker=false\ntransport.threadFactory.clientSelectorThreadPrefix=NettyClientSelector\ntransport.threadFactory.clientSelectorThreadSize=1\ntransport.threadFactory.clientWorkerThreadPrefix=NettyClientWorkerThread\ntransport.threadFactory.bossThreadSize=1\ntransport.threadFactory.workerThreadSize=default\ntransport.shutdown.wait=3\ntransport.serialization=seata\ntransport.compressor=none\n', 'cbb0d9fd782a2dd58a465b199fe3da4e', '2025-04-12 17:45:52', '2025-04-12 09:45:52', 'nacos', '0:0:0:0:0:0:0:1', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (3, 18, 'yumeng-gateway.yml', 'DEFAULT_GROUP', '', '# 安全配置\nsecurity:\n  # 不校验白名单\n  ignore:\n    whites:\n      - /auth/code\n      - /auth/logout\n      - /auth/login\n      - /auth/binding/*\n      - /auth/social/callback\n      - /auth/register\n      - /auth/tenant/list\n      - /resource/sms/code\n      - /resource/sse/close\n      - /*/v3/api-docs\n      - /*/error\n      - /csrf\n\nspring:\n  cloud:\n    # 网关配置\n    gateway:\n      # 打印请求日志(自定义)\n      requestLog: true\n      discovery:\n        locator:\n          lowerCaseServiceId: true\n          enabled: true\n      routes:\n        # 认证中心\n        - id: yumeng-auth\n          uri: lb://yumeng-auth\n          predicates:\n            - Path=/auth/**\n          filters:\n            - StripPrefix=1\n        # 代码生成\n        - id: yumeng-gen\n          uri: lb://yumeng-gen\n          predicates:\n            - Path=/tool/**\n          filters:\n            - StripPrefix=1\n        # 系统模块\n        - id: yumeng-system\n          uri: lb://yumeng-system\n          predicates:\n            - Path=/system/**,/monitor/**\n          filters:\n            - StripPrefix=1\n        # 资源服务\n        - id: yumeng-resource\n          uri: lb://yumeng-resource\n          predicates:\n            - Path=/resource/**\n          filters:\n            - StripPrefix=1\n        # workflow服务\n        - id: yumeng-workflow\n          uri: lb://yumeng-workflow\n          predicates:\n            - Path=/workflow/**\n          filters:\n            - StripPrefix=1\n        # 演示服务\n        - id: yumeng-demo\n          uri: lb://yumeng-demo\n          predicates:\n            - Path=/demo/**\n          filters:\n            - StripPrefix=1\n        # MQ演示服务\n        - id: yumeng-test-mq\n          uri: lb://yumeng-test-mq\n          predicates:\n            - Path=/test-mq/**\n          filters:\n            - StripPrefix=1\n\n    # sentinel 配置\n    sentinel:\n      filter:\n        enabled: false\n      # nacos配置持久化\n      datasource:\n        ds1:\n          nacos:\n            server-addr: ${spring.cloud.nacos.server-addr}\n            dataId: sentinel-${spring.application.name}.json\n            groupId: ${spring.cloud.nacos.config.group}\n            username: ${spring.cloud.nacos.username}\n            password: ${spring.cloud.nacos.password}\n            namespace: ${spring.profiles.active}\n            data-type: json\n            rule-type: gw-flow\n', 'fb6a0c83b471bdbb3bef52f7d8fef97b', '2025-04-12 19:58:48', '2025-04-12 11:58:48', 'nacos', '***************', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (7, 19, 'yumeng-gen.yml', 'DEFAULT_GROUP', '', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      seata: false\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.system-master.url}\n          username: ${datasource.system-master.username}\n          password: ${datasource.system-master.password}\n#        oracle:\n#          type: ${spring.datasource.type}\n#          driverClassName: oracle.jdbc.OracleDriver\n#          url: ${datasource.system-oracle.url}\n#          username: ${datasource.system-oracle.username}\n#          password: ${datasource.system-oracle.password}\n#        postgres:\n#          type: ${spring.datasource.type}\n#          driverClassName: org.postgresql.Driver\n#          url: ${datasource.system-postgres.url}\n#          username: ${datasource.system-postgres.username}\n#          password: ${datasource.system-postgres.password}\n\n# 代码生成\ngen:\n  # 作者\n  author: luoming\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ym.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', '1c0f0e275e4b988dab06ace8fb80a3a8', '2025-04-12 20:36:08', '2025-04-12 12:36:09', 'nacos', '0:0:0:0:0:0:0:1', 'U', 'dev', '');
INSERT INTO `his_config_info` (`id`, `nid`, `data_id`, `group_id`, `app_name`, `content`, `md5`, `gmt_create`, `gmt_modified`, `src_user`, `src_ip`, `op_type`, `tenant_id`, `encrypted_data_key`) VALUES (7, 20, 'yumeng-gen.yml', 'DEFAULT_GROUP', '', 'spring:\n  datasource:\n    dynamic:\n      # 设置默认的数据源或者数据源组,默认值即为 master\n      primary: master\n      seata: false\n      datasource:\n        # 主库数据源\n        master:\n          type: ${spring.datasource.type}\n          driver-class-name: com.mysql.cj.jdbc.Driver\n          url: ${datasource.salary.url}\n          username: ${datasource.salary.username}\n          password: ${datasource.salary.password}\n#        oracle:\n#          type: ${spring.datasource.type}\n#          driverClassName: oracle.jdbc.OracleDriver\n#          url: ${datasource.system-oracle.url}\n#          username: ${datasource.system-oracle.username}\n#          password: ${datasource.system-oracle.password}\n#        postgres:\n#          type: ${spring.datasource.type}\n#          driverClassName: org.postgresql.Driver\n#          url: ${datasource.system-postgres.url}\n#          username: ${datasource.system-postgres.username}\n#          password: ${datasource.system-postgres.password}\n\n# 代码生成\ngen:\n  # 作者\n  author: luoming\n  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool\n  packageName: com.ym.system\n  # 自动去除表前缀，默认是false\n  autoRemovePre: false\n  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）\n  tablePrefix: sys_\n', 'd4c86821693601fdfed7e118f449dd0d', '2025-04-12 20:38:24', '2025-04-12 12:38:25', 'nacos', '0:0:0:0:0:0:0:1', 'U', 'dev', '');
COMMIT;

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `role` varchar(50) NOT NULL COMMENT 'role',
  `resource` varchar(128) NOT NULL COMMENT 'resource',
  `action` varchar(8) NOT NULL COMMENT 'action',
  UNIQUE KEY `uk_role_permission` (`role`,`resource`,`action`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of permissions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `username` varchar(50) NOT NULL COMMENT 'username',
  `role` varchar(50) NOT NULL COMMENT 'role',
  UNIQUE KEY `idx_user_role` (`username`,`role`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of roles
-- ----------------------------
BEGIN;
INSERT INTO `roles` (`username`, `role`) VALUES ('nacos', 'ROLE_ADMIN');
COMMIT;

-- ----------------------------
-- Table structure for tenant_capacity
-- ----------------------------
DROP TABLE IF EXISTS `tenant_capacity`;
CREATE TABLE `tenant_capacity` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'Tenant ID',
  `quota` int unsigned NOT NULL DEFAULT '0' COMMENT '配额，0表示使用默认值',
  `usage` int unsigned NOT NULL DEFAULT '0' COMMENT '使用量',
  `max_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个配置大小上限，单位为字节，0表示使用默认值',
  `max_aggr_count` int unsigned NOT NULL DEFAULT '0' COMMENT '聚合子配置最大个数',
  `max_aggr_size` int unsigned NOT NULL DEFAULT '0' COMMENT '单个聚合数据的子配置大小上限，单位为字节，0表示使用默认值',
  `max_history_count` int unsigned NOT NULL DEFAULT '0' COMMENT '最大变更历史数量',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='租户容量信息表';

-- ----------------------------
-- Records of tenant_capacity
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for tenant_info
-- ----------------------------
DROP TABLE IF EXISTS `tenant_info`;
CREATE TABLE `tenant_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `kp` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'kp',
  `tenant_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_id',
  `tenant_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'tenant_name',
  `tenant_desc` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'tenant_desc',
  `create_source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT 'create_source',
  `gmt_create` bigint NOT NULL COMMENT '创建时间',
  `gmt_modified` bigint NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_info_kptenantid` (`kp`,`tenant_id`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='tenant_info';

-- ----------------------------
-- Records of tenant_info
-- ----------------------------
BEGIN;
INSERT INTO `tenant_info` (`id`, `kp`, `tenant_id`, `tenant_name`, `tenant_desc`, `create_source`, `gmt_create`, `gmt_modified`) VALUES (1, '1', 'dev', 'dev', '开发环境', NULL, 1641741261189, 1641741261189);
INSERT INTO `tenant_info` (`id`, `kp`, `tenant_id`, `tenant_name`, `tenant_desc`, `create_source`, `gmt_create`, `gmt_modified`) VALUES (2, '1', 'prod', 'prod', '生产环境', NULL, 1641741270448, 1641741287236);
COMMIT;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `username` varchar(50) NOT NULL COMMENT 'username',
  `password` varchar(500) NOT NULL COMMENT 'password',
  `enabled` tinyint(1) NOT NULL COMMENT 'enabled',
  PRIMARY KEY (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of users
-- ----------------------------
BEGIN;
INSERT INTO `users` (`username`, `password`, `enabled`) VALUES ('nacos', '$2a$10$eBH1sXaVxMmqqQyLYbxfSu8gEB5Mk3EpGudrrEVM8GuMqjWPM8HxG', 1);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
